import Vue from 'vue'
import App from './App.vue'
import router from './router'
import './plugins/element.js'
import axios from 'axios'
import VueAxios from 'vue-axios'
import ElementUI from 'element-ui'
ElementUI.Dialog.props.lockScroll.default = false
import 'element-ui/lib/theme-chalk/index.css'

import echarts from 'echarts'
Vue.prototype.$echarts = echarts
import './icons'
import UmyUi from 'umy-ui'
import ajax from './ajax.js'
Vue.prototype.ajax = ajax
import store from './store'
import 'umy-ui/lib/theme-chalk/index.css' // 引入样式
import 'vant/lib/index.css'
import Vant from 'vant'
import JsonViewer from 'vue-json-viewer'
import Fingerprint2 from 'fingerprintjs2'
import {
  getTime_Http,
  getTime_HttpSession,
  getTimeForPublickey,
} from '@/api/api.js'
import axiosInstance from '@/utils/getimerequest.js'
import { sm2 } from 'sm-crypto'
ElementUI.TableColumn.props.showOverflowTooltip = {
  type: Boolean,
  default: true,
}
console.log(window.wgParameter.edition)
Vue.use(ElementUI, {
  size: 'small',
  menuType: 'text',
})
Vue.use(JsonViewer)
Vue.use(Vant)
Vue.use(VueAxios, axios)
Vue.prototype.$ELEMENT = { size: 'small' }
// Vue.prototype.$Cookies = Cookies
Vue.config.productionTip = false
Vue.use(UmyUi)

console.log(process.env, '环境', ' 0114.1')
const VueInstance = new Vue({
  router,
  store,
  render: (h) => h(App),
})
var render = () => {
  VueInstance.$mount('#app')
}
// render()
// 单加密
;(async () => {
  // 安全数据存在时，加载vue

  if (sessionStorage.getItem('bhxnsafetyData')) {
    console.log('111111111')
    render()
    return
  }
  // 获取安全数据


  let safetyData = sessionStorage.getItem('wbSafetyData')
  sessionStorage.setItem('bhxnsafetyData', safetyData)
  if (safetyData) {
    console.log(safetyData)
    render()
    return
  }

  console.error('浏览器安全验证未通过,请刷新重试')
})()

// 全局路由守卫
// router.beforeEach((to, from, next) => {
//   if (Cookies.get('userToken') && to.path !== '/login') {
//     // 若存在token则放行
//     next()
//   } else if (Cookies.get('userToken') && to.path === '/login') {
//     // 若存在token则不可以访问登录页面
//     next('/')
//   } else if (!Cookies.get('userToken') && to.path === '/login') {
//     // 若token不存在，且访问的是登录页面则放行
//     next()
//   } else {
//     next('/login')
//   }
// })

// .beforeEach((进入到哪一个路由,从哪一个路由离开,对应的函数)=>{})
// router.beforeEach((to,form,next) =>{
//   //如果进入到的路由是登录页或者注册页面，则正常展示
//   if(to.path == '/login' ){
//       next();
//   }else if( !Cookies.get('userToken') ){
//       alert("还没有登录，请先登录！");
//       next('/login');     //转入login登录页面，登录成功后会将token存入localStorage
//   }else{
//       next();
//   }
// })

// new Vue({
//   router,
//   store,
//   render: h => h(App)
// }).$mount('#app')
