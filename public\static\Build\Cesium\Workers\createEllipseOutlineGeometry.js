define(["./Cartographic-3309dd0d","./when-b60132fc","./EllipseOutlineGeometry-4e6de759","./Cartesian2-47311507","./Check-7b2a090c","./Math-119be1a3","./arrayFill-4513d7ad","./buildModuleUrl-4e1b81e7","./PrimitiveType-a54dc62f","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./EllipseGeometryLibrary-96261ee5","./GeometryAttribute-3a88ba31","./FeatureDetection-c3b71206","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4"],(function(e,t,a,r,i,n,l,o,d,c,b,s,u,p,y,f,m,G,C,E){"use strict";return function(i,n){return t.defined(n)&&(i=a.EllipseOutlineGeometry.unpack(i,n)),i._center=e.Cartesian3.clone(i._center),i._ellipsoid=r.Ellipsoid.clone(i._ellipsoid),a.EllipseOutlineGeometry.createGeometry(i)}}));
