<template>
  <div>
    <!--同杆并架-->
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div v-if="showBackAdds" class="maps-zhedNav" @click="backCurrentDom">
            <img class="mapites-backImg" :src="require('@/assets/'+'map/settingImg/backAdds.png')" alt="">
          </div>
          <span />
          同杆并架
          <div v-if="!showBackAdds" class="maps-zhedNav" @click="showEveryItemSet">
            <img v-show="!isFoldArea" class="mapites-zhed" :src="require('@/assets/'+'map/settingImg/zhedie.png')" alt="">
            <img v-show="isFoldArea" class="mapites-zhed" :src="require('@/assets/'+'map/settingImg/zkzhedie.png')" alt="">
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img class="settingImg" :src="require('@/assets/'+'map/settingImg/useSetting.png')" alt="">
          <p>启用</p>
        </div>
      </div>
      <div v-show="isFoldArea" class="pro-addForm">
        <div v-show="isShowNav" class="map-showNav">基本信息</div>
        <van-row v-show="isShowNav">
          <van-field
            v-model="startPointMark"
            label="开始点编号"
            disabled
            placeholder="请输入杆塔编号"
          />
        </van-row>
        <van-row v-show="isShowNav">
          <van-field
            v-model="endPointMark"
            disabled
            label="结束点编号"
            placeholder="请输入杆塔编号"
          />
        </van-row>
        <div class="map-showNav">选型信息</div>
        <van-row>
          <!--同杆并架电压等级-->
          <van-field
            readonly
            clickable
            :value="leverHead.voltage"
            label="电压等级"
            placeholder="选择电压等级"
            @click="settingObj.leverHead.voltageVis = true"
          />
          <van-popup v-model="settingObj.leverHead.voltageVis" round position="bottom">
            <van-picker
              show-toolbar
              title="电压等级"
              value-key="key"
              :columns="allVoltage"
              @confirm="onConfirmTgbjSel(0, $event)"
              @cancel="settingObj.leverHead.voltageVis = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--同杆并架线路名称-->
          <van-field
            v-model="leverHead.lineName"
            label="线路名称"
            placeholder="请输入线路名称"
          />
        </van-row>
        <van-row>
          <!--同杆并架导线状态-->
          <van-field
            readonly
            clickable
            :value="leverHead.state"
            label="导线状态"
            placeholder="选择状态"
            @click="settingObj.leverHead.stateVis = true"
          />
          <van-popup v-model="settingObj.leverHead.stateVis" round position="bottom">
            <van-picker
              show-toolbar
              title="导线状态"
              value-key="key"
              :columns="cableLineState"
              @confirm="onConfirmTgbjSel(2, $event)"
              @cancel="settingObj.leverHead.stateVis = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--同杆并架杆头状态-->
          <van-field
            readonly
            clickable
            :value="leverHead.lineState"
            label="杆头状态"
            placeholder="请选择杆头状态"
            @click="settingObj.leverHead.lineStateVis = true"
          />
          <van-popup v-model="settingObj.leverHead.lineStateVis" round position="bottom">
            <van-picker
              title="杆头状态"
              show-toolbar
              value-key="key"
              :columns="cableLineState"
              @confirm="onConfirmTgbjSel(3, $event)"
              @cancel="settingObj.leverHead.lineStateVis = false"
            />
          </van-popup>
        </van-row>
        <!--同杆并架导线型号-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="leverHead.lineModel"
            label="导线型号"
            placeholder="选择导线型号"
            @click="settingObj.leverHead.lineModelVis = true"
          />
          <van-popup v-model="settingObj.leverHead.lineModelVis" round position="bottom">
            <van-picker
              title="导线型号"
              show-toolbar
              value-key="name"
              :columns="tgbjlineType"
              @confirm="onConfirmTgbjSel(1, $event)"
              @cancel="settingObj.leverHead.lineModelVis = false"
            />
          </van-popup>
        </van-row>
        <div v-if="isShowNav" class="map-showNav">其他信息</div>
        <van-row v-if="isShowNav">
          <!--经度-->
          <van-field
            v-model="startLngtitude"
            label="起始点经度"
            disabled
          />
        </van-row>
        <van-row v-if="isShowNav">
          <!--纬度-->
          <van-field
            v-model="startLattitude"
            label="起始点纬度"
            disabled
          />
        </van-row>
        <van-row v-if="isShowNav">
          <!--高程-->
          <van-field
            v-model="endLngtitude"
            label="结束点经度"
            disabled
          />
        </van-row>
        <van-row v-if="isShowNav">
          <!--高程-->
          <van-field
            v-model="endLattitude"
            label="结束点纬度"
            disabled
          />
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { apiget,apipost } from '@/utils/mapRequest'

export default {
  components: { },
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false
    },
    // 编辑的id
    editData: {
      type: Object,
      defaults: () => {
      }
    },
    // 导线型号默认值
    dxList: {
      type: Array,
      defaults: () => []
    },
    remodeState: {
      type: Boolean,
      defaults: false
    }
  },
  watch: {
    // 监听改前改后状态
    remodeState: {
      handler(newVal) {
        if (newVal) {
          // 改后不显示拆除的数据
          this.cableLineState = this.lineState.slice(0, 2)
          this.leverHead.state = '新建'
          this.leverHead.lineState = '新建'
        } else {
          // 改前不显示新建的数据
          this.cableLineState = this.lineState.slice(1)
          this.leverHead.state = '原有'
          this.leverHead.lineState = '原有'
        }
      },
      deep: true,
      immediate: true
    },
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal
      },
      deep: true,
      immediate: true
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal
        if (data.moudleType === 'TGBJHZ') {
          const valatege = data.voltage === '10kV' ? '10kV' : '0.4kV'
          this.getAwaitTowerOrLineType(2, 1, '', '', '', '', valatege)
          this.startLattitude = data.lonA // 有起始点存在情况的开始点纬度
          this.endLngtitude = data.lonB // 有起始点存在情况的结束点经度
          this.endLattitude = data.latB // 有起始点存在情况的结束点纬度
          this.startLngtitude = data.latA
          this.startPointMark = data.startMark
          this.endPointMark = data.endMark
          this.leverHead.lineState = data.state
          this.leverHead.voltage = data.voltage
          this.leverHead.lineName = data.lineName
          this.leverHead.state = data.gtType
          this.leverHead.lineModelId = data.lineModelId
          this.leverHead.lineModel = data.lineModel
        }
      },
      deep: true
    },
    dxList: {
      handler(newVal) {
        this.leverHead.lineModel = newVal[0].name
        this.leverHead.lineModelId = newVal[0].id
        this.tgbjlineType = newVal.tenDx
      },
      deep: true
    }
  },
  data() {
    return {
      isFoldArea: false,
      startPointMark: '', // 开始点编号
      endPointMark: '', // 结束点编号
      startLngtitude: 0, // 有起始点存在情况的开始点经度
      startLattitude: 0, // 有起始点存在情况的开始点纬度
      endLngtitude: 0, // 有起始点存在情况的结束点经度
      endLattitude: 0, // 有起始点存在情况的结束点纬度
      tgbjlineType: [], // 同杆并架导线型号
      cableLineState: [], // 电缆线路状态
      lineState: [
        {
          key: '新建',
          value: '新建'
        },
        {
          key: '原有',
          value: '原有'
        },
        {
          key: '拆除',
          value: '拆除'
        }
      ],
      allVoltage: [
        {
          key: '10kV',
          value: '10kV'
        },
        {
          key: '380V',
          value: '380V'
        },
        {
          key: '220V',
          value: '220V'
        }
      ], // 电压等级
      settingObj: {
        // 同杆并架
        leverHead: {
          voltageVis: false, // 电压等级
          stateVis: false, // 状态
          lineStateVis: false, // 导线状态
          lineModelVis: false // 导线型号
        }
      },
      // 同杆并架
      leverHead: {
        voltage: '10kV', // 电压等级
        state: '新建', // 导线状态
        lineName: '线路一', // 线路名称
        lineState: '新建', // 杆头状态
        lineModel: '', // 导线型号
        lineModelId: '', // 导线型号id
        imgList: [], // 文件列表
        message: '', // 备注信息
        audioList: [] // 语音列表
      }
    }
  },
  mounted() {},
  methods: {
    backCurrentDom() {
      this.$emit('backCurrentDom')
      this.leverHead.lineModel = this.dxList[0].name
      this.leverHead.lineModelId = this.dxList[0].id
      this.tgbjlineType = this.dxList
      // 同杆并架的选型值
      this.leverHead.voltage = '10kV'
      const stateText = this.remodeState ? '新建' : '原有'
      this.leverHead.state = stateText
      this.leverHead.lineName = '线路一'
      this.leverHead.lineState = stateText
      this.leverHead.lineModel = ''
      this.leverHead.lineModelId = ''
      this.leverHead.imgList = []
      this.leverHead.message = ''
      this.leverHead.audioList = []
      this.settingObj.leverHead.voltageVis = false
      this.settingObj.leverHead.stateVis = false
      this.settingObj.leverHead.lineStateVis = false
      this.settingObj.leverHead.lineModelVis = false
    },
    /**
     * 提交数据
     */
    submitData() {
      const parma = {
        type: 2,
        param: this.leverHead,
        visParam: this.settingObj.leverHead
      }
      this.$emit('submitChildData', parma)
    },
    showEveryItemSet() {
      this.isFoldArea = !this.isFoldArea
    },
    /**
     * 获取上传图片数据
     */
    getImgData(data) {
      this.leverHead.imgList = data.imgList
    },
    /**
     * 获取备注信息
     * @param data
     */
    getMsgData(data) {
      this.leverHead.message = data.message
    },
    /**
     * 获取语音数据
     */
    getAudioData(data) {
      this.leverHead.audioList = data.aduioList
    },
    /**
     * 同杆并架
     * @params type 类型
     * @params item 单个项
     */
    onConfirmTgbjSel(type, item) {
      const val = item.value
      switch (type) {
        case 0:
          this.leverHead.voltage = val
          // 220v和330v都按照0.4kv去查
          const valltege = val === '10kV' ? '10kV' : '0.4kV'
          // 查询杆塔类型
          this.getTowerOrLineType(2, 1, '', '', '', '', valltege)
          // 查询电缆线路类型
          this.settingObj.leverHead.voltageVis = false
          break
        case 1:
          this.leverHead.lineModel = item.name
          this.leverHead.lineModelId = item.id
          this.settingObj.leverHead.lineModelVis = false
          break
        case 2:
          this.leverHead.state = val
          this.settingObj.leverHead.stateVis = false
          break
        case 3:
          this.leverHead.lineState = val
          this.settingObj.leverHead.lineStateVis = false
          break
      }
    },
    getFirstTowerOrLineType(type, moduleType, materialsTypeKey, moduleTypeKey, parentKey, moduleCode, voltage, moduleName) {
      const that = this
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: 'aaf86d47-14b7-4da6-9938-398e476a2a75' // 版本号暂时写死 版本号是后台给的
      }
      apipost('/moduleSelection/selectModuleData', param).then(function(res) {
        if (res.code === 1001) {
          if (voltage === '10kV') {
            // 同杆并架导线型号
            that.leverHead.lineModel = res.data.tenDx[0].name
            that.leverHead.lineModelId = res.data.tenDx[0].id
            that.tgbjlineType = res.data.tenDx
          } else {
            // 同杆并架导线型号
            that.tgbjlineType = res.data.lowDx
            that.leverHead.lineModel = res.data.lowDx[0].name
            that.leverHead.lineModelId = res.data.lowDx[0].id
          }
        }
      })
    },
    getAwaitTowerOrLineType(settype, type, moduleType, materialsTypeKey, moduleTypeKey, parentKey, moduleCode, voltage, moduleName, selectVal, selectLevelTwoVal) {
      const that = this
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: 'aaf86d47-14b7-4da6-9938-398e476a2a75' // 版本号暂时写死 版本号是后台给的
      }
      apipost('/moduleSelection/selectModuleData', param).then(function(res) {
        if (res.code === 1001) {
          // 同杆并架导线型号
          if (voltage === '10kV') {
            that.settingObj.dxlineType = res.data.tenDx
          } else {
            that.settingObj.dxlineType = res.data.lowDx
          }
        }
      })
    },
    getTowerOrLineType(settype, type, moduleType, materialsTypeKey, moduleTypeKey, parentKey, moduleCode, voltage, moduleName) {
      const that = this
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: 'aaf86d47-14b7-4da6-9938-398e476a2a75' // 版本号暂时写死 版本号是后台给的
      }
      apipost('/moduleSelection/selectModuleData', param).then(function(res) {
        if (res.code === 1001) {
          // 分线路杆塔型号
          if (voltage === '10kV') {
            that.branchtowerModel = res.data.tenSNG
            that.branchLine.towerModel = res.data.tenSNG[0].name
            that.branchLine.towerModelId = res.data.tenSNG[0].id
          } else {
            that.branchtowerModel = res.data.lowSNG
            that.branchLine.towerModel = res.data.lowSNG[0].name
            that.branchLine.towerModelId = res.data.lowSNG[0].id
          }
        }
      })
    },

  }
}

</script>

<style lang="sass" scoped>
</style>

