<template>
  <div>
    <div class="main-header">自动选型管理</div>
    <div class="driver" style="margin-bottom: 0"></div>
    <div class="cp-tableInfo">
      <div class="cp-btns">
        <div class="cp-handleBtn" @click="save">
          <span class="cp-texts">
            <span style="margin-left: 5px">
              保存
              <i class="el-icon-plus"></i>
            </span>
          </span>
        </div>
      </div>
    </div>
    <div>
      <div class="item-content">
        <DrawingTool :tabKey="activeName" v-if="activeName === 'drawing'" />
      </div>
      <el-container>
        <el-aside
          :style="{ height: tableHeight + 'px' }"
          style="width: 240px; overflow-y: scroll"
        >
          <Treeleft
            :tabKey="activeName"
            v-if="activeName === 'module'"
            v-on:treeclick="treeclick"
          />
        </el-aside>
        <el-container>
          <el-main>
            <el-table
              :data="moduleData"
              v-loading="loading"
              :height="tableHeight"
              align="center"
              :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
              style="width: 98%"
              :row-class-name="rowIndex"
              class="widthFull"
            >
              <el-table-column
                type="index"
                align="center"
                label="序号"
                width="60"
              >
              </el-table-column>
              <el-table-column type="selection" width="55"> </el-table-column>
              <!-- <el-table-column prop="ModuleTypeKey" label="模块类别" width="100">
              </el-table-column> -->
              <el-table-column prop="ModuleName" label="模块名称" align="center">
              </el-table-column>
              <el-table-column prop="ModuleCode" label="模块编码" align="center">
              </el-table-column>
              <el-table-column prop="Voltage" label="电压等级" align="center">
              </el-table-column>
              <el-table-column prop="Level" label="模块层级" align="center"> </el-table-column>
              <el-table-column prop="isStandard" label="是否标准模块" align="center">
              </el-table-column>
              <el-table-column prop="StateFlag" label="原有标志" align="center">
              </el-table-column>
              <el-table-column prop="UseInfer" label="推导优先" align="center">
              </el-table-column>
            </el-table>
            <el-pagination
              style="margin: 10px"
              background
              :current-page="queryParams2.pageNum"
              :page-sizes="pageSize"
              :page-size="queryParams2.pageSize"
              layout="total, sizes, prev, pager, next"
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
              :total="total2"
            >
            </el-pagination>
          </el-main>
        </el-container>
      </el-container>
    </div>
  </div>
</template>

<script>
import {
  getDrawingList,
  getModuleList,
  getMaterialsProjectList,
} from '@/views/basicParameter/data-static/data.js'
import Treeleft from './components/Treeleft.vue'
import MaterialTool from './components/material-tool.vue'
import ModuleTool from './components/module-tool.vue'
import DrawingTool from './components/drawing-tool.vue'
export default {
  name: 'autoChangeType',
  components: { Treeleft, MaterialTool, ModuleTool, DrawingTool },
  data() {
    return {
      name: '',
      labelPosition: 'right',
      activeName: 'module',
      form: {
        selectVersion: 1,
      },
      loading: true,
      // 总条数
      total1: 0,
      total2: 0,
      total3: 0,
      tableHeight: 0,
      // 查询参数
      queryParams1: {
        pageNum: 1,
        pageSize: 10,
        search: '',
      },
      pageSize: [10, 20, 50, 100], //分页页数
      queryParams2: {
        pageNum: 1,
        pageSize: 10,
        search: '',
      },
      queryParams3: {
        pageNum: 1,
        pageSize: 10,
        search: '',
      },
      materialData: [],
      moduleData: [],
      drawingData: [],
      materialProperty: [],
      rules: {},
      version: [
        { Id: 1, Name: '20221024' },
        { Id: 2, Name: '20220915' },
      ],
    }
  },
  mounted() {
    this.setTablesHeight()
    const that = this
    window.onresize = function () {
      that.setTablesHeight()
    }
  },
  created() {
    this.loading = false
  },
  methods: {
    setTablesHeight() {
      this.$nextTick(() => {
        const tablesAreaHeight = document
          .getElementsByClassName('driver')[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        this.tableHeight = windowsAreaHeight - tablesAreaHeight - 130
      })
    },
    save() {
      this.$message({
        message: '保存成功',
        type: 'success',
      })
    },
    handleCurrentChange(val) {
      this.queryParams2.pageNum = val
      this.getCurrentModuleList()
    },
    handleSizeChange(val) {
      this.queryParams2.pageSize = val
      this.getCurrentModuleList()
    },
    rowIndex({ row, rowIndex }) {
      //增加索引
      if (this.activeName === 'material') {
        row.index =
          rowIndex +
          1 +
          (this.queryParams1.pageNum - 1) * this.queryParams1.pageSize
      } else if (this.activeName === 'module') {
        row.index =
          rowIndex +
          1 +
          (this.queryParams2.pageNum - 1) * this.queryParams2.pageSize
      } else if (this.activeName === 'drawing') {
        row.index =
          rowIndex +
          1 +
          (this.queryParams3.pageNum - 1) * this.queryParams3.pageSize
      }
    },
    formatterWeight(row, column, cellValue, index) {
      if (cellValue == 0) return ''
      return cellValue.toFixed(2)
    },
    formatterIsDonor(row, column, cellValue, index) {
      if (cellValue === undefined || cellValue === 0) return '甲供'
      else if (cellValue === 1) return '乙供'
      return ''
    },
    treeclick(nodekey, tabkey) {
      if (tabkey === 'material') {
        if (this.queryParams1.search != nodekey) {
          this.queryParams1.pageNum = 1
        }
        this.queryParams1.search = nodekey
        this.getCurrentMaterialsProjectList()
      } else if (tabkey === 'module') {
        if (this.queryParams2.search != nodekey) {
          this.queryParams2.pageNum = 1
        }
        this.queryParams2.search = nodekey
        this.getCurrentModuleList()
      } else if (tabkey === 'drawing') {
        if (this.queryParams3.search != nodekey) {
          this.queryParams3.pageNum = 1
        }
        this.queryParams3.search = nodekey
        this.getCurrentDrawingList()
      }
    },
    getCurrentMaterialsProjectList() {
      this.loading = true
      console.log('获取物料')
      let response = getMaterialsProjectList(this.queryParams1)
      this.materialData = response.rows
      this.total1 = response.total
      this.loading = false
    },
    getCurrentModuleList() {
      this.loading = true
      console.log('获取模块')
      let response = getModuleList(this.queryParams2)
      this.moduleData = response.rows
      this.total2 = response.total
      this.loading = false
    },
    getCurrentDrawingList() {
      this.loading = true
      console.log('获取图纸')
      let response = getDrawingList(this.queryParams3)
      this.drawingData = response.rows
      this.total3 = response.total
      this.loading = false
    },
    handleClick(tab, event) {},
  },
}
</script>

<style scoped lang="scss">
.app-container {
  background-color: #fff;
}
.widthFull {
  width: 100%;
}
aside {
  padding: 8px;
}
.el-main {
  padding: 0;
  padding-left: 16px !important;
}
.content-title {
  position: relative;
  padding-left: 10px;
  color: #526ade;
}
.content-title::after {
  content: '';
  position: absolute;
  left: 0;
  top: 50%; /* 将竖杠放置在元素的垂直中心位置 */
  transform: translateY(-50%);
  width: 5px; /* 竖杠的宽度 */
  height: 17px; /* 竖杠的高度 */
  background-color: #526ade; /* 竖杠的颜色 */
  border-radius: 5px; /* 添加弧度 */
  content: '';
}
::v-deep.widthFull .cell {
  font-size: 14px !important;
}
.footerBtn {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cp-tableInfo {
  background: #ffffff;
  display: flex;
  .cp-checkNav {
    width: 400px;
    background: #f5f6fa;
    padding-left: 32px;
    height: 36px;
  }
  .cp-btns {
    background: #ffffff;
    margin: 16px 0 16px 16px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-left: 12px;
    color: #7286e8;
    .cp-handleBtn {
      height: 26px;
      font-size: 12px;
      display: flex;
      color: #333333;
      background: #ffffff;
      border-radius: 5px;
      padding: 0 12px;
      align-items: center;
      cursor: pointer;
      border: 1px solid #abbdf7;
    }
    .cp-handleBtn:nth-child(2) {
      margin-left: 10px;
    }
    .cp-drawer {
      margin: 0 10px;
      color: #ccc;
    }

    .cp-handleUnBtn:hover {
      color: #7487e4;
      border-color: #7487e4;
    }

    .cp-handleUnBtn:active {
      color: #0c6b66;
      border-color: #0c6b66;
    }

    .cp-handleUnBtn:focus {
      color: #0c6b66;
      border-color: #0c6b66;
    }

    .cp-handleBtn {
      height: 26px;
      font-size: 12px;
      display: flex;
      color: #333333;
      background: #ffffff;
      border-radius: 5px;
      padding: 0 12px;
      align-items: center;
      cursor: pointer;
      border: 1px solid #abbdf7;
      .cp-icons {
        margin-right: 4px;

        .iconfont {
          font-size: 12px !important;
        }
      }

      .cp-texts {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        /*padding: 0 5px;*/
      }
    }

    .cp-handleUnBtn {
      height: 26px;
      font-size: 12px;
      display: flex;
      border-radius: 20px;
      border: 1px solid #7487e4;
      align-items: center;
      background-color: #fff;
      padding: 0 12px;
      cursor: pointer;
      color: #7487e4;

      .cp-icons {
        margin-right: 2px;

        .iconfont {
          font-size: 12px !important;
        }
      }
    }
  }
}
</style>
