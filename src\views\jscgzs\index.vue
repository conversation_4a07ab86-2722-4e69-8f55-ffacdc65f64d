<template>
  <!-- 结算成果展示 -->
  <div class="index flex-c h100">
    <div class="query-form-box">
      <!-- <el-form
        ref="form"
        :model="form"
        :inline="true"
        @submit.native.prevent
        label-width="120px"
      >
        <el-form-item label="工程名称:">
          <span>{{ projname || '-' }}</span>
        </el-form-item>
        <el-form-item label="建设单位:">
          {{ investmentorg || '-' }}
        </el-form-item>
        <el-form-item label="咨询审核单位:">
          {{ revieworg || '-' }}
        </el-form-item>
      </el-form> -->
      <el-descriptions labelClassName="my-label" size="medium" title="">
        <el-descriptions-item labelClassName="my-label" label="工程名称">{{
          projname || '-'
        }}</el-descriptions-item>
        <el-descriptions-item labelClassName="my-label" label="建设单位">{{
          investmentorg || '-'
        }}</el-descriptions-item>
        <el-descriptions-item labelClassName="my-label" label="咨询审核单位">{{
          revieworg || '-'
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="driver"></div>
    <el-button
      style="width: 98px; margin: 0 16px 16px 16px"
      
      :loading="btnLoading"
      @click="download()"
    >
      <i class="el-icon-download"></i>
      全部导出
    </el-button>
    <div class="tablesArea">
      <el-table
        v-loading="loading"
        :data="tableData"
        :height="tableHeight"
        style="width: 98%; margin: 0px 16px 0 16px"
        :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
        highlight-current-row
        :span-method="objectSpanMethod"
      >
        <el-table-column prop="index" label="序号" width="80" align="center">
        </el-table-column>
        <el-table-column prop="title" label="项目名称" width="400px" align="center">
        </el-table-column>
        <el-table-column prop="name" label="成果形式" width="" align="center">
        </el-table-column>
        <el-table-column prop="name" label="文件" width="" align="center">
          <template slot-scope="scope">
            <!-- <p  v-for="(item, i) in scope.row.fileList" :key=i>
              <el-link>{{item.name}}</el-link>
            </p> -->
            <ul class="el-upload-list el-upload-list--text">
              <li
                class="el-upload-list__item is-success"
                v-for="(item, i) in scope.row.fileList"
                :key="i"
              >
                <!-- <a class="el-upload-list__item-name" @click="openUrl(item)">{{ item.name }}</a> -->
                <a
                  class="el-upload-list__item-name"
                  @click="clickPreview(item)"
                  >{{ item.name }}</a
                >
                <i class="el-icon-close" @click="delChange(item)"></i>
              </li>
            </ul>
          </template>
        </el-table-column>
        <el-table-column prop="" label="操作" align="center" width="200">
          <template slot-scope="scope">
            <el-upload
              style="display: inline-block; margin-right: 16px"
              class="upload-demo"
              ref="upload"
              :action="url + '/tDtfFileModule/fileUpload'"
              multiple
              accept=""
              :data="getformData(scope.row)"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :on-change="beforeUpload"
              :on-success="handleSuccess"
              :on-error="handleError"
              :file-list="scope.row.fileList"
              :before-upload="beforeAvatarUpload"
              :on-progress="onProgress"
              :show-file-list="false"
            >
              <el-button class="el-buttonStyle" plain>上传</el-button>
            </el-upload>
            <el-button
              class="el-buttonStyle"
              plain
              @click="downloadId(scope.row)"
              >下载</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- <add
      v-if="false"
      :ProCode="ProCode"
      @getList="getList"
      ref="add"
    ></add>
    <add-item
      v-if="dialogFormVisible"
      :ProCode="ProCode"
      @getList="getList"
      ref="addItem"
    ></add-item> -->
    <preview
      v-if="dialogFormVisible"
      @getList="getList"
      ref="preview"
    ></preview>
  </div>
</template>

<script>
import { getJsData, jscgDownloadAll, resquest, jscgFileDel } from '@/api/api'
import { tableHeightMixin } from '@/assets/mixin/tableHeightMixin'
// import add from './add.vue'
// import addItem from './addItem.vue'
import preview from './preview.vue'
export default {
  name: 'jscgzs',
  mixins: [tableHeightMixin],
  // 演示需要------
  /* props:{
     ProCode: {
       type: String,
       default: '',
     },
   },
   watch: {
     ProCode(val){
       this.getList()
     }
   }, */
  // -------------
  components: {
    //   add,
    //  addItem,
    preview,
  },
  data() {
    return {
      // tableHeight: 0,
      // timer: 0,
      form: {
        name: '',
      },
      url: resquest,
      investmentorg: '',
      projname: '',
      revieworg: '',
      tableData: [
        // {
        //   index: '1',
        //   title: '配电网工程结算审核情况统计表',
        //   info: [
        //     {
        //       name: 'Word版审核报告',
        //       upload: false,
        //       fileList: [
        //         { id: 1, name: 'food.jpeg', url: '' }
        //       ],
        //     },
        //   ],
        // },
        // {
        //   index: '2',
        //   title: '结算审核报告',
        //   info: [
        // {
        //   index: '1',
        //   title: '配电网工程结算审核情况统计表',
        //   name: '结算书',
        //   upload: false,
        //   fileList: [
        //     { id: 1, name: 'food.jpeg', url: '' }
        //   ],
        // },
        // {
        //   index: '2',
        //   title: '结算审核报告',
        //   name: 'Word版审核报告',
        //   upload: false,
        //   fileList: [
        //     { id: 1, name: 'food.jpeg', url: '' },
        //     { id: 2, name: 'haha.jpeg', url: '' },
        //   ],
        // },
        // {
        //   index: '2',
        //   title: '结算审核报告',
        //   name: 'PDF签字盖章版审核报告',
        //   upload: true,
        //   fileList: [
        //     { id: 1, name: 'food.jpeg', url: '' }
        //   ],
        // },
        // {
        //   index: '2',
        //   title: '结算审核报告',
        //   name: '胶装版审核报告（线下）',
        //   upload: false,
        //   fileList: [
        //     { id: 1, name: 'food.jpeg', url: '' }
        //   ],
        // },
        // ],
        // },
        /*{
          index: '2.1',
          name: '审定结算书',
          info: [
            {
              name: '软件版结算书',
              upload: false,
              url: '',
            },
            {
              name: '结算书',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '2.2',
          name: '甲供物资结算支撑资料',
          info: [
            {
              name: 'ERP物料清单',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '2.3',
          name: '其他费用结算支撑资料',
          info: [
            {
              name: '文物保护评估、环评、洪评',
              upload: false,
              url: '',
            },
            {
              name: '勘察报告，PDF签字盖章版',
              upload: false,
              url: '',
            },
            {
              name: '勘察设计费结算计算说明',
              upload: false,
              url: '',
            },
            {
              name: '监理费结算计算说明',
              upload: false,
              url: '',
            },
            {
              name: '造价咨询费用计算说明',
              upload: false,
              url: '',
            },
            {
              name: '其他',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '2.4',
          name: '施工费结算支撑资料',
          info: [
            {
              name: '设计变更，PDF签字盖章版',
              upload: false,
              url: '',
            },
            {
              name: '工程签证，PDF签字盖章版',
              upload: false,
              url: '',
            },
            {
              name: '其他',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '2.5',
          name: '建设场地征用及清理费结算支撑资料',
          info: [
            {
              name: '协议、赔偿明细、证明等',
              upload: false,
              url: '',
            },
            {
              name: '其他',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '3',
          name: '工程初步设计批复文件（附初步设计评审意见、项目明细表）',
          info: [
            {
              name: '批复文件',
              upload: false,
              url: '',
            },
            {
              name: '项目明细表',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '4',
          name: '工程初步设计批复概算',
          info: [
            {
              name: 'Excel版',
              upload: false,
              url: '',
            },
            {
              name: '软件版',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '5',
          name: '工程开工、竣工验收报告',
          info: [
            {
              name: '开工报告',
              upload: false,
              url: '',
            },
            {
              name: '竣工验收报告',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '6',
          name: '施工招标文件',
          info: [
            {
              name: '公告',
              upload: false,
              url: '',
            },
            {
              name: '文件',
              upload: false,
              url: '',
            },
            {
              name: '技术规范书',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '7',
          name: '施工投标文件',
          info: [
            {
              name: '商务、技术文件等',
              upload: false,
              url: '',
            },
            {
              name: '软件版/Excel版投标文件',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '8',
          name: '施工合同文件',
          info: [
            {
              name: 'Word表单（封面带二维码）',
              upload: false,
              url: '',
            },
            {
              name: 'PDF签字盖章版',
              upload: false,
              url: '',
            },
            {
              name: '中标通知书',
              upload: false,
              url: '',
            },
            {
              name: '框架协议',
              upload: false,
              url: '',
            },
            {
              name: '施工考核单',
              upload: false,
              url: '',
            },
            {
              name: '补充协议',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '9',
          name: '监理审核意见',
          info: [
            {
              name: 'PDF签字盖章版',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '10',
          name: '监理招标文件',
          info: [
            {
              name: '公告、文件和技术规范书',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '11',
          name: '监理投标文件',
          info: [
            {
              name: '商务、技术文件等',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '12',
          name: '监理合同文件',
          info: [
            {
              name: 'Word表单（封面带二维码）',
              upload: false,
              url: '',
            },
            {
              name: 'PDF签字盖章版',
              upload: false,
              url: '',
            },
            {
              name: '中标通知书（PDF、jpg版）',
              upload: false,
              url: '',
            },
            {
              name: '框架协议（PDF、jpg版）',
              upload: false,
              url: '',
            },
            {
              name: '监理考核单（如有）',
              upload: false,
              url: '',
            },
            {
              name: '补充协议（如有）',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '13',
          name: '勘察设计招标文件',
          info: [
            {
              name: '公告、文件和技术规范书',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '14',
          name: '勘察设计投标文件',
          info: [
            {
              name: '商务、技术文件等',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '15',
          name: '勘察设计合同文件',
          info: [
            {
              name: 'Word表单（封面带二维码）',
              upload: false,
              url: '',
            },
            {
              name: 'PDF签字盖章版',
              upload: false,
              url: '',
            },
            {
              name: '中标通知书（PDF、jpg版）',
              upload: false,
              url: '',
            },
            {
              name: '勘察、设计考核单（如有）',
              upload: false,
              url: '',
            },
            {
              name: '补充协议（如有）',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '16',
          name: '结算审核合同文件',
          info: [
            {
              name: 'Word版（封面带二维码）',
              upload: false,
              url: '',
            },
            {
              name: 'PDF签字盖章版',
              upload: false,
              url: '',
            },
            {
              name: '中标通知书',
              upload: false,
              url: '',
            },
            {
              name: '框架协议',
              upload: false,
              url: '',
            },
            {
              name: '考核单',
              upload: false,
              url: '',
            },
            {
              name: '补充协议',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '17',
          name: '竣工图纸',
          info: [
            {
              name: 'PDF版、CAD版均可',
              upload: false,
              url: '',
            },
            {
              name: 'Excel版或PDF版图纸目录一份',
              upload: false,
              url: '',
            },
          ],
        }, */
      ],
      loading: false,
      dialogFormVisible: false,
      btnLoading: false,
      downloadAllUrl: resquest + '/tDtfFileModule/downloadAll',
      ProCode: '',

      mergeObj: {}, // 用来记录需要合并行的下标
      mergeArr: ['index', 'title'], // 表格中的列名
    }
  },
  created() {
    this.ProCode = this.$route.query.ProCode || '1806CB210007-001'
  },
  mounted() {
    this.getList()
  },
  methods: {
    // 斑马纹效果
    xxxTableRowClassName({ row, rowIndex }) {
      // if (rowIndex % 2 == 0) {
      //   return ''
      // } else {
      //   return 'statistics-warning-row'
      // }
    },
    getList() {
      this.loading = true
      let data = {
        // ProCode: '1806CB210007-001',
        ProCode: this.ProCode,
      }
      getJsData(data)
        .then((res) => {
          this.tableData = res.data.data.tableData
          this.getSpanArr(this.tableData)
          this.investmentorg = res.data.data.investmentorg
          this.projname = res.data.data.projname
          this.revieworg = res.data.data.revieworg
          // this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },
    add(e) {
      console.log(e)
      this.dialogFormVisible = true // 控制弹出框显示
      this.$nextTick(() => {
        this.$refs.add.init(e) // init（）是子组件函数
      })
    },
    clickCg(row, i) {
      this.dialogFormVisible = true // 控制弹出框显示
      this.$nextTick(() => {
        this.$refs.addItem.init(row, i) // init（）是子组件函数
      })
    },
    clickPreview(row) {
      let fileType = row.url.split('.')[1]
      let url = `${this.url}/tDtfFileModule/conversionPdf?uuid=${row.url}&suffix=.${fileType}`
      this.dialogFormVisible = true // 控制弹出框显示
      this.$nextTick(() => {
        this.$refs.preview.init(url) // init（）是子组件函数
      })
    },
    download() {
      window.open(`${this.downloadAllUrl}?proCode=${this.ProCode}`)
    },
    downloadId(row) {
      window.open(
        `${this.url}/tDtfFileModule/download?proCode=${this.ProCode}&flag=${row.flag}`
      )
    },
    delChange(row) {
      // this.loading = true
      let data = {
        // ProCode: '1806CB210007-001',
        id: row.id,
      }
      jscgFileDel(data)
        .then((res) => {
          if (res) {
            this.getList()
            this.$message.success('删除成功')
          }
          // this.loading = false
        })
        .catch(() => {
          // this.loading = false
        })
    },

    // getSpanArr方法
    getSpanArr(data) {
      this.mergeArr.forEach((key, index1) => {
        let count = 0 // 用来记录需要合并行的起始位置
        this.mergeObj[key] = [] // 记录每一列的合并信息
        data.forEach((item, index) => {
          // index == 0表示数据为第一行，直接 push 一个 1
          if (index === 0) {
            this.mergeObj[key].push(1)
          } else {
            // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并 并push 一个 0 作为占位
            if (item[key] === data[index - 1][key]) {
              this.mergeObj[key][count] += 1
              this.mergeObj[key].push(0)
            } else {
              // 如果当前行和上一行其值不相等
              count = index // 记录当前位置
              this.mergeObj[key].push(1) // 重新push 一个 1
            }
          }
        })
      })
    },
    // objectSpanMethod方法
    // 默认接受四个值 { 当前行的值, 当前列的值, 行的下标, 列的下标 }
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 判断列的属性
      if (this.mergeArr.indexOf(column.property) !== -1) {
        // 判断其值是不是为0
        if (this.mergeObj[column.property][rowIndex]) {
          return [this.mergeObj[column.property][rowIndex], 1]
        } else {
          // 如果为0则为需要合并的行
          return [0, 0]
        }
      }
    },

    getformData(row) {
      return {
        flag: row.flag,
        proCode: this.ProCode,
      }
    },
    submitUpload() {
      this.$refs.upload.submit()
    },
    handleRemove(file, fileList) {},
    handlePreview(file) {},
    beforeUpload(file, fileList) {
      // this.form.file = file.raw
      console.log('beforeUpload', file, fileList)
      if (file.status === 'ready') {
        this.loading = true
      }
    },
    beforeAvatarUpload(file) {
      return new Promise((resolve, reject) => {
        const isType =
          /\.(wmv|rm|rmvb|mov|mp4|flv|3gp|mkv|avi|f4v|webv|mepg)$/.test(
            file.type
          )
        const isLt2M = file.size / 1024 / 1024 < 200
        //文件上传之前的校验
        // if (!isType) {
        //   // 限制文件类型校验
        //   this.$message.error('视频上传格式不对！')
        //   return reject(false)
        // } else
        if (!isLt2M) {
          this.$message.error('文件大小不能超过 200MB!')
          return reject(false)
        } else {
          resolve(true)
        }
      })
    },
    // 上传中
    onProgress(event, file, fileList) {
      console.log('onProgress', event, file, fileList)
    },
    //上传成功
    handleSuccess(response, file, fileList) {
      this.loading = false
      console.info(fileList, response, 'fileList')
      this.getList()
    },
    // 上传失败
    handleError(err, file, fileList) {
      console.log(file, fileList)
      console.log('error', err)
      this.$message.error('上传失败')
      this.loading = false
    },

    openUrl(row) {
      let fileType = row.url.split('.')[1]
      window.open(
        `${this.url}/tDtfFileModule/conversionPdf?uuid=${row.url}&suffix=.${fileType}`
      )
    },
  },
}
</script>
<style>
.my-label {
  color: #000;
  font-weight: 600;
}
</style>
<style lang="scss" scoped>
::v-deep .el-table__row > td {
  /* 去除表格线 */
  border: none;
  border-left: none;
}

.el-upload-list__item:hover {
  background-color: rgba(230, 234, 239, 0.8);
}

.el-upload-list__item:first-child {
  margin-top: 0;
}

.el-icon-close {
  color: red;
}
</style>
