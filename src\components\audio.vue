<template>
  <div style="padding: 20px;">
    <div class="audio-main">
      <van-row :style="{border: 'none'}">
        <van-col :span="3">
          <!--录音按钮-->
          <div class="audio-startImg">
            <img
              class="audioImg"
              :src="require('@/assets/' + 'main/luyin.png')"
              alt=""
              @click="isShowVanpop = true"
            >
          </div>
        </van-col>
        <van-col :span="21">
          <div class="audio-items">
            <div v-for="(item,index) in audioList" :key="'audio'+index" class="audio-showEvery">
              <van-row :style="{width: '100%',border: 'none',height: '100%'}">
                <van-col :span="22">
                  <audio ref="audios" class="audio-music" controls :style="{width: '100%'}"/>
                </van-col>
                <van-col :span="2" :style="{textAlign: 'center'}" @click="removeAduio(index)">
                  <van-icon name="cross" />
                </van-col>
              </van-row>
            </div>
          </div>
        </van-col>
      </van-row>
    </div>
    <van-popup v-model="isShowVanpop" position="bottom" :style="{ height: '30%' }">
      <div class="audio-pop">
        <div class="audio-time">时长: {{ (recorder && recorder.duration.toFixed(0)) === null? 0: recorder && recorder.duration.toFixed(0) }}</div>
        <div
          class="audio-musicImg"
          @touchstart="gtouchstart"
          @touchend="showDeleteButton"
        >
          <div v-show="isShowLy" class="audio-jtimg">
            <img :src="require('@/assets/'+lyurls)" alt="">
          </div>
          <div v-show="!isShowLy" class="audio-dtimg">
            <img :src="require('@/assets/'+lyurls)" alt="">
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Vue from 'vue'
import Recorder from 'js-audio-recorder'
import { Toast, Dialog } from 'vant'
Vue.use(Toast).use(Dialog)
export default {
  props: {
    //  接受自父组件的录音数组
    audioFile: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isShowVanpop: false,
      recorder: null,
      src: null,
      audioList: [], // 存放录音数组
      timeOutEvent: 0, // 定时器 模拟长按事件
      timeOutAduio: 0, // 定时器 audio标签长按事件
      isloadingTouch: false, // 是否长按
      lyurls: 'main/luyin.png',
      isShowLy: true,
      blobArray: [],
      removeTimer: null
    }
  },
  watch: {
    audioFile: {
      handler(newVal, oldVal) {
        const that = this
        this.audioList = []
        this.$nextTick(() => {
          for (const j in newVal) {
            that.base64UrlToFile(newVal[j].content, that.audioList)
          }
          setTimeout(() => {
            // 需要等文档更新完毕后去给audio标签动态绑定url 在dom中直接绑定不生效 注意！！！
            for (const j in that.audioList) {
              const url = window.URL.createObjectURL(that.audioList[j])
              that.$refs.audios[j].src = url
            }
          }, 500)
        })
      },
      immediate: true,
      deep: true
    }
  },

  created() {
    this.recorder = new Recorder()
  },
  mounted() {

  },
  methods: {
    /**
     * base64转换为file类型
     * @param base64Url
     * @param filename
     * @returns
     */
    base64UrlToFile(base64Url, fileList) {
      const arr = base64Url.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      fileList.push(new File([u8arr], 'voice', { type: mime }))
    },
    gtouchstart() {
      var self = this
      this.timeOutEvent = setTimeout(function() {
        self.longPress()
      }, 500) // 这里设置定时器，定义长按500毫秒触发长按事件
      return false
    },
    removeAduio(index) {
      this.audioList.splice(index, 1)
    },
    // 手释放，如果在500毫秒内就释放，则取消长按事件，此时可以执行onclick应该执行的事件
    showDeleteButton() {
      if (this.timeOutEvent !== 0) {
        // 这里写要执行的内容（如onclick事件）
        clearTimeout(this.timeOutEvent) // 清除定时器
        this.timeOutEvent = 0
      } else {
        if (this.isloadingTouch) {
          /* 将二进制流文件转成地址 这一步主要为了在aduio文件当中播放*/
          const blob = this.recorder.getWAVBlob()// 获取wav格式音频数据
          if (this.audioList.length > 4) {
            return Toast.fail('最多允许录入5条语音')
          } else {
            this.audioList.push(
              blob
            )
          }
          this.$nextTick(() => {
            for (const j in this.audioList) {
              this.getBase64(this.audioList[j])
            }
            this.$emit('submitAudio', this.blobArray)
          })
          this.lyurls = 'main/luyin.png'
          this.isShowLy = true
          this.recorder = null
          this.isloadingTouch = false
          this.timeOutEvent = 0
        }
      }
      return false
    },
    getBase64(file) {
      this.blobArray = []
      return new Promise((resolve, reject) => {
        // /FileReader类就是专门用来读文件的
        const reader = new FileReader()
        // 开始读文件
        // readAsDataURL: dataurl它的本质就是图片的二进制数据， 进行base64加密后形成的一个字符串，
        reader.readAsDataURL(file)
        // 成功和失败返回对应的信息，reader.result一个base64，可以直接使用
        reader.onload = () => {
          const obj = {
            content: reader.result
          }
          this.blobArray.push(obj)
        }
        // 失败返回失败的信息
        reader.onerror = error => reject(error)
      })
    },
    // 真正长按后应该执行的内容
    longPress() {
      const that = this
      this.timeOutEvent = 0
      // 执行长按要执行的内容，如弹出菜单
      this.recorder = new Recorder({
        sampleBits: 8, // 采样位数，支持 8 或 16，默认是16
        sampleRate: 11025, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
        numChannels: 1 // 声道，支持 1 或 2， 默认是1
        // compiling: false,(0.x版本中生效,1.x增加中)  // 是否边录边转换，默认是false
      })
      Recorder.getPermission().then(() => {
        console.log('开始录音')
        that.recorder.start() // 开始录音
        that.isloadingTouch = true
        that.lyurls = 'main/luyin.gif'
        that.isShowLy = false
      }, (error) => {
        Toast.success({
          message: '请先开启麦克风权限'
        })
        console.log(`${error.name} : ${error.message}`)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  body {
    -webkit-touch-callout: none !important;
    -webkit-overflow-scrolling: touch;
  }
  .audio-main{
    .audio-startImg{
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .audioImg{
      height: 2rem;
      width: 2rem;
    }
    .audio-showEvery{
      position: relative;
      border-radius: 5rem;
      height: 2rem;
      line-height: 2rem;
      display: flex;
      align-items: center;
      ::v-deep .van-swipe-cell{
        width: 100%;
      }
      img{
        width: 2rem;
        height: 2rem;
        margin-left: 1rem;
      }
      span{
        margin-left: 0.5rem;
      }
      .audio-music{
        height: 2rem;
        border-radius: 2rem !important;
      }
      .aduio-handle{
        position: absolute;
        top: -2.5rem;
        left: 9rem;
        height: 2rem;
        line-height: 2rem;
        background: black;
        color: white;
        width: 3rem;
        text-align: center;
        border-radius: 0.5rem;
        display: none;
      }
      .aduio-handle::before{
        content:'';
        position:absolute;
        right:50%;
        top:100%;
        transform: translateX(50%);
        border-top:5px solid black;
        border-right:5px solid transparent;
        border-left:5px solid transparent;
      }
    }
    .audio-showEvery:nth-of-type(n+2){
      margin-top: 1rem;
    }
  }
  .audio-pop{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
  }
  .audio-time{
    font-size: 1.2rem;
    font-weight: bold;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .audio-musicImg{
    width: 7rem;
    margin-top: -2rem;
    height: 7rem;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .audio-jtimg{
      margin-top: 1rem;
      width: 68%;
      height: 68%;
      img{
        width: 100%;
        height: 100%;
      }
    }
    .audio-dtimg{
      width: 100%;
      height: 100%;
      margin-top: 1rem;
      img{
        width: 100%;
        height: 100%;
      }
    }
  }
</style>
