<template>
  <div class="index flex-c h100">
    <div
      style="
        height: 100vh;
        width: 100vw;
        background: #fff;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 99;
      "
      v-show="!showPage"
    ></div>
    <!-- 标题 -->
    <div class="main-header">结算审核</div>
    <div
      class=""
      style="width: 100%; height: 1px; border-bottom: 1px solid #eee"
    ></div>
    <!-- 表单 -->
    <div class="query-form-box">
      <el-form ref="form" :model="form" :inline="true" label-width="80px">
        <el-row>
          <el-col :span="5">
            <el-form-item label="年度:" prop="pcnd">
              <el-date-picker
                v-model="form.pcnd"
                type="year"
                format="yyyy"
                value-format="yyyy"
                placeholder="选择日期"
                @change="NDPoint"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="批次名次:" prop="batch">
              <el-select v-model="form.batch" clearable placeholder="请选择">
                <el-option
                  v-for="item in batchOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="地市公司:" prop="jhxz">
              <el-select
                v-model="form.dsgs"
                clearable
                placeholder="请选择"
                @change="CityPoint"
              >
                <el-option
                  v-for="item in dsgsOptions"
                  :key="item.cityid"
                  :label="item.cityname"
                  :value="item.cityid"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="lastSearchArea">
            <el-form-item label="县公司:" prop="jszt">
              <el-select v-model="form.xgs" clearable placeholder="请选择">
                <el-option
                  v-for="item in xgsOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="项目名称:" prop="xmName">
              <el-input
                v-model="form.xmName"
                clearable
                placeholder="请输入项目名称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="工程名称:" prop="projectName">
              <el-input
                v-model="form.projectName"
                clearable
                placeholder="请输入工程名称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="是否通过:" prop="region">
              <el-select v-model="form.tgzt" placeholder="请选择通过状态">
                <el-option label="全部" :value="2"></el-option>
                <el-option label="通过" :value="3"></el-option>
                <el-option label="未通过" :value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="lastSearchArea" v-show="!isShowExpand">
            <el-form-item label="工程编码:">
              <el-input
                v-model="form.code"
                clearable
                placeholder="请输入工程编码"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" v-show="isShowExpand" class="lastSearchArea">
            <el-button class="blue-btn" @click="query()">查询</el-button>
            <el-button @click="clearForm()">重置</el-button>
            <span @click="changeExpand">
              <span class="expandArea">
                展开
                <img
                  style="width: 16px; height: 16px"
                  :src="require('@/assets/main/botArrow.png')"
                  alt=""
              /></span>
            </span>
          </el-col>
        </el-row>
        <el-row>
          <el-col
            :span="24"
            class="lastSearchArea"
            v-show="!isShowExpand"
            style="margin-bottom: 16px"
          >
            <el-button class="blue-btn" @click="query()">查询</el-button>
            <el-button @click="clearForm()">重置</el-button>
            <span @click="changeExpand">
              <span class="expandArea">
                收起
                <img
                  style="width: 16px; height: 16px"
                  :src="require('@/assets/main/topArrow.png')"
                  alt=""
              /></span>
            </span>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="driver"></div>
    <div class="cp-tableInfo">
      <div class="cp-btns">
        <div class="cp-handleBtn" @click="batchAgree">
          <span class="cp-icons">
            <i class="iconfont icon-tianjiaxinxi" />
          </span>
          <span class="cp-texts">
            <img
              style="width: 16px; height: 16px"
              :src="require('@/assets/main/mantAgree.png')"
              alt=""
            />
            <span style="margin-left: 5px"> 批量通过 </span>
          </span>
        </div>
        <div class="cp-handleBtn" @click="batchBack">
          <span class="cp-icons">
            <i class="iconfont icon-tianjiaxinxi" />
          </span>
          <span class="cp-texts">
            <img
              style="width: 16px; height: 16px"
              :src="require('@/assets/main/mantAgree.png')"
              alt=""
            />
            <span style="margin-left: 5px"> 批量退回 </span>
          </span>
        </div>
      </div>
    </div>
    <!-- 项目列表 -->
    <div class="tablesArea">
      <el-table
        :data="tableData"
        :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
        :default-sort="{ prop: 'noticeTime', order: 'ascending' }"
        highlight-current-row
        :height="tableHeight"
        @selection-change="handleSelectionChange"
        :row-key="getRowKeys"
        style="width: 98%; margin: 0px 16px 0 16px"
      >
        <el-table-column type="selection" :reserve-selection="true" width="55">
        </el-table-column>
        <el-table-column
          type="index"
          :index="indexMethod"
          label="序号"
          align="center"
          :resizable="false"
          width="60"
        >
        </el-table-column>
        <el-table-column prop="pcnd" label="年度" align="left">
        </el-table-column>
        <el-table-column prop="batchname" label="项目批次" align="left">
        </el-table-column>
        <el-table-column
          prop="projectName"
          label="工程名称"
          align="left"
          width="400px"
        >
        </el-table-column>
        <el-table-column
          prop="code"
          label="项目编码"
          align="left"
          min-width="160"
        >
        </el-table-column>
        <el-table-column
          prop="xmmc"
          label="项目名称"
          align="left"
          width="400px"
        >
        </el-table-column>
        <el-table-column
          prop="cityName"
          label="地市公司"
          align="left"
          width="130px"
        >
        </el-table-column>
        <el-table-column
          prop="countyName"
          label="县公司"
          align="left"
          width="130px"
        >
        </el-table-column>
        <el-table-column
          prop="projState"
          label="任务状态"
          align="left"
          width="120"
        >
          <template slot-scope="scope">
            <!--县的显示-->
            <span
              v-if="scope.row.tgzt == '2' && userType == '3'"
              style="color: red"
              >待审核</span
            >
            <span
              v-if="scope.row.tgzt == '4' && userType == '3'"
              style="color: #526ade"
              >市公司待评审</span
            >
            <span
              v-if="scope.row.tgzt == '3' && userType == '3'"
              style="color: #526ade"
              >市公司评审通过</span
            >
            <span
              v-if="scope.row.tgzt == '5' && userType == '3'"
              style="color: #526ade"
              >市公司评审通过</span
            >
            <!--市的显示-->
            <span
              v-if="scope.row.tgzt == '3' && userType == '2'"
              style="color: #526ade"
              >市公司评审通过</span
            >
            <span
              v-if="scope.row.tgzt == '4' && userType == '2'"
              style="color: red"
              >待审核</span
            >
            <span
              v-if="scope.row.tgzt == '5' && userType == '2'"
              style="color: #526ade"
              >市公司评审通过</span
            >
            <!--省的显示-->
            <span
              v-if="scope.row.tgzt == '2' && userType == '1'"
              style="color: green"
              >县公司待审核</span
            >
            <span
              v-if="scope.row.tgzt == '3' && userType == '1'"
              style="color: #526ade"
              >市公司评审通过</span
            >
            <span
              v-if="scope.row.tgzt == '4' && userType == '1'"
              style="color: green"
              >县公司评审通过</span
            >
            <span
              v-if="scope.row.tgzt == '5' && userType == '1'"
              style="color: green"
              >已提交</span
            >
          </template>
        </el-table-column>
        <!--        <el-table-column prop="address" label="审核" align="center" width="100">-->
        <!--          <template slot-scope="scope">-->
        <!--            <el-button size="mini" class="el-buttonStyle" @click="handleEamine(scope.row)">审核-->
        <!--            </el-button>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="操作" align="left" fixed="right" width="150">
          <template slot-scope="scope">
            <span class="directiveHandle">
              <div :class="'el-buttonStyle'">
                <span @click="handleEamine(scope.row)"> 审核 </span>
              </div>
              <span class="el-buttonDriver">|</span>

              <div
                :class="
                  (scope.row.tgzt == '4' && userType == '3') ||
                  scope.row.tgzt == '3' ||
                  userType == '1'
                    ? ' el-buttonDisabled'
                    : 'el-buttonStyle'
                "
              >
                <span @click="handleSubt(scope.row)"> 通过 </span>
              </div>
              <span class="el-buttonDriver">|</span>

              <div
                :class="
                  (scope.row.tgzt == '4' && userType == '3') ||
                  scope.row.tgzt == '3' ||
                  userType == '1'
                    ? ' el-buttonDisabled'
                    : 'el-buttonStyle'
                "
              >
                <span @click="handleReturn(scope.row)"> 退回 </span>
              </div>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      style="margin: 10px"
      background
      :current-page="q.page"
      :page-sizes="pageSize"
      :page-size="q.limit"
      layout="total, sizes, prev, pager, next"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      :total="total"
    >
    </el-pagination>

    <!-- 申报 -->
    <el-dialog
      title="审核"
      top="4vh"
      width="95%"
      style="height: 100vh"
      :visible.sync="dialogSB"
    >
      <div style="height: 100%">
        <Dialog
          :ProCode="cuttentProCode"
          v-if="flag"
          @cloaseDialogVis="changeDialog"
          :sbOrSh="isSb"
        ></Dialog>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSb,
  getCity,
  getCounty,
  SHReturn,
  getAnnex,
  getFBCost,
  getJSInformation,
  SHSubmint,
  SHSubmintZT,
  handleReturn,
  getPCSel,
  getuserInfo,
  jssbtj,
  jssbth,

} from '@/api/api'
import Dialog from '@/views/settlementsh/components/index'
export default {
  components: {
    Dialog,
  },
  data () {
    return {
      isShowExpand: false,
      flag: true,
      tableHeight: 0,
      getRowKeys (row) {
        return row.taskID
      },
      userType: '', // 1省 2市 3县
      form: { //表单参数
        id: '',
        projState: '',
        stageType: 5,
        projectName: '',
        pcnd: '',
        batch: '',
        dsgs: '',
        xgs: '',
        xmName: '',
        tgzt: 2,
        dwmc: '',
        page: 1,
        rank: '',
        perPage: 10,
        code: '',
        source: "1",
        csmc: null
      },
      tableSelctionDatas: [],
      cuttentProCode: '',
      xgsOptions: [], //县公司下拉数据
      dsgsOptions: [], //城市下拉
      batchOptions: [], //批次名次下拉数据
      pageSize: [10, 20, 50, 100], //分页页数
      q: {
        page: 1,
        limit: 5
      },
      total: 0, //总共页数
      tableData: [],
      dialogSB: false, //申报
      tableDatas: [],
      // 附件
      formAnnex: {
        code: '',
        pageSize: 10,
        pageIndex: 1
      },
      tableDataAnnex: [],
      formJY2: { //概算信息
        sgjsf: '',
        bstjf: '',
        bsazf: '',
        bstf: '',
        jbybf: '',
        bsjgsbf: '',
        bsjgclf: '',
        jhshrq: '',
        sendDate: '',
        sdgczj: '',
        szz: '',
        sjz: '',
        approvalTime: '',
        auditCompanyName: '',
        sdxlcd: '',
        cjpsyjrq: '',
        psyjwh: '',
        gcshfzr: '',
        zyshr: '',
        zxdw: '',
        jzgcf: '',
        sdazf: '',
        jsdttz: '',
        jsjttz: '',
        sdjgsbf: '',
        sdjgclf: '',
        sdqtf: '',
      },
      formJY3: { //概算信息
        zyfbgcl: '',
        sszyfbfy: '',
        sdzybfy: '',
        sdzyfbfyzk: '',
        lwfbzgq: '',
        sslwbfy: '',
        sdlwfbfy: '',
        sdsgf: '',
        fbzb: ''
      },
      isSb: 'cksh',
      showPage: false
    }
  },
  mounted () {

    this.NDPoint()
    this.setTablesHeight()
    const token = sessionStorage.getItem('bhneToken')
    const pageType = sessionStorage.getItem('bhnePageType')
    getuserInfo(token).then((res) => {
      if (Object.keys(res.data.result).length == 0) {
        this.$message.error("获取用户信息失败，请重新进入页面")
      }
      let menuTypes = res.data.result.zmenu.split(',')

      if (menuTypes.includes(pageType)) {
        console.log("允许进入")
        this.showPage = true
      } else {
        console.log("无权限")
        this.$message.warning("无权限")
        this.showPage = false
      }
      // this.form.csmc = res.data.result.USERID
      this.form.dwmc = res.data.result.DWMC
      this.userType = res.data.result.rank
      this.form.rank = res.data.result.rank
      this.getList()
    })
    this.showPage = true
    const that = this
    window.onresize = function () {
      that.setTablesHeight()
    }
    this.showPage = true
  },
  methods: {
    changeExpand () {
      this.isShowExpand = !this.isShowExpand
      this.setTablesHeight()
    },
    setTablesHeight () {
      this.$nextTick(() => {
        const tablesAreaHeight = document.getElementsByClassName('driver')[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        this.tableHeight = windowsAreaHeight - tablesAreaHeight - 120
      })
    },
    batchAgree () {
      if (this.tableSelctionDatas.length < 1) {
        this.$message.warning('请至少选择一条数据')
      } else {
        let newArr = []
        for (let j in this.tableSelctionDatas) {
          newArr.push(this.tableSelctionDatas[j].taskID)

        }
        let isTg = this.tableSelctionDatas.every(item => {
          console.log(item.tgzt)
          //  待确定
          return !["3", '4', '5'].includes(item.tgzt)
        })
        console.log(isTg, '是否能通过')
        if (isTg) {
          let paramstj = {
            taskid: newArr.join(','),
            rank: this.userType,
          }
          jssbtj(paramstj).then((res) => {
            if (res.data.message == 'success') {
              this.$message.success('批量通过成功')
              this.getList()
            }
          })
        } else {
          this.$message.warning('不能选中评审通过的数据')
        }

      }
    },
    batchBack () {
      if (this.tableSelctionDatas.length < 1) {
        this.$message.warning('请至少选择一条数据')
      } else {
        let newArr = []
        for (let j in this.tableSelctionDatas) {
          newArr.push(this.tableSelctionDatas[j].taskID)
        }
        let isTg = this.tableSelctionDatas.every(item => {
          console.log(item.tgzt)
          //  待确定
          return !["3", '4', '5'].includes(item.tgzt)
        })
        if (isTg) {
          handleReturn(newArr.join(',')).then((res) => {
            if (res.data.message == 'success') {
              this.$message.success('批量退回成功')
              this.getList()
            }
          })
        } else {
          this.$message.warning('不能选中评审通过的数据')
        }


      }
    },
    // 获取列表
    getList () {
      console.log('this.form', this.form)
      getSb(this.form)
        .then((res) => {
          this.tableData = res.data.data
          this.total = res.data.total
        })
        .catch(() => { })
      if (this.dsgsOptions.length === 0) {
        // 获取城市下拉
        const params = {
          optId: this.form.dwmc,
        }
        getCity(params)
          .then((res) => {
            this.dsgsOptions = res.data.result
          })
          .catch(() => { })
      }
    },
    changeDialog (val) {
      this.dialogSB = val
      this.flag = val
      this.isSb = 'cksh'
      this.getList()
    },
    // 城市点击获取县下拉
    CityPoint (val) {
      const params = {
        optId: val,
        dwmc: this.form.dwmc,
      }
      getCounty(params)
        .then((res) => {
          this.xgsOptions = res.data.result

        })
        .catch(() => { })
    },
    NDPoint () {
      // 获取批次名词
      const param = {
        optId: this.form.pcnd,
      }
      getPCSel(param)
        .then((res) => {
          console.log(res)
          this.batchOptions = res.data.result
        })
        .catch(() => { })
    },
    // 查询
    query () {
      this.getList()
    },
    // table列表序号索引
    indexMethod (index) {
      return (this.form.page - 1) * this.form.perPage + index + 1
    },
    // 重置
    clearForm () {
      this.form = {
        id: '',
        projState: '',
        stageType: 5,
        projectName: '',
        pcnd: null,
        batch: '',
        code: '',
        dsgs: '',
        xgs: '',
        xmName: '',
        tgzt: 2,
        dwmc: this.form.dwmc,
        page: 1,
        perPage: 10,

        rank: this.form.rank
      }

      this.getList()
    },
    // 复选框
    handleSelectionChange (row) {
      console.log(row, '数据')
      this.tableSelctionDatas = row
    },

    handleCurrentChange (val) {
      this.form.page = val
      this.getList()
    },
    handleSizeChange (val) {
      this.form.perPage = val
      this.getList()
    },
    // 退回按钮
    handleReturn (row) {
      this.$confirm('是否确定退回?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: 'warning'
      }).then(() => {
        // SHReturn(row.taskID)
        //   .then((res) => {
        //     if (res.data.message == 'success') {
        //       this.$message.success('退回成功')
        //       this.getList()
        //     } else {
        //       this.$message.error('退回失败')
        //     }
        //   })
        let params = {
          taskid: row.taskID,
          rank: this.userType,
          tgzt: row.tgzt
        }
        jssbth(params).then((res) => {
          console.log("退回成功", res)
          this.getList()
          this.$message.success("退回成功")
        })
      }).catch(() => {

      })
    },
    // 提交
    handleSubt (row) {
      this.formAnnex.code = row.code // 附件
      let paramstj = {
        taskid: row.taskID,
        rank: this.userType,
        tgzt: row.tgzt
      }
      const date = new Date()
      let zTtjParmas = {
        XMID: row.code,
        ZTGXSJ: date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate(),
        WCZT: "03"
      }
      this.$confirm('是否确定提交?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: 'warning'
      }).then(() => {
        if (this.userType == '2' && row.tgzt == "4") {
          console.log("市公司通过")
          jssbtj(paramstj).then(res => {
            console.log(res, '通过成功')
            this.$message.success('通过成功')
            this.getList()

          })
          SHSubmintZT(zTtjParmas).then(res => {
            console.log(res, '通过成功')
          })
        } else {
          console.log("县公司通过")
          jssbtj(paramstj).then(res => {
            console.log(res, '通过成功')
            this.$message.success('通过成功')
            this.getList()

          })
        }

      })

      // getJSInformation(row.code)
      //   .then((res) => {
      //     this.formJY2 = res.data.result
      //   })
      //   .catch(() => { })
      // getFBCost(row.code)
      //   .then((res) => {
      //     this.formJY3 = res.data.result
      //   })
      //   .catch(() => { })
      // this.$confirm('是否确定提交?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   confirmButtonClass: "confirmBtn",
      //   cancelButtonClass: "cancleBtn",
      //   type: 'warning'
      // }).then(() => {
      //   // 获取上传附件判断是否上传
      //   getAnnex(this.formAnnex)
      //     .then((res) => {
      //       this.tableDataAnnex = res.data.result
      //       let flag = this.tableDataAnnex.every(item => !!item.name)
      //       if (!flag) {
      //         this.$message.warning('请上传附件')
      //         return
      //       } else {
      //         // 提交前验证是否未填必填项
      //         if (!this.formJY2.zxdw || !this.formJY2.zyshr || !this.formJY2.gcshfzr || !this
      //           .formJY2.jsjttz ||
      //           !this
      //             .formJY2
      //             .jsdttz || !this.formJY2.psyjwh || !this.formJY2.cjpsyjrq || !this.formJY2
      //               .sdxlcd || !this.formJY2.zyshr || !this.formJY2.gcshfzr || !this
      //                 .formJY2.auditCompanyName ||
      //           !this
      //             .formJY2
      //             .approvalTime || !this.formJY2.sjz || !this.formJY2.szz || !this.formJY2
      //               .bsjgsbf || !this.formJY2.zxdw || !this.formJY2.zyshr || !this.formJY2
      //                 .gcshfzr || !this
      //                   .formJY2.sdjgclf ||
      //           !this
      //             .formJY2
      //             .sdjgsbf || !this.formJY2.sbgzf || !this.formJY2.jbybf || !this.formJY2
      //               .sdqtf || !this.formJY2.zyshr || !this.formJY2.gcshfzr || !this
      //                 .formJY2.sdazf ||
      //           !this
      //             .formJY2
      //             .jzgcf || !this.formJY2.sdgczj || !this.formJY2.bsjgsbf ||
      //           !this
      //             .formJY2
      //             .bsjgclf || !
      //           this
      //             .formJY3.zyfbgcl || !this.formJY3.sszyfbfy || !this.formJY3.sdzybfy || !
      //           this.formJY3
      //             .sdzyfbfyzk ||
      //           !
      //           this
      //             .formJY3.lwfbzgq || !this.formJY3.sslwbfy || !this.formJY3.sdlwfbfy || !
      //           this.formJY3
      //             .sdsgf || !this
      //               .formJY3
      //               .fbzb || !this.formJY2.sgjsf || !this.formJY2.bstf || !this.formJY2
      //                 .bstjf || !
      //           this
      //             .formJY2
      //             .bsazf || !this.formJY2.jbybf || !this.formJY2.jhshrq || !this.formJY2
      //               .sbgzf || !this
      //                 .formJY2
      //                 .sendDate
      //         ) {
      //           this.$message.error("未填写必填项")
      //           return

      //         } else {
      //           // 提交成功
      //           SHSubmint(row.taskID)
      //             .then((res) => {
      //               if (res.data.message == 'success') {
      //                 // 判断tgzt==5 提交给中台
      //                 const date = new Date()
      //                 const nowDate = date.getFullYear() + "-" + (date
      //                   .getMonth() + 1) + "-" + date.getDate()
      //                 const params = {
      //                   XMID: row.code,
      //                   ZTGXSJ: nowDate,
      //                   WCZT: "03"
      //                 }
      //                 if (row.tgzt === 5) {
      //                   SHSubmintZT(params)
      //                     .then(() => { })
      //                     .catch(() => { })
      //                 }
      //                 this.$message.success('提交成功')
      //                 this.getList()
      //               } else {
      //                 this.$message.error('提交失败')
      //               }
      //             })
      //             .catch(() => { })
      //         }
      //       }
      //     })
      //     .catch(() => { })
      // }).catch(() => {

      // })

    },
    // 编辑保存
    dialogQuery () {
      this.dialogProblem = false
    },
    // 审核功能挪到了申报页面
    handleEamine (row) {
      this.cuttentProCode = row
      this.dialogSB = true
      this.flag = false
      this.$nextTick(() => {
        this.flag = true
      })
    },
    // 附件删除
    handleFJDet () {

    },
    operation () {
      this.dialogSB = true
    }
  }
}
</script>

<style lang="scss" scoped>
.cp-tableInfo {
  height: 36px;
  margin-bottom: 16px;
  line-height: 36px;

  .cp-checkNav {
    width: 400px;
    background: #f5f6fa;
    padding-left: 32px;
    height: 36px;
  }

  .cp-btns {
    display: flex;
    flex: 1;
    height: 36px;
    line-height: 36px;
    align-items: center;
    padding-right: 32px;
    justify-content: flex-start;
    margin-left: 12px;
    color: #7286e8;

    .cp-handleBtn {
      border-radius: 10px;
      height: 25px;
      padding: 0 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #526ade;
      color: #ffff;
      font-size: 14px;
    }

    .cp-handleBtn:nth-child(2) {
      margin-left: 10px;
    }

    .cp-drawer {
      margin: 0 10px;
      color: #ccc;
    }

    .cp-handleUnBtn:hover {
      color: #7487e4;
      border-color: #7487e4;
    }

    .cp-handleUnBtn:active {
      color: #0c6b66;
      border-color: #0c6b66;
    }

    .cp-handleUnBtn:focus {
      color: #0c6b66;
      border-color: #0c6b66;
    }

    .cp-handleBtn {
      height: 26px;
      font-size: 12px;
      display: flex;
      color: #333333;
      background: #ffffff;
      border-radius: 5px;
      padding: 0 12px;
      align-items: center;
      cursor: pointer;
      border: 1px solid #abbdf7;

      .cp-icons {
        margin-right: 4px;

        .iconfont {
          font-size: 12px !important;
        }
      }

      .cp-texts {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        /*padding: 0 5px;*/
      }
    }

    .cp-handleUnBtn {
      height: 26px;
      font-size: 12px;
      display: flex;
      border-radius: 20px;
      border: 1px solid #7487e4;
      align-items: center;
      background-color: #fff;
      padding: 0 12px;
      cursor: pointer;
      color: #7487e4;

      .cp-icons {
        margin-right: 2px;

        .iconfont {
          font-size: 12px !important;
        }
      }
    }
  }
}

.expandArea {
  margin-left: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #606266;
  cursor: pointer;

  img {
    margin-left: 2px;
  }
}
::v-deep .el-dialog__body {
  padding: 24px !important;
}
::v-deep .el-dialog__footer {
  padding: 16px 24px !important;
}
</style>
