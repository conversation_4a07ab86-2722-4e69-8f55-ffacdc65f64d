<template>
  <div class="index flex-c h100">
    <div style="height:100vh;width:100vw;background:#fff;  position: absolute;left:0;top:0;z-index:99" v-show="!showPage"></div>
    <!-- 标题 -->
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane name="sgsj"><span class="main-headerss" slot="label">施工设计</span></el-tab-pane>
      <el-tab-pane name="sjbg"><span class="main-headerss" slot="label">设计变更</span></el-tab-pane>
    </el-tabs>
    <!-- <div
      class=""
      style="width: 100%; height: 1px; border-bottom: 1px solid #eee"
    ></div> -->
    <!-- 表单 -->
    <div class="query-form-box">
      <el-form ref="form" :model="form" :inline="true" label-width="80px">
        <el-row>
          <el-col :span="5">
            <el-form-item label="年度:" prop="pcnd">
              <el-date-picker v-model="form.pcnd" type="year" format="yyyy" value-format="yyyy" placeholder="选择年">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="地市公司:" prop="dsgs">
              <el-select v-model="form.dsgs" clearable placeholder="请选择" @change="CityPoint">
                <el-option v-for="item in dsgsOptions" :key="item.cityid" :label="item.cityname" :value="item.cityid">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="县公司:" prop="xgs">
              <el-select v-model="form.xgs" clearable placeholder="请选择">
                <el-option v-for="item in xgsOptions" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="lastSearchArea">
            <el-form-item label="工程名称:" prop="projectName">
              <el-input v-model="form.projectName" clearable placeholder="请输入工程名称"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="状态:" prop="projState">
              <el-select v-model="form.projState" clearable placeholder="请选择">
                <el-option label="待处理" value="01">待处理</el-option>
                <el-option label="处理中" value="02">处理中</el-option>
                <el-option label="已提交" value="03">已提交</el-option>
                <el-option label="评审通过" value="04">评审通过</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="19" class="lastSearchArea">
            <el-button class="blue-btn" @click="query()">查询</el-button>
            <el-button @click="clearForm()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="driver"></div>
    <div class="actionAreaPage">
      <el-upload class="uploadAction" action="test" accept=".xlsx,.xls" :before-upload="beforeDesignUpload" :show-file-list="false" :http-request="uploadFilesProject">
        <div><el-button>
            <div class="buttonArea"><img src="@/assets/img/批量导入.svg" alt="" style="margin-right:3px">
              <div> 批量导入</div>
            </div>
          </el-button>
        </div>
      </el-upload>

      <el-button @click="downLoadTemplates" style="margin-left:12px">
        <div class="buttonArea"><img src="@/assets/img/下载.svg" alt="" style="margin-right:3px">
          <div> 模板下载</div>
        </div>
      </el-button>

      <el-button @click="removeListPorj" style="margin-left:12px">
        <div class="buttonArea"><img src="@/assets/img/回退.svg" alt="" style="margin-right:3px">
          <div> 批量删除</div>
        </div>
      </el-button>
    </div>
    <!-- 项目列表 -->
    <div class="tablesArea">
      <el-table ref="table" :data="tableData" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }" :height="tableHeight" @selection-change="handleSelectionChange" style="width: 98%; margin: 0px 16px 0 16px">
        <el-table-column type="selection" :reserve-selection="true" width="55" fixed="left">
        </el-table-column>
        <el-table-column type="index" :index="indexMethod" :resizable="false" label="序号" align="center" width="60">
        </el-table-column>
        <el-table-column prop="pcnd" label="年度" align="center">
        </el-table-column>
        <el-table-column prop="projectName" label="工程名称" align="center" width="400px">
        </el-table-column>
        <el-table-column prop="batchname" label="项目批次" align="center" min-width="150px">
        </el-table-column>
        <el-table-column prop="xmmc" label="项目名称" align="center" width="400px">
        </el-table-column>
        <el-table-column prop="code" label="项目管理编码" align="center" width="150px">
        </el-table-column>
        <el-table-column prop="cityName" label="地市公司" align="center" width="130px">
        </el-table-column>
        <el-table-column prop="countyName" label="县公司" align="center" width="130px">
        </el-table-column>
        <el-table-column prop="voltageLevel" label="电压等级" align="center" width="100px">
        </el-table-column>
        <el-table-column label="适用深度规范" align="center" width="140px">
          <template slot-scope="scope">
            <span v-if="scope.row.projectType == '01'">架空</span>
            <span v-if="scope.row.projectType == '02'">电缆</span>
            <span v-if="scope.row.projectType == '03'">配变</span>
          </template>
        </el-table-column>
        <el-table-column prop="projState" label="任务状态" align="center" width="100px">
          <template slot-scope="scope">
            <span v-if="scope.row.projState == '01'" style="color: skyblue">待处理</span>
            <span v-else-if="scope.row.projState == '02'" style="color: orangered">处理中</span>
            <span v-else-if="scope.row.projState == '03'" style="color: royalblue">已提交</span>
            <span v-else-if="scope.row.projState == '04'" style="color: green">评审通过</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="250px">
          <template slot-scope="scope">
            <span class="directiveHandle">
              <div :class="scope.row.projState !== '02' ? 'el-buttonDisabled' : 'el-buttonStyle'">
                <span @click="handleSubt(scope.row)">
                  提交
                </span>
              </div>
              <span class="el-buttonDriver">|</span>
              <div :class="scope.row.projState !== '02' ? 'el-buttonDisabled' : 'el-buttonStyle'">
                <span @click="handleReturn(scope.row)">
                  退回
                </span>
              </div>
              <span class="el-buttonDriver">|</span>
              <div class="el-buttonStyle">
                <span @click="handleEdit(scope.row)">
                  编辑
                </span>
              </div>
              <span class="el-buttonDriver">|</span>
              <el-dropdown>
                <el-button type="text">
                  <i class="el-icon-more directiveicon" style="font-size: 14px"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="获取现状" :class="scope.row.projState !== '01' ? 'el-buttonDisabled' : 'el-buttonStyle'">
                    <span @click="handleCofirm(scope.row)">
                      获取现状
                    </span>
                  </el-dropdown-item>
                  <el-dropdown-item command="设计成果" :class="scope.row.projState !== '02' ? 'el-buttonDisabled' : 'el-buttonStyle'">
                    <span @click="handleDesign(scope.row)">
                      设计成果
                    </span>
                  </el-dropdown-item>
                  <el-dropdown-item command="查看馈线" :class="scope.row.projState !== '02' ? 'el-buttonDisabled' : 'el-buttonStyle'">
                    <span @click="handleFeeder(scope.row)">
                      查看馈线
                    </span>
                  </el-dropdown-item>
                  <el-dropdown-item command="设计软件" :class="scope.row.projState !== '02' ? 'el-buttonDisabled' : 'el-buttonStyle'">
                    <span @click="handleEamine(scope.row)">
                      设计软件
                    </span>
                  </el-dropdown-item>
                  <el-dropdown-item command="成果分析" :class="scope.row.projState !== '02' ? 'el-buttonDisabled' : 'el-buttonStyle'">
                    <span @click="handleResults(scope.row)">
                      成果分析
                    </span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination style="margin: 10px" background :current-page="form.page" :page-sizes="pageSize" :page-size="form.perPage" layout="total, sizes, prev, pager, next" @current-change="handleCurrentChange" @size-change="handleSizeChange"
      :total="total">
    </el-pagination>
    <!-- 编辑数据 -->
    <el-dialog title="编辑数据" :visible.sync="editDialog" width="24%" center>
      <el-form ref="formEdit" :model="formEdit" :inline="true">
        <el-form-item label="批次年度:" prop="pcnd" label-width="120px">
          <el-date-picker v-model="formEdit.pcnd" format="yyyy" value-format="yyyy" type="year" placeholder="选择年" @change="NDPointEdit">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="项目名称:" prop="xmmc" label-width="120px">
          <el-input v-model="formEdit.projectName" clearable></el-input>
        </el-form-item>
        <el-form-item label="批次名称:" prop="batch" label-width="120px">
          <el-select v-model="formEdit.batch" clearable placeholder="请选择">
            <el-option v-for="item in batchOptionsEdit" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialog = false">取 消</el-button>
        <el-button class="blue-btn" @click="editDialogQuery">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 设计成果导入 -->
    <el-dialog title="设计成果导入" :visible.sync="requirementsDialog" width="30%" center>
      <el-upload class="upload-demo" :data="getformData()" :action="url + '/tDtfProject/importXqSms'" accept=".zip" :before-upload="beforeAvatarUpload" :on-error="handleError" :file-list="addProjectInfo.file">
        <button class="uploadFiles">
          <i class="el-icon-upload2">点击上传</i>
        </button>
        <div slot="tip" class="el-upload__tip">只能上传.zip文件</div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="requirementsDialog = false">取 消</el-button>
        <el-button class="blue-btn" @click="handleSuccess">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 现状数据 -->
    <!-- <el-dialog
      title="获取现状数据"
      :visible.sync="obtainDialog"
      width="30%"
      center
    >
      <el-table
        :data="gridData"
        :header-cell-style="{ background: 'rgb(160 167 176 / 45%)' }"
        border
      >
        <el-table-column type="index" width="60" align="center" label="序号">
        </el-table-column>
        <el-table-column
          property="name"
          label="步骤"
          width="180"
          align="center"
        ></el-table-column>
        <el-table-column property="" label="进度" align="center">
          <div class="progresswrapper">
            <div class="pro">100%</div>
          </div>
        </el-table-column>
      </el-table>
    </el-dialog> -->
    <el-dialog title="获取现状数据" :visible.sync="obtainDialog" width="30%" left :close-on-click-modal="false">
      <el-table :data="gridData" :header-cell-style="{ background: '#F7F8FA' }">
        <el-table-column type="index" width="60" align="left" label="序号">
        </el-table-column>
        <el-table-column property="name" label="步骤" align="left"></el-table-column>
        <el-table-column width="100" align="left" property="isSuccess" label="是否成功">
          <template slot-scope="scope">
            {{ scope.row.isSuccess ? "成功" : "加载中" }}
          </template>
        </el-table-column>
        <el-table-column property="" label="进度" align="left" width="150px">
          <template slot-scope="scope">
            <div class="progresswrapper">
              <!-- <div class="pro" :style="{width:scope.row.pro+'%'}">{{scope.row.pro}}%</div> -->
              <el-progress :text-inside="true" :stroke-width="15" :percentage="scope.row.pro" text-color="#fff" :color="scope.row.isSuccess ? '#16A065' : '#7286E8'"></el-progress>
              <!-- #526ADE -->
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!-- 馈线ID -->
    <!-- <el-dialog
      title="馈线ID查看"
      width="95%"
      style="height: 100vh"
      v-if="feederDialog"
      :visible.sync="feederDialog"
    >
      <div style="height: 100%">
        <FeederDialog :ProCode="cuttentProCode" v-if="flag"></FeederDialog>
      </div>
    </el-dialog> -->
    <!-- 馈线ID -->
    <el-dialog title="馈线ID查看" width="70%" style="height: 100vh" v-if="feederDialog" :visible.sync="feederDialog">
      <div style="height: 500px; overflow-y: auto" v-loading="fullscreenLoading" element-loading-text="拼命加载中">
        <!-- <FeederDialog :ProCode="cuttentProCode" v-if="flag"></FeederDialog> -->
        <json-viewer class="jsonview" :value="feederData" :expand-depth="jsonformat.expandDepth" :copyable="jsonformat.copyable">
        </json-viewer>
      </div>
      <!-- <span slot="footer" class="dialog-footer">
				<el-button @click="feederDialog = false">取 消</el-button>
				<el-button class="blue-btn" @click="feederDialogQuery">保 存</el-button>
			</span> -->
    </el-dialog>
    <!-- 成果分析 -->
    <el-dialog title="选择解析内容" width="25%" style="height: 100vh" :visible.sync="resultDialog">
      <div style="height: 100%">
        <ResultsDialog :ProCode="cuttentresult" v-if="flag"></ResultsDialog>
      </div>
    </el-dialog>
    <el-dialog title="设计成果查看" width="95%" top="5vh" :style="{ height: '100%', 'margin-top': '0' }" :visible.sync="dialogDesign" v-if="dialogDesign">
      <div style="height: 100%; display: flex">
        <Declaration :ProCode.sync="SJProCode" v-if="flag"></Declaration>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogDesign = false">取 消</el-button>
        <el-button class="blue-btn" @click="dialogQuery">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSb,
  getCity,
  getCounty,
  getXQEditPull,
  EditSave,
  resquest,
  handleReturn,
  handleSubt,
  getURLSJ,
  getuserInfo,
  getSettlement,
  feeederObtainCurrent,
  getPCSel
} from "@/api/api"
import FeederDialog from "@/views/projectAgent/SScomonents/tree"
import ResultsDialog from "@/views/projectAgent/SScomonents/table"
import Declaration from "@/views/projecthz/components/index"
import { progressMixins } from "@/utils/progressMixins.js"
import { EDITMixins } from "@/utils/EDITMixins.js"
export default {
  components: {
    FeederDialog,
    ResultsDialog,
    Declaration
  },
  mixins: [progressMixins, EDITMixins],
  data() {
    return {
      flag: true,
      activeName: "sgsj",
      batchOptionsEdit: [],//批次信息
      tableHeight: 0,
      feederData: [],
      jsonformat: {
        expandDepth: 2,
        copyable: true,
      },
      form: {
        //表单参数
        id: "",
        projState: "",
        stageType: "3",
        projectName: "",
        pcnd: null,
        BATCH: "",
        dsgs: "",
        xgs: "",
        xmName: "",
        tgzt: "09",
        csmc: "",
        dwmc: "",
        page: 1,
        perPage: 10,
        code: ''
      },
      xgsOptions: [], //县公司下拉数据
      dsgsOptions: [], //城市下拉
      jsdwOptions: [], //批次名次下拉数据
      fileList: [],
      pageSize: [5, 10, 20, 50, 100], //分页页数
      formEdit: {
        pcnd: new Date().getFullYear() + '',
        taskID: "",
      },
      url: resquest,
      addProjectInfo: {
        file: [], // 文件上传
        id: "",
      },
      //获取现状图e
      total: 0, //总共页数
      tableData: [],
      cuttentProCode: "", //馈线ID
      cuttentresult: "", //成果分析
      editDialog: false, //编辑
      obtainDialog: false, //现状数据
      feederDialog: false, //馈线ID
      resultDialog: false, //成果分析
      requirementsDialog: false,
      userId: "",
      showPage: false,
      fullscreenLoading: false,
      dialogDesign: false,
      taskID:''
    }
  },
  mounted() {
    this.setTablesHeight()
    const token = sessionStorage.getItem("bhneToken")
    const pageType = sessionStorage.getItem('bhnePageType')
    getuserInfo(token).then((res) => {
      if (Object.keys(res.data.result).length == 0) {
        this.$message.warning("获取用户信息失败，请重新进入页面")
      }
      let menuTypes = res.data.result.zmenu.split(',')

      if (menuTypes.includes(pageType)) {
        console.log("允许进入");
        this.showPage = true
      } else {
        console.log("无权限")
        this.$message.warning("无权限")
        this.showPage = false
      }
      this.form.csmc = res.data.result.USERID
      this.form.dwmc = res.data.result.DWMC
      this.userId = res.data.result.USERID
      this.getList()
    })
    const that = this
    window.onresize = function () {
      that.setTablesHeight()
    }
  },
  methods: {
    setTablesHeight() {
      this.$nextTick(() => {
        const tablesAreaHeight = document.getElementsByClassName('driver')[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        this.tableHeight = windowsAreaHeight - tablesAreaHeight - 120
      })
    },
    // 获取列表
    getList() {
      getSettlement(this.form)
        .then((res) => {
          this.tableData = res.data.data
          this.total = res.data.total
        })
        .catch(() => { })
      if (this.dsgsOptions.length === 0) {
        // 获取城市下拉
        const params = {
          optId: this.form.dwmc,
        }
        getCity(params)
          .then((res) => {
            this.dsgsOptions = res.data.result
          })
          .catch(() => { })
      }
    },
    // 城市点击获取县下拉
    CityPoint(val) {
      const params = {
        optId: val,
        dwmc: this.form.dwmc,
      }
      getCounty(params)
        .then((res) => {
          this.xgsOptions = res.data.result
        })
        .catch(() => { })
    },
    // table列表序号索引
    indexMethod(index) {
      return (this.form.page - 1) * 10 + index + 1
    },
    handleDesign(row) {
      this.dialogDesign = true
      this.SJProCode = row.taskID
      this.taskID = row.taskID
      console.log(row, "rororo")

      this.flag = false
      this.$nextTick(() => {
        this.flag = true
      })
    },
    // 查询
    query() {
      this.getList()
    },
    // 重置
    clearForm() {
      this.form = {
        //表单参数
        id: "",
        projState: "",
        stageType:this.form.stageType,
        projectName: "",
        pcnd: "",
        BATCH: "",
        dsgs: "",
        xgs: "",
        xmName: "",
        tgzt: "09",
        csmc: this.form.csmc,
        dwmc: this.form.dwmc,
        page: 1,
        perPage: 10,
      }
      this.getList()
    },
    // 复选框
    handleClick(tab) {
      console.log(tab.name);
      if (tab.name == "sjbg") {
        this.form.stageType = '4'
      } else {
        this.form.stageType = '3'
      }
      this.getList()
    },
    handleCurrentChange(row) {
      this.form.page = row
      this.getList()
    },
    handleSizeChange(row) {
      this.form.perPage = row
      this.getList()
    },
    // 提交
    handleSubt(row) {
      this.$confirm("是否确定提交?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: "warning",
      })
        .then(() => {
          handleSubt(row.taskID)
            .then((res) => {
              if (res.data.message == "success") {
                this.$message({
                  message: "提交成功!",
                  type: "success",
                })
              } else {
                this.$message.error("提交失败!")
              }
              this.getList()
            })
            .catch(() => { })
        })
    },
    // 启动设计软件
    handleEamine(row) {
      console.log(row)
      // const routeUrl = this.$router.resolve({
      //   path: "/onlineDesign",
      //   query: { taskID: row.taskID },
      // });
      // this.$router.push({
      //   path: "/onlineDesign",
      //   query: { taskID: row.taskID, routerType: '/projectAgent/SSProject' },
      // })
      // window.open(routeUrl.href, "_blank");
      getURLSJ(row.taskID)
        .then((res) => {
          if (res.data.message == "success") {
            window.location = encodeURI(
              "BhneSJRJ://taskID=" +
              row.taskID +
              "&userID=" +
              this.userId +
              "&token=" +
              "11" +
              "&stageType=0"
            );
          } else {
            this.$message.error("启动失败!");
          }
          console.log(res);
        })
        .catch(() => { });
    },
    // 退回
    handleReturn(row) {
      this.$confirm("是否确定退回?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: "warning",
      })
        .then(() => {
          handleReturn(row.taskID)
            .then((res) => {
              if (res.data.message == "success") {
                this.$message({
                  message: "退回成功!",
                  type: "success",
                })
              } else {
                this.$message.error("退回失败!")
              }
              this.getList()
            })
            .catch(() => { })
        })
    },
    // table 编辑按钮
    handleEdit(row) {
      this.formEdit.taskID = row.taskID
      this.formEdit = row
      this.editDialog = true
      getXQEditPull()
        .then((res) => {
          const param = {
            optId: row.pcnd,
          }
          this.batch = res.data.result.batch
          this.xmmc = res.data.result.xmmc
          this.pcnd = res.data.result.pcnd
          getPCSel(param)
            .then((res) => {
              this.batchOptionsEdit = res.data.result
            })
            .catch(() => { })
        })
        .catch(() => { })
    },
    // 编辑弹框确认按钮
    editDialogQuery() {
      EditSave(this.formEdit)
        .then((res) => {
          if (res.data.message === "success") {
            this.$message({
              message: "修改成功!",
              type: "success",
            })
          } else {
            this.$message.error("修改失败!")
          }
        })
        .catch(() => { })
      this.editDialog = false
    },
    NDPointEdit() {
      // 获取批次名词
      const param = {
        optId: this.formEdit.pcnd,
      }
      getPCSel(param)
        .then((res) => {
          console.log(res)
          this.batchOptionsEdit = res.data.result
        })
        .catch(() => { })
    },
    // table 现状数据
    handleCofirm(row) {
      this.taskID = row.taskID
      // if (row.lineID === '') return this.$message.warning("未获取到有效数据")
      this.$confirm("现状数据只能获取一次,是否确定?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: "warning",
      })
        .then(() => {
          this.handleObtain()
        })
        .catch(() => {

        })
    },
    // 馈线ID
    handleFeeder(row) {
      // this.cuttentProCode = row.taskID
      // this.feederDialog = true
      // this.flag = false
      // this.$nextTick(() => {
      //   this.flag = true
      // })
      this.fullscreenLoading = true
      this.cuttentProCode = row.taskID
      this.feederDialog = true
      feeederObtainCurrent(row.taskID).then((res) => {
        console.log(res, " res")
        this.fullscreenLoading = false
        this.feederData = res.data.result.data
      })
    },
    // 馈线保存按钮
    feederDialogQuery() {
      this.feederDialog = false
    },
    // 成果分析
    handleResults(row) {
      this.cuttentresult = row.taskID
      this.resultDialog = true
      this.flag = false
      this.$nextTick(() => {
        this.flag = true
      })
    },
    // table 设计成果导入
    handleRequirements(row) {
      this.addProjectInfo.id = row.taskID
      this.requirementsDialog = true
    },
    // 设计成果导入弹框确认按钮
    requirementsDialogQuery() {
      this.requirementsDialog = false
    },
    // 上传传参
    getformData() {
      return {
        id: this.addProjectInfo.id,
      }
    },
    //上传成功
    handleSuccess() {
      this.$message.success("上传成功")
      this.requirementsDialog = false
    },
    // 上传失败
    handleError() {
      this.$message.error("上传失败")
    },
    // 上传前判断
    beforeAvatarUpload(file) {
      let fileName = file.name
      let pos = fileName.lastIndexOf(".")
      let lastName = fileName.substring(pos, fileName.length)
      if (lastName.toLowerCase() !== ".zip") {
        this.$message.error("文件必须为.zip")
        return
      }
    },
  },
};
</script>

<style>
.actionAreaPage {
  padding: 0 16px 12px 16px;
  display: flex;
}

.buttonArea {
  height: 18px;
  display: flex;
  line-height: 18px;
}

.progresswrapper {
  width: 100%;
  height: 20px;
  color: white;
  margin-left: 3%;
  background: #ffffff;
  position: relative;
}

.pro {
  width: 90%;
  height: 100%;
  background: #00b83f;
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 40px 40px;
  animation: progressbar 2s linear infinite;
}

@keyframes progressbar {
  0% {
    background-position: 40px 0;
  }

  100% {
    background-position: 0 0;
  }
}
.main-headerss {
  height: 48px;
  line-height: 48px;
  background: #ffffff;
  font-weight: bold;
  color: #000000;
  font-size: 16px;
}
.el-dialog__body {
  padding: 24px !important;
}
.el-dialog__footer {
  padding: 16px 24px !important;
}
</style>
