<template>
  <div>
    <div class="dqycsj">
      <div class="dqycsj-leftArea">
        <div class="dqycsj-search">
          <el-form :inline="true" :model="formInline" class="demo-form-inline">
            <el-form-item label="电压等级">
              <el-select v-model="formInline.dy" placeholder="请选择电压等级" @change="getValtage">
                <el-option v-for="item in valltageOptions" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="间隔类别">
              <el-select v-model="formInline.jglb" placeholder="请选择间隔类别" @change="getTableList">
                <el-option v-for="item in jglbOptions" :key="item.key" :value="item.key" :label="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="dqycsj-table">
          <el-table
            :max-height="600"
            :data="tableData"
            :size="small"
            :highlight-current-row="true"
            @current-change="handleCurrentRowChange"
            border
            style="width: 100%">
            <el-table-column
              prop="intervalname"
              label="间隔名称"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="tj"
              align="center"
              label="间隔体积"
            >
            </el-table-column>
            <el-table-column
              prop="materialdescription"
              align="center"
              label="物料信息">
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              width="100">
              <template slot-scope="scope">
                <el-button @click="handleClick(scope.row)" type="text" size="small">修改</el-button>
                <el-button type="text" size="small" @click="removeItem(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="dqycsj-rightArea">
        <img :src="imgUrl" alt="">
      </div>
    </div>
    <el-dialog title="间隔基本信息" :visible.sync="setVisible" width="40%" :append-to-body="true">
      <div class="dqycsj-info">
        <div class="dqycsj-infoLeft">
          <div class="dqycsj-infoTitle">基本信息</div>
          <div class="dqycsj-infoBasic">
            <div class="dqycsj-leftInfo">
              <div class="dqycsj-leftInfoTitle">电压等级</div>
              <div class="dqycsj-leftInfoMsg">{{ updateInfo.dydj }}</div>
            </div>
            <div class="dqycsj-leftInfo">
              <div class="dqycsj-leftInfoTitle">间隔类别</div>
              <div class="dqycsj-leftInfoMsg">{{ updateInfo.jglb }}</div>
            </div>
          </div>
          <el-divider :style="{margin: '12px 0 24px 0'}"></el-divider>
          <div class="dqycsj-infoBasic">
            <div class="dqycsj-leftEdit">
              <div class="dqycsj-leftEditTitle">间隔体积</div>
              <div class="">
                <el-form :inline="true" :model="updateInfo" class="demo-form-inline">
                  <el-form-item label="宽(mm)">
                    <el-input v-model="updateInfo.width"></el-input>
                  </el-form-item>
                  <el-form-item label="深(mm)">
                    <el-input v-model="updateInfo.deep"></el-input>
                  </el-form-item>
                  <el-form-item label="高(mm)">
                    <el-input v-model="updateInfo.height"></el-input>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </div>
        <el-divider direction="vertical" :style="{height: '1'}"></el-divider>
        <div class="dqycsj-infoRight">
          <div class="dqycsj-infoTitle">间隔图纸预览</div>
          <div class="dqycsj-infoImage">
            <img :src="infoImgUrl" alt="">
          </div>
        </div>
      </div>
      <div class="dqycsj-footer">
        <div class="submitBtn" @click="submiteInfo">确 定</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { apipost, apipostGetImg } from '@/utils/mapRequest'
  export default {
    components: {},
    props: {
      searchId: {
        type: String,
        defaults: ''
      }
    },
    data() {
      return {
        formInline: {
          dy: '',
          jglb: ''
        },
        updateInfo: {
          intervalid: '',
          width: '',
          height: '',
          deep: '',
          jglb: '',
          fyjg: '',
          dydj: ''
        },
        setVisible: false,
        imgUrl: '',
        infoImgUrl: '',
        valltageOptions: [], // 电压等级的下拉
        jglbOptions: [], // 间隔类别的下拉
        tableData: [],
      };
    },
    mounted() {
      this.getValtage()
      this.imgUrl = require('@/assets/map/settingImg/noImage.png')
    },
    methods: {
      // 获取电压
      getValtage() {
        apipost('/mapModel/queryIntervalVoltage').then((res) => {
          this.valltageOptions = res.data
          this.formInline.dy = res.data[0].id
          let params={voltage:res.data[0].id}
          apipost(`/mapModel/queryIntervalType`,params).then((res) => {
            this.jglbOptions = res.data
            const param = {
              key: ''
            }
            apipost('/mapModel/queryInterval', param).then((res) => {
              this.tableData = res.result
              let params ={taskid:res.result[0].intervalid}
              apipostGetImg(`/mapModel/downloadIntervalPicture`,params).then((res)=>{
                const reader = new FileReader()
                reader.onload = (e) => {
                  this.imgUrl = e.target.result
                }
                reader.readAsDataURL(res)
                // 处理方式2——直接使用URL.createObjectURL
                // res.data必须是Blob类型数据
                this.imgUrl = URL.createObjectURL(res)
              })
            })
          })
        })
      },
      getIntervalType(val) {
        let params={voltage:val}
        apipost(`/mapModel/queryIntervalType`,params).then((res) => {
          this.jglbOptions = res.data
        })
      },
      // 获取间隔类型
      getTableList(val) {
        const param = {
          key: val
        }
        apipost('/mapModel/queryIntervalById', param).then((res) => {
          this.tableData = res.result
        })
      },
      searchTable() {

      },
      // 点击当前行
      handleCurrentRowChange(val) {
        let params={taskid:val.intervalid}
        apipostGetImg(`/mapModel/downloadIntervalPicture`,params).then((res)=>{
          const reader = new FileReader()
          reader.onload = (e) => {
            this.imgUrl = e.target.result
          }
          reader.readAsDataURL(res)
          // 处理方式2——直接使用URL.createObjectURL
          // res.data必须是Blob类型数据
          this.imgUrl = URL.createObjectURL(res)
        })
      },
      submiteInfo() {
        const param = {
          length: this.updateInfo.width,
          depth: this.updateInfo.deep,
          height: this.updateInfo.height,
          intervalid: this.updateInfo.intervalid
        }
        apipost(`/mapModel/saveInterval`, param).then(()=>{
          this.$message.success('保存成功')
          this.setVisible = false
        })
      },
      handleClick(val) {
        this.updateInfo.dydj = val.voltage
        this.updateInfo.intervalid = val.intervalid
        let params={taskid:val.intervalid}
        apipost(`/mapModel/queryIntervalById`,params).then((res) => {
          this.setVisible = true
          this.updateInfo.width = res.result.length
          this.updateInfo.height= res.result.height
          this.updateInfo.deep = res.result.depth
          this.updateInfo.jglb = res.result.intervaltypekey
          let paramsI={taskid:val.intervalid}
          apipostGetImg(`/mapModel/downloadIntervalPicture`,paramsI).then((res)=>{
            const reader = new FileReader()
            reader.onload = (e) => {
              this.infoImgUrl = e.target.result
            }
            reader.readAsDataURL(res)
            // 处理方式2——直接使用URL.createObjectURL
            // res.data必须是Blob类型数据
            this.infoImgUrl = URL.createObjectURL(res)
          })
        })
      },
    }
  }

</script>

<style lang="scss" scoped>
  .dqycsj-info{
    display: flex;
    .dqycsj-infoTitle{
      border-left: 2px solid #526ade;
      padding-left: 5px;
    }
    .dqycsj-infoImage{
      max-height: 500px;
      margin-top: 10px;
    }
    .dqycsj-infoBasic{
      .dqycsj-leftInfo{
        height: 40px;
        line-height: 40px;
        display: flex;
        .dqycsj-leftInfoMsg{
          padding-left: 12px;
          color: #b1aeae;
        }
      }
      .dqycsj-leftEdit{
        display: flex;
        .dqycsj-leftEditTitle{
          width: 150px;
          display: flex;
          align-items: center;
        }
      }
    }
    .dqycsj-infoLeft{
      flex: 1;
    }
    .dqycsj-infoRight{
      width: 200px;
    }
  }
  .dqycsj-footer{
    position: relative;
    display: flex;
    justify-content: end;
    margin: 12px 0 12px 0;
    .submitBtn{
      width: 80px;
      background: #526ADE;
      border-radius: 20px;
      display: block;
      color: white;
      text-align: center;
      height: 30px;
      line-height: 30px;
    }
  }
  .dqycsj{
    display: flex;
    .dqycsj-leftArea{
      flex: 1;
      .dqycsj-table{
        height: 620px;
      }
    }
    .dqycsj-rightArea{
      width: 400px;
      max-height: 600px;
      img{
        width: 100%;
        height: 100%;
      }
    }
    ::v-deep{
      .el-divider{
        margin: 12px  24px 0;
      }
      .el-dialog__header{
        padding: 10px 20px 10px;
      }
      .el-table .cell {
        white-space: normal;
      }
    }
  }
</style>

