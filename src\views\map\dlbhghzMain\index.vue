<template>
  <div>
    <!--电缆保护管-->
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div v-if="showBackAdds" class="maps-zhedNav" @click="backCurrentDom">
            <img class="mapites-backImg" :src="require('@/assets/'+'map/settingImg/backAdds.png')" alt="">
          </div>
          <span />
          电缆保护管
          <div v-if="!showBackAdds" class="maps-zhedNav" @click="showEveryItemSet">
            <img v-show="!isFoldArea" class="mapites-zhed" :src="require('@/assets/'+'map/settingImg/zhedie.png')" alt="">
            <img v-show="isFoldArea" class="mapites-zhed" :src="require('@/assets/'+'map/settingImg/zkzhedie.png')" alt="">
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img class="settingImg" :src="require('@/assets/'+'map/settingImg/useSetting.png')" alt="">
          <p>启用</p>
        </div>
      </div>
      <div v-show="isFoldArea" class="pro-addForm">
        <div v-show="isShowNav" class="map-showNav">基本信息</div>
        <van-row v-show="isShowNav">
          <van-field
            v-model="startPointMark"
            label="开始点编号"
            disabled
            placeholder="请输入杆塔编号"
          />
        </van-row>
        <van-row v-show="isShowNav">
          <van-field
            v-model="endPointMark"
            disabled
            label="结束点编号"
            placeholder="请输入杆塔编号"
          />
        </van-row>
        <div class="map-showNav">选型信息</div>
        <van-row>
          <!--电缆保护管管材类型-->
          <van-field
            readonly
            clickable
            :value="cableTube.type"
            label="管材类型"
            placeholder="请选择管材类型"
            @click="settingObj.cableTube.type = true"
          />
          <van-popup v-model="settingObj.cableTube.type" round position="bottom">
            <van-picker
              show-toolbar
              title="管材类型"
              value-key="key"
              :columns="cableTubeType"
              @confirm="onConfirmDlbhgSel(0, $event)"
              @cancel="settingObj.cableTube.type = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--电缆保护管管材型号-->
          <van-field
            readonly
            clickable
            :value="cableTube.model"
            label="管材型号"
            placeholder="请选择管材型号"
            @click="settingObj.cableTube.model = true"
          />
          <van-popup v-model="settingObj.cableTube.model" round position="bottom">
            <van-picker
              show-toolbar
              title="管材型号"
              value-key="moduleName"
              :columns="cableTubeModel"
              @confirm="onConfirmDlbhgSel(1, $event)"
              @cancel="settingObj.cableTube.model = false"
            />
          </van-popup>
        </van-row>
        <!--电缆保护管状态-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="cableTube.state"
            label="状态"
            placeholder="请选择状态"
            @click="settingObj.cableTube.state = true"
          />
          <van-popup v-model="settingObj.cableTube.state" round position="bottom">
            <van-picker
              show-toolbar
              title="状态"
              value-key="key"
              :columns="cableChannelsState"
              @confirm="onConfirmDlbhgSel(2, $event)"
              @cancel="settingObj.cableTube.state = false"
            />
          </van-popup>
        </van-row>
        <div v-if="isShowNav" class="map-showNav">其他信息</div>
        <van-row v-if="isShowNav">
          <!--经度-->
          <van-field
            v-model="startLngtitude"
            label="起始点经度"
            disabled
          />
        </van-row>
        <van-row v-if="isShowNav">
          <!--纬度-->
          <van-field
            v-model="startLattitude"
            label="起始点纬度"
            disabled
          />
        </van-row>
        <van-row v-if="isShowNav">
          <!--高程-->
          <van-field
            v-model="endLngtitude"
            label="结束点经度"
            disabled
          />
        </van-row>
        <van-row v-if="isShowNav">
          <!--高程-->
          <van-field
            v-model="endLattitude"
            label="结束点纬度"
            disabled
          />
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { apiget ,apipost} from '@/utils/mapRequest'

export default {
  components: {},
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false
    },
    // 编辑的id
    editData: {
      type: Object,
      defaults: () => {
      }
    },
    remodeState: {
      type: Boolean,
      defaults: false
    }
  },
  data() {
    return {
      isFoldArea: false,
      startPointMark: '', // 开始点编号
      endPointMark: '', // 结束点编号
      startLngtitude: 0, // 有起始点存在情况的开始点经度
      startLattitude: 0, // 有起始点存在情况的开始点纬度
      endLngtitude: 0, // 有起始点存在情况的结束点经度
      endLattitude: 0, // 有起始点存在情况的结束点纬度
      cableTubeType: [
        {
          key: 'MPP',
          value: 'MPP',
          type: 'MPP'
        },
        {
          key: 'CPVC',
          value: 'CPVC',
          type: 'CPVC'
        },
        {
          key: 'GCLX',
          value: 'GCLX',
          type: 'GCLX'
        },
        {
          key: 'PVC',
          value: 'PVC',
          type: 'PVC'
        },
        {
          key: '玻璃钢纤维',
          value: '玻璃钢纤维',
          type: '玻璃钢纤维'
        }
      ], // 管材类型
      cableTubeModel: [], // 电缆保护管管材型号 从后台读
      cableChannelsState: [], // 土建路径状态
      lineState: [
        {
          key: '新建',
          value: '新建'
        },
        {
          key: '原有',
          value: '原有'
        }
      ],
      settingObj: {
        // 电缆保护管
        cableTube: {
          type: false, // 电缆保护管管材类型
          model: false, // 电缆保护管管材型号
          state: false // 状态
        }
      },
      // 电缆保护管
      cableTube: {
        type: 'MPP', // 电缆保护管管材类型
        model: '', // 电缆保护管管材型号
        modelId: '', // 电缆保护管管材型号
        state: '新建', // 状态
        imgList: [], // 文件列表
        message: '', // 备注信息
        audioList: [] // 语音列表
      }
    }
  },
  watch: {
    // 监听改前改后状态
    remodeState: {
      handler(newVal) {
        if (newVal) {
          // 改后不显示拆除的数据
          this.cableChannelsState = this.lineState.slice(0, 2)
          this.cableTube.state = '新建'
        } else {
          // 改前不显示新建的数据
          this.cableChannelsState = this.lineState.slice(1)
          this.cableTube.state = '原有'
        }
      },
      deep: true,
      immediate: true
    },
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal
      },
      deep: true,
      immediate: true
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal
        if (data.moudleType === 'TGBJHZ') {
          this.getAwaitTowerOrLineType('', 4, '', 'DLBHG', '', '', '', data.gcType)
          this.addParam.cableTube.type = data.gcType
          this.addParam.cableTube.modelId = data.gcModel
          this.addParam.cableTube.model = data.gcModuleSpec
          this.addParam.cableTube.state = data.state
          this.addParam.cableTube.message = data.note
        }
      },
      deep: true
    },
    dxList: {
      handler(newVal) {
        this.leverHead.lineModel = newVal[0].name
        this.leverHead.lineModelId = newVal[0].id
        this.tgbjlineType = newVal.tenDx
      },
      deep: true
    }
  },
  mounted() {
    /* 电缆保护管管材型号*/
    this.getFirstTowerOrLineType('', 4, '', 'DLBHG', '', '', '', 'MPP')
  },
  methods: {
    backCurrentDom() {
      this.$emit('backCurrentDom')
      this.getFirstTowerOrLineType('', 4, '', 'DLBHG', '', '', '', 'MPP')
      this.cableTube.type = 'MPP'
      this.cableTube.model = ''
      this.cableTube.shaojLengthL = 0
      const stateText = this.remodeState ? '新建' : '原有'
      this.cableTube.state = stateText
      this.cableTube.imgList = []
      this.cableTube.message = ''
      this.cableTube.audioList = []
      this.settingObj.cableTube.type = false
      this.settingObj.cableTube.model = false
      this.settingObj.cableTube.state = false
    },
    /**
     * 提交数据
     */
    submitData() {
      const parma = {
        type: 14,
        param: this.cableTube,
        visParam: this.settingObj.cableTube
      }
      this.$emit('submitChildData', parma)
    },
    showEveryItemSet() {
      this.isFoldArea = !this.isFoldArea
    },
    /**
     * 电缆保护管
     */
    onConfirmDlbhgSel(type, item) {
      const val = item.value
      switch (type) {
        case 0:
          this.cableTube.type = val
          this.getTowerOrLineType('', 4, '', 'DLBHG', '', '', '', val)
          this.settingObj.cableTube.type = false
          break
        case 1:
          this.cableTube.model = item.moduleName
          this.cableTube.modelId = item.moduleID
          this.settingObj.cableTube.model = false
          break
        case 2:
          this.cableTube.state = val
          this.settingObj.cableTube.state = false
          break
      }
    },
    getFirstTowerOrLineType(type, moduleType, materialsTypeKey, moduleTypeKey, parentKey, moduleCode, voltage, moduleName) {
      const that = this
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: 'aaf86d47-14b7-4da6-9938-398e476a2a75' // 版本号暂时写死 版本号是后台给的
      }
      apipost('/moduleSelection/selectModuleData', param).then(function(res) {
        if (res.code === 1001) {
          that.cableTube.model = res.data[0].moduleName
          that.cableTube.modelId = res.data[0].moduleID
          that.cableTubeModel = res.data
        }
      })
    },
    getTowerOrLineType(settype, type, moduleType, materialsTypeKey, moduleTypeKey, parentKey, moduleCode, voltage, moduleName) {
      const that = this
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: 'aaf86d47-14b7-4da6-9938-398e476a2a75' // 版本号暂时写死 版本号是后台给的
      }
      apipost('/moduleSelection/selectModuleData', param).then(function(res) {
        if (res.code === 1001) {
          // 电缆保护管管材型号
          that.cableTubeModel = res.data
          that.cableTube.model = res.data[0].moduleName
          that.cableTube.modelId = res.data[0].moduleID
        }
      })
    }
  }
}

</script>

<style lang="sass" scoped>
</style>

