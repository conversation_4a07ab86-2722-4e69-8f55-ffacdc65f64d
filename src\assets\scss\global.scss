$primary-color: #526ade;

.mt16 {
  margin-top: 16px;
}

.fr {
  float: right;
}

html,
body,
#app {
  height: 100%;
  margin: 0;
  padding: 0;
  // min-width: 1366px;
  overflow: hidden;
  position: relative;
  background-color: #f5f6fa;
}
li {
  /* 去掉li前面的小圆点 */
  list-style: none;
}

// .el-header {
//   height: 70px;
// }
.h100 {
  height: 100%;
}

.flex-c {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}
// #region 滚动条
::-webkit-scrollbar {
  //滚动条样式
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-thumb {
  //滚动条滑块
  border-radius: 5px;
  // background-color: rgb(85, 136, 184);
  background-color: #a7a3a37a;
}

::-webkit-scrollbar-track {
  //滚动条轨道
  // background: #f8f6f6ad;
  background: transparent;
  // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
}

// #endregion
// 使用el-table的fixed属性固定表格列的时候滚动条被固定列覆盖部分无法拖动
// 可以使用下面配置来解决
.el-table__fixed {
  height: auto !important; // 让固定列的高自适应，且设置!important覆盖ele-ui的默认样式
  bottom: 12px; // 固定列默认设置了定位，
  position: absolute;
  top: 0;
  left: 0;
  position: absolute;
  top: 0;
  left: 0;
}

// .table-content {
//   // padding: 16px 0;
//   position: relative;
//   .table-container-inner {
//     position: absolute;
//     left: 0;
//     right: 0;
//     top: 0
// }
// }
.query-form-box {
  background-color: #fff;
  // padding: 16px;
  // margin-bottom: 16px;

  padding: 16px 0;
}

.card-box {
  // padding: 16px;
  background: #fff;
  // box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
}

.operate-card {
  // background-color: #EDF0FC;
  background-color: rgba(237, 240, 252, 0.6);
  padding: 8px 16px;
  margin-bottom: 16px;

  // padding-bottom: 8px;
}
.my-label {
  font-family: '宋体';
  color: #666;
}
// 斑马纹
.el-table__row.statistics-warning-row {
  background: #f3f5fc;
}
.el-table__row {
  font-family: '宋体';
  color: #000 !important;
  height: 20px;
  font-size: 14px;
}
.el-dialog__header {
  text-align: center;
}
.el-descriptions__title {
  font-weight: bolder;
  font-size: 20px;
}
.el-descriptions__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px !important;
  font-family: '宋体' !important;
}
.el-descriptions-item__content {
  font-size: 15px;
  font-family: '宋体' !important;
  color: #000 !important;
}
.el-descriptions-item__label:not(.is-bordered-label) {
  font-size: 14px;
  font-family: '宋体' !important;
  color: #000;
  margin-left: 10px;
  font-weight: 600;
}
.el-pagination {
  padding: 2px 5px;
  color: #303133;
  font-weight: 700;
  display: flex;
  justify-content: flex-end;
}
.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background: #f3f5fc !important;
}
.has-gutter {
  color: #666 !important;
  font-size: 18px;
}
.cell {
  color: #000000;
}
.table-content {
  background-color: #fff;
  // padding: 8px;
  position: relative;
  font-family: '宋体';

  .table-container-inner {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
  }

  // .el-table {
  //   tbody tr:hover>td {
  //     background-color: rgba(231, 243, 243, 0.7) !important // background-color: #edf0fc !important
  //   }
  // }

  /**
改变边框颜色
*/
  .el-table--border,
  .el-table--group {
    // border: 2px solid #ddd !important;
    border: none !important;
  }
  .el-table::before {
    height: 0px;
  }
  .el-table--border::after {
    width: 0px;
  }
  .el-table--border {
    border: none;
  }
  /**
改变表格内竖线颜色
*/
  .el-table--border td,
  .el-table--border th,
  .el-table__body-wrapper
    .el-table--border.is-scrolling-left
    ~ .el-table__fixed {
    border-right: 0px solid #ebedf4 !important;
  }

  /**
改变表格内行线颜色
*/
  .el-table td,
  .el-table th.is-leaf {
    border-bottom: 1px solid #ebedf4 !important;
  }

  .el-table thead tr th {
    // border-color: #b3b3b3;
    color: #000;
  }
  .el-table--border,
  .el-table--group {
    // border: 1px solid #b3b3b3;
  }
  /* g改变表头字体颜色 */
  /* .el-table thead {
    color: black;
  } */
  .el-table__body tr.current-row > td.el-table__cell {
    background-color: rgba(229, 233, 250, 0.5);
    color: #526ade;
  }

  // 表格边框
  .elx-table.border--full .elx-body--column,
  .elx-table.border--full .elx-footer--column,
  .elx-table.border--full .elx-header--column {
    background-image: linear-gradient(#ebedf4, #ebedf4),
      linear-gradient(#ebedf4, #ebedf4);
    font-family: '宋体';
    color: #606265;
  }
  // fixed边框
  .elx-table.border--full .elx-table--fixed-left-wrapper {
    border-right: 1px solid #ebedf4;
  }
  // 外边框
  .elx-table .elx-table--border-line {
    border: 1px solid #ebedf4;
  }
  .el-upload-list {
    margin: 0;
    padding: 0;
    list-style: none;
    width: 94%;
  }
  // 上传提示文字
  .el-upload-list__item {
    width: 92% !important;
    white-space: normal !important;
  }
  .elx-table .elx-body--row.row--checked,
  .elx-table .elx-body--row.row--current,
  .elx-table .elx-body--row.row--radio {
    background-color: rgba(229, 233, 250, 0.5);
    color: #526ade;
  }

  .elx-header--column .elx-cell--edit-icon,
  .elx-header--column .elx-cell-help-icon {
    color: #526ade;
  }
}

// 弹框样式
.el-dialog__header {
  background: #526ade;
  .el-dialog__headerbtn .el-dialog__close {
    color: #fff;
  }
}

.el-dialog__title {
  color: #fff !important;
}

.el-dialog__footer {
  text-align: right !important;
}
// 表单
.el-form-item__label {
  font-weight: 400;
  color: #000 !important;
  font-weight: 500;
  margin-left: 10px;
  text-align: left !important;
}

// 分页相关

.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: $primary-color !important;
  /*进行修改选中项背景和字体 */
  color: #fff;
}
// 未选中
.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
  // background-color: transparent !important;
}
// 鼠标悬浮
// .el-pagination.is-background .el-pager li:not(.disabled):hover {
//   color: $primary-color !important;
// }

// ----------------------------------
// 修改选择框颜色
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  border-color: $primary-color !important;
  background-color: $primary-color !important;
}

.el-checkbox__inner:hover {
  border-color: $primary-color !important;
}

.el-checkbox__input.is-focus .el-checkbox__inner {
  border-color: $primary-color !important;
}

// ---------------------------------
// 颜色
.color-primary {
  color: $primary-color;
}

// 默认按钮 蓝色
.blue-btn {
  color: #fff !important;
  background-color: #526ade !important;
  border-color: #526ade !important;
}

.blue-btn:hover {
  // 鼠标悬浮
  background-color: #7286e8 !important;
  border-color: #7286e8 !important;
}

.blue-btn:focus {
  // 鼠标点击
  background-color: #7286e8 !important;
  border-color: #7286e8 !important;
}

.blue-btn.is-disabled {
  color: #fff !important;
  background-color: #7286e8 !important;
  border-color: #7286e8 !important;
}

.blue-btn.is-disabled:hover {
  background-color: #7286e8 !important;
  border-color: #7286e8 !important;
}
.descriptions-borerStyle {
  width: 98%;
  height: 100%;
  border-radius: 5px;
  padding: 10px 10px 10px 10px;
  border: 1px solid #ccc;
  margin-bottom: 10px;
}

.el-buttonStyle {
  color: #526ade !important;
  border-color: #526ade !important;
}
.pro-setBtn {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
  .pro-confirm {
    margin-right: 12px;
    border: none;
    background: #526ade;
    color: #ffffff;
    padding: 0 20px;
    border-radius: 10px;
  }
  .pro-cancle {
    background: #ffff;
    border: 1px solid #526ade;
    padding: 3px 20px;
    border-radius: 10px;
  }
}
