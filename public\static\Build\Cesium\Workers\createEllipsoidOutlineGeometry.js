define(["./when-b60132fc","./EllipsoidOutlineGeometry-7e6fabdb","./arrayFill-4513d7ad","./Check-7b2a090c","./buildModuleUrl-4e1b81e7","./Cartographic-3309dd0d","./Math-119be1a3","./Cartesian2-47311507","./PrimitiveType-a54dc62f","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-3a88ba31","./FeatureDetection-c3b71206","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4"],(function(e,t,a,r,i,n,d,b,o,u,c,f,l,s,y,m,p,G,C){"use strict";return function(a,r){return e.defined(a.buffer)&&(a=t.EllipsoidOutlineGeometry.unpack(a,r)),t.EllipsoidOutlineGeometry.createGeometry(a)}}));
