<template>
  <!-- 结算统计 -->
  <div class="index flex-c h100">
    <div class="query-form-box">
      <el-form
        ref="form"
        :model="form"
        :inline="true"
        @submit.native.prevent
        label-width="120px"
      >
        <el-form-item label="建设单位" prop="jsdw">
          <el-select v-model="form.jsdw" clearable placeholder="请选择">
            <!-- <el-option
              v-for="item in jsdwOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option> -->
          </el-select>
        </el-form-item>
        <el-form-item label="结算时间" prop="jssj">
          <el-date-picker
            v-model="form.jssj"
            clearable
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="工程性质" prop="">
          <el-select v-model="form.jsdw" clearable placeholder="请选择">
            <el-option
              v-for="item in jsdwOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="建设性质" prop="jsxz">
          <el-select v-model="form.jsxz" clearable placeholder="请选择">
            <!-- <el-option
              v-for="item in jsdwOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option> -->
          </el-select>
        </el-form-item>
        <el-form-item label="项目名称" prop="xmmc">
          <el-input v-model="form.xmmc" clearable></el-input>
        </el-form-item>
        <el-form-item label="项目编码" prop="xmbm">
          <el-input v-model="form.xmbm" clearable></el-input>
        </el-form-item>
        <div style="float: right">
          <el-form-item label="">
            <el-button class="blue-btn" :loading="loading" @click="query()"
              >查询</el-button
            >
          </el-form-item>
          <el-form-item label="">
            <el-button @click="clearForm()">重置</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="card-box flex-1 flex-c">
      <div class="operate-card">
        <div class="fr">
          <el-button type="text" style="color: #000" @click="exportChange()"
            ><i class="el-icon-s-claim" style="color: #526ade"></i>
            全部导出</el-button
          >
        </div>
      </div>
      <div class="table-content flex-1" ref="tablecontent">
        <div class="table-container-inner">
          <el-table
            :data="tableData"
            border
            :height="tableHeight"
            :cell-class-name="tableCellClassName"
            ref="table"
            @cell-dblclick="dbclick"
            :row-key="getRow"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            style="width: 100%"
            :header-cell-style="{
              'text-align': 'center',
              // background: '#EDF0FC',
              background: '#fff',
              color: '#000',
            }"
            v-loading="loading"
            :cell-style="{ 'text-align': 'center' }"
            :row-class-name="xxxTableRowClassName"
            @selection-change="handleSelectionChange"
          >
            <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
            <el-table-column
              prop="id"
              label="序号"
              show-overflow-tooltip
              fixed
              width="90"
            >
            </el-table-column>
            <el-table-column
              prop="projectCode"
              label="项目编码"
              show-overflow-tooltip
              fixed
              width="250"
            >
              <template slot-scope="scope">
                <span
                  v-html="keyWordHandle(scope.row.projectCode, form.xmbm)"
                ></span>
              </template>
            </el-table-column>
            <el-table-column
              prop=""
              label="项目名称"
              show-overflow-tooltip
              fixed
              width="350px"
            >
              <template slot-scope="scope">
                <span
                  v-html="
                    keyWordHandle(
                      scope.row.gcxz == '项目包'
                        ? scope.row.xmmc
                        : scope.row.projectName,
                      form.xmmc,
                      form.xmmc
                    )
                  "
                ></span>
              </template>
            </el-table-column>
            <el-table-column
              prop="voltageLevel"
              label="电压等级"
              show-overflow-tooltip
              width=""
            >
            </el-table-column>
            <el-table-column
              prop="gcxz"
              label="工程性质"
              show-overflow-tooltip
              width="250"
            >
              <!-- <template slot="header">
                <span
                  >工程性质
                  <i class="el-icon-edit-outline"></i>
                </span>
              </template>
              <template slot-scope="scope">
                <el-select
                  @blur="hideInput(scope)"
                  size="mini"
                  :ref="scope.row.index + ',' + scope.column.index"
                  :id="scope.row.index + ',' + scope.column.index"
                  v-model="scope.row.gcxz"
                  v-if="
                    scope.row.index + ',' + scope.column.index == currentCell
                  "
                  placeholder="请选择"
                >
                  <el-option label="项目包" value="项目包"></el-option>
                  <el-option label="单体工程" value="单体工程"></el-option>
                </el-select>
                <span v-else>{{ scope.row.gcxz }}</span>
              </template> -->
            </el-table-column>
            <el-table-column
              prop="xmdl"
              label="建设性质"
              show-overflow-tooltip
              width=""
            >
            </el-table-column>
            <el-table-column
              prop=""
              label="建设规模"
              show-overflow-tooltip
              width=""
            >
              <el-table-column prop="" label="" show-overflow-tooltip width="">
                <el-table-column
                  prop="dtgs"
                  label="单体个数"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
              </el-table-column>
              <el-table-column
                prop=""
                label="10kV"
                show-overflow-tooltip
                width=""
              >
                <el-table-column
                  prop="numberoftransformers"
                  label="变压器数量（台）"
                  show-overflow-tooltip
                  width=""
                >
                  <template slot="header">
                    <span
                      >变压器数量（台）
                      <i class="el-icon-edit-outline"></i>
                    </span>
                  </template>
                  <template slot-scope="scope">
                    <el-input
                      @blur="hideInput(scope)"
                      @keyup.enter.native="$event.target.blur()"
                      size="mini"
                      :ref="scope.row.index + ',' + scope.column.index"
                      :id="scope.row.index + ',' + scope.column.index"
                      v-model="scope.row.numberoftransformers"
                      v-if="
                        scope.row.index + ',' + scope.column.index ==
                        currentCell
                      "
                      placeholder=""
                    >
                    </el-input>
                    <span v-else>{{ scope.row.numberoftransformers }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="substationcapacity"
                  label="变电容量（千伏安）"
                  show-overflow-tooltip
                  width=""
                >
                  <template slot="header">
                    <span
                      >变电容量（千伏安）
                      <i class="el-icon-edit-outline"></i>
                    </span>
                  </template>
                  <template slot-scope="scope">
                    <el-input
                      @blur="hideInput(scope)"
                      @keyup.enter.native="$event.target.blur()"
                      size="mini"
                      :ref="scope.row.index + ',' + scope.column.index"
                      :id="scope.row.index + ',' + scope.column.index"
                      v-model="scope.row.substationcapacity"
                      v-if="
                        scope.row.index + ',' + scope.column.index ==
                        currentCell
                      "
                      placeholder=""
                    >
                    </el-input>
                    <span v-else>{{ scope.row.substationcapacity }}</span>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column
                prop=""
                label="10kV"
                show-overflow-tooltip
                width=""
              >
                <el-table-column
                  prop="lengthofoverheadline"
                  label="架空线路长度（km）"
                  show-overflow-tooltip
                  width=""
                >
                  <template slot="header">
                    <span
                      >架空线路长度（km）
                      <i class="el-icon-edit-outline"></i>
                    </span>
                  </template>
                  <template slot-scope="scope">
                    <el-input
                      @blur="hideInput(scope)"
                      @keyup.enter.native="$event.target.blur()"
                      size="mini"
                      :ref="scope.row.index + ',' + scope.column.index"
                      :id="scope.row.index + ',' + scope.column.index"
                      v-model="scope.row.lengthofoverheadline"
                      v-if="
                        scope.row.index + ',' + scope.column.index ==
                        currentCell
                      "
                      placeholder=""
                    >
                    </el-input>
                    <span v-else>{{ scope.row.lengthofoverheadline }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="cablelinelength"
                  label="电缆线路长度（km）"
                  show-overflow-tooltip
                  width=""
                >
                  <template slot="header">
                    <span
                      >电缆线路长度（km）
                      <i class="el-icon-edit-outline"></i>
                    </span>
                  </template>
                  <template slot-scope="scope">
                    <el-input
                      @blur="hideInput(scope)"
                      @keyup.enter.native="$event.target.blur()"
                      size="mini"
                      :ref="scope.row.index + ',' + scope.column.index"
                      :id="scope.row.index + ',' + scope.column.index"
                      v-model="scope.row.cablelinelength"
                      v-if="
                        scope.row.index + ',' + scope.column.index ==
                        currentCell
                      "
                      placeholder=""
                    >
                    </el-input>
                    <span v-else>{{ scope.row.cablelinelength }}</span>
                  </template>
                </el-table-column>
              </el-table-column>

              <el-table-column
                prop=""
                label="0.4kV"
                show-overflow-tooltip
                width=""
              >
                <el-table-column
                  prop="lowoverheadline"
                  label="架空线路长度（km）"
                  show-overflow-tooltip
                  width=""
                >
                  <template slot="header">
                    <span
                      >架空线路长度（km）
                      <i class="el-icon-edit-outline"></i>
                    </span>
                  </template>
                  <template slot-scope="scope">
                    <el-input
                      @blur="hideInput(scope)"
                      @keyup.enter.native="$event.target.blur()"
                      size="mini"
                      :ref="scope.row.index + ',' + scope.column.index"
                      :id="scope.row.index + ',' + scope.column.index"
                      v-model="scope.row.lowoverheadline"
                      v-if="
                        scope.row.index + ',' + scope.column.index ==
                        currentCell
                      "
                      placeholder=""
                    >
                    </el-input>
                    <span v-else>{{ scope.row.lowoverheadline }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="lowcablelinelength"
                  label="电缆线路长度（km）"
                  show-overflow-tooltip
                  width=""
                >
                  <template slot="header">
                    <span
                      >电缆线路长度（km）
                      <i class="el-icon-edit-outline"></i>
                    </span>
                  </template>
                  <template slot-scope="scope">
                    <el-input
                      @blur="hideInput(scope)"
                      @keyup.enter.native="$event.target.blur()"
                      size="mini"
                      :ref="scope.row.index + ',' + scope.column.index"
                      :id="scope.row.index + ',' + scope.column.index"
                      v-model="scope.row.lowcablelinelength"
                      v-if="
                        scope.row.index + ',' + scope.column.index ==
                        currentCell
                      "
                      placeholder=""
                    >
                    </el-input>
                    <span v-else>{{ scope.row.lowcablelinelength }}</span>
                  </template>
                </el-table-column>
              </el-table-column>
            </el-table-column>
            <el-table-column
              prop="csSjpssj"
              label="初级评审时间"
              show-overflow-tooltip
              width=""
            >
            </el-table-column>

            <el-table-column
              prop="csCjpsyjsj"
              label="初级审核意见时间"
              show-overflow-tooltip
              width=""
            >
            </el-table-column>
            <el-table-column
              prop="csPsyjwh"
              label="审核意见文号"
              show-overflow-tooltip
              width=""
            >
            </el-table-column>
            <el-table-column
              prop="csSjpfrq"
              label="初设批复时间"
              show-overflow-tooltip
              width=""
            >
            </el-table-column>
            <el-table-column
              prop="csPfwh"
              label="初设批复文号"
              show-overflow-tooltip
              width=""
            >
            </el-table-column>
            <!-- <el-table-column
              prop=""
              label="服务框架编号"
              show-overflow-tooltip
              width=""
            >
            </el-table-column> -->
            <el-table-column
              prop="biddingtime"
              label="招标时间"
              show-overflow-tooltip
              width=""
            >
            </el-table-column>
            <el-table-column
              prop="commencementtime"
              label="开工时间"
              show-overflow-tooltip
              width=""
            >
            </el-table-column>
            <el-table-column
              prop="productiontime"
              label="竣工投产时间"
              show-overflow-tooltip
              width=""
            >
            </el-table-column>
            <el-table-column
              prop="settlementTime"
              label="结算时间"
              show-overflow-tooltip
              width=""
            >
            </el-table-column>
            <el-table-column
              prop="cityName"
              label="所属市公司"
              show-overflow-tooltip
              width=""
            >
            </el-table-column>
            <el-table-column
              prop="countyName"
              label="建设单位"
              show-overflow-tooltip
              width=""
            >
            </el-table-column>
            <!-- <el-table-column
              prop="index"
              label="所属县公司"
              show-overflow-tooltip
              width=""
            >
            </el-table-column> -->
            <el-table-column
              prop="designInstituteName"
              label="设计单位"
              show-overflow-tooltip
              width=""
            >
            </el-table-column>
            <el-table-column
              prop="constructioncontrolunit"
              label="监理单位"
              show-overflow-tooltip
              width=""
            >
            </el-table-column>
            <el-table-column
              prop="consultant"
              label="咨询单位"
              show-overflow-tooltip
              width=""
            >
            </el-table-column>
            <!-- <el-table-column
              prop="index"
              label="施工单位"
              show-overflow-tooltip
              width=""
            >
            </el-table-column> -->
            <!-- <el-table-column
              prop=""
              label="咨询单位"
              show-overflow-tooltip
              width=""
            >
              <el-table-column
                prop=""
                label="可研阶段"
                show-overflow-tooltip
                width=""
              >
              </el-table-column>
              <el-table-column
                prop="index"
                label="初设评审"
                show-overflow-tooltip
                width=""
              >
              </el-table-column>
              <el-table-column
                prop="index"
                label="结算审核"
                show-overflow-tooltip
                width=""
              >
              </el-table-column>
              <el-table-column
                prop="index"
                label="其他"
                show-overflow-tooltip
                width=""
              >
              </el-table-column>
            </el-table-column> -->
            <el-table-column
              prop=""
              label="结算审核专家"
              show-overflow-tooltip
              width=""
            >
              <el-table-column
                prop="jsshzjGcshfzr"
                label="工程审核负责人"
                show-overflow-tooltip
                width=""
              >
                <template slot="header">
                  <span
                    >工程审核负责人
                    <i class="el-icon-edit-outline"></i>
                  </span>
                </template>
                <template slot-scope="scope">
                  <!--v-if去判断双击的是不是当前单元格-->
                  <el-input
                    @blur="hideInput(scope)"
                    @keyup.enter.native="$event.target.blur()"
                    size="mini"
                    :ref="scope.row.index + ',' + scope.column.index"
                    :id="scope.row.index + ',' + scope.column.index"
                    v-model="scope.row.jsshzjGcshfzr"
                    v-if="
                      scope.row.index + ',' + scope.column.index == currentCell
                    "
                  >
                  </el-input>
                  <span v-else>{{ scope.row.jsshzjGcshfzr }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="jsshzjZyshr"
                label="专业审核人"
                show-overflow-tooltip
                width=""
                ><template slot="header">
                  <span
                    >专业审核人
                    <i class="el-icon-edit-outline"></i>
                  </span>
                </template>
                <template slot-scope="scope">
                  <!--v-if去判断双击的是不是当前单元格-->
                  <el-input
                    @blur="hideInput(scope)"
                    @keyup.enter.native="$event.target.blur()"
                    size="mini"
                    :ref="scope.row.index + ',' + scope.column.index"
                    :id="scope.row.index + ',' + scope.column.index"
                    v-model="scope.row.jsshzjZyshr"
                    v-if="
                      scope.row.index + ',' + scope.column.index == currentCell
                    "
                  >
                  </el-input>
                  <span v-else>{{ scope.row.jsshzjZyshr }}</span>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column
              prop=""
              label="批准概算（元）"
              show-overflow-tooltip
              width=""
            >
              <el-table-column
                prop="pzGsdttz"
                label="动态投资"
                show-overflow-tooltip
                width=""
              >
              </el-table-column>
              <el-table-column
                prop="pzGsjttz"
                label="静态投资"
                show-overflow-tooltip
                width=""
              >
              </el-table-column>
              <el-table-column
                prop="pzGsjzgcf"
                label="其中：建筑工程费"
                show-overflow-tooltip
                width=""
              >
              </el-table-column>
              <el-table-column
                prop="pzGssbgzf"
                label="其中：设备购置费"
                show-overflow-tooltip
                width=""
              >
              </el-table-column>
              <el-table-column
                prop="pzGsazgcf"
                label="其中：安装工程费"
                show-overflow-tooltip
                width=""
              >
              </el-table-column>
              <el-table-column
                prop="pzGsqtfy"
                label="其中：其他费用"
                show-overflow-tooltip
                width=""
              >
              </el-table-column>
            </el-table-column>

            <el-table-column
              prop=""
              label="结算审核（元）"
              show-overflow-tooltip
              width=""
            >
              <el-table-column
                prop="jsShdttz"
                label="动态投资"
                show-overflow-tooltip
                width=""
              >
              </el-table-column>
              <el-table-column
                prop="jsShjttz"
                label="静态投资"
                show-overflow-tooltip
                width=""
              >
              </el-table-column>
              <el-table-column
                prop="jsShjzgcf"
                label="其中：建筑工程费"
                show-overflow-tooltip
                width=""
              >
              </el-table-column>
              <el-table-column
                prop="jsShsbgzf"
                label="其中：设备购置费"
                show-overflow-tooltip
                width=""
              >
              </el-table-column>
              <el-table-column
                prop="jsShazgcf"
                label="其中：安装工程费"
                show-overflow-tooltip
                width=""
              >
              </el-table-column>
              <el-table-column
                prop="jsShqtfy"
                label="其中：其他费用"
                show-overflow-tooltip
                width=""
              >
              </el-table-column>
            </el-table-column>

            <el-table-column
              prop=""
              label="结算审核较概算变化率"
              show-overflow-tooltip
              width=""
            >
              <el-table-column
                prop=""
                label="静态投资"
                show-overflow-tooltip
                width=""
              >
                <el-table-column
                  prop="jtChangeMoney"
                  label="变化金额（元）"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
                <el-table-column
                  prop="jtChangeRate"
                  label="变化率（%）"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
              </el-table-column>
              <el-table-column
                prop=""
                label="其中：建筑工程费"
                show-overflow-tooltip
                width=""
              >
                <el-table-column
                  prop="buildChangeMoney"
                  label="变化金额（元）"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
                <el-table-column
                  prop="buildChangeRate"
                  label="变化率（%）"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
              </el-table-column>
              <el-table-column
                prop=""
                label="其中：设备购置费"
                show-overflow-tooltip
                width=""
              >
                <el-table-column
                  prop="shebeiChangeMoney"
                  label="变化金额（元）"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
                <el-table-column
                  prop="shebeiChangeRate"
                  label="变化率（%）"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
              </el-table-column>
              <el-table-column
                prop=""
                label="其中：安装工程费"
                show-overflow-tooltip
                width=""
              >
                <el-table-column
                  prop="installChangeMoney"
                  label="变化金额（元）"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
                <el-table-column
                  prop="installChangeRate"
                  label="变化率（%）"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
              </el-table-column>
              <el-table-column
                prop=""
                label="其中：其他费用"
                show-overflow-tooltip
                width=""
              >
                <el-table-column
                  prop="otherChangeMoney"
                  label="变化金额（元）"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
                <el-table-column
                  prop="otherChangeRate"
                  label="变化率（%）"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
              </el-table-column>
            </el-table-column>
            <el-table-column
              prop=""
              label="与初设原则有无重大变化"
              show-overflow-tooltip
              width=""
            >
              <el-table-column
                prop=""
                label="静态投资"
                show-overflow-tooltip
                width=""
              >
                <el-table-column
                  prop=""
                  label="重大变化项目数（次数、变更几次）"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
                <el-table-column
                  prop=""
                  label="费用（元）"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
              </el-table-column>

              <el-table-column
                prop=""
                label="其中：建筑工程费"
                show-overflow-tooltip
                width=""
              >
                <el-table-column
                  prop=""
                  label="重大变化项目数"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
                <el-table-column
                  prop=""
                  label="费用（元）"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
              </el-table-column>

              <el-table-column
                prop=""
                label="其中：设备购置费"
                show-overflow-tooltip
                width=""
              >
                <el-table-column
                  prop=""
                  label="重大变化项目数"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
                <el-table-column
                  prop=""
                  label="费用（元）"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
              </el-table-column>

              <el-table-column
                prop=""
                label="其中：其他费用"
                show-overflow-tooltip
                width=""
              >
                <el-table-column
                  prop=""
                  label="重大变化项目数"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
                <el-table-column
                  prop=""
                  label="费用（元）"
                  show-overflow-tooltip
                  width=""
                >
                </el-table-column>
              </el-table-column>
            </el-table-column>
            <el-table-column
              prop="jstjRemark"
              label="备注"
              show-overflow-tooltip
              width=""
            >
              <template slot="header">
                <span
                  >备注
                  <i class="el-icon-edit-outline"></i>
                </span>
              </template>
              <template slot-scope="scope">
                <!--v-if去判断双击的是不是当前单元格-->
                <el-input
                  @blur="hideInput(scope)"
                  @keyup.enter.native="$event.target.blur()"
                  size="mini"
                  :ref="scope.row.index + ',' + scope.column.index"
                  :id="scope.row.index + ',' + scope.column.index"
                  v-model="scope.row.jstjRemark"
                  v-if="
                    scope.row.index + ',' + scope.column.index == currentCell
                  "
                >
                </el-input>
                <span v-else>{{ scope.row.jstjRemark }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <!-- 分页 -->
      <div class="mt16">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="->,total, sizes, prev, pager, next"
          :total="total"
          background
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { getJstj, editJstj, resquest } from '@/api/api'
export default {
  name: 'jscgzs',
  data() {
    return {
      tableHeight: 0,
      timer: 0,
      // 用一个字符串来保存当前双击的是哪一个单元格
      currentCell: null,
      form: {
        jsdw: '',
        jsxz: '',
        xmmc: '',
        xmbm: '',
        jssj: [],
      },
      cityId: '',
      countryId: '',
      pageNum: 1,
      pageSize: 10,
      total: 0,
      jsdwOptions: [
        {
          value: '选项1',
          label: '黄金糕',
        },
        {
          value: '选项2',
          label: '双皮奶',
        },
        {
          value: '选项3',
          label: '蚵仔煎',
        },
        {
          value: '选项4',
          label: '龙须面',
        },
        {
          value: '选项5',
          label: '北京烤鸭',
        },
      ],
      loading: false,
      tableData: [
        // {
        //   index: '1',
        //   name: '配电网工程结算审核情况统计表',
        //   jstjRemark: '111',
        // },
        // {
        //   index: '2',
        //   name: '结算审核报告',
        // },
      ],
      multipleSelection: [],
      downloadAllUrl: resquest + '/settlementStatistics/download',
    }
  },
  mounted() {
    this.setTableHeight()
    window.addEventListener('resize', this.onResize)
    this.getList()
  },
  beforeDestroy() {
    this.timer && clearTimeout(this.timer)
    window.removeEventListener('resize', this.onResize)
  },
  methods: {
    // 斑马纹效果
    xxxTableRowClassName({ row, rowIndex }) {
      // if (rowIndex % 2 == 0) {
      //   return 'statistics-warning-row'
      // } else {
      //   return ''
      // }
    },
    // 设置表格高度
    setTableHeight() {
      this.$nextTick(() => {
        let rect = this.$refs.tablecontent.getBoundingClientRect()
        this.tableHeight = rect.height
      })
    },
    onResize() {
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.setTableHeight()
      }, 300)
    },
    clearForm() {
      this.$refs['form'].resetFields()
      this.query()
      //重置的时候 让数据全部收起
      if (this.tableData.length > 0) {
        this.forArr(this.tableData, false)
      }
    },
    //列表展开和收起
    forArr(arr, isExpand) {
      arr.forEach((i) => {
        this.$refs.table.toggleRowExpansion(i, isExpand)
        if (i.itemList) {
          this.forArr(i.itemList, isExpand)
        }
      })
    },
    // 查询关键字变色
    keyWordHandle(e, tags) {
      e = e + ''
      if (tags !== null && tags !== '') {
        let reg = new RegExp('(' + tags + ')', 'g')
        if (e !== null && e !== '') {
          return e.replace(reg, "<font style='color:red'>$1</font>")
        } else {
          return e
        }
      } else {
        return e
      }
    },

    query() {
      this.pageNum = 1
      this.getList()
    },
    getRow(row) {
      return row.taskId
    },
    getList() {
      this.loading = true
      let jskssj = ''
      let jsjssj = ''
      if (this.form.jssj.length > 1) {
        jskssj = this.form.jssj[0]
        jsjssj = this.form.jssj[1]
      }
      let data = {
        current: this.pageNum,
        size: this.pageSize,
        jskssj,
        jsjssj,
        jsdw: this.form.jsdw,
        jsxz: this.form.jsxz,
        xmmc: this.form.xmmc,
        xmbm: this.form.xmbm,
        cityId: this.cityId,
        countryId: this.countryId,
      }
      getJstj(data)
        .then((res) => {
          this.tableData = res.data.data
          this.total = res.data.total
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.pageNum = val
      this.getList()
    },
    // 表格勾选事件
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log(val)
    },
    //批量导出
    exportChange() {
      if (this.tableData.length == 0) {
        this.$message.info('暂无数据')
        return
      }
      console.log('download')
      let jskssj = ''
      let jsjssj = ''
      if (this.form.jssj.length > 1) {
        jskssj = this.form.jssj[0]
        jsjssj = this.form.jssj[1]
      }
      let data = {
        current: this.pageNum,
        size: this.pageSize,
        jskssj,
        jsjssj,
        jsdw: this.form.jsdw,
        jsxz: this.form.jsxz,
        xmmc: this.form.xmmc,
        xmbm: this.form.xmbm,
        cityId: this.cityId,
        countryId: this.countryId,
      }
      function changeParam(param) {
        return JSON.stringify(param)
          .replace(/:/g, '=')
          .replace(/,/g, '&')
          .replace(/{/g, '?')
          .replace(/}/g, '')
          .replace(/"/g, '')
      }
      window.open(`${this.downloadAllUrl}${changeParam(data)}`)

      // if (this.multipleSelection.length == 0)
      //   return this.$message.info('请至少选择一项')
      // console.log(this.multipleSelection)
    },
    // 给单元格绑定横向和竖向的index，这样就能确定是哪一个单元格
    tableCellClassName({ row, column, rowIndex, columnIndex }) {
      row.index = rowIndex
      column.index = columnIndex
    },
    // 获得当前双击的单元格的横竖index，然后拼接成一个唯一字符串用于判断，并赋给currentCell
    // 拼接后类似这样："1,0","1,1",
    dbclick(row, column) {
      if (
        column.property != 'jsshzjGcshfzr' &&
        column.property != 'jsshzjZyshr' &&
        column.property != 'jstjRemark' &&
        column.property != 'numberoftransformers' &&
        column.property != 'substationcapacity' &&
        column.property != 'lengthofoverheadline' &&
        column.property != 'cablelinelength' &&
        column.property != 'lowoverheadline' &&
        column.property != 'lowcablelinelength'
      )
        return
      if (row.gcxz != '单体工程') return this.$message.info('只能编辑单体工程')

      this.currentCell = row.index + ',' + column.index
      // 这里必须要setTimeout，因为在点击的时候，input才刚被v-if显示出来，不然拿不到dom
      setTimeout(() => {
        // 双击后自动获得焦点 el-table使用fixed后通过ref不能获得焦点，通过id解决
        // this.$refs[row.index + ',' + column.index].focus()
        document.getElementById(row.index + ',' + column.index).focus()
      })
    },
    // 当input失去焦点的时候，隐藏input
    hideInput(e) {
      // this.submit(e)

      setTimeout(() => {
        //   this.$confirm('是否编辑?', '提示', {
        //     confirmButtonText: '确定',
        //     cancelButtonText: '取消',
        //     type: 'warning',
        //     closeOnClickModal: false,
        //     callback: () => {},
        //     beforeClose: (action, ctx, close) => {
        //       if (action !== 'confirm') {
        //         close()
        //         this.currentCell = null
        //         this.getList()
        //         return
        //       }
        //       // confirmButtonLoading 是在 elementUI-message-box下的 main.vue 文件中封装的参数
        //       ctx.confirmButtonLoading = true

        //       setTimeout(() => {
        // this.submit(e.row, ctx, close)
        //         // ctx.confirmButtonLoading = false
        //         // close()
        //       }, 300)
        //     },
        //   }).finally(() => {})

        // this.loading = true
        this.submit(e.row)
      }, 200)
    },
    submit(e, ctx, close) {
      let data = {
        projectList: [
          {
            taskID: e.taskId,
            jsshzjGcshfzr: e.jsshzjGcshfzr,
            jsshzjZyshr: e.jsshzjZyshr,
            jstjRemark: e.jstjRemark,
          },
        ],
        structuredDataList: [
          {
            singlePrjWbs: e.projectCode,
            numberoftransformers: e.numberoftransformers,
            substationcapacity: e.substationcapacity,
            lengthofoverheadline: e.lengthofoverheadline,
            cablelinelength: e.cablelinelength,
            lowoverheadline: e.lowoverheadline,
            lowcablelinelength: e.lowcablelinelength,
          },
        ],
      }
      data.projectList = JSON.stringify(data.projectList)
      data.structuredDataList = JSON.stringify(data.structuredDataList)
      editJstj(data)
        .then((res) => {
          if (res) {
            this.getList()
            this.currentCell = null
            // ctx.confirmButtonLoading = false
            // close()
            this.$message.success('修改成功')
          }
        })
        .catch((err) => {
          console.log(err)

          this.getList()
          this.currentCell = null
          // ctx.confirmButtonLoading = false
          // close()
          this.$message.success('修改失败')
        })
    },
  },
}
</script>
<style lang="scss" scoped></style>
