<template>
  <!-- 结算计划 -->
  <div class="index flex-c h100">
    <div class="main-header">结算计划</div>
    <div
      class=""
      style="width: 100%; height: 1px; background-color: #eee"
    ></div>
    <div class="query-form-box">
      <el-form
        ref="form"
        :model="form"
        :inline="true"
        @submit.native.prevent
        label-width="120px"
      >
        <el-row>
          <el-col :span="5">
            <el-form-item
              label="建设单位(市公司):"
              prop="jsdw"
              label-width="140px"
            >
              <el-select
                v-model="form.jsdw"
                clearable
                placeholder="请选择"
                @change="chooseJsdw"
              >
                <el-option
                  v-for="item in jsdwOptions"
                  :key="item.id"
                  :label="item.value"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="建设单位(县公司):" label-width="140px">
              <el-select
                v-model="form.countryId"
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in jsdwOptionsXgs"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="计划时间:" prop="jhsj" label-width="140px">
              <el-date-picker
                v-model="form.jhsj"
                clearable
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="lastSearchArea">
            <el-form-item label="实际时间:" prop="sjsj" label-width="140px">
              <el-date-picker
                v-model="form.sjsj"
                clearable
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="计划性质:" prop="jhxz" label-width="140px">
              <el-select v-model="form.jhxz" clearable placeholder="请选择">
                <el-option label="开工" value="开工"></el-option>
                <el-option label="力争开工" value="力争开工"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="结算状态:" prop="jszt" label-width="140px">
              <el-select v-model="form.jszt" clearable placeholder="请选择">
                <el-option label="提前" value="提前"></el-option>
                <el-option label="按时" value="按时"></el-option>
                <el-option label="超期" value="超期"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="项目名称:" prop="xmmc" label-width="140px">
              <el-input
                v-model="form.xmmc"
                clearable
                placeholder="请输入项目名称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" v-show="!isShowExpand">
            <el-form-item label="项目编码:" prop="xmbm" label-width="140px">
              <el-input
                v-model="form.xmbm"
                clearable
                placeholder="请输入项目编码"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="lastSearchArea" v-show="isShowExpand">
            <el-button
              class="blue-btn"
              :loading="loading"
              @click="query()"
              icon="el-icon-search"
              >查询</el-button
            >
            <el-button
              :loading="loading"
              @click="clearForm()"
              icon="el-icon-refresh"
              >重置</el-button
            >
            <span @click="changeExpand">
              <span class="expandArea">
                展开
                <img
                  style="width: 16px; height: 16px"
                  :src="require('@/assets/main/botArrow.png')"
                  alt=""
              /></span>
            </span>
          </el-col>
        </el-row>
        <el-row v-show="!isShowExpand" style="margin-bottom: 16px">
          <el-col :span="24" class="lastSearchArea">
            <el-button
              class="blue-btn"
              :loading="loading"
              @click="query()"
              icon="el-icon-search"
              >查询</el-button
            >
            <el-button
              :loading="loading"
              @click="clearForm()"
              icon="el-icon-refresh"
              >重置</el-button
            >
            <span @click="changeExpand">
              <span class="expandArea">
                收起
                <img
                  style="width: 16px; height: 16px"
                  :src="require('@/assets/main/topArrow.png')"
                  alt=""
              /></span>
            </span>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="driver"></div>
    <el-button
      style="width: 98px; margin: 0 16px 16px 16px"
      :loading="btnLoading"
      @click="exportChange()"
    >
      <i class="el-icon-download"></i>
      全部导出
    </el-button>
    <div class="tablesArea">
      <ux-grid
        border
        style="width: 98%; margin: 0px 16px 0 16px"
        :tree-config="{
          children: 'children',
          indent: 5,
          trigger: 'cell',
        }"
        :data="tableData"
        ref="plxTable"
        size="small"
        v-loading="loading"
        :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
        :cell-style="{ 'text-align': 'center' }"
        :height="tableHeight"
        show-overflow
        keep-source
        :edit-config="{
          trigger: 'click',
          mode: 'cell',
          activeMethod: dbclick,
          showIcon: false,
        }"
      >
        <ux-table-column field="xh" title="序号" tree-node width="90">
        </ux-table-column>
        <ux-table-column
          field="projectCode"
          title="项目编码"
          width="180"
          resizable
        >
          <template slot-scope="scope">
            <span
              v-html="keyWordHandle(scope.row.projectCode, form.xmbm)"
            ></span>
          </template>
        </ux-table-column>
        <ux-table-column field="" title="项目名称" resizable width="250">
          <template slot-scope="scope">
            <span
              v-html="
                keyWordHandle(
                  scope.row.gcxz == '项目包'
                    ? scope.row.xmmc
                    : scope.row.projectName,
                  form.xmmc
                )
              "
            ></span>
          </template>
        </ux-table-column>
        <ux-table-column
          field="voltageLevel"
          title="电压等级（千伏）"
          min-width="80"
          resizable
        >
        </ux-table-column>
        <ux-table-column
          field="cityName"
          title="所属市公司"
          width="100"
          resizable
        >
        </ux-table-column>
        <ux-table-column
          field="countyName"
          title="建设单位"
          width="100"
          resizable
        >
        </ux-table-column>
        <ux-table-column field="gcxz" title="工程性质" min-width="80" resizable>
        </ux-table-column>
        <ux-table-column field="xmdl" title="建设性质" min-width="80" resizable>
        </ux-table-column>
        <ux-table-column
          field="jhXz"
          title="计划性质"
          :edit-render="{ autofocus: '.el-input__inner' }"
          width="100"
          resizable
        >
          <template v-slot:header="scope">
            <el-tooltip
              class="item"
              effect="dark"
              content="此列单体工程可单击单元格编辑"
              placement="top"
            >
              <span
                ><i class="el-icon-edit-outline"></i
                >{{ scope.column.title }}</span
              >
            </el-tooltip>
          </template>
          <template v-slot:edit="scope">
            <el-select
              @blur="saveEvent(scope.row)"
              @keyup.enter.native="$event.target.blur()"
              size="mini"
              v-model="scope.row.jhXz"
              placeholder="请选择"
            >
              <el-option label="开工" value="开工"></el-option>
              <el-option label="力争开工" value="力争开工"></el-option>
            </el-select>
          </template>
        </ux-table-column>
        <ux-table-column
          field="index"
          title="建设规模"
          min-width="150"
          resizable
        >
          <ux-table-column field="index" title="" min-width="150" resizable>
            <ux-table-column
              field="dtgs"
              title="单体个数"
              min-width="60"
              resizable
            >
            </ux-table-column>
          </ux-table-column>
          <ux-table-column field="" title="10kV" min-width="150" resizable>
            <ux-table-column
              field="disTransfor"
              title="变压器数量（台）"
              min-width="80"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="disTransforKva"
              title="变电容量（千伏安）"
              min-width="90"
              resizable
            >
            </ux-table-column>
          </ux-table-column>
          <ux-table-column field="" title="10kV" min-width="150" resizable>
            <ux-table-column
              field="overheadLine"
              title="架空线路长度（km）"
              min-width="90"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="cableLine"
              title="电缆线路长度（km）"
              min-width="90"
              resizable
            >
            </ux-table-column>
          </ux-table-column>

          <ux-table-column field="" title="0.4kV" min-width="150" resizable>
            <ux-table-column
              field="lowVolLine"
              title="架空线路长度（km）"
              min-width="90"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="lowVolCabLine"
              title="电缆线路长度（km）"
              min-width="90"
              resizable
            >
            </ux-table-column>
          </ux-table-column>
        </ux-table-column>
        <ux-table-column field="" title="年度计划" min-width="150" resizable>
          <ux-table-column field="" title="竣工计划" min-width="150" resizable>
            <ux-table-column
              field="plannedcompletiondate"
              title="计划日期"
              min-width="120"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="actualdateofcompletion"
              title="实际日期"
              min-width="120"
              resizable
            >
            </ux-table-column>
          </ux-table-column>
          <ux-table-column field="" title="投产计划" min-width="150" resizable>
            <ux-table-column
              field="planneddateofproduction"
              title="计划日期"
              min-width="120"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="actualdateofproduction"
              title="实际日期"
              min-width="120"
              resizable
            >
            </ux-table-column>
          </ux-table-column>
          <ux-table-column
            field=""
            title="结算审核计划"
            min-width="150"
            resizable
          >
            <ux-table-column
              field="planYear"
              title="计划年度"
              min-width="90"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="sjNd"
              title="实际年度"
              min-width="90"
              resizable
            >
              <!-- <template v-slot:edit="scope">
                <el-date-picker
                  style="width:120px"
                  type="year"
                  value-format="yyyy"
                  @blur="saveEvent(scope.row)"
                  @keyup.enter.native="$event.target.blur()"
                  size="mini"
                  v-model="scope.row.sjNd"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </template> -->
            </ux-table-column>
            <ux-table-column
              field="planMouth"
              title="计划月份"
              min-width="100"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="sjYf"
              title="实际月份"
              min-width="100"
              resizable
            >
              <!-- <template v-slot:edit="scope">
                <el-date-picker
                  style="width:130px"
                  type="month"
                  value-format="yyyy-MM"
                  @blur="saveEvent(scope.row)"
                  @keyup.enter.native="$event.target.blur()"
                  size="mini"
                  v-model="scope.row.sjYf"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </template> -->
            </ux-table-column>
            <ux-table-column
              field="planDate"
              title="计划日期"
              edit-render
              min-width="160"
              resizable
            >
              <template v-slot:header="scope">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="此列单体工程可单击单元格编辑"
                  placement="top"
                >
                  <span
                    ><i class="el-icon-edit-outline"></i
                    >{{ scope.column.title }}</span
                  >
                </el-tooltip>
              </template>
              <template v-slot:edit="scope">
                <!--v-if去判断双击的是不是当前单元格-->
                <el-date-picker
                  style="width: 130px"
                  type="date"
                  value-format="yyyy-MM-dd"
                  @blur="saveEvent(scope.row)"
                  @keyup.enter.native="$event.target.blur()"
                  size="mini"
                  v-model="scope.row.planDate"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </template>
            </ux-table-column>
            <ux-table-column
              field="sjRq"
              title="实际日期"
              :edit-render="{ autofocus: '.el-input__inner' }"
              min-width="160"
              resizable
            >
              <template v-slot:header="scope">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="此列单体工程可单击单元格编辑"
                  placement="top"
                >
                  <span
                    ><i class="el-icon-edit-outline"></i
                    >{{ scope.column.title }}</span
                  >
                </el-tooltip>
              </template>
              <template v-slot:edit="scope">
                <!--v-if去判断双击的是不是当前单元格-->
                <el-date-picker
                  style="width: 130px"
                  type="date"
                  value-format="yyyy-MM-dd"
                  @blur="saveEvent(scope.row)"
                  @keyup.enter.native="$event.target.blur()"
                  size="mini"
                  v-model="scope.row.sjRq"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </template>
            </ux-table-column>
          </ux-table-column>
        </ux-table-column>
        <ux-table-column
          field="jszt"
          title="结算状态"
          min-width="150"
          resizable
        >
        </ux-table-column>
        <ux-table-column field="" title="可研估算" min-width="150" resizable>
          <ux-table-column
            field="kyPsjhsj"
            title="计划日期"
            min-width="120"
            resizable
          >
          </ux-table-column>
          <ux-table-column field="kySjpsrq" title="实际日期" min-width="120">
          </ux-table-column>
          <ux-table-column
            field="kyHzwh"
            title="核准文号"
            min-width="150"
            resizable
          >
          </ux-table-column>
          <ux-table-column
            field="kyGsdttz"
            title="估算动态投资（元）"
            min-width="100"
            resizable
          >
          </ux-table-column>
          <ux-table-column
            field="kyGsjttz"
            title="估算静态投资（元）"
            min-width="100"
            resizable
          >
          </ux-table-column>
        </ux-table-column>
        <ux-table-column field="" title="初设概算" min-width="150" resizable>
          <ux-table-column field="" title="初设评审" min-width="150" resizable>
            <ux-table-column
              field="csPsjhsj"
              title="计划评审日期"
              min-width="120"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="csSjpssj"
              title="实际评审日期"
              min-width="120"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="csCjpsyjsj"
              title="出具评审意见日期"
              min-width="120"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="csPsyjwh"
              title="评审意见文号"
              min-width="150"
              resizable
            >
            </ux-table-column>
          </ux-table-column>
          <ux-table-column field="" title="初设批复" min-width="150" resizable>
            <ux-table-column
              field="csJhpfrq"
              title="计划批复日期"
              min-width="120"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="csSjpfrq"
              title="实际批复日期"
              min-width="120"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="csPfwh"
              title="批复文号"
              min-width="150"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="csGsdttz"
              title="概算动态投资（元）"
              min-width="90"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="csGsjttz"
              title="概算静态投资（元）"
              min-width="90"
              resizable
            >
            </ux-table-column>
          </ux-table-column>
        </ux-table-column>
        <ux-table-column field="" title="竣工结算" min-width="150" resizable>
          <ux-table-column
            field="planneddate"
            title="计划审核日期"
            :edit-render="{ autofocus: '.el-input__inner' }"
            min-width="160"
            resizable
          >
            <template v-slot:header="scope">
              <el-tooltip
                class="item"
                effect="dark"
                content="此列单体工程可单击单元格编辑"
                placement="top"
              >
                <span
                  ><i class="el-icon-edit-outline"></i
                  >{{ scope.column.title }}</span
                >
              </el-tooltip>
            </template>
            <template v-slot:edit="scope">
              <!--v-if去判断双击的是不是当前单元格-->
              <el-date-picker
                style="width: 130px"
                type="date"
                value-format="yyyy-MM-dd"
                @blur="saveEvent(scope.row)"
                @keyup.enter.native="$event.target.blur()"
                size="mini"
                v-model="scope.row.planneddate"
                placeholder="选择日期"
              >
              </el-date-picker>
            </template>
          </ux-table-column>
          <ux-table-column
            field="actualdate"
            title="实际审核日期"
            :edit-render="{ autofocus: '.el-input__inner' }"
            min-width="160"
            resizable
          >
            <template v-slot:header="scope">
              <el-tooltip
                class="item"
                effect="dark"
                content="此列单体工程可单击单元格编辑"
                placement="top"
              >
                <span
                  ><i class="el-icon-edit-outline"></i
                  >{{ scope.column.title }}</span
                >
              </el-tooltip>
            </template>
            <template v-slot:edit="scope">
              <!--v-if去判断双击的是不是当前单元格-->
              <el-date-picker
                style="width: 130px"
                type="date"
                value-format="yyyy-MM-dd"
                @blur="saveEvent(scope.row)"
                @keyup.enter.native="$event.target.blur()"
                size="mini"
                v-model="scope.row.actualdate"
                placeholder="选择日期"
              >
              </el-date-picker>
            </template>
          </ux-table-column>
          <ux-table-column
            field="reviewdate"
            title="出具评审意见日期"
            :edit-render="{ autofocus: '.el-input__inner' }"
            min-width="160"
            resizable
          >
            <template v-slot:header="scope">
              <el-tooltip
                class="item"
                effect="dark"
                content="此列单体工程可单击单元格编辑"
                placement="top"
              >
                <span
                  ><i class="el-icon-edit-outline"></i
                  >{{ scope.column.title }}</span
                >
              </el-tooltip>
            </template>
            <template v-slot:edit="scope">
              <!--v-if去判断双击的是不是当前单元格-->
              <el-date-picker
                style="width: 130px"
                type="date"
                value-format="yyyy-MM-dd"
                @blur="saveEvent(scope.row)"
                @keyup.enter.native="$event.target.blur()"
                size="mini"
                v-model="scope.row.reviewdate"
                placeholder="选择日期"
              >
              </el-date-picker>
            </template>
          </ux-table-column>
          <ux-table-column
            field="reviewdocumentno"
            title="评审意见文号"
            :edit-render="{ autofocus: '.el-input__inner' }"
            min-width="150"
            resizable
          >
            <template v-slot:header="scope">
              <el-tooltip
                class="item"
                effect="dark"
                content="此列单体工程可单击单元格编辑"
                placement="top"
              >
                <span
                  ><i class="el-icon-edit-outline"></i
                  >{{ scope.column.title }}</span
                >
              </el-tooltip>
            </template>
            <template v-slot:edit="scope">
              <!--v-if去判断双击的是不是当前单元格-->
              <el-input
                @blur="saveEvent(scope.row)"
                @keyup.enter.native="$event.target.blur()"
                size="mini"
                v-model="scope.row.reviewdocumentno"
                placeholder=""
              >
              </el-input>
            </template>
          </ux-table-column>
          <ux-table-column
            field="dynamicinvestment"
            title="结算动态投资（元）"
            min-width="90"
            resizable
          >
            <!-- :edit-render="{ autofocus: '.el-input__inner' }" -->
            <!-- <template v-slot:edit="scope">
              <el-input
                @blur="saveEvent(scope.row)"
                @keyup.enter.native="$event.target.blur()"
                size="mini"
                v-model="scope.row.dynamicinvestment"
                placeholder=""
              >
              </el-input>
            </template> -->
          </ux-table-column>
          <ux-table-column
            field="staticinvestment"
            title="结算静态投资（元）"
            min-width="90"
            resizable
          >
            <!-- :edit-render="{ autofocus: '.el-input__inner' }" -->
            <!-- <template v-slot:edit="scope">
              <el-input
                @blur="saveEvent(scope.row)"
                @keyup.enter.native="$event.target.blur()"
                size="mini"
                v-model="scope.row.staticinvestment"
                placeholder=""
              >
              </el-input>
            </template> -->
          </ux-table-column>
        </ux-table-column>
        <ux-table-column field="" title="费用对比" min-width="150" resizable>
          <ux-table-column
            field=""
            title="（概算-估算）/估算"
            min-width="150"
            resizable
          >
            <ux-table-column
              field="bhje1"
              title="变化金额（元）"
              min-width="90"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="bhl1"
              title="变化率（%）"
              min-width="90"
              resizable
            >
            </ux-table-column>
          </ux-table-column>
          <ux-table-column
            field=""
            title="（结算-概算）/概算"
            min-width="150"
            resizable
          >
            <ux-table-column
              field="bhje2"
              title="变化金额（元）"
              min-width="90"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="bhl2"
              title="变化率（%）"
              min-width="90"
              resizable
            >
            </ux-table-column>
          </ux-table-column>
        </ux-table-column>
        <ux-table-column
          field="tsqksm"
          title="特殊情况说明"
          :edit-render="{ autofocus: '.el-input__inner' }"
          min-width="150"
          resizable
        >
          <template v-slot:header="scope">
            <el-tooltip
              class="item"
              effect="dark"
              content="此列单体工程可单击单元格编辑"
              placement="top"
            >
              <span
                ><i class="el-icon-edit-outline"></i
                >{{ scope.column.title }}</span
              >
            </el-tooltip>
          </template>
          <template v-slot:edit="scope">
            <el-input
              @blur="saveEvent(scope.row)"
              @keyup.enter.native="$event.target.blur()"
              size="mini"
              v-model="scope.row.tsqksm"
            >
            </el-input>
          </template>
        </ux-table-column>
        <ux-table-column
          field="remark"
          title="备注"
          :edit-render="{ autofocus: '.el-input__inner' }"
          min-width="150"
          resizable
        >
          <template v-slot:header="scope">
            <el-tooltip
              class="item"
              effect="dark"
              content="此列单体工程可单击单元格编辑"
              placement="top"
            >
              <span
                ><i class="el-icon-edit-outline"></i
                >{{ scope.column.title }}</span
              >
            </el-tooltip>
          </template>
          <template v-slot:edit="scope">
            <el-input
              @blur="saveEvent(scope.row)"
              @keyup.enter.native="$event.target.blur()"
              size="mini"
              v-model="scope.row.remark"
            >
            </el-input>
          </template>
        </ux-table-column>

        <!-- <ux-table-column title="操作" width="120">
          <template v-slot="{ row }">
            <el-button @click="saveEvent(row)">保存</el-button>
          </template>
        </ux-table-column> -->
      </ux-grid>
    </div>
    <el-pagination
      style="margin: 10px"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="->,total, sizes, prev, pager, next"
      :total="total"
      background
    >
    </el-pagination>
  </div>
</template>

<script>
import {
  getJsjh,
  editJsjh,
  downloadJsjh,
  getJsdw,
  resquest,
  getuserInfo,
  getCounty,
} from "@/api/api"
export default {
  name: "jsjh",
  data () {
    return {
      tableHeight: 0,
      // timer: 0,
      // 用一个字符串来保存当前双击的是哪一个单元格
      currentCell: null,
      isShowExpand: true,
      form: {
        jsdw: "",
        jhsj: [],
        sjsj: [],
        gcxz: "",
        jhxz: "",
        jszt: "",
        xmmc: "",
        dwmc: "",
        xmbm: "",
        countryId: "", //县级公司
      },
      pageNum: 1,
      pageSize: 10,
      total: 0,
      jsdwOptions: [],
      jsdwOptionsXgs: [],
      loading: false,
      btnLoading: false,
      tableData: [
        /*{
            id: '1',
            projectCode: '1213232455345342',
            xmmc: '配电网工程结算审核情况统计表',
            remark: '111',
            gcxz: '项目包',
            tsqksm: '',
            children: [
              {
                id: '1.1',
                projectCode: '12121212133333',
                projectName: '配电网工程结算审核情况统计表',
                remark: '111',
                gcxz: '单体工程',
                tsqksm: '',
              },
            ],
          },
          {
            id: '2',
            xmmc: '结算审核报告',
            projectCode: '1213232455323345342',
            gcxz: '项目包',
            tsqksm: '',
            children: [
              {
                id: '2.1',
                projectCode: '12132324553453411112',
                projectName: '配电网工程结算审核情况统计表',
                remark: '111',
                tsqksm: '',
              },
            ],
          },*/
      ],

      multipleSelection: [],
      downloadAllUrl: resquest + "/settlementPlan/download",

      collapseStatus3: true,
      expandIds: [], // 当前展开的节点 id
      rows: [],
      cityId: "",
      countryId: "",
    }
  },
  mounted () {
    this.setTablesHeight()
    const token = sessionStorage.getItem('bhneToken')
    getuserInfo(token).then((res) => {
      this.form.csmc = res.data.result.USERID
      this.form.dwmc = res.data.result.DWMC
      this.getList()
    })
    this.getJsdwList()
    const that = this
    window.onresize = function () {
      that.setTablesHeight()
    }
  },
  created () {
    this.cityId = this.$route.query.dsgs || ""
    this.countryId = this.$route.query.xgs || ""
  },
  beforeDestroy () {
    // this.timer && clearTimeout(this.timer)
    // window.removeEventListener('resize', this.onResize)
  },
  methods: {
    changeExpand () {
      this.isShowExpand = !this.isShowExpand
      this.setTablesHeight()
    },
    setTablesHeight () {
      this.$nextTick(() => {
        const tablesAreaHeight = document.getElementsByClassName('driver')[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        this.tableHeight = windowsAreaHeight - tablesAreaHeight - 115
      })
    },
    chooseJsdw (val) {
      console.log(this.form.dwmc)
      console.log(val, "lll")
      let params = {
        optId: val,
        dwmc: this.form.dwmc,
      }
      getCounty(params).then((res) => {
        console.log(res)
        this.jsdwOptionsXgs = res.data.result || []
       
      })
    },
    getJsdwList () {
      let key = this.cityId ? "dsgs" : this.countryId ? "xgs" : ""
      let id = this.cityId || this.countryId || ""
      let data = {
        key,
        id,
      }
      getJsdw(data).then((res) => {
        console.log(res)
        this.jsdwOptions = res.data.data
        console.log(this.jsdwOptions)
      })
    },
    // 斑马纹效果
    xxxTableRowClassName ({ row, rowIndex }) {
      // if (rowIndex % 2 == 0) {
      //   return 'statistics-warning-row'
      // } else {
      //   return ''
      // }
    },
    // 设置表格高度
    // setTableHeight() {
    //   this.$nextTick(() => {
    //     let rect = this.$refs.tablecontent.getBoundingClientRect()
    //     this.tableHeight = rect.height
    //     console.log(this.tableHeight)
    //   })
    // },
    // onResize() {
    //   this.timer && clearTimeout(this.timer)
    //   this.timer = setTimeout(() => {
    //     this.setTableHeight()
    //   }, 300)
    // },
    clearForm () {
      // this.$refs['form'].resetFields()
      this.$refs["form"].reset()
      this.query()
      //重置的时候 让数据全部收起
      // if (this.tableData.length > 0) {
      //   this.forArr(this.tableData, false)
      // }
    },
    //列表展开和收起
    forArr (arr, isExpand) {
      arr.forEach((i) => {
        this.$refs.table.toggleRowExpansion(i, isExpand)
        if (i.itemList) {
          this.forArr(i.itemList, isExpand)
        }
      })
    },
    // 查询关键字变色
    keyWordHandle (e, tags) {
      e = e + ""
      if (tags !== null && tags !== "") {
        let reg = new RegExp("(" + tags + ")", "g")
        if (e !== null && e !== "") {
          return e.replace(reg, "<font style='color:red'>$1</font>")
        } else {
          return e
        }
      } else {
        return e
      }
    },
    query () {
      this.pageNum = 1
      this.getList()
    },
    getRow (row) {
      // return row.taskId
      return row.id
    },
    getList () {
      this.loading = true
      let jhkssj = ""
      let jhjssj = ""
      let sjkssj = ""
      let sjjssj = ""
      if (this.form.jhsj.length > 1) {
        jhkssj = this.form.jhsj[0]
        jhjssj = this.form.jhsj[1]
      }
      if (this.form.sjsj.length > 1) {
        sjkssj = this.form.sjsj[0]
        sjjssj = this.form.sjsj[1]
      }
      let data = {
        current: this.pageNum,
        size: this.pageSize,
        jsdw: this.form.jsdw,
        jhkssj,
        jhjssj,
        sjkssj,
        sjjssj,
        gcxz: this.form.gcxz,
        jhxz: this.form.jhxz,
        jszt: this.form.jszt,
        xmmc: this.form.xmmc,
        xmbm: this.form.xmbm,
        countryId: this.countryId,
        cityId: this.cityId,
        countryId: this.form.countryId, //县级公司
      }
      getJsjh(data)
        .then((res) => {
          this.tableData = res.data.data
          this.total = res.data.total
          //重置的时候 让数据全部收起
          // if (this.tableData.length > 0) {
          //   this.forArr(this.tableData, false)
          // }
          if (this.rows.length > 0) {
            this.rows.forEach((i) => {
              const obj = this.tableData.find((item) => item.id == i.id)
              this.$refs.plxTable.setTreeExpand(obj, true)
            })
          }

          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleSizeChange (val) {
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.pageNum = val
      this.getList()
    },
    // 表格勾选事件
    handleSelectionChange (val) {
      this.multipleSelection = val
      console.log(val)
    },
    //批量导出
    exportChange () {
      // if (this.multipleSelection.length == 0)
      //   return this.$message.info('请至少选择一项')
      // console.log(this.multipleSelection)

      // this.btnLoading = true
      // if (this.tableData.length == 0) {
      //   this.$message.info('暂无数据')
      //   return
      // }
      let jhkssj = ""
      let jhjssj = ""
      let sjkssj = ""
      let sjjssj = ""
      if (this.form.jhsj.length > 1) {
        jhkssj = this.form.jhsj[0]
        jhjssj = this.form.jhsj[1]
      }
      if (this.form.sjsj.length > 1) {
        sjkssj = this.form.sjsj[0]
        sjjssj = this.form.sjsj[1]
      }
      let data = {
        current: this.pageNum,
        size: this.pageSize,
        jsdw: this.form.jsdw,
        jhkssj,
        jhjssj,
        sjkssj,
        sjjssj,
        gcxz: this.form.gcxz,
        jhxz: this.form.jhxz,
        jszt: this.form.jszt,
        xmmc: this.form.xmmc,
        xmbm: this.form.xmbm,
        cityId: this.cityId,
        countryId: this.countryId,
      }

      function changeParam (param) {
        return JSON.stringify(param)
          .replace(/:/g, "=")
          .replace(/,/g, "&")
          .replace(/{/g, "?")
          .replace(/}/g, "")
          .replace(/"/g, "")
      }
      window.open(`${this.downloadAllUrl}${changeParam(data)}`)

      // // downloadJsjh(formData)
      // //   .then((res) => {
      // //     // this.tableData = res.data.data
      // //     // this.total = res.data.total
      // //     if (res) {
      // //       this.$message.success('导出成功')
      // //       this.btnLoading = false
      // //     }
      // //   })
      // //   .catch(() => {
      // //     this.$message.success('导出失败')
      // //     this.btnLoading = false
      // //   })

      // // 调用下载接口，返回bolb文件流
      // this.axios({
      //   method: 'get',
      //   url: resquest + '/settlementPlan/download', //后端下载接口地址
      //   responseType: 'blob', // 设置接受的流格式
      //   params: data,
      // }).then((res) => {
      //   console.log(res)
      //   // res 中还有一层data ,如果直接用blob流，文件里面是object，我就是犯错了。
      //   this.handleExport(res.data)
      // })
    },
    // 给单元格绑定横向和竖向的index，这样就能确定是哪一个单元格
    tableCellClassName ({ row, column, rowIndex, columnIndex }) {
      row.index = rowIndex
      column.index = columnIndex
    },

    submit (e) {
      let jszt = ""
      if (e.planDate && e.sjRq) {
        let planDateTimestamp = Date.parse(new Date(e.planDate))
        let sjRqTimestamp = Date.parse(new Date(e.sjRq))
        sjRqTimestamp < planDateTimestamp
          ? (jszt = "提前")
          : sjRqTimestamp == planDateTimestamp
            ? (jszt = "按时")
            : sjRqTimestamp > planDateTimestamp
              ? (jszt = "超期")
              : ""
      }
      let sjNd = ""
      let sjYf = ""
      let planMouth = ""
      let planYear = ""
      if (e.sjRq) {
        let rqArr = e.sjRq.split("-")
        sjNd = rqArr[0]
        sjYf = rqArr[0] + "-" + rqArr[1]
      }
      if (e.planDate) {
        let rqArr = e.planDate.split("-")
        planYear = rqArr[0]
        planMouth = rqArr[0] + "-" + rqArr[1]
      }
      /*let data = {
          projectList: [
            {
              taskID: e.taskId,
              jhXz: e.jhXz,
              // sjNd: e.sjNd,
              // sjYf: e.sjYf,
              sjNd,
              sjYf,
              sjRq: e.sjRq,
              jszt,
              tsqksm: e.tsqksm,
              remark: e.remark,
            },
          ],
          structuredDataList: [
            {
              singlePrjWbs: e.projectCode,
              settlementauditplandate: e.planDate,
              planneddate: e.planneddate,
              actualdate: e.actualdate,
              reviewdate: e.reviewdate,
              reviewdocumentno: e.reviewdocumentno,
              dynamicinvestment: e.dynamicinvestment,
              staticinvestment: e.staticinvestment,
              // numberoftransformers: e.disTransfor,
              // substationcapacity: e.disTransforKva,
              // lengthofoverheadline: e.overheadLine,
              // cablelinelength: e.cableLine,
              // lowoverheadline: e.lowVolLine,
              // lowcablelinelength: e.lowVolCabLine,
            },
          ],
        }*/
      let data = {
        structuredDataList: [
          {
            singlePrjWbs: e.projectCode,
            planDate: e.planDate,
            planneddate: e.planneddate,
            actualdate: e.actualdate,
            reviewdate: e.reviewdate,
            reviewdocumentno: e.reviewdocumentno,
            dynamicinvestment: e.dynamicinvestment,
            staticinvestment: e.staticinvestment,
            // taskID: e.taskId,
            id: e.id,
            jhXz: e.jhXz,
            sjNd,
            sjYf,
            planYear,
            planMouth,
            sjRq: e.sjRq,
            jszt,
            tsqksm: e.tsqksm,
            remark: e.remark,
          },
        ],
      }
      // data.projectList = JSON.stringify(data.projectList)
      data.structuredDataList = JSON.stringify(data.structuredDataList)
      editJsjh(data)
        .then((res) => {
          if (res.data.status == "000000") {
            this.$refs.plxTable.clearActived()
            this.$refs.plxTable.reloadRow(e, null, null)
            this.rows = []
            this.rows = this.$refs.plxTable.getTreeExpandRecords()
            this.getList()
            this.loading = false
            this.$message.success(res.data.message)
          } else {
            this.getList()
            this.$message.error("修改失败")
          }
        })
        .catch(() => {
          this.getList()
          this.loading = false
          this.$message.error("修改失败")
        })
    },
    dbclick (row) {
      if (row.row.gcxz != "单体工程") {
        this.$message.info("只能编辑单体工程")
        return false
      } else return true
    },
    // 点击保存
    saveEvent (row) {
      this.loading = true
      setTimeout(() => {
        // 判断是否发生改变
        console.log(this.$refs.plxTable.isUpdateByRow(row))
        if (this.$refs.plxTable.isUpdateByRow(row)) {
          // 局部保存，并将行数据恢复到初始状态（如果 第二个参数record 为空则不改动行数据，只恢复状态）
          // 必须执行这个，不然下次点击保存，还是保存成功哦！状态没改变哦
          // this.$refs.plxTable.reloadRow(row, null, null)
          this.submit(row)
        } else {
          // this.$message({
          //   message: '保存失败，你没改变当前行的数据',
          //   type: 'error',
          // })
          this.loading = false
        }
      }, 300)
    },

    /* 下载文件的公共方法，参数就传blob文件流*/
    handleExport (data) {
      // 动态创建iframe下载文件
      // let fileName = this.selectedTabelRow[0].dirName
      if (!data) {
        return
      }
      let blob = new Blob([data], {
        type: "application/octet-stream",
      })
      if ("download" in document.createElement("a")) {
        // 不是IE浏览器
        let url = window.URL.createObjectURL(blob)
        let link = document.createElement("a")
        link.style.display = "none"
        link.href = url
        link.setAttribute("download", "表格.xlsx")
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link) // 下载完成移除元素
        window.URL.revokeObjectURL(url) // 释放掉blob对象
        this.btnLoading = false
      } else {
        // IE 10+
        window.navigator.msSaveBlob(blob, "表格.xlsx")
        this.btnLoading = false
      }
    },
    // searchEvent () {
    //   VXETable.modal.message({ message: '查询事件', status: 'info' })
    // },
    // resetEvent () {
    //   VXETable.modal.message({ message: '重置事件', status: 'info' })
    // },
    collapseHandle () {
      this.setTableHeight()
    },
  },
};
</script>
<style lang="scss" scoped>
.el-icon-edit-outline {
  color: #526ade;
  margin-right: 3px;
}

.elx-header--column > .col_10 {
  background-color: rgb(82, 113, 130) !important;
}
.expandArea {
  margin-left: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  img {
    margin-left: 2px;
  }
}
</style>
