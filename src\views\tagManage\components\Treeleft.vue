<template>
  <div class="">
    <el-tree
      :data="treeDataList"
      :props="treetypeProps"
      node-key="id"
      v-on:node-click="handleNodeClick"
    >
    </el-tree>
  </div>
</template>

<script>
import { getTagBindData } from '@/views/basicParameter/data-static/data.js'
export default {
  name: '',
  data() {
    return {
      treeDataList: [],
      TagList: getTagBindData(),
      treetypeProps: {
        children: 'children',
        label: 'text',
        id: 'id',
      },
    }
  },
  mounted() {
    this.getTreeData()
  },
  methods: {
    handleNodeClick(data, node) {
      if (node.childNodes.length != 0) return
      this.$emit('treeclick', data.attributes)
    },
    getTreeData() {
      this.treeDataList = this.TagList
    },
  },
}
</script>

<style scoped lang="scss"></style>
