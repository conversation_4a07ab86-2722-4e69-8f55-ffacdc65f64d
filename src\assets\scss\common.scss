.layout {
  height: 100vh;
  font-size: 15px;

  /*.header {
    background: url(~@/assets/img/顶部背景.png);
    color: #fff;
    height: 65px !important;
    display: flex;
    justify-content: space-between;

    // -------------------------------
    .grid-content {
      font-size: 0;
    }

    .headerLeft {
      display: flex;
      align-items: center;
    }

    .headerRight {
      display: flex;
      align-items: center;

      .userHead {
        font-size: 0;
      }

      .el-dropdown {
        margin-left: 10px;

        .el-dropdown-link {
          cursor: pointer;
          color: #fff;
        }

        .el-icon-arrow-down {
          font-size: 12px;
        }
      }
    }
  }
*/
.buttonArea {
  height: 18px;
  display: flex;
  line-height: 18px;
}
.actionAreaPage {
  padding: 0 16px 12px 16px;
  display: flex;
  
}
  .aside {
    background: #fff;
    // color: #fff;
    /* height: 100%; */
    margin:  16px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);

    .el-menu {
      border-right: 0
    }
  }

  .main {
    /* height: 100%; */
    display: flex;
    flex-direction: column;
    overflow: hidden;
    color: #212121;
    padding: 16px;
    margin: 16px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)


  }
}