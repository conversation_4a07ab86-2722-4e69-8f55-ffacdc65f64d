export function emptyMapData(vueObjs) {
  // 附属设施
  vueObjs.addParam.facilities.state = '新建'
  vueObjs.addParam.facilities.type = '底盘'
  vueObjs.addParam.facilities.model = ''
  vueObjs.addParam.facilities.modelId = ''
  vueObjs.addParam.facilities.imgList = []
  vueObjs.addParam.facilities.message = ''
  vueObjs.addParam.facilities.audioList = []
  vueObjs.settingObj.facilities.state = false
  vueObjs.settingObj.facilities.type = false
  vueObjs.settingObj.facilities.model = false
  // 电缆线路
  vueObjs.addParam.cableLine.type = '直埋'
  vueObjs.addParam.cableLine.model = ''
  vueObjs.addParam.cableLine.modelId = ''
  vueObjs.addParam.cableLine.cableLineHeadModel = ''
  vueObjs.addParam.cableLine.plmval = '混凝土路面(150mm以下)'
  vueObjs.addParam.cableLine.voltage = '10kV'
  vueObjs.addParam.cableLine.imgList = []
  vueObjs.addParam.cableLine.message = ''
  vueObjs.addParam.cableLine.audioList = []
  vueObjs.addParam.cableLine.listArr = [
    {
      lineName: '线路1', // 线路名称
      linemodel: '', // 型号
      lineModelId: '', // 型号
      lineState: '新建', // 状态
      terminal: true, // 终端头
      zdtModel: '', // 终端头型号
      zdtModelId: '', // 终端头型号
      zjtModel: '', // 中间头型号
      tdTypeSpec: '', // 通道型号
      tdType: '', // 通道型号id
      zjtModelId: '', // 终端头型号
      lineLength: 0,
      middle: '250' // 中间头
    }
  ]
  vueObjs.settingObj.cableLine.type = false
  vueObjs.settingObj.cableLine.backLine = false,
  vueObjs.settingObj.cableLine.voltage = false,
  vueObjs.settingObj.cableLine.cableLineHeadModel = false,
  vueObjs.settingObj.cableLine.plmval = false,
  vueObjs.settingObj.cableLine.listArr = [
    {
      cableLineModel: false, // 电缆型号
      cableLineState: false, // 电缆状态
      tdType: false, // 通道型号
      zdtModel: false, // 中间头下拉是否使用
      zjtModel: false // 中间头是否使用
    }
  ]
  // 电缆头
  vueObjs.addParam.cableHead.type = '终端头'
  vueObjs.addParam.cableHead.voltage = '10kV'
  vueObjs.addParam.cableHead.number = '1'
  vueObjs.addParam.cableHead.spacing = '250'
  vueObjs.addParam.cableHead.state = '新建'
  vueObjs.addParam.cableHead.imgList = []
  vueObjs.addParam.cableHead.message = ''
  vueObjs.addParam.cableHead.audioList = []
  vueObjs.settingObj.cableHead.type = false
  vueObjs.settingObj.cableHead.voltage = false
  vueObjs.settingObj.cableHead.model = false
  vueObjs.settingObj.cableHead.state = false
  // 电缆保护管
  vueObjs.addParam.cableTube.type = 'MPP'
  vueObjs.addParam.cableTube.model = ''
  vueObjs.addParam.cableTube.shaojLengthL = 0
  vueObjs.addParam.cableTube.state = '新建'
  vueObjs.addParam.cableTube.imgList = []
  vueObjs.addParam.cableTube.message = ''
  vueObjs.addParam.cableTube.audioList = []
  vueObjs.settingObj.cableTube.type = false
  vueObjs.settingObj.cableTube.model = false
  vueObjs.settingObj.cableTube.state = false

  // 电缆分支箱
  vueObjs.addParam.cableBranch.voltage = '10kV'
  vueObjs.addParam.cableBranch.branch = ''
  vueObjs.addParam.cableBranch.state = '新建'
  vueObjs.addParam.cableBranch.number = 'dlfzx001'
  vueObjs.addParam.cableBranch.lineName = '线路一'
  vueObjs.addParam.cableBranch.imgList = []
  vueObjs.addParam.cableBranch.message = ''
  vueObjs.addParam.cableBranch.audioList = []
  vueObjs.settingObj.cableBranch.voltage = false
  vueObjs.settingObj.cableBranch.branch = false
  vueObjs.settingObj.cableBranch.state = false

  // 土建路径
  vueObjs.addParam.cableChannels.name = '线路一'
  vueObjs.addParam.cableChannels.laying = ''
  vueObjs.addParam.cableChannels.model = ''
  vueObjs.addParam.cableChannels.state = '新建'
  vueObjs.addParam.cableChannels.number = '1'
  vueObjs.addParam.cableChannels.spacing = '250'
  vueObjs.addParam.cableChannels.imgList = []
  vueObjs.addParam.cableChannels.message = ''
  vueObjs.addParam.cableChannels.audioList = []
  vueObjs.settingObj.cableChannels.laying = false
  vueObjs.settingObj.cableChannels.model = false
  vueObjs.settingObj.cableChannels.state = false

  // 电缆井
  vueObjs.addParam.cablePit.operation = '插入电缆井'
  vueObjs.addParam.cablePit.type = '直通'
  vueObjs.addParam.cablePit.model = ''
  vueObjs.addParam.cablePit.state = '新建'
  vueObjs.addParam.cablePit.imgList = []
  vueObjs.addParam.cablePit.message = ''
  vueObjs.addParam.cablePit.audioList = []
  vueObjs.settingObj.cablePit.state = false
  vueObjs.settingObj.cablePit.type = false
  vueObjs.settingObj.cablePit.model = false
  vueObjs.settingObj.cablePit.state = false

  // 配电站房

  // 变电站
  vueObjs.addParam.changeCable.valtage = '35kV'
  vueObjs.addParam.changeCable.state = '新建'
  vueObjs.addParam.changeCable.name = '编号1'
  vueObjs.addParam.changeCable.plmType = '混凝土路面(150mm以下)'
  vueObjs.addParam.changeCable.listArr = [
    {
      lineName: '线路1', // 线路名称
      lineModel: '',
      lineModelId: '',
      lineState: '新建', // 状态
      terminal: true, // 终端头
      lineLength: 0,
      middle: '250' // 中间头
    }
  ]
  vueObjs.settingObj.changeCable.state = false
  vueObjs.settingObj.changeCable.valtage = false
  vueObjs.settingObj.changeCable.listArr = [
    {
      lineState: false, // 线路状态
      tdType: false, // 通道型号
      lineType: false, // 线路状态
      lineModel: false, // 线路型号
      zjtModel: false, // 线路型号
      zdtModel: false // 线路型号
    }
  ]
}
