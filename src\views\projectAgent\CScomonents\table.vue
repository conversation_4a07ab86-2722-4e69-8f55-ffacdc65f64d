<template>
  <div id="">
    <el-table :data="gridData" :header-cell-style="{ background: 'rgb(160 167 176 / 45%)' }" border>
      <el-table-column type="index" width="50" align="center">
      </el-table-column>
      <el-table-column property="name" label="解析内容" width="150" align="center"></el-table-column>
      <el-table-column property="" label="解析" align="center">
        <template slot-scope="scope">
          <span @click="handleView(scope.row)" style="color: #526ade; cursor: pointer">查看</span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination style="margin: 10px 0px" background :current-page="q.page" :page-sizes="pageSize"
      :page-size="q.limit" layout="total, sizes, prev, pager, next" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" :total="3">
    </el-pagination>
    <el-dialog title="成果完整性筛查" :visible.sync="DialogVisible" width="50%" center :append-to-body="true">
      <el-table :data="gridDatas" :header-cell-style="{ background: 'rgb(160 167 176 / 45%)' }" border>
        <el-table-column type="index" :index="indexMethod" :resizable="false" width="50">
        </el-table-column>
        <el-table-column property="name" label="必备材料" width="120" align="center"></el-table-column>
        <el-table-column property="ist" label="是否具有" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.ist == '1'" style="color: green">是</span>
            <span v-else style="color: red">否</span>
          </template>
        </el-table-column>
        <el-table-column property="filenum" label="文件数" align="center">
        </el-table-column>
      </el-table>
      <el-pagination style="margin: 10px 0px" background :current-page="params.pageIndex" :page-sizes="pageSize"
        :page-size="params.pageSize" layout="total, sizes, prev, pager, next" @current-change="handleCurrentChanges"
        @size-change="handleSizeChanges" :total="totalCGFX">
      </el-pagination>
    </el-dialog>
    <el-dialog title="图纸合理性分析" :visible.sync="rationality" width="50%" center :append-to-body="true">
      <el-table :data="gridDatarationality" :header-cell-style="{ background: 'rgb(160 167 176 / 45%)' }" border>
        <el-table-column property="name" label="技术要点" width="120" align="center"></el-table-column>
        <el-table-column property="ms" label="描述" align="center" width="120">
        </el-table-column>
        <el-table-column property="ist" label="是否合规" align="center" width="120">
        </el-table-column>
        <el-table-column property="wtms" label="问题描述" align="center">
        </el-table-column>
      </el-table>
      <el-pagination style="margin: 10px 0px" background :current-page="params.pageIndex" :page-sizes="pageSize"
        :page-size="params.pageSize" layout="total, sizes, prev, pager, next" @current-change="handleCurrentChanges"
        @size-change="handleSizeChanges" :total="total1">
      </el-pagination>
    </el-dialog>
    <el-dialog title="标准化分析" :visible.sync="standardizationShow" width="50%" center :append-to-body="true">
      <el-table :data="gridDatastandardization" :header-cell-style="{ background: 'rgb(160 167 176 / 45%)' }" border>
        <el-table-column type="index" align="center" :resizable="false" width="50">
        </el-table-column>
        <el-table-column property="name" label="技术要点" align="center">
        </el-table-column>
        <el-table-column  label="指标" align="center">
          <template  slot-scope="scope">
            <span>{{scope.row.zb.substring(0,1)=='.'?'0':scope.row.zb}}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination style="margin: 10px 0px" background :current-page="params.pageIndex" :page-sizes="pageSize"
        :page-size="params.pageSize" layout="total, sizes, prev, pager, next" @current-change="handleCurrentChanges"
        @size-change="handleSizeChanges" :total="total2">
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import { completeView, rationalan, standardization } from "@/api/api"
export default {
  components: {},
  props: {
    ProCode: {
      type: String,
      default: "",
    },
  },
  data () {
    return {
      gridData: [
        {
          name: "成果完整性筛查",
        },
        {
          name: "图纸合理性分析",
        },
        {
          name: "标准化分析",
        },
      ],
      gridDatas: [
      ], //成果完整性筛查
      gridDatarationality: [], //图纸合理性分析
      gridDatastandardization: [
      
      ], //标准化分析
      pageSize: [5, 10, 20, 50, 100], //分页页数
      totalCGFX:'',
      total1:'',
      total2:"",
      q: {
        page: 1,
        limit: 5,
      },
      totals: 1,
      total: 3, //总共页数
      DialogVisible: false, //分析
      rationality: false, //图纸合理性分析
      standardizationShow: false, //标准化分析
      params: {
        taskid: this.ProCode,
        pageIndex: 1,
        pageSize: 5,
      },
    }
  },
  created () {
    this.getHeight()
    window.addEventListener("resize", this.getHeight)
  },
  methods: {
    // ↓在methods里面(窗体大小改变计算表格高度)
    getHeight () {
      this.tableHeight = window.innerHeight - 320
    },
    // table列表序号索引
    indexMethod (index) {
      return (this.params.pageIndex - 1) * 10 + index + 1
    },
    // 查看
    handleView (row) {
      console.log(row.name, "nummmm")
      this.params.taskid = this.ProCode

      if (row.name == "成果完整性筛查") {
        this.DialogVisible = true
        completeView(this.params)
          .then((res) => {
            console.log(res)
            this.gridDatas = res.data.result
            this.totalCGFX = res.data.count
          })
          .catch((err) => {
            console.log(err, "failed")
          })
      } else if (row.name == "图纸合理性分析") {
        this.rationality = true
        rationalan(this.params).then((res) => {
          console.log(res, "图纸合理性分析")
          this.gridDatarationality = res.data.result
          this.total1=res,data.count
        })
      } else {
        this.standardizationShow = true
        standardization(this.params).then((res) => {
          console.log(res)
          this.gridDatastandardization = res.data.result
          this.total2=res.data.count
        })
      }
    },
    handleCurrentChange () { },
    handleSizeChange () { },
    handleCurrentChanges (row) {
      this.params.pageIndex = row
      this.handleView()
    },
    handleSizeChanges (row) {
      this.params.pageSize = row
      this.handleView()
    },
  },
}
</script>

<style scoped="scoped">
.TreeBorder {
  border: 1px solid #cccccc;
  height: 100vh;
  border-radius: 5px;
  overflow: hidden;
}
</style>
