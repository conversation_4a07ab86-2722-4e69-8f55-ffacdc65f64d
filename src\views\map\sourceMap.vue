<template>
  <div>
    <van-action-sheet v-model="isShowAction" :actions="actions" @select="onSelectAction($event)" />
    <div class="map">
      <div class="zuobiao">经度：{{loactionlatlon.lng}} 纬度：{{loactionlatlon.lat}}</div>
      <div class="map-midArea">
        <van-row class="map-rows">
          <div class="map-leftArea">
            <!--地图区域-->
            <div id="dw2map" class="map-area">
              <div class="map-setting mapimg" @click="isShowAction = true"></div>
              <div class="map-removeDrawPoy mapimg" @click="removeDrawPoy"></div>
              <div class="map-endkc mapimg" @click="closeKcGohead">
                <svg-icon icon-class="stopkc" />
              </div>
            </div>
          </div>
        </van-row>
      </div>
      <!--选择icon区域-->
      <div class="map-choseIcon">
        <div class="map-iconleftArea">
          <!--架空未选中添加时候-->
          <div v-if="showChoseIncoIndex !== 1" class="map-levelHead" @click="chooseSeelctType(1)" />
          <!--架空选中添加时候-->
          <div v-if="showChoseIncoIndex === 1" class="map-highlevelHead" @click="chooseMapPoint" />
        </div>
        <div class="map-iconmidArea">
          <!--台区未添加时候-->
          <div v-show="showChoseIncoIndex !== 3" class="map-courts" @click="chooseSeelctType(3)" />
          <!--台区选中添加时候-->
          <div v-show="showChoseIncoIndex === 3" class="map-highcourts" @click="chooseMapPoint" />
        </div>
        <div class="map-iconmidArea">
          <!--电缆未选中添加时候-->
          <div v-show="showChoseIncoIndex !== 2" class="map-cable" @click="chooseSeelctType(2)" />
          <!--电缆选中添加时候-->
          <div v-show="showChoseIncoIndex === 2" class="map-highcable" @click="chooseMapPoint" />
        </div>
        <!--        <div class="map-iconrightArea">-->
        <!--          <div-->
        <!--            v-show="showChoseIncoIndex === 1"-->
        <!--            class="map-courts"-->
        <!--            @click="chooseSeelctType(3)"-->
        <!--          />-->
        <!--          <div-->
        <!--            v-show="showChoseIncoIndex === 2"-->
        <!--            class="map-levelHead"-->
        <!--            @click="chooseSeelctType(1)"-->
        <!--          />-->
        <!--          <div-->
        <!--            v-show="showChoseIncoIndex === 3"-->
        <!--            class="map-cable"-->
        <!--            @click="chooseSeelctType(2)"-->
        <!--          />-->
        <!--        </div>-->
        <div class="map-iconleftArea">
          <!--站内设计-->
          <div v-if="!znsjType" class="map-znsj" @click="znsjChange()" />
          <div v-if="znsjType" class="map-znsj-active" @click="znsjChange()" />
        </div>
      </div>
      <!--踩点区域-->
      <div v-show="selectCheck.isShowCpVis !== 4" class="map-rightArea">
        <div v-show="selectCheck.isShowCpVis === 0" class="map-selectType">
          <!--2022.628修改-->
          <div v-show="selectCheck.isShowNavBar" class="map-selectArea">
            <div v-for="(item, index) in tarbarList" :key="'gomodel' + index" class="map-selectAreaChos" @click="goSetModel(item, index)">
              {{ chooseItems }}
              <div class="map-packdown" @click="hideSelect" />
            </div>
          </div>
          <!--架空线路-->
          <div v-show="chooseItems === '架空线路'" class="pro-addAllArea">
            <!--主线路-->
            <zxlhzMain :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :gt-list="gtTypeList" :dx-list="dxTypeList" :remode-state="currentIsAfter" :show-main-area="selectCheck.objVis.showMainLine"
              :is-edit-line="isIditLine" @submitChildData="subMitDatas" @backCurrentDom="backsAddRrea" />
            <!--分支线路-->
            <fxlhzMain :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :gt-list="gtTypeList" :dx-list="dxTypeList" :remode-state="currentIsAfter" :show-main-area="selectCheck.objVis.showBranchLine"
              @submitChildData="subMitDatas" @backCurrentDom="backsAddRrea" />
            <tgbjhzMain :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :dx-list="dxTypeList" :remode-state="currentIsAfter" :show-main-area="selectCheck.objVis.showleverHead"
              @submitChildData="subMitDatas" @backCurrentDom="backsAddRrea" />
            <dxhz-main :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :dx-list="dxTypeList" :remode-state="currentIsAfter" :show-main-area="selectCheck.objVis.showonductor"
              @submitChildData="subMitDatas" @backCurrentDom="backsAddRrea" />
            <!--低压下户暂时数据不准先屏蔽-->
            <dyxhhz-main :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :remode-state="currentIsAfter" :show-main-area="selectCheck.objVis.showLowDoor" @submitChildData="subMitDatas"
              @backCurrentDom="backsAddRrea" />
            <!--交叉跨越-->
            <jxkyhz-main :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :show-main-area="selectCheck.objVis.showCrossOver" @submitChildData="subMitDatas" @backCurrentDom="backsAddRrea" />
            <!--架空电缆-->
            <jkdl-main :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :dlxl-list="dlxhTypeList" :gt-list="gtTypeList" :remode-state="currentIsAfter" :show-main-area="selectCheck.objVis.showOverLine"
              @submitChildData="subMitDatas" @backCurrentDom="backsAddRrea" />
            <!--拉线-->
            <lxhz-main :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :remode-state="currentIsAfter" :show-main-area="selectCheck.objVis.showStay" @submitChildData="subMitDatas"
              @backCurrentDom="backsAddRrea" />
            <!--柱上变压器-->
            <zsbyq-main :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :gt-list="gtTypeList" :dx-list="dxTypeList" :remode-state="currentIsAfter" :show-main-area="selectCheck.objVis.showTransformer"
              @submitChildData="subMitDatas" @backCurrentDom="backsAddRrea" />
            <!--柱上设备-->
            <zssb-main :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :remode-state="currentIsAfter" :show-main-area="selectCheck.objVis.showColumnEqu" @submitChildData="subMitDatas"
              @backCurrentDom="backsAddRrea" />
          </div>
          <!--台区站房-->
          <div v-show="chooseItems === '台区站房'" class="pro-addAllArea">
            <!--配电站房-->
            <pdzfhz-main :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :tdxh-list="tdxhTypeList" :dlxl-list="dlxhTypeList" :tdlb-list="tdlaying" :zdt-type-list="zdtTypeList"
              :remode-state="currentIsAfter" :zjt-type-list="zjtTypeList" :show-main-area="selectCheck.objVis.showElectrical" @submitChildData="subMitDatas" @backCurrentDom="backsAddRrea" />
            <!--变电站-->
            <bdzfhz-main :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :tdxh-list="tdxhTypeList" :dlxl-list="dlxhTypeList" :tdlb-list="tdlaying" :remode-state="currentIsAfter"
              :zdt-type-list="zdtTypeList" :zjt-type-list="zjtTypeList" :show-main-area="selectCheck.objVis.showChangeCable" @submitChildData="subMitDatas" @backCurrentDom="backsAddRrea" />
          </div>
          <!--电缆线路-->
          <div v-show="chooseItems === '电缆线路'" class="pro-addAllArea">
            <!--电缆线路-->
            <dlxlhz-main :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :tdxh-list="tdxhTypeList" :dlxl-list="dlxhTypeList" :tdlb-list="tdlaying" :remode-state="currentIsAfter"
              :zdt-type-list="zdtTypeList" :zjt-type-list="zjtTypeList" :show-main-area="selectCheck.objVis.showCableLine" @submitChildData="subMitDatas" @backCurrentDom="backsAddRrea" />
            <!--电缆头-->
            <dlthz-main :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :remode-state="currentIsAfter" :zdt-type-list="zdtTypeList" :show-main-area="selectCheck.objVis.showCableHead"
              @submitChildData="subMitDatas" @backCurrentDom="backsAddRrea" />
            <!--电缆保护管-->
            <dlbhghz-main :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :remode-state="currentIsAfter" :show-main-area="selectCheck.objVis.showCableTube" @submitChildData="subMitDatas"
              @backCurrentDom="backsAddRrea" />
            <!--电缆分支箱-->
            <dlfzxhzMain :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :remode-state="currentIsAfter" :show-main-area="selectCheck.objVis.showCableBranch" @submitChildData="subMitDatas"
              @backCurrentDom="backsAddRrea" />
            <!--土建路径类别-->
            <dltdhzMain :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :remode-state="currentIsAfter" :tdlb-list="tdlaying" :tdxh-list="tdxhTypeList"
              :show-main-area="selectCheck.objVis.showCableChannels" @submitChildData="subMitDatas" @backCurrentDom="backsAddRrea" />
            <!--电缆井-->
            <dljhzMain :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :tdlb-list="tdlaying" :remode-state="currentIsAfter" :tdxh-list="tdxhTypeList" :show-main-area="selectCheck.objVis.showCablePit"
              @submitChildData="subMitDatas" @backCurrentDom="backsAddRrea" />
            <!--电缆线路-->
            <sbljhzMain :show-back-adds="isShowBackAdds" :is-show-nav="addParam.isShowNav" :edit-data="editData" :tdxh-list="tdxhTypeList" :dlxl-list="dlxhTypeList" :tdlb-list="tdlaying" :remode-state="currentIsAfter"
              :zdt-type-list="zdtTypeList" :zjt-type-list="zjtTypeList" :show-main-area="selectCheck.objVis.showEquipLine" @submitChildData="subMitDatas" @backCurrentDom="backsAddRrea" />
          </div>
        </div>
        <!--选择杆号重排功能-->
        <div v-show="selectCheck.isShowCpVis === 1" class="map-selectType">
          <van-nav-bar title="请选择杆号重排" />
          <div>
            <div class="pro-addAllArea">
              <div class="pro-addEveryArea">
                <van-row>
                  <van-field v-model="addParam.numeral.startPrefix" label="前缀" placeholder="请输入杆塔前缀" />
                </van-row>
                <van-row>
                  <van-field v-model="addParam.numeral.number" label="编号" placeholder="请输入编号" />
                </van-row>
                <van-row>
                  <van-field v-model="addParam.numeral.endPrefix" label="后缀" placeholder="请输入杆塔后缀" />
                </van-row>
              </div>
              <div class="pro-setRageBtn">
                <button class="pro-cancle" @click="closeNumeral">取消</button>
                <button class="pro-confirm" @click="submitNumeral">确认</button>
              </div>
            </div>
          </div>
        </div>
        <!--排杆功能-->
        <div v-show="selectCheck.isShowCpVis === 2" class="map-rankgt">
          <van-nav-bar title="插入杆塔">
            <template #right>
              <van-icon name="cross" size="18" @click="closeRangeGt" />
            </template>
          </van-nav-bar>
          <div class="map-rangegt">
            <van-row>
              <van-field readonly clickable :value="addParam.rangeGt.batchType" label="插入方式" placeholder="选择插入方式" @click="settingObj.rangeGt.batchType = true" />
              <van-popup v-model="settingObj.rangeGt.batchType" round position="bottom">
                <van-picker show-toolbar title="插入方式" value-key="key" :columns="settingObj.batchTypeList" @confirm="onConfirmRaneg(4, '', $event)" @cancel="settingObj.rangeGt.batchType = false" />
              </van-popup>
            </van-row>
            <van-row>
              <van-field readonly clickable :value="addParam.rangeGt.voltage" label="电压等级" placeholder="选择电压等级" @click="settingObj.rangeGt.voltage = true" />
              <van-popup v-model="settingObj.rangeGt.voltage" round position="bottom">
                <van-picker show-toolbar title="电压等级" value-key="key" :columns="settingObj.allVoltage" @confirm="onConfirmRaneg(0, '', $event)" @cancel="settingObj.rangeGt.voltage = false" />
              </van-popup>
            </van-row>
            <van-row>
              <van-field readonly clickable :value="addParam.rangeGt.legendTypeKey" label="杆塔类型" placeholder="选择杆塔类型" @click="settingObj.rangeGt.legendTypeKey = true" />
              <van-popup v-model="settingObj.rangeGt.legendTypeKey" round position="bottom">
                <van-picker show-toolbar title="杆塔类型" value-key="key" :columns="settingObj.towerType" @confirm="onConfirmRaneg(1, '', $event)" @cancel="settingObj.rangeGt.legendTypeKey = false" />
              </van-popup>
            </van-row>
            <van-row>
              <van-field readonly clickable :value="addParam.rangeGt.moudle" label="杆塔型号" placeholder="选择杆塔型号" @click="settingObj.rangeGt.moudle = true" />
              <van-popup v-model="settingObj.rangeGt.moudle" round position="bottom">
                <van-picker title="杆塔型号" show-toolbar value-key="name" :columns="settingObj.rangeGtMoudle" @confirm="onConfirmRaneg(2, '', $event)" @cancel="settingObj.rangeGt.moudle = false" />
              </van-popup>
            </van-row>
            <van-row>
              <van-field readonly clickable :value="addParam.rangeGt.state" label="杆塔状态" placeholder="选择状态" @click="settingObj.rangeGt.state = true" />
              <van-popup v-model="settingObj.rangeGt.state" round class-name="mapsPopup" position="bottom">
                <van-picker show-toolbar title="主线路状态" value-key="key" :columns="settingObj.mainLineState" @confirm="onConfirmRaneg(3, '', $event)" @cancel="settingObj.rangeGt.state = false" />
              </van-popup>
            </van-row>
            <van-row>
              <!--距前档距-->
              <div v-if="changeRadioObj.isShowZy">
                <van-field v-model="addParam.rangeGt.pointNum" label="距前档距" type="number" placeholder="请输入距前档距(m)" />
              </div>
              <!--杆塔间距-->
              <div v-if="changeRadioObj.isShowDf">
                <van-field v-model="addParam.rangeGt.pointNum" label="杆塔数量" type="number" placeholder="请输入杆塔数量" />
              </div>
              <!--杆塔数量-->
              <div v-if="changeRadioObj.isShowDj">
                <van-field v-model="addParam.rangeGt.pointNum" label="杆塔间距" type="number" placeholder="请输入杆塔间距" />
              </div>
            </van-row>
            <div class="pro-setRageBtn">
              <button class="pro-cancle" @click="closeRangeGt">取消</button>
              <button class="pro-confirm" @click="submitRange">确认</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog top="3vh" width="80vw" title="参数设置" :append-to-body="true" :visible.sync="projectParmSet">
      <projectSetting @saveSuccess="savePramas" />
    </el-dialog>
    <el-dialog top="3vh" width="80vw" title="间隔设计" :append-to-body="true" :visible.sync="jgsjVisible">
      <jgsjSetting :searchId="projectSearchId" v-if="jgsjVisible" />
    </el-dialog>
    <el-dialog top="3vh" width="60%" title="电气一次设计" :append-to-body="true" :visible.sync="dqycVisible">
      <dqycsjSetting :pointId="ciclkPointId" v-if="dqycVisible" />
    </el-dialog>
    <el-dialog top="15vh" width="30%" title="方案选择" :append-to-body="true" :visible.sync="fyzdyfaVisible">
      <fyzdyfaSetting :pointId="ciclkPointId" />
    </el-dialog>
    <el-dialog top="15vh" width="80%" title="平面布置图设计" :append-to-body="true" :visible.sync="pmpzhtsjVisible">
      <pmbzhtSetting :pllx="pmbztType" />
    </el-dialog>
    <el-dialog top="15vh" width="80%" title="设备台账" :append-to-body="true" :visible.sync="sbtzVisible">
      <sbtzSetting />
    </el-dialog>

  </div>
</template>
<script>
var highlightLayerPoint; // 图层所有点的高亮图层
var highlightMapsLayer; // 地图上绘制中心，距离的图层
var graphiclineLayer = null; // 绘制辅助线的
var polylineDistance = null; // 计算辅助线距离声明的矢量化线变量
import dw2gis from "dw2gis";
import "dw2gis/style.css";
import Vue from "vue";
import { apiget, apipost } from "@/utils/mapRequest";
import {
  gettoken,
  getlogin

} from "@/api/api.js";
import $ from "jquery";
import mapData from "@/utils/global";
import { Toast, Dialog } from "vant";
import {
  Point,
  Circle,
  getPoints,
  getInterect,
  getEndpoint,
} from "@/constryctor/maps";
import zxlhzMain from "@/views/map/zxlhzMain/index"; // 主线路绘制模块
import fxlhzMain from "@/views/map/fxlhzMain/index"; // 分支线路绘制模块
import tgbjhzMain from "@/views/map/tgbjhzMain/index"; // 同杆并架绘制模块
import dxhzMain from "@/views/map/dxhzMain/index"; // 导线绘制模块
import jxkyhzMain from "@/views/map/jckyhzMain/index"; // 交叉跨越绘制模块
import jkdlMain from "@/views/map/jkdlMain/index"; // 交叉跨越绘制模块
import lxhzMain from "@/views/map/lxhzMain/index"; // 拉线绘制模块
import zsbyqMain from "@/views/map/zsbyqHzMain/index"; // 柱上变压器绘制模块
import zssbMain from "@/views/map/zssbMain/index"; // 柱上设备绘制模块
import pdzfhzMain from "@/views/map/pdzfhzMain/index"; // 配电站房绘制模块
import bdzfhzMain from "@/views/map/bdzhzMain/index"; // 配电站房绘制模块
import dlxlhzMain from "@/views/map/dlxlhzMain/index"; // 电缆线路绘制模块
import sbljhzMain from "@/views/map/sbljhz/index"; // 电缆线路绘制模块
import dlthzMain from "@/views/map/dlthzMain/index"; // 电缆头绘制模块
import dlbhghzMain from "@/views/map/dlbhghzMain/index"; // 电缆保护管绘制模块
import dlfzxhzMain from "@/views/map/dlfzxhzMain/index"; // 电缆保护管绘制模块
import dltdhzMain from "@/views/map/dltdhzMain/index"; // 土建路径绘制模块
import dljhzMain from "@/views/map/dljhzMain/index"; // 电缆井绘制模块
import dyxhhzMain from "@/views/map/dyxhMain/index"; // 电缆井绘制模块

// 工程参数设置
import projectSetting from "@/views/map/projectSetting/index.vue";
// 间隔设计
import jgsjSetting from "@/views/map/pdzfComponents/jgsj/index.vue";
// 电气一次图设计间隔设计
import dqycsjSetting from "@/views/map/pdzfComponents/dqycsj/index.vue";
// 电气一次图复用自定义方案
import fyzdyfaSetting from "@/views/map/pdzfComponents/fyzdyfa/index.vue";
// 平面配置图设计
import pmbzhtSetting from "@/views/map/pdzfComponents/pmbztsj/index.vue";
import sbtzSetting from "@/views/map/pdzfComponents/sbtz/index.vue";
import { map } from "jquery";
Vue.use(Toast).use(Dialog);
export default {
  components: {
    zxlhzMain,
    fxlhzMain,
    tgbjhzMain,
    dxhzMain,
    jxkyhzMain,
    jkdlMain,
    lxhzMain,
    zsbyqMain,
    zssbMain,
    pdzfhzMain,
    bdzfhzMain,
    dlxlhzMain,
    sbljhzMain,
    dlthzMain,
    dlbhghzMain,
    dlfzxhzMain,
    dltdhzMain,
    dljhzMain,
    dyxhhzMain,
    projectSetting,
    jgsjSetting,
    dqycsjSetting,
    fyzdyfaSetting,
    pmbzhtSetting,
    sbtzSetting,
  },
  data() {
    return {
      loactionlatlon:{
        lat:'',
        lng:''
      },
      lontA:[],
      znsjType: false,
      allRangeArry: [], // 存放所有的矩形的坐标系
      isGetProjectXml: false, // 是否沿布现状
      isChoosePdzfIcon: false, // 是否点了地图上得选择配电站房得icon 电气一次化
      isChoosePdzfIconByPmbzt: false, // 是否点了地图上得平面布置图设计
      projectParmSet: false,
      jgsjVisible: false, // 间隔设计的dialog
      dqycVisible: false, // 电气一次设计
      fyzdyfaVisible: false, // 复用自定义方案
      pmpzhtsjVisible: false, // 平面布置图设计
      sbtzVisible: false, // 工程的设备台账
      pmbztType: -1,
      projectSearchId: "", // 间隔设计等页面需要的工程id
      onlineIntomapsType: "", // 从在线设计传进来的类型
      userId: "",
      isShowBackAdds: false,
      assistInfo: {
        isShowAssist: false,
        assistActions: [
          { name: "辅助档距" },
          { name: "坐标" },
          { name: "辅助档距/坐标" },
        ],
        result: "辅助档距",
      },
      showMarkInfo: {
        isShowAssist: false,
        assistActions: [
          { name: "编号/自动选型" },
          { name: "导线型号" },
          { name: "全部隐藏" },
        ],
        result: "辅助档距",
      },
      isShowRange: false, // 是否展示批量踩点
      isReformMark: false, // 是否dw2gismounted给编号自动加1 如果是手动启用的情况下不加 自动踩点时候加1
      changeRadioObj: {
        isShowZy: true,
        isShowDf: false,
        isShowDj: false,
      },
      imgList: [],
      cpGtform: {
        name: "",
        number: "",
      }, // 杆号重排参数
      tarbarList: ["架空线路", "台区站房", "电缆线路"],
      chooseItems: "架空线路",
      editData: {},
      isIditLine: false, // 是否编辑主线路和分支线路得线id
      isShowAction: false, // 是否显示下拉面板
      isAddDifLine: false, // 是否点线在线上新增点
      isAddPointUnAddLine: false, // 是否是新增点不点击线
      isClickDlj: false, // 是否点击了电缆井
      isClickLines: false, // 是否点击了线
      isShowDistance: false, // 是否显示线距离
      isShowAutoMark: false, // 是否显示线距离
      isShowSatellite: false, // 是否显示卫星定位/拖动
      currentIsAfter: true, // false改前  true改后
      isInitMap: true,
      mapTimer: null, // 连续定位的定时器
      isInitimgAndAduio: {
        mainline: false,
      },
      mapStyle: "aegis://styles/aegis/Satellite512",
      isEditPointParam: {
        id: "",
        lng: "",
        lat: "",
      }, // 点位更新时候的参数
      isEditLineParam: {
        startId: "", // 开始点id
        endId: "", // 结束
        moudleType: "",
      },
      isFirtLoadData: true,
      initMapLngLat: {
        x: 0,
        y: 0,
      },
      mapAllLineList: [], // 所有线数组
      // actions: [
      //   { name: "全部删除" },
      //   { name: "杆号重排" },
      //   { name: "更换地图风格" },
      // ],
      actions: [
        { name: "全部删除" },
        { name: '切换矢量地图' },
        { name: '切换影像地图' }
      ],
      saveLine: {
        isSaveLine: false,
        lonA: "",
        lat: "",
        id: "",
      },
      subMitData: {}, // 连续踩点的参数
      subMitDataVis: {}, // 连续踩点的下拉框的true和false值
      isChooseAfterPoint: false, // 是否是选择起始点后接着踩点 控制在覆盖物上是否继续踩点
      allPointList: [], // 所有的点
      allSourecePoint: [], // 所有现状的点
      allSjPoint: [], // 所有设计的点
      allSourceLine: [], // 所有现状的线
      allsjLine: [], // 所有现状的线
      allGtPointList: [], // 所有杆塔的点
      saveAnglePoint: [], // 存放带角度的点(拉线和附属设施)
      submitType: -1, // 提交时候得类型
      startPointId: {
        startId: "", // 开始点id
        endId: "", // 结束点id
      },
      startPointTime: {
        startTime: "", // 开始点时间
        endTime: "", // 结束点时间
      },
      isGoheadPoint: false, // 是否继续采点  控制是否在地图上继续踩点
      ciclkPointId: "", // 点击的点id
      isAddMarkers: false, // 是否点了覆盖物了
      isChooseStartGT: false, // 是否选择了起始杆后继续踩点
      isAddMarkerInit: false, // 是否是新增一个杆塔点后去调用的查询点位接口  需要更新上一个点的状态
      lastPointId: "", // 打点时候上个点得id
      lastPointLngAndLat: [], // 最后一个点得经纬度
      lineimagesList: [], // 存放线图片id得数组
      drawlineIdList: [], // 存放线图片id得数组
      poIntAllIdList: [], // 存放所有得点id数组
      startPoint: {
        x: 0,
        y: 0,
      },
      currentMapPoint: {
        x: 0,
        y: 0,
      },
      options: {
        enableHighAccuracy: true, // 是否使用 GPS
        maximumAge: 300000, // 缓存时间
        timeout: 270000, // 超时时间
        coorType: "gcj02", // 默认是 gcj02，可填 bd09ll 以获取百度经纬度用于访问百度 API
      },
      // 选型采集类型页面
      selectCheck: {
        isShowNavBar: true, // 显示添加页导航栏
        radio: "",
        activeNames: [],
        visible: false, // 选型采集页面是否展示
        isShowCpVis: 4, // 重排编号的页面
        title: "架空线路设置",
        objVis: {
          showMainLine: true, // 主线路
          showBranchLine: true, // 分支线路
          showleverHead: true, // 同杆并架
          showonductor: true, // 导线
          showOpticCable: true, // 光缆
          showLowDoor: true, // 低压下户
          showCrossOver: true, // 交叉跨域
          showOverLine: true, // 架空电缆
          showTransformer: true, // 柱上变压器
          showColumnEqu: true, // 柱上设备
          showStay: true, // 拉线
          showFacilities: true, // 附属设备
          showCableLine: true, // 电缆线路
          showEquipLine: true, // 电缆线路
          showCableHead: true, // 电缆头
          showCableTube: true, // 电缆保护管
          showCableBranch: true, // 电缆分支箱
          showCableChannels: true, // 土建路径
          showCablePit: true, // 电缆井
          showElectrical: true, // 配电站房
          showChangeCable: true, // 变电站
        },
      },
      gtTypeList: [], // 杆塔型号，包含(主线路杆塔，分支线路，架空杆塔)
      dxTypeList: [], // 导线型号，包含(主线路，分支线路，导线模块，同杆并架)
      tdxhTypeList: [], // 通道型号，包含(配电站房通道型号，电缆线路通道型号，变电站通道型号，土建路径通道型号)
      dlxhTypeList: [], // 电缆型号(架空电缆电缆线路, 电缆线路电缆型号, 配电站房电缆型号, 变电站电缆型号)
      tdlaying: [], // 通道类别(配电站房通道类别,电缆线路通道类别, 土建路径通道类别, 变电站通道类别)
      zjtTypeList: [], // 中间头型号(配电站房中间头型号,电缆线路中间头型号，变电站中间头型号 )
      zdtTypeList: [], // 中间头型号(配电站房中间头型号,电缆线路中间头型号,变电站中间头型号,电缆头终端头型号 )
      // 选型设置参数
      settingObj: {
        visible: false,
        addAreaVis: false,
        isDisabled: false,
        isShowDrawRaw: false, // 架空电缆线路时候需要判断
        isDisabledDraw: false, // 禁用绘制设置
        rangeGt: {
          voltage: false,
          state: false,
          moudle: false,
          legendTypeKey: false,
          batchType: false,
        },
        allVoltage: [
          {
            key: "10kV",
            value: "10kV",
          },
          {
            key: "380V",
            value: "380V",
          },
          {
            key: "220V",
            value: "220V",
          },
        ], // 电压等级
        mainLineState: [
          {
            key: "新建",
            value: "新建",
          },
          {
            key: "原有",
            value: "原有",
          },
          {
            key: "改造",
            value: "改造",
          },
          {
            key: "拆除",
            value: "拆除",
          },
        ], // 电缆线路状态
        batchTypeList: [
          {
            key: "自由插入",
            value: "自由插入",
          },
          {
            key: "等分插入",
            value: "等分插入",
          },
          {
            key: "等距插入",
            value: "等距插入",
          },
        ],
        towerType: [
          {
            key: "水泥杆",
            value: "水泥杆",
            type: "SNG",
          },
          {
            key: "水泥双杆",
            value: "水泥双杆",
            type: "SNSG",
          },
          {
            key: "钢管杆",
            value: "钢管杆",
            type: "GGG",
          },
          {
            key: "窄基塔",
            value: "窄基塔",
            type: "ZJT",
          },
          {
            key: "低压水泥杆",
            value: "低压水泥杆",
            type: "DYSNG",
          },
        ], // 杆塔类型
        rangeGtMoudle: [], // 排杆时候的杆塔型号
      },
      showAddItemsType: "", // 打开绘图页得类型
      addParam: {
        lngtitude: "", // 每个点绑定的经度
        lattitude: "", // 每个点绑定的纬度
        highNum: "", // 高程
        isShowNav: false,
        isShowStartlnglat: false,
        numeral: {
          startPrefix: "", // 编号前缀
          number: "", // 编号
          endPrefix: "", // 编号后缀
        },
        rangeGt: {
          projectId: this.$route.query.childProjectId,
          batchType: "自由插入", // 类型 0:等距插入 1:等分插入2：自由插入",
          voltage: "10kV", // 电压
          state: "新建", // 点状态
          legendTypeKey: "", // 点类型
          moudle: "", // 杆塔型号
          moduleId: "", // 型号id
          pointNum: "", // 档距、间距、数量
          lineId: "",
        },
      },
      basicSettingData: {
        plArray: [], // 排列的数组
        nzLength: 0, // 耐张段长度
        qxType: "", // 气象区类型
      },
      showChoseIncoIndex: -1, //1是架空 2是台区 3是电缆
      isShowIconIndex: false,
      clickLines: {},
      drawLXHZStartPoint: {
        lng: 0,
        lat: 0,
      },
      isLoading: false,
      directive: {
        // 这里初始化的样式和地图层级缩放方法setMapZoom里面判断当前地图初始化的层级里面的样式一致（地图默认是14层）
        mapMarker: "width: 1.5rem; height: 1.5rem", // 初始化的mapMarker类样式内容
        mapDlhzMarker: "width: 0.8rem; height: 0.8rem", // 初始化mapDlhzMarker类样式内容
        movePointShowother: "fontSize: 0.8rem; left: 3rem", // 初始化movePointShowother类样式内容
        mapDlhzgdMarker: "width: 1rem; height: 1rem", // 初始化mapDlhzgdMarker类样式内容
        dlxxj: "0.8rem", //
        zssbxj: "width: 0.8rem; height: 0.8rem", //
        mapsZssbMarker: "width: 1rem; height: 1rem; left: 1rem; top: -1rem",
        mapPdzfMarker: "width: 1rem; height: 1rem",
        mapdLJMarker: "width: 1rem; height: 1rem",
        markerPointLine: "fontsize: 0.6rem; display: none",
        markerEveryLines: "fontsize: 0.6rem; display: none",
        movePointShowFSSS: "fontsize: 0.6rem;display: none",
        movePointShowZSSBHZ: "fontsize: 0.6rem; display: none",
        movePointShowAutoLine: "fontsize: 0.8rem;top:-1rem;display: none",
        movePointShowMark: "fontsize: 0.8rem;top: -2rem; display: none",
      },
      // 矢量化平台新增的参数
      mapsDistance: '0',
      mapsAllLinesPoly: [], // 存放所有线坐标声明的变量
      mapsAllLinesGraphic: [], // 存放所有的线声明的标记物
      mapsAllPointsGraphic: [], // 存放所有的点声明的标记物
      mapsAllIdentifyGraphic: [], // 存放所有的拆除 新建220v 330v的标记
      mapsAllIdentifyIDS: [], // 存放所有的拆除 新建220v 330v的标记的id
      mapsAllLineMarkersIDS: [], // 存放所有的沿线标注物的id
      mapsAllLineMarkers: [], // 存放所有的沿线标注物
      mapsAllRanges: [] // 存放格式刷里面的所有绘制矩形
    };
  },
  watch: {
    $route: {
      handler: function (val) {
        if (val.query.mapsType !== -1) {
          switch (val.query.mapsType) {
            // 插入图框
            case "1":
              {
                let imgWidth = 0;
                let imgHigh = 0;
                switch (val.query.imgSize) {
                  case "1":
                    imgWidth = 841;
                    imgHigh = 1189;
                    break;
                  case "2":
                    imgWidth = 594;
                    imgHigh = 841;
                    break;
                  case "3":
                    imgWidth = 594;
                    imgHigh = 420;
                    break;
                  case "4":
                    imgWidth = 420;
                    imgHigh = 297;
                    break;
                  case "5":
                    imgWidth = 210;
                    imgHigh = 297;
                    break;
                  case "6":
                    imgWidth = 297;
                    imgHigh = 210;
                    break;
                }
                let mapLngLat = mapData.map.getCenter();
                let transStartlnglatZssb = mapData.map.project([
                  mapLngLat.lng,
                  mapLngLat.lat,
                ]);
                // transStartlnglatZssb.x代表经度 transStartlnglatZssb.y代表纬度
                // 矩形右下的平面坐标系坐标
                let leftBotPointRight_lng = transStartlnglatZssb.x + imgWidth;
                // 矩形左上的的平面坐标系坐标
                let lefttopPointLeft_lat = transStartlnglatZssb.y - imgHigh;
                let left_bot_Point = mapLngLat;
                let right_bot_Point = mapData.map.unproject([
                  leftBotPointRight_lng,
                  transStartlnglatZssb.y,
                ]);
                let left_top_Point = mapData.map.unproject([
                  transStartlnglatZssb.x,
                  lefttopPointLeft_lat,
                ]);
                let right_top_Point = mapData.map.unproject([
                  leftBotPointRight_lng,
                  lefttopPointLeft_lat,
                ]);
                $(".map-removeDrawPoy").show();
                if (mapData.drawPolygon) {
                  mapData.drawPolygon.remove();
                  mapData.drawPolygon = null;
                }
                // 定义矩形的坐标范围
                var bounds = [[right_top_Point.lng, right_top_Point.lat], [left_bot_Point.lng, left_bot_Point.lat]];
                // 创建orange色的矩形
                const mapsRange = L.path.rectangle(bounds, { color: "#ff7800", weight: 2, fill: false }).addTo(mapData.map);
                // 将地图缩放至矩形
                mapData.map.fitBounds(bounds);
                this.$forceUpdate();
                // window.parent.postMessage('hideIframe', '*')
              }
              break;
            case "3":
              this.jgsjVisible = true;
              window.parent.postMessage("hideIframe", "*");
              break;
            case "4":
              this.isChoosePdzfIconByPmbzt = false;
              this.isChoosePdzfIcon = false;
              this.choosePmsjt();
              window.parent.postMessage("hideIframe", "*");
              break;
            case "5":
              this.isChoosePdzfIconByPmbzt = false;
              this.isChoosePdzfIcon = false;
              this.znsjChange();
              window.parent.postMessage("hideIframe", "*");
              break;
            case "6":
              switch (val.query.mapZoom) {
                case "1":
                  // 地图层级18级别
                  mapData.map.setZoom(18);
                  this.setMapZoom(18);
                  break;
                case "2":
                  // 地图层级17级别
                  mapData.map.setZoom(17);
                  this.setMapZoom(17);
                  break;
                case "3":
                  // 地图层级16级别
                  mapData.map.setZoom(16);
                  this.setMapZoom(16);
                  break;
                case "4":
                  // 地图层级15级别
                  mapData.map.setZoom(15);
                  this.setMapZoom(15);
                  break;
                case "5":
                  // 地图层级14级别
                  mapData.map.setZoom(14);
                  this.setMapZoom(14);
                  break;
              }
              window.parent.postMessage("hideIframe", "*");
              break;
            case "7":
              Toast.success("请选择参考设备");
              const that = this
              startDraw()
              function startDraw() {
                mapData.map.drawTool.setDrawRectTool({
                  showArea: true,
                  drawStopTooltip: "点击结束绘制",
                  onDrawEnd: function (e) {
                    var graphic = e.graphic;
                    graphic.symbol = L.simpleLineSymbol({ style: 'solid', stroke: true, weight: 1, color: '#00FF00', fill: false, opacity: 1 });
                    const range = graphic.addTo(highlightMapsLayer);
                    that.mapsAllRanges.push(range)
                    if (that.mapsAllRanges.length == 1) {
                      Toast.success("请选择对比区域");
                      startDraw()
                    } else {
                      Toast.success("格式刷成功");
                      setTimeout(() => {
                        for (let j = 0; j < that.mapsAllRanges.length; j++) {
                          that.mapsAllRanges[j].remove()
                        }
                        that.mapsAllRanges = []
                        window.parent.postMessage("hideIframe", "*");
                      }, 2000);
                    }
                  },
                });
              }
              break;
            case "8":
              {
                const param = {
                  name: "杆号重排",
                };
                this.onSelectAction(param);
              }
              break;
            case "9":
              {
                if (!this.isGetProjectXml) {
                  this.isFirtLoadData = true;
                  this.isGetProjectXml = true;
                  let params = { taskid: this.$route.query.childProjectId }
                  apipost(`/mapModel/addMapState`, params).then((res) => {
                    this.initMap(1);
                  });
                } else {
                  Toast.loading({
                    message: "现状数据沿布中...",
                    forbidClick: true,
                    duration: 2000,
                  });
                  mapData.map.setCenterPoint([
                    this.initMapLngLat.x,
                    this.initMapLngLat.y,
                  ]);
                }
                window.parent.postMessage("hideIframe", "*");
              }
              break;
            case "10":
              this.sbtzVisible = true;
              window.parent.postMessage("hideIframe", "*");
              break;
          }
        }
      },
      // 深度观察监听
      deep: true,
    },
    // 电缆拐点下自动生成终端头的时候会在后端删除一个电缆间的中间头
    // 这里需要监听去删除下 更新地图上的图元
    poIntAllIdList: {
      handler: function (newVal) {
        const differentId = this.poIntAllIdList.find((info) => {
          return this.allPointList.every((item) => item.pointId !== info);
        });
        if (differentId !== undefined) {
          $("#" + differentId).remove();
        }
      },
      deep: true,
    }
  },
  mounted() {
    const that = this;
    this.userId = "cf3f4ab8-eedd-478d-b73d-d46dfe15b334";
    // 动态创建的dom需要使用
    window.hidePop = this.hidePop;
    window.showDetails = this.showDetails;
    window.removeLines = this.removeLines;
    window.showLineDetails = this.showLineDetails;
    window.removePoint = this.removePoint;
    window.removeLXpoint = this.removeLXpoint;
    window.showPmbztsj = this.showPmbztsj;
    window.rankGt = this.rankGt;
    this.getBasicSetting();
    this.getProjcetSetting();
    this.projectSearchId = this.$route.query.childProjectId;
    this.onlineIntomapsType = this.$route.query.mapsType;
    this.getFirstTowerOrLineType(0, 1, 1, "", "", "", "", "10kV");
    // // 查询导线型号类型
    this.getFirstTowerOrLineType(1, 2, 1, "", "", "", "", "10kV");
    /* 配电站房中间头型号*/
    this.getFirstTowerOrLineType(13, "", 4, "", "DLZJT", "", "", "10kV");
    /* 配电站房终端头型号*/
    this.getFirstTowerOrLineType(14, "", 4, "", "DLZDT", "", "", "10kV");
    // /* 电缆型号*/
    this.getFirstTowerOrLineType(19, 4, 1, "", "", "", "", "10kV");
    /* 查询配电站房通道型号 */
    this.getFirstTowerOrLineType(23, "", 3, "", "", "DLTD", "", "");
    this.initSlhMapOption();
  },
  methods: {
    /**
     * @param 初始化适量化平台
     */
    initSlhMapOption() {
      const that = this;
      const urls = window.wgParameter.publicUrl
      // 获取权限接口
      gettoken().then((response) => {
        window.localStorage.setItem("a", response.data.a);
        window.localStorage.setItem("r", response.data.r);
        window.localStorage.setItem("u", response.data.u);
        let params = {
          user: "pwszhyjybj", //xxxxx为权限账号
          pwd: sm2.doEncrypt("Szhyjybj@123456", response.data.u), //Xxxxx@123456为密码
          rand: response.data.a,
        };
        getlogin(params).then((response) => {
          if (response.code == 200) {
            window.localStorage.setItem("is_encode", response.data.data.is_encode);
          }
          // 初始化矢量平台地图
          mapData.map = L.map("dw2map", {
            crs: new L.CRS.EPSG4326({ resolution: 180 / 256 }),
            center: L.mapPoint({ x: 118.828125, y: 36.41693115234375 }),
            zoom: 16,
            minZoom: 0,
            maxZoom: 20,
            attributionControl: false,
            zoomControl: true,
            doubleClickZoom: false,
          });
          var sdvecurl =
            "http://25.41.42.74:18091/api/oss/SLPT/TILE/TIANDITU/SD/2021/VEC/{z}/{y}/{x}?auth=" +
            window.localStorage.getItem("a") +
            "&suid=pwszhyjybj"; //xxxxx为权限账号
          var sdvectorLayer = L.tileLayer(sdvecurl, {
            minZoom: 0,
            maxNativeZoom: 17,
            maxZoom: 21,
            tileSize: 256,
            zoomOffset: 1,
          });
          // 矢量地图默认先添加到地图上;
          var vectorLayer = L.layerGroup([sdvectorLayer]).addTo(mapData.map);
          that.initMasCenterPointAndLine()
        })
      })
    },
    /**
     * 初始化地图中心点及与上个点的距离
     */
    initMasCenterPointAndLine() {
      const that = this
      // 添加地图上辅助中心点，距离文字，辅助线
      highlightMapsLayer = L.graphicsLayer({
        id: "highlightMapsLayer",
        zIndex: false, // 隐藏高亮图层
        pane: "overlayPane",
        minZoom: 0,
        maxZoom: 20,
      }).addTo(mapData.map);
      // 中心点和距离文字的配置项
      var symbol = L.pictureMarkerSymbol({
        image: "./images/define_green.png",
        width: 25,
        height: 25,
        offset: [0, 0],
        opacity: 1,
      });
      let mapPoint = L.mapPoint(118.828125, 36.41693115234375);
      // 弹框中移动点
      mapData.movePointDOM = L.graphic(mapPoint, symbol).addTo(highlightMapsLayer);
      // 弹框移动点绑定显示的距离
      mapData.movePointDOM.bindTooltip(that.mapsDistance, {
        offset: [8, -25],
        permanent: true, // 永久打开弹出框
        className: "directiveDisTance", // 自定义的显示文字的弹框
      }).openTooltip();
      mapData.map.on("zoom", function (e) {
        bindEvents();
        const isShowAuxiliary = sessionStorage.getItem("isShowAuxiliary");
        if (isShowAuxiliary === "1") that.updateGuide();
      });
      mapData.map.on("move", function (e) {
        bindEvents();
        const isShowAuxiliary = sessionStorage.getItem("isShowAuxiliary");
        if (isShowAuxiliary === "1") that.updateGuide();
      });
      mapData.map.on("zoomend", function (e) {
        const mapsZoom = mapData.map.getZoom()
        that.setMapZoom(mapsZoom)
        that.setLxPointStyle()
      });
      mapData.map.on("mousemove", function (e) {
        that.loactionlatlon={
          lat:e.latlng.lat,
          lng:e.latlng.lng
        }

      });
      function bindEvents() {
        mapData.movePointDOM.unbindTooltip();
        // 动态更新中心点
        const mapCenter = mapData.map.getCenter();
        mapData.movePointDOM.geometry.x = mapCenter.lng;
        mapData.movePointDOM.geometry.y = mapCenter.lat;
        mapData.movePointDOM.redraw();
        // 移动过程中需要更新弹框位置
        mapData.movePointDOM
          .bindTooltip(that.mapsDistance, {
            offset: [8, -25],
            permanent: true, // 永久打开弹出框
            className: "directiveDisTance", // 自定义的显示文字的弹框
          })
          .openTooltip();
      }
      const param = {
        projectId: that.$route.query.childProjectId,
      }
      let paramsQM = { taskid: that.$route.query.childProjectId }
      // LDA 接口改造
      apipost("/t-dtf-app-point/selectAllPointOne", param).then(res => {
        console.log(res);
        if (res.data.length == 0) {
          mapData.map.setCenterPoint([118.828125, 36.41693115234375])
        } else {
          mapData.map.setCenterPoint([Number(res.data[0].longitude), Number(res.data[0].latitude)])
        }
        // 先判断是不是获取过运行态，如果获取过运行态，直接获取
        apipost(`/mapModel/queryMapState`, paramsQM).then((res) => {
          Toast.loading({
            message: "数据解析沿布中...",
            forbidClick: true,
            duration: 0,
          });
          if (res.result == '0') {
            console.log('res.result == 0', res.result == '0')
            setTimeout(() => {
              that.initMap(0)
            }, 2000)
          } else {
            // 如果手动获取过了 这里获取现状和设计态两种数据
            setTimeout(() => {
              that.initMap(1)
              // LDA 去掉 that.initMap(0)

            }, 2000)

          }
        })
      })
    },
    setMapZoom(val) {
      const that = this;
      that.setLineStyle(val)
      that.setPointsStyle(val)
    },
    /**
     * 计算当前点与正北方向的弧度
     * @params start 开始点经纬度
     * @params end 结束点经纬度
     */
    bearing(start, end) {
      const rad = Math.PI / 180;
      const lat1 = start.latitude * rad;
      const lat2 = end.latitude * rad;
      const lon1 = start.longitude * rad;
      const lon2 = end.longitude * rad;
      const a = Math.sin(lon2 - lon1) * Math.cos(lat2);
      const b =
        Math.cos(lat1) * Math.sin(lat2) -
        Math.sin(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1);
      // 计算的与正北的夹角
      const countAngle = this.radiansToDegrees(Math.atan2(a, b));
      // 将计算的旋转角度转换成思极地图的 思极地图方向是从正下方从0开始 逆时针旋转
      let transAngle = 0;
      if (countAngle < 0) {
        transAngle = Math.abs(countAngle);
      } else {
        transAngle = 360 - countAngle;
      }
      return transAngle;
    },
    /**
     * 选择电器一次性得类型
     */
    chooseDqycxsjType() {
      this.$confirm("请选择电气一次图设计", {
        customClass: "chooseJgsj",
        confirmButtonText: "间隔拼接",
        cancelButtonText: "自定义方案设计",
        cancelButtonClass: "",
        confirmButtonClass: "",
        type: "info",
      })
        .then(() => {
          this.dqycVisible = true;
        })
        .catch(() => {
          this.fyzdyfaVisible = true;
        });
    },
    /*
     * 弧度转换为角度
     */
    radiansToDegrees(radians) {
      const degrees = radians % (2 * Math.PI);
      return (degrees * 180) / Math.PI;
    },
    closeRangeGt() {
      this.selectCheck.isShowCpVis = -1;
      $(".map-rightArea").hide();
      mapData.map.closePopup();
    },
    /**
     * 在地图上踩点
     */
    chooseMapPoint() {
      const mapcenter = mapData.map.getCenter();
      const that = this;
      const lng = Number(mapcenter.lng);
      const lat = Number(mapcenter.lat);
      // 卫星模式下不允许点击地图踩点
      if (!that.isShowSatellite) {
        // 当是拉线,柱上变压器绘制的时候单独处理
        if (that.submitType === 10 || that.submitType === 8) {
          that.isGoheadPoint = true;
          that.paramsChangeSubmit(that.drawLXHZStartPoint.lat, that.drawLXHZStartPoint.lng);
        } else {
          // 采点时候先判断是不是继续踩点
          if (that.isGoheadPoint) {
            // paramsChangeSubmit方法是等选择起始杆和结束杆后得打点方法
            switch (that.submitType) {
              case 0:
                if (that.allPointList.length !== 0) {
                  that.paramsChangeSubmit(lat, lng);
                }
                break;
              case 1:
              case 18:
                if (that.allPointList.length !== 0) {
                  that.paramsChangeSubmit(lat, lng);
                }
                break;
              // case 6:
              //   if (that.isClickLines) {
              //     that.paramsChangeSubmit(lat, lng);
              //   } else {
              //     return Toast.fail("请选择架空电缆线路");
              //   }
              //   break;
              case 5: // 低压下户
                that.paramsChangeSubmit(lat, lng);
                break;
              case 7: // 架空电缆
                if (that.subMitData.drawSet === "绘制起始杆") {
                  that.paramsChangeSubmit(lat, lng);
                }
                break;
              case 12: // 电缆线路
                that.isGoheadPoint = true;
                that.paramsChangeSubmit(lat, lng);
                break;
              case 20: // 配电站
                that.isGoheadPoint = true;
                that.paramsChangeSubmit(lat, lng);
                break;
            }
          } else {
            Toast.fail("请先选择起始点");
          }
        }
      }
    },
    backsAddRrea() {
      this.isEditPointParam.id = "";
      this.isShowBackAdds = false;
      this.isIditLine = false;
      // 将所有下拉恢复成默认状态
      this.showDraw();
      // 控制修改的dom的隐藏
      this.selectCheck.isShowCpVis = -1;
      $(".map-rightArea").hide();
      this.subMitData = {};
    },
    /**
     * 编号累加1
     */
    transform(str) {
      const arr = Array.from(str);
      if (arr.length < 1) return str;
      let indexOfLastNumber = NaN;
      arr.forEach((it, index) => {
        if (!isNaN(it)) {
          indexOfLastNumber = index;
        }
      });
      if (Number.isNaN(indexOfLastNumber)) return str;
      const newArr = arr.map((it, index) => {
        if (index === indexOfLastNumber) return Number(it) + 1;
        else return it;
      });
      return newArr.join("");
    },
    /**
     * base64转换为file类型
     * @param base64Url
     * @param filename
     * @returns
     */
    base64UrlToFile(base64Url, fileList) {
      const arr = base64Url.split(",");
      const mime = arr[0].match(/:(.*?);/)[1];
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      fileList.push(new File([u8arr], "voice", { type: mime }));
    },
    /**
     * 杆号重排功能
     */
    submitNumeral() {
      this.selectCheck.isShowCpVis = 1;
      $(".map-rightArea").show();
      this.startPointId.startId = "";
      this.startPointId.endId = "";
      this.submitType = 19;
      this.isGoheadPoint = false;
      this.isChooseAfterPoint = true;
      Toast.success({
        message: "请选择起始杆塔!",
        position: "bottom",
      });
    },
    /**
     * 关闭杆号重排功能
     */
    closeNumeral() {
      this.addParam.numeral.startPrefix = "";
      this.addParam.numeral.number = "";
      this.addParam.numeral.endPrefix = "";
      this.selectCheck.isShowCpVis = -1;
      $(".map-rightArea").hide();
    },
    savePramas() {
      this.projectParmSet = false;
      this.getProjcetSetting();
    },
    /**
     * 批量提交
     */
    submitRange() {
      let barchtype; // 分支类型
      let pointNums; // 杆塔类型
      const that = this;
      switch (this.addParam.rangeGt.batchType) {
        case "等距插入":
          barchtype = 0;
          pointNums = this.addParam.rangeGt.pointNum;
          break;
        case "等分插入":
          barchtype = 1;
          pointNums = Number(this.addParam.rangeGt.pointNum) + 1;
          break;
        case "自由插入":
          barchtype = 2;
          pointNums = this.addParam.rangeGt.pointNum;
          break;
      }
      const param = {
        projectId: this.addParam.rangeGt.projectId,
        batchType: barchtype,
        voltage: this.addParam.rangeGt.voltage,
        state: this.addParam.rangeGt.state,
        legendTypeKey: this.addParam.rangeGt.legendTypeKey,
        moduleId: this.addParam.rangeGt.moduleId,
        pointNum: pointNums,
        lineId: this.addParam.rangeGt.lineId,
      };
      Toast.loading({
        duration: 0, // 持续展示 toast
        message: "正在排杆中,请稍后...",
      });
      apipost("/t-dtf-app-point/insertAllPoint", param).then((res) => {
        if (res.code === 1001) {
          Toast.clear();
          that.isShowRange = false;
          that.isGoheadPoint = false;
          that.isChooseAfterPoint = false;
          that.getRangePoles(res.data);
          mapData.map.closePopup();
        }
      });
    },
    changeRadio(val) {
      this.changeRadioObj.isShowZy = false;
      this.changeRadioObj.isShowDf = false;
      this.changeRadioObj.isShowDj = false;
      this.addParam.rangeGt.pointNum = "";
      switch (val) {
        case "等分插入":
          this.changeRadioObj.isShowDf = true;
          break;
        case "等距插入":
          this.changeRadioObj.isShowDj = true;
          break;
        case "自由插入":
          this.changeRadioObj.isShowZy = true;
          break;
      }
    },

    /**
     * 计算当前采集点和前两个点组成线的延长线形成的夹角,取点规则如下
     * ..(x1,y1)\....../(x2,y2)....
     * ..........\..../......
     * ...........\../......
     * ............\/......

     * ............(x3,y3)......
     */
    getAngles(x1, y1, x2, y2, x3, y3) {
      const getAngle = ({ x: x1, y: y1 }, { x: x2, y: y2 }) => {
        const dot = x1 * x2 + y1 * y2;
        const det = x1 * y2 - y1 * x2;
        const angle = Math.abs((Math.atan2(det, dot) / Math.PI) * 180);
        return angle;
      };
      const angle = getAngle(
        {
          x: x1 - x3,
          y: y1 - y3,
        },
        {
          x: x2 - x3,
          y: y2 - y3,
        }
      );
      return 180 - angle;
    },
    /**
     * 显示杆塔自动选型型号
     */
    showGtType() {
      this.showMarkInfo.isShowAssist = true;
    },
    /**
     * tarbar设置页面
     */
    goSetModel(item, index) {
      const doms = document.querySelectorAll(".map-selectAreaChos");
      for (let j = 0; j < doms.length; j++) {
        $(doms[j]).css({ color: "#333", background: "#fff" });
      }
      $(doms[index]).css({ color: "#fff", background: "#526ADE" });
      this.chooseItems = item;
    },
    /**
     * 设置最后一个杆塔类型的点自动选型
     */
    setLastPoint() {
      const pointType = ["ZXLHZ", "FXLHZ", "JKDLHZ"];
      const lastPoint = this.allPointList[this.allPointList.length - 1];
      let lineLength = 0;
      const that = this;
      // 结束自动踩点的时候去判断最后一个点是不是杆塔 如果是推导型号
      if (lastPoint !== undefined) {
        if (pointType.includes(lastPoint.moduleType)) {
          if (lastPoint.autoModuleId === "") {
            switch (lastPoint.loopNum) {
              case "单回":
                lineLength = 0;
                break;
              case "双回":
                lineLength = 1;
                break;
              case "三回":
                lineLength = 2;
                break;
              case "四回":
                lineLength = 3;
                break;
            }
            const params = {
              angle: 0, // 角度
              pointID: "",
              arrayMode: "", // 排列方式
              cementMaterialID: lastPoint.moduleId, // 水泥杆物料id
              gtlb: lastPoint.legendTypeKey === "" ? "水泥杆" : lastPoint.legendTypeKey, // 杆塔类别
              isTerminalPole: true, // 是否终端
              loopNumber: lastPoint.loopNum, // 回路数
              meteorological: that.basicSettingData.qxType, // 气象区
              spanLength: 0, // 总耐张长度
              equipType: lastPoint.equipType, // 设备类型
              strainLength: that.basicSettingData.nzLength, // 耐张段长度
              tddj: 0, // 档距
              voltage: lastPoint.voltage, // 电压等级
              zxArr: [that.basicSettingData.plArray[lineLength][0]], // 基础参数设置直线
              zjArr: [that.basicSettingData.plArray[lineLength][1]], // 基础参数设置转角
              nzArr: [that.basicSettingData.plArray[lineLength][2]], // 基础参数设置耐张
              zdArr: [that.basicSettingData.plArray[lineLength][3]], // 基础参数设置终端
            }; // 自动选型的入参
            params.arrayMode = that.getPlType(
              lastPoint.loopNum,
              "",
              that.basicSettingData.nzLength,
              true
            );
            params.pointID = lastPoint.pointId;
            if (params.legendTypeKey === "钢管杆") {
              params.towerHeght = lastPoint.towerHeght;
              params.shaoJing = lastPoint.shaoJing;
            } else if (params.legendTypeKey === "窄基塔") {
              params.towerHeght = lastPoint.towerHeght;
            }
            apipost("/autoRule/getPole", params).then((res) => {
              if (res.code === 1001) {
                if (that.poIntAllIdList.length > 0) {
                  // 将最后一个杆上绑定的杆型解绑，查询所有的时候在显示出来
                  this.mapsAllPointsGraphic[this.mapsAllPointsGraphic.length - 1].unbindTooltip()
                  that.poIntAllIdList.splice(that.poIntAllIdList.length - 1, 1);
                }
                that.initMap(0);
                $(".map-endkc").css({ display: "none" });
              }
            });
          }
        }
      }
    },
    /**
     * 结束自动勘测
     */
    closeKcGohead() {
      this.isGoheadPoint = false;
      this.isReformMark = false;
      this.isChooseAfterPoint = false;
      this.isAddMarkerInit = true;
      this.showChoseIncoIndex = -1;
      this.submitType = -1;
      this.znsjType = false;
      this.isChoosePdzfIcon = false;
      this.setLastPoint();
      this.isAddPointUnAddLine = false
      $(".map-endkc").css({ display: "none" });
    },
    removeDrawPoy() {
      if (mapData.drawPolygon) {
        this.allRangeArry = [];
        mapData.drawPolygon.clearData();
        $(".map-removeDrawPoy").hide();
      }
    },

    /**
     * 下拉动作面板change事件
     */
    onSelectAction(val) {
      // eslint-disable-next-line no-debugger
      const that = this;
      switch (val.name) {
        case "全部删除":
          const param = {
            projectid: this.$route.query.childProjectId,
          };
          // LDA 接口改造 apipost
          apipost("/t-dtf-app-point/delAll", param).then(function (res) {
            if (res.code === 1001) {
              for (let j = 0; j < that.mapsAllPointsGraphic.length; j++) {
                that.mapsAllPointsGraphic[j].unbindTooltip()
                that.mapsAllPointsGraphic[j].remove()
              }
              for (let j = 0; j < that.mapsAllLinesGraphic.length; j++) {
                that.mapsAllLinesGraphic[j].remove()
              }
              for (let j = 0; j < that.mapsAllLineMarkers.length; j++) {
                that.mapsAllLineMarkers[j].remove()
              }
              that.mapsAllLineMarkers = []
              that.mapsAllLineMarkersIDS = []
              that.mapsAllPointsGraphic = []
              that.mapsAllLinesGraphic = []
              that.poIntAllIdList = [];
              that.saveAnglePoint = [];
              that.allPointList = [];
              that.allSourecePoint = [];
              that.allSjPoint = [];
              that.allSourceLine = [];
              that.allsjLine = [];
              that.lastPointLngAndLat = [];
              that.allGtPointList = [];
              that.showChoseIncoIndex = -1;
              that.mapAllLineList = [];
              that.lineimagesList = [];
              that.initMap(0);
              that.updateGuide()
              Toast.success("数据清除完毕!");
              that.showDraw();
              that.isShowAction = false;
              that.setMapCenter();
              that.isGetProjectXml = false;
              window.parent.postMessage("hideIframe", "*");
            }
          });
          break;
        case "杆号重排":
          this.selectCheck.isShowCpVis = 1;
          $(".map-rightArea").show();
          this.addParam.numeral.startPrefix = "";
          this.addParam.numeral.number = "";
          this.addParam.numeral.endPrefix = "";
          that.isShowAction = false;
          window.parent.postMessage("hideIframe", "*");
          break;
        case "切换矢量地图":
          var sdvecurl =
            "http://25.41.42.74:18091/api/oss/SLPT/TILE/TIANDITU/SD/2021/VEC/{z}/{y}/{x}?auth=" +
            window.localStorage.getItem("a") +
            "&suid=pwszhyjybj"; //xxxxx为权限账号
          var sdvectorLayer = L.tileLayer(sdvecurl, {
            minZoom: 0,
            maxNativeZoom: 19,
            // maxNativeZoom: 8,
            maxZoom: 19,
            tileSize: 256,
            zoomOffset: 1,
          });
          // 矢量地图默认先添加到地图上;
          L.layerGroup([sdvectorLayer]).addTo(mapData.map);
          that.isShowAction = false;
          break;
        case '切换影像地图':
          var sdimgurl =
            "http://25.41.42.74:18091/api/oss/SLPT/TILE/TIANDITU/SD/2021/IMG/{z}/{y}/{x}?auth=" +
            window.localStorage.getItem("a") +
            "&suid=pwszhyjybj"; //xxxxx为权限账号
          var rasterImageLayer = L.tileLayer(sdimgurl, {
            minZoom: 0,
            maxNativeZoom: 19,
            maxZoom: 19,
            tileSize: 256,
            zoomOffset: 1,
          });
          // 切换影像地图
          L.layerGroup([rasterImageLayer]).addTo(mapData.map);
          that.isShowAction = false;
          break
      }
    },
    /**
     * 更新辅助线
     */
    updateGuide() {
      const center = mapData.map.getCenter();
      if (this.lastPointLngAndLat.length !== 0) {
        this.mapsDistance = mapData.map.distance([this.lastPointLngAndLat[0], this.lastPointLngAndLat[1]], [center.lng, center.lat]).toFixed(0) + 'm'
        // 绘制辅助线
        if (graphiclineLayer === null) {
          polylineDistance = L.polyline([this.lastPointLngAndLat, [center.lng, center.lat]]);
          // let mapPoint = L.mapPoint([this.lastPointLngAndLat, [center.lng, center.lat]]);
          var symbollien = L.simpleLineSymbol({ style: 'solid', stroke: true, weight: 1, color: '#00FF00', fill: false, opacity: 1 });
          graphiclineLayer = L.graphic(polylineDistance, symbollien).addTo(highlightMapsLayer);
        } else {
          polylineDistance.setPoints([this.lastPointLngAndLat, [center.lng, center.lat]])
          graphiclineLayer.redraw()
        }
      } else {
        if (graphiclineLayer !== null) {
          this.mapsDistance = '0m'
          graphiclineLayer.remove()
          graphiclineLayer = null
          mapData.movePointDOM.unbindTooltip();
          // 删除全部点后需要将显示的辅助距离清空
          mapData.movePointDOM
            .bindTooltip(this.mapsDistance, {
              offset: [8, -25],
              permanent: true, // 永久打开弹出框
              className: "directiveDisTance", // 自定义的显示文字的弹框
            })
            .openTooltip();
          mapData.movePointDOM.redraw();
        }
      }
    },
    // 添加辅助线
    addAuxiliaryline() {
      this.assistInfo.isShowAssist = true;
    },
    hideSelect() {
      this.isEditPointParam.id = "";
      this.isShowBackAdds = false;
      this.showDraw();
      // 关闭下拉框
      this.selectCheck.isShowCpVis = -1;
      $(".map-rightArea").hide();
      this.subMitData = {};
    },
    /**
     * 根据不同的类型打开不同的选择区域
     * @type
     */
    chooseSeelctType(type) {
      const that = this;
      this.showChoseIncoIndex = -1;
      const mapZoom = parseInt(mapData.map.getZoom());
      if (mapZoom <= 15) {
        mapData.map.setZoom(15);
      }
      if (this.allPointList.length === 0) {
        if (type !== 2) {
          showRules();
        } else {
          Toast.fail("当前没有数据,请先绘制台区或者架空数据");
        }
      } else {
        showRules();
      }
      function showRules() {
        that.selectCheck.isShowCpVis = 0;
        $(".map-rightArea").show();
        that.isEditPointParam.id = "";
        that.isShowBackAdds = false;
        that.showDraw();
        // 将所有下拉恢复成默认状态
        that.subMitData = {};
        switch (type) {
          case 1:
            that.chooseItems = "架空线路";
            break;
          case 2:
            that.chooseItems = "电缆线路";
            break;
          case 3:
            that.chooseItems = "台区站房";
            break;
        }
      }
    },
    /**
     * 第一次初始化得时候查询各种型号赋默认值
     * 这里是为了减少修改方法这么处理了
     * @params settype 请求的具体某个模块的类型
     * @params type 请求的来自于基础参数设置当中的四类的类型 1: 水泥杆 2: 导线 3: 拉线 4: 电缆
     * @params moduleType 请求的不来自于基础参数当中的数据类型 打点类型（1：地图页查询用户参数设置、打点时选择水泥杆、导线、拉线、电缆
     2：绘制光缆3：柱上变压器方案类别、柱上设备类型、通道类别、通道类别、电缆井类型4：下户线型号、户表型号、柱上变压器方案、柱上设备型号、箱变方案类别、站房型号、环网箱方案类别、站房型号、环网室方案类别、开关站方案类别、配电室方案类别、通道信号、终端头型号、中间头型号、管材型号、电缆分支箱、电缆井型号）
     * @params materialsTypeKey 物料key
     * @params moduleTypeKey  模块key
     * @params parentKey 父类Key
     * @params moduleCode 模块code
     * @params moduleName 模块名称
     * @params voltage 请求的模块电压
     */
    getFirstTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
        // LDA 接口改造 apipost
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          switch (settype) {
            case 0:
              that.gtTypeList = res.data.tenSNG;
              break;
            case 1:
              // 主线路
              that.dxTypeList = res.data.tenDx;
              break;
            case 12:
              // 配电站房通道型号 电缆线路通道型号 配电站通道型号 土建路径通道型号
              that.tdxhTypeList = res.data;
              break;
            case 13:
              that.zjtTypeList = res.data;
              break;
            case 14:
              that.zdtTypeList = res.data;
              break;
            case 19:
              // 架空电缆电缆线路 电缆线路电缆型号 配电站房电缆型号 变电站电缆型号
              that.dlxhTypeList = res.data.tenDl;
              break;
            case 23:
              that.tdlaying = res.data;
              that.getFirstTowerOrLineType(
                12,
                "",
                4,
                "",
                res.data[0].moduletypekey,
                "",
                "",
                ""
              );
              break;
          }
        }
      });
    },
    /**
     * 排杆查询下拉项
     */
    getRangeGt(type, moduleType, voltage, isFirstLoad, searchParams) {
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: "",
        moduleType: moduleType,
        moduleTypeKey: "",
        parentKey: "",
        moduleCode: "",
        userId: this.userId,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75",
      };
      const that = this;
      // 接口改造 apipost
      apipost("/moduleSelection/selectModuleData", param).then(function (
        resPonse
      ) {
        if (resPonse.code === 1001) {
          if (isFirstLoad) {
               // 接口改造 apipost
            apipost("/t-dtf-app-point/selectPointOne", searchParams).then(
              function (res) {
                if (res.code === 1001) {
                  that.addParam.rangeGt.voltage = res.data.voltage;
                  that.addParam.rangeGt.moudle = res.data.pointModule;
                  that.addParam.rangeGt.moduleId = res.data.moduleId;
                  if (res.data.voltage === "10kV") {
                    that.settingObj.rangeGtMoudle = resPonse.data.tenSNG;
                  } else {
                    that.settingObj.rangeGtMoudle = resPonse.data.lowSNG;
                  }
                }
              }
            );
          } else {
            if (voltage === "10kV") {
              that.addParam.rangeGt.moudle = resPonse.data.tenSNG[0].name;
              that.addParam.rangeGt.moduleId = resPonse.data.tenSNG[0].id;
              that.settingObj.rangeGtMoudle = resPonse.data.tenSNG;
            } else {
              that.addParam.rangeGt.moudle = resPonse.data.lowSNG[0].name;
              that.addParam.rangeGt.moduleId = resPonse.data.lowSNG[0].id;
              that.settingObj.rangeGtMoudle = resPonse.data.lowSNG;
            }
          }
        }
      });
    },
    /**
     * 获取地图初始化的位置
     */
    setMapCenter() {
      // const that = this
      // navigator.mapData.geolocation.getCurrentPosition(function (position) {
      //   this.startPoint.x = position.coords.longitude;
      //   this.startPoint.y = position.coords.latitude;
      // }, that.geoerror, that.options);
    },
    getLineAngle(startPoint, endPoint) {
      let line = [
        [startPoint[0], startPoint[1]],
        [endPoint[0], endPoint[1]]
      ]
      let gcs = this.jd([line[0], line[1]]);
      let gc = (gcs / 180) * Math.PI;
      let length = 0.03;
      let excursionAngle = 15;
      let X1, X2, Y1, Y2;
      X1 = length * Math.cos(((gcs - excursionAngle) * Math.PI) / 180);
      Y1 = length * Math.sin(((gcs - excursionAngle) * Math.PI) / 180);
      X2 = length * Math.cos(((gcs + excursionAngle) * Math.PI) / 180);
      Y2 = length * Math.sin(((gcs + excursionAngle) * Math.PI) / 180);
      let x1 = line[0][0];
      let y1 = line[0][1];
      let x2 = line[1][0];
      let y2 = line[1][1];
      return this.xrlcsj({ points: [[x1, y1], [x2, y2]], num: 1 })
    },
    // 返回角度
    jd(points) {
      var points1;
      if (points.length == 2) {
        points1 = points;
      } else if (
        points.length == 4 &&
        points[0] &&
        points[1] &&
        points[2] &&
        points[3]
      ) {
        points1 = [points[0], points[3]];
      }
      //新角度
      let d = Math.atan2(
        points1[1][1] - points1[0][1],
        points1[1][0] - points1[0][0]
      );
      d = (d / Math.PI) * 180;
      if (d < 0) {
        d += 360;
      }
      return d;
    },
    //渲染量测数据
    xrlcsj(xlobj) {
      let points = xlobj.points,
        num = xlobj.num;
      let gc;
      if (points == undefined) {
        return;
      }
      if (points.length == 2) {
        let gcs = this.jd([points[0], points[1]]);
        gc = (gcs / 180) * Math.PI;
      } else if (points.length == 4) {
        let gcs = this.jd([points[1], points[2]]);
        gc = (gcs / 180) * Math.PI;
      }
      var direct;
      let angle = gc;
      //第三象限改为第一象限
      if (angle > Math.PI && angle <= 1.5 * Math.PI) {
        angle = angle - Math.PI;
        //三象限 角度改为锐角 第一条线该状态
        if (num && num == 1) {
          length = length - 20;
        }
        // if (num && num == 2) {
        //   length2 = 20;
        // }
        direct = false;
      } else if (angle > 0.5 * Math.PI && angle < Math.PI) {
        angle = angle + Math.PI;
        //二象限 角度改为锐角 第一条线该状态
        if (num && num == 1) {
          length = length - 20;
        }
        // if (num && num == 2) {
        //   length2 = 20;
        // }
        direct = false;
      } else if (angle > 0 && angle < 0.5 * Math.PI) {
        if (num && num == 2) {
          length = -length - 20;
        }
        direct = true;
      } else if (angle <= 2 * Math.PI && angle > 1.5 * Math.PI) {
        if (num && num == 2) {
          length = length - 20;
        }
        direct = true;
      }
      return angle
    },
    // 初始化的时候渲染地图
    initMap(type, state, urls, params) {

      // 渲染地图
      sessionStorage.setItem("isShowAuxiliary", "1");
      // 改变this指向 匿名函数当中this指向改变了
      const that = this;
      const lineParam = {
        projectId: this.$route.query.childProjectId,
        projectType: that.currentIsAfter ? "0" : "1",
      };
      const param = {
        projectId: this.$route.query.childProjectId,
      };
      const param_xml = {
        id: this.$route.query.childProjectId,
      };
      const getPointParam = type === 1 ? param_xml : param;
      const getLineParam = type === 1 ? param_xml : lineParam;
      const getPointUrl = type === 1 ? "/mapModel/queryMapXmlPointInfo" : "/t-dtf-app-point/selectAllPoint";
      const getLineUrl = type === 1 ? "/mapModel/queryMapXmlLineInfo" : "/t-dtf-app-point/selectAllLine";
      // 判断如果解析过了就不执行了
      // 回显点的接口
      // 接口改造 apipost
      apipost(getPointUrl, getPointParam).then(function (res) {
        /* 自定义所有点配置项信息 矢量化平台的固定写法开始 */
        highlightLayerPoint = L.graphicsLayer({
          id: "highlightLayerPoint",
          zIndex: false, // 隐藏高亮图层
          pane: "overlayPane",
          // LDA 控制图元在那个层级显示
          minZoom: 19,
          maxZoom: 20,
        }).addTo(mapData.map);
        /* 自定义所有点配置项信息 矢量化平台的固定写法结束 */
        // 如果是获取现状的时候 删除所有点线 重新获取
        Toast.clear();
// LDA
if (type === 1 && state != 'sjpoint') {
          that.poIntAllIdList = [];
          that.allSourecePoint = res.result;
         console.log("先获取现状点");
          if (res.result.length != 0) {
            mapData.map.setCenterPoint([
              Number(res.result[0].longitude),
              Number(res.result[0].latitude),
            ]);
            that.initMapLngLat.x = Number(res.result[0].longitude);
            that.initMapLngLat.y = Number(res.result[0].latitude);
          }
      // LDA解决图元mark名字乱窜问题
          that.initMap(0, 'sjpoint')
        } else if(type==0&&state=='sjpoint'){
          console.log("后获取设计点");
          that.isFirtLoadData = true;
          that.allSjPoint = res.data;
        }else{
          that.isFirtLoadData = true;
          that.allSjPoint = res.data;
        }
        // console.log(that.allSjPoint,'that.allSjPoint')
        // console.log(that.allSourecePoint,'that.allSourecePoint')

        that.allPointList = that.allSourecePoint.concat(that.allSjPoint);
        that.allGtPointList = []; // 置空所有的杆塔类型数组
        const allZgtArray = [];
        // // 地图上的点超过一个的时候 更新右边选项页面
        if (that.allPointList.length === 1) {
          that.tarbarList = ["架空线路", "台区站房", "电缆线路"];
          that.selectCheck.isShowNavBar = true;
          that.selectCheck.objVis.showMainLine = true;
          that.selectCheck.objVis.showBranchLine = true;
          that.selectCheck.objVis.showleverHead = true;
          that.selectCheck.objVis.showonductor = true;
          that.selectCheck.objVis.showOpticCable = true;
          that.selectCheck.objVis.showLowDoor = true;
          that.selectCheck.objVis.showCrossOver = true;
          that.selectCheck.objVis.showOverLine = true;
          that.selectCheck.objVis.showTransformer = true;
          that.selectCheck.objVis.showColumnEqu = true;
          that.selectCheck.objVis.showStay = true;
          that.selectCheck.objVis.showFacilities = true;
          that.selectCheck.objVis.showCableLine = true;
          that.selectCheck.objVis.showEquipLine = true;
          that.selectCheck.objVis.showCableHead = true;
          that.selectCheck.objVis.showCableTube = true;
          that.selectCheck.objVis.showCableBranch = true;
          that.selectCheck.objVis.showCableChannels = true;
          that.selectCheck.objVis.showCablePit = true;
          that.selectCheck.objVis.showElectrical = true;
          that.selectCheck.objVis.showChangeCable = true;
        }
        // that.mapsAllPointsGraphic=[]
        for (let j = 0; j < that.allPointList.length; j++) {
          let mapPointmarker
          // 交叉跨域的点不参与到连线上
          const displayNoneArr = ["通讯线", "电力线", "道路(普通道路)", "道路(高速道路)", "耕地", "水塘", "房屋", "铁路", "河沟", "经济作物", "DLFZXHZ",];
          // 台区上的点踩点的时候不参与更改辅助线
          const dispalyTqArr = ["ZSBYQHZ", "DLJHZ", "LXHZ", "ZSSBHZ"];
          const gtTypeArr = ["ZXLHZ", "FXLHZ", "JKDLHZ"];
          // 终端头和中间头不参与连线
          const zdtList = ["ZDT", "ZJT"];
          if (!zdtList.includes(that.allPointList[j].moduleName)) {
            if (!displayNoneArr.includes(that.allPointList[j].crossoverType)) {
              if (!dispalyTqArr.includes(that.allPointList[j].moduleType)) {
                that.lastPointLngAndLat = [];
                // 这里需要给结束点赋值 否则自动踩点时候上一个点得开始点id有问题
                that.startPointId.startId = that.allPointList[j].pointId;
                // 如果是拉线绘制的话 最后一个点取的是拉线点的杆塔的id
                if (that.submitType === 10) {
                  that.lastPointLngAndLat.push(that.drawLXHZStartPoint.lng);
                  that.lastPointLngAndLat.push(that.drawLXHZStartPoint.lat);
                } else {
                  that.lastPointLngAndLat.push(
                    Number(that.allPointList[j].longitude)
                  );
                  that.lastPointLngAndLat.push(
                    Number(that.allPointList[j].latitude)
                  );
                }
              }
            }
          }
          if (that.allPointList[j].moduleType !== "BDZHZ") {
            // 不能是和终端头相连接
            if (!zdtList.includes(that.allPointList[j].moduleName)) {
              that.lastPointId = that.allPointList[j].pointId;
            }
          }
          if (gtTypeArr.includes(that.allPointList[j].moduleType)) {
            allZgtArray.push(that.allPointList[j]);
            that.allGtPointList.push(that.allPointList[j]);
          }
          if (["LXHZ", "ZSBYQHZ"].includes(that.allPointList[j].moduleType)) {
            that.saveAnglePoint.push(that.allPointList[j]);
          }
          // 地图上绘制的点的配置项
          const pointStae = that.allPointList[j].state === "Original" ? "原有" : that.allPointList[j].state;
          const moudleType = that.allPointList[j].moduleType;
          const pointType = that.allPointList[j].source
          // 这里按需加载点位 避免地图加载多个覆盖物
          if (that.poIntAllIdList.indexOf(that.allPointList[j].pointId) === -1) {

            that.poIntAllIdList.push(that.allPointList[j].pointId)

            let mapPoint = L.mapPoint(that.allPointList[j].longitude, that.allPointList[j].latitude);
            let symbol = L.pictureMarkerSymbol({
              image: "./images/mapmarker/new/ganta.png",
              width: 25,
              height: 25,
              offset: [0, 0],
              opacity: 1,
            });
            // 地图上添加的每个点
            mapPointmarker = L.graphic(mapPoint, symbol, {
              id: that.allPointList[j].pointId
            }).addTo(highlightLayerPoint);
            mapPointmarker.addEventListener("click", showPoint)
            // 1 绑定显示采集点对应的编号，自动选型型号等内容 设置杆塔的编号和型号
            // 2 中台类型的杆塔19层的时候展示，避免杆之间距离太长，杆号太长展示不开
            const mapsZoom = mapData.map.getZoom()
            switch (pointType) {
              case '中台':
                if (mapsZoom === 19) {
                  const pointTypedesc = that.allPointList[j].autoModuleDes === undefined ? '' : that.allPointList[j].autoModuleDes

                  const showTexts = `<p class='marks'><span>${that.allPointList[j].mark}</span><br /><span>${pointTypedesc}</span></p>`


                  mapPointmarker.bindTooltip(showTexts, {
                    offset: [0, -10],
                    permanent: true, // 永久打开弹出框
                    direction: 'top', // 提示语的位置
                    className: "directiveMarks", // 自定义的显示文字的弹框
                  }).openTooltip();
                }
                break
              case '设计':

                if (mapsZoom > 16) {

                  const pointTypedesc = that.allPointList[j].autoModuleDes === undefined ? '' : that.allPointList[j].autoModuleDes
                  const showTexts = `<p class='marks'><span>${that.allPointList[j].mark}</span><br /><span>${pointTypedesc}</span></p>`
                  mapPointmarker.bindTooltip(showTexts, {
                    offset: [0, -10],
                    permanent: true, // 永久打开弹出框
                    direction: 'top', // 提示语的位置
                    className: "directiveMarks", // 自定义的显示文字的弹框
                  }).openTooltip();
                }
                break
            }

            that.mapsAllPointsGraphic.push(mapPointmarker)
            // console.log(that.mapsAllPointsGraphic.length,that.allPointList.length);
            // 如果是改后状态 显示的新建原有改造状态的数据 改前显示的是原有拆除改造的数据
            // 更新完dom上覆盖物图元需要设置为false 避免dom上重复加载多个数据
            that.isAddMarkerInit = false;

            switch (moudleType) {

              case "ZXLHZ":
              case "JKDLHZ":
                if (pointStae === "新建") {

                  mapPointmarker.symbol.options.image = "./images/mapmarker/new/ganta.png";
                  mapPointmarker.redraw();
                } else if (pointStae === "原有") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/original/ganta.png";
                  mapPointmarker.redraw();
                } else if (pointStae === "改造") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/reform/ganta.png";
                  mapPointmarker.redraw();
                } else if (pointStae === "拆除") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/remove/ganta.png";
                  mapPointmarker.redraw();
                }
                break;
              // case '':
              //   // 为空时不展示设备点位LDA
              //   mapPointmarker.symbol.options.image = ''
              //   mapPointmarker.redraw();
              //   break;
              // 分支线路杆塔根据pointType判断点图元
              case "FXLHZ":
                if (pointStae === "新建") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/new/fzxlg_new.png";
                  mapPointmarker.redraw();
                } else if (pointStae === "原有") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/original/fzxlg_yy.png";
                  mapPointmarker.redraw();
                } else if (pointStae === "改造") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/new/fzxlg_gz.png";
                  mapPointmarker.redraw();
                } else if (pointStae === "拆除") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/new/fzxlg_cc.png";
                  mapPointmarker.redraw();
                }
                break;
              // 低压下户杆塔图元
              case "DYXHHZ":
                const hbType = that.allPointList[j].hbType;
                const hbState = that.allPointList[j].hbState;
                // 低压下户的杆塔图元
                if (hbState === "新建" && hbType === "单相表箱") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/new/dxbx_new.png";
                  mapPointmarker.redraw();
                } else if (hbState === "原有" && hbType === "单相表箱") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/original/dxbx_yy.png";
                  mapPointmarker.redraw();
                } else if (hbState === "拆除" && hbType === "单相表箱") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/remove/dxbx_cc.png";
                  mapPointmarker.redraw();
                }
                if (hbState === "新建" && hbType === "三相表箱") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/new/sxbx_new.png";
                  mapPointmarker.redraw();
                } else if (hbState === "原有" && hbType === "三相表箱") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/original/sxbx_yy.png";
                  mapPointmarker.redraw();
                } else if (hbState === "拆除" && hbType === "三相表箱") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/remove/sxbx_cc.png";
                  mapPointmarker.redraw();
                }
                break;
              // 交叉跨域绘制图元 这个需要处理角度 稍后
              case "JCKYHZ":
                const crossType = that.allPointList[j].crossoverType;
                mapPointmarker.symbol.options.angle = that.allPointList[j].angle;
                mapPointmarker.symbol.options.width = 20;
                mapPointmarker.symbol.options.height = 20;
                // 交叉跨域绘制
                if (crossType === "通讯线") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/nostate/txx.png";
                  mapPointmarker.redraw();
                } else if (crossType === "电力线") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/nostate/dlx.png";
                  mapPointmarker.redraw();
                } else if (crossType === "道路(普通道路)") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/nostate/ptdl.png";
                  mapPointmarker.redraw();
                } else if (crossType === "道路(高速道路)") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/nostate/gsgl.png";
                  mapPointmarker.redraw();
                } else if (crossType === "耕地") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/nostate/gd.png";
                  mapPointmarker.redraw();
                } else if (crossType === "水塘") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/nostate/st.png";
                  mapPointmarker.redraw();
                } else if (crossType === "房屋") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/nostate/fw.png";
                  mapPointmarker.redraw();
                } else if (crossType === "铁路") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/nostate/tl.png";
                  mapPointmarker.redraw();
                } else if (crossType === "河沟") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/nostate/hg.png";
                  mapPointmarker.redraw();
                } else if (crossType === "经济作物") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/nostate/jjzw.png";
                  mapPointmarker.redraw();
                }
                break;
              // 柱上变压器 需要  处理角度
              case "ZSBYQHZ":
                const zsbyqjd = (360 - that.allPointList[j].angle) / 58.3
                const transStartlnglat_zsbyq = mapData.map.project([
                  Number(that.allPointList[j].latitude),
                  Number(that.allPointList[j].longitude),
                ]);
                const endPointpmzb_zsbyq = getEndpoint(
                  transStartlnglat_zsbyq,
                  20,
                  Number(that.allPointList[j].angle)
                );
                // 将平面直角坐标转换成经纬度
                const changeEndLngLat_zsbyq = mapData.map.unproject([
                  Math.abs(endPointpmzb_zsbyq.x),
                  Math.abs(endPointpmzb_zsbyq.y),
                ]);
                // 赋值
                // 主上变压器位置不对LDA 618
                mapPointmarker.symbol.options.angle = zsbyqjd
                // mapPointmarker.geometry.x = changeEndLngLat_zsbyq.lng
                mapPointmarker.geometry.x = that.allPointList[j].longitude
                // mapPointmarker.geometry.y = changeEndLngLat_zsbyq.lat
                mapPointmarker.geometry.y = that.allPointList[j].latitude
                if (pointStae === "新建") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/new/zsbyq_new.png";
                  mapPointmarker.redraw();
                } else if (pointStae === "原有") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/original/zsbyq_yy.png";
                  mapPointmarker.redraw();
                } else if (pointStae === "改造") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/reform/zsbyq_gz.png";
                  mapPointmarker.redraw();
                } else if (pointStae === "拆除") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/remove/zsbyq_cc.png";
                  mapPointmarker.redraw();
                }
                break;
              // 柱上设备绘制
              case "ZSSBHZ":
                const sbModel = that.allPointList[j].zssbType;
                // 注意空格分开
                let className = " mapsZssbMarker";
                const domZssb = document.querySelectorAll(".mapsZssbMarker");
                for (let j = 0; j < domZssb.length; j++) {
                  if (domZssb[j].lng !== undefined) {
                    if (domZssb[j].lng === longitude) {
                      className = className + "After";
                    }
                  }
                }
                mapPointmarker.symbol.options.width = 20
                mapPointmarker.symbol.options.height = 20
                mapPointmarker.symbol.options.offset = [0, 20]
                switch (sbModel) {
                  case "柱上断路器":
                  case "柱上电容器":
                    if (pointStae === "新建") {
                      mapPointmarker.symbol.options.image = "./images/mapmarker/new/zsdlq_new.png";
                      mapPointmarker.redraw();
                    } else if (pointStae === "原有") {
                      mapPointmarker.symbol.options.image = "./images/mapmarker/original/zsdlq_yy.png";
                      mapPointmarker.redraw();
                    } else if (pointStae === "拆除") {
                      mapPointmarker.symbol.options.image = "./images/mapmarker/remove/zsdlq_yy.png";
                      mapPointmarker.redraw();
                    }
                    break;
                  case "柱上负荷开关":
                    if (pointStae === "新建") {
                      mapPointmarker.symbol.options.image = "./images/mapmarker/new/zsfhkg_new.png";
                      mapPointmarker.redraw();
                    } else if (pointStae === "原有") {
                      mapPointmarker.symbol.options.image = "./images/mapmarker/original/zsfhkg_yy.png";
                      mapPointmarker.redraw();
                    } else if (pointStae === "拆除") {
                      mapPointmarker.symbol.options.image = "./images/mapmarker/remove/zsfhkg_cc.png";
                      mapPointmarker.redraw();
                    }
                    break;
                  case "柱上隔离开关":
                    if (pointStae === "新建") {
                      mapPointmarker.symbol.options.image = "./images/mapmarker/new/zsglkg_new.png";
                      mapPointmarker.redraw();
                    } else if (pointStae === "原有") {
                      mapPointmarker.symbol.options.image = "./images/mapmarker/original/zsglkg_yy.png";
                      mapPointmarker.redraw();
                    } else if (pointStae === "拆除") {
                      mapPointmarker.symbol.options.image = "./images/mapmarker/remove/zsglkg_cc.png";
                      mapPointmarker.redraw();
                    }
                    break;
                  case "跌落式熔断器":
                    mapPointmarker.symbol.options.width = 30
                    mapPointmarker.symbol.options.height = 30
                    if (pointStae === "新建") {
                      mapPointmarker.symbol.options.image = "./images/mapmarker/new/zsrdq_new.png";
                      mapPointmarker.redraw();
                    } else if (pointStae === "原有") {
                      mapPointmarker.symbol.options.image = "./images/mapmarker/original/zsrdq_yy.png";
                      mapPointmarker.redraw();
                    } else if (pointStae === "拆除") {
                      mapPointmarker.symbol.options.image = "./images/mapmarker/remove/zsrdq_cc.png";
                      mapPointmarker.redraw();
                    }
                    break
                }
                break;
              case "LXHZ":
                /**
                 * 拉线绘制是用的思极绘制线标记物的方法绘制
                 */
                // 矢量化平台旋转一度相当于平面直接坐标系的58.5度， 原计算旋转角是按照顺时针方向计算的，矢量化平台按照逆时针方向计算的，需要先用360度减去原坐标除58.3
                const slhjd = (360 - that.allPointList[j].angle) / 58.3
                const transStartlnglat = mapData.map.project([
                  Number(that.allPointList[j].latitude),
                  Number(that.allPointList[j].longitude),
                ]);
                const endPointpmzb = getEndpoint(
                  transStartlnglat,
                  20,
                  Number(that.allPointList[j].angle)
                );
                // 将平面直角坐标转换成经纬度
                const changeEndLngLat = mapData.map.unproject([
                  Math.abs(endPointpmzb.x),
                  Math.abs(endPointpmzb.y),
                ]);
                // 赋值
                mapPointmarker.symbol.options.image = "./images/mapmarker/new/ptlx_new.png";
                mapPointmarker.symbol.options.angle = slhjd
                mapPointmarker.geometry.x = changeEndLngLat.lng
                mapPointmarker.geometry.y = changeEndLngLat.lat
                mapPointmarker.redraw();
                break;
              case "DLXLHZ":
              case "DLTHZ":
                // 电缆线路绘制
                const type = that.allPointList[j].moduleName;
                if (moudleType === 'DLXLHZ') {
                  mapPointmarker.symbol.options.width = 15
                  mapPointmarker.symbol.options.height = 15
                } else if (moudleType === 'DLTHZ') {
                  mapPointmarker.symbol.options.width = 20
                  mapPointmarker.symbol.options.height = 20
                  mapPointmarker.symbol.options.offset = [20, 0]
                }
                mapPointmarker.redraw()
                if (type === "ZDT" && pointStae === "新建") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/new/dlzdt_new.png";
                  mapPointmarker.redraw();
                } else if (type === "ZDT" && pointStae === "原有") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/original/dlzdt_yy.png";
                  mapPointmarker.redraw();
                } else if (type === "ZDT" && pointStae === "拆除") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/remove/dlzdt_cc.png";
                  mapPointmarker.redraw();
                }
                // 电缆中间头绘制
                else if (type === "ZJT" && pointStae === "新建") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/new/dlzjt_new.png";
                  mapPointmarker.redraw();
                } else if (type === "ZJT" && pointStae === "原有") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/original/dlzjt_yy.png";
                  mapPointmarker.redraw();
                } else if (type === "ZJT" && pointStae === "拆除") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/remove/dlzjt_cc.png";
                  mapPointmarker.redraw();
                } else {
                  mapPointmarker.symbol.options.offset = [0, 0]
                  mapPointmarker.symbol.options.image = "./images/mapmarker/new/dlgd_new.png";
                  mapPointmarker.redraw();
                }
                break;
              case "DLFZXHZ":
                // 电缆分支箱绘制
                mapPointmarker.symbol.options.offset = [10, -20]
                mapPointmarker.symbol.options.width = 20
                mapPointmarker.symbol.options.height = 20
                if (pointStae === "新建") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/new/dlfzx_new.png";
                  mapPointmarker.redraw();
                } else if (pointStae === "原有") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/original/dlfzx_yy.png";
                  mapPointmarker.redraw();
                } else if (pointStae === "拆除") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/remove/dlfzx_cc.png";
                  mapPointmarker.redraw();
                }
                break;
              case "BDZHZ":
                // 电缆分支箱绘制
                if (pointStae === "新建") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/new/bdzx_.png";
                  mapPointmarker.redraw();
                } else if (pointStae === "原有") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/original/bdzy_.png";
                  mapPointmarker.redraw();
                } else if (pointStae === "拆除") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/remove/bdzc_.png";
                  mapPointmarker.redraw();
                }
                break;
              case "DLJHZ":
                // 电缆井绘制
                const pointDljType = that.allPointList[j].isStartDlj;
                if (pointStae === "新建") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/new/dlj_new.png";
                  mapPointmarker.redraw();
                } else if (pointStae === "原有") {
                  mapPointmarker.symbol.options.image = "./images/mapmarker/original/dlj_yy.png";
                  mapPointmarker.redraw();
                }
                break;
              case "PDZFHZ":
                // 配电站房绘制
                const pdzState = that.allPointList[j].hbState;
                const zfType = that.allPointList[j].zfType;
                if (pdzState === "新建") {
                  if (zfType === "箱式变电站（XA）") {
                    mapPointmarker.symbol.options.image = "./images/mapmarker/new/pdzfxsb_new.png";
                    mapPointmarker.redraw();
                  } else if (zfType === "环网箱（HA）") {
                    mapPointmarker.symbol.options.image = "./images/mapmarker/new/pdzfhwx_new.png";
                    mapPointmarker.redraw();
                  } else if (zfType === "环网室（HB）") {
                    mapPointmarker.symbol.options.image = "./images/mapmarker/new/pdzfhws_new.png";
                    mapPointmarker.redraw();
                  } else if (zfType === "配电室（PB）") {
                    mapPointmarker.symbol.options.image = "./images/mapmarker/new/pdzfpds_new.png";
                    mapPointmarker.redraw();
                  } else if (zfType === "开关站") {
                    mapPointmarker.symbol.options.image = "./images/mapmarker/new/pdzfkgz_new.png";
                    mapPointmarker.redraw();
                  }
                } else if (pdzState === "原有") {
                  if (zfType === "箱式变电站（XA）") {
                    mapPointmarker.symbol.options.image = "./images/mapmarker/original/pdzfxsb_yy.png";
                    mapPointmarker.redraw();
                  } else if (zfType === "环网箱（HA）") {
                    mapPointmarker.symbol.options.image = "./images/mapmarker/original/pdzfhwx_yy.png";
                    mapPointmarker.redraw();
                  } else if (zfType === "环网室（HB）") {
                    mapPointmarker.symbol.options.image = "./images/mapmarker/original/pdzfhws_yy.png";
                    mapPointmarker.redraw();
                  } else if (zfType === "配电室（PB）") {
                    mapPointmarker.symbol.options.image = "./images/mapmarker/original/pdzfpds_yy.png";
                    mapPointmarker.redraw();
                  } else if (zfType === "开关站") {
                    mapPointmarker.symbol.options.image = "./images/mapmarker/original/pdzfkgz_yy.png";
                    mapPointmarker.redraw();
                  }else if (zfType === "用户站") {
                    mapPointmarker.symbol.options.image = "./images/mapmarker/original/TY_YH_yy.png";
                    mapPointmarker.redraw();
                  }
                } else if (pdzState === "拆除") {
                  if (zfType === "箱式变电站（XA）") {
                    mapPointmarker.symbol.options.image = "./images/mapmarker/remove/pdzfxsb_cc.png";
                    mapPointmarker.redraw();
                  } else if (zfType === "环网箱（HA）") {
                    mapPointmarker.symbol.options.image = "./images/mapmarker/remove/pdzfhwx_cc.png";
                    mapPointmarker.redraw();
                  } else if (zfType === "环网室（HB）") {
                    mapPointmarker.symbol.options.image = "./images/mapmarker/remove/pdzfhws_cc.png";
                    mapPointmarker.redraw();
                  } else if (zfType === "配电室（PB）") {
                    mapPointmarker.symbol.options.image = "./images/mapmarker/remove/pdzfpds_cc.png";
                    mapPointmarker.redraw();
                  } else if (zfType === "开关站") {
                    mapPointmarker.symbol.options.image = "./images/mapmarker/remove/pdzfkgz_cc.png";
                    mapPointmarker.redraw();
                  }else if (zfType === "用户站") {
                    mapPointmarker.symbol.options.image = "./images/mapmarker/original/TY_YH_cc.png";
                    mapPointmarker.redraw();
                  }
                }
                break;
            }

          }

          /**
           * 每个点得点击事件
           * @param lng
           * @param lat
           */
          // eslint-disable-next-line no-inner-declarations
          function showPoint() {
            // 一点击覆盖物就不允许在地图上接着踩点了
            that.isGoheadPoint = false;
            // 是不是点击了覆盖物
            if (!that.isGoheadPoint) {
              // 这里判断是选完设置参数后设置起始杆还是直接点开点上得设置选项
              if (!that.isChooseAfterPoint) {
                // eslint-disable-next-line no-return-assign
                // 如果是配电站房得类型 就先禁止其他得修改选型得事件 避免有冲突
                if (that.isChoosePdzfIcon) {
                  if (that.allPointList[j].moduleType === "PDZFHZ") {
                    that.znsjType = true;
                    that.ciclkPointId = that.allPointList[j].pointId;
                    that.chooseDqycxsjType();
                    return;
                  } else {
                    Toast.fail({
                      message: "请选择站房图源",
                      duration: 1000,
                    });
                    return;
                  }
                }
                // 如果点击平面布置图设计 并且是配电站房得类型 就先禁止其他得修改选型得事件 避免有冲突
                if (that.isChoosePdzfIconByPmbzt) {
                  if (that.allPointList[j].moduleType === "PDZFHZ") {
                    mapPointmarker
                      .bindPopup(
                        `<div class="" onclick="showPmbztsj(0)">单列布置</div>
                           <div class="" onclick="showPmbztsj(1)">双列面对面布置</div>
                           <div class="" onclick="showPmbztsj(2)">双列背对背布置</div>
                           <div class="" onclick="showPmbztsj(3)">取消</div>
                        `,
                        {
                          offset: [0, -10],
                        }
                      )
                      .openPopup();
                    return;
                  } else {
                    Toast.fail({
                      message: "请选择站房图源",
                      duration: 1000,
                    });
                    return;
                  }
                }
                // 柱上变压器的时候将弹框挪动下位置
                let conText = "";
                if (that.allPointList[j].moduleType === "ZSBYQHZ") {
                  conText = "修改柱上变压器";
                } else if (that.allPointList[j].moduleType === "ZSSBHZ") {
                  conText = "修改柱上设备";
                } else if (that.allPointList[j].moduleType === "LXHZ") {
                  conText = "修改拉线信息";
                } else if (that.allPointList[j].moduleType === "DLTHZ") {
                  conText = "修改终端头信息";
                } else if (that.allPointList[j].moduleType === "DLFZXHZ") {
                  conText = "修改电缆分支箱";
                } else if (that.allPointList[j].moduleType === "DLJHZ") {
                  conText = "修改电缆井";
                  that.isClickDlj = true;
                } else {
                  conText = "修改选型";
                }
                // 弹框中移动点
                // 弹框移动点绑定显示的距离
                mapPointmarker
                  .bindPopup(
                    `<div class="setDoms" onclick="showDetails('${that.allPointList[j].pointId}')">${conText}</div>
                         <div class="" onclick="removePoint('${that.allPointList[j].pointId}','${that.allPointList[j].longitude}','${that.allPointList[j].latitude}')">删除</div>
                         <div class="" onclick=hidePop()>取消</div>
                      `,
                    {
                      offset: [0, -10],
                    }
                  )
                  .openPopup();
              } else {
                // 根据类型判断
                const isShowAuxiliary = sessionStorage.getItem("isShowAuxiliary");
                switch (that.submitType) {
                  case 0: // 主线路
                    if (that.allPointList.length !== 0) {
                      // 限制主杆塔只能和最开始的主杆塔或者最后的主杆塔选择
                      that.lastPointLngAndLat = [];
                      if (
                        ["ZXLHZ", "PDZFHZ", "BDZHZ", "DLXLHZ"].includes(
                          that.allPointList[j].moduleType
                        )
                      ) {
                        that.lastPointId = that.allPointList[j].pointId;
                        // 这里判断如果是设计的点 就单独调接口给线表插入一根线
                        if (that.allPointList[j].source === "中台") {
                          that.saveLine.isSaveLine = true;
                          that.saveLine.lonA = that.allPointList[j].longitude;
                          that.saveLine.latA = that.allPointList[j].latitude;
                          that.saveLine.id = that.allPointList[j].pointId;
                        }
                        that.lastPointLngAndLat.push(
                          Number(that.allPointList[j].longitude)
                        );
                        that.lastPointLngAndLat.push(
                          Number(that.allPointList[j].latitude)
                        );
                        if (isShowAuxiliary === "1") that.updateGuide();
                        that.showChoseIncoIndex = 1;
                        // 这里必须延迟设置isGoheadPoint 否则覆盖物的点击事件和地图的点击事件同时触发了
                        setTimeout(() => {
                          that.isGoheadPoint = true;
                        }, 1000);
                      } else {
                        return Toast.fail(
                          "请选择主杆塔,配电站房或变电站继续绘制"
                        );
                      }
                    }
                    break;
                  case 1: // 分支线路
                    that.lastPointLngAndLat = [];
                    that.lastPointId = that.allPointList[j].pointId;
                    that.lastPointLngAndLat.push(
                      Number(that.allPointList[j].longitude)
                    );
                    that.lastPointLngAndLat.push(
                      Number(that.allPointList[j].latitude)
                    );
                    if (isShowAuxiliary === "1") that.updateGuide();
                    // 这里必须延迟设置isGoheadPoint 否则覆盖物的点击事件和地图的点击事件同时触发了
                    setTimeout(() => {
                      that.isGoheadPoint = true;
                    }, 1000);
                    break;
                  case 2: // 同杆并架
                    // 这里判断第一次点 点击的点设为起始点 然后第二个点击的点设为结束点
                    if (that.startPointId.startId === "") {
                      if (that.allPointList[j].loopNum !== "单回") {
                        Toast.success("该杆塔下存在两回路,不允许同杆并架");
                      } else {
                        that.startPointTime.startTime = that.allPointList[j].dotTime;
                        that.startPointId.startId = that.allPointList[j].pointId;
                        Toast.success("请选择结束点");
                      }
                    } else {
                      that.startPointId.endId = that.allPointList[j].pointId;
                      that.startPointTime.endTime = that.allPointList[j].dotTime;
                      that.paramsChangeSubmit("", "");
                    }
                    break;
                  case 3: // 导线绘制
                  case 4: // 光缆绘制
                  case 21: // 设备连接绘制
                    // 这里判断第一次点 点击的点设为起始点 然后第二个点击的点设为结束点
                    if (that.startPointId.startId === "") {
                      that.startPointTime.startTime = that.allPointList[j].dotTime;
                      that.startPointId.startId = that.allPointList[j].pointId;
                      Toast.success("请选择结束点");
                    } else {
                      that.startPointId.endId = that.allPointList[j].pointId;
                      that.startPointTime.endTime =
                        that.allPointList[j].dotTime;
                      that.paramsChangeSubmit("", "");
                    }
                    break;
                  case 5: // 低压下户
                    that.lastPointLngAndLat = [];
                    that.lastPointId = that.allPointList[j].pointId;
                    that.startPointId.startId = that.allPointList[j].pointId;
                    that.lastPointLngAndLat.push(
                      Number(that.allPointList[j].longitude)
                    );
                    that.lastPointLngAndLat.push(
                      Number(that.allPointList[j].latitude)
                    );
                    if (isShowAuxiliary === "1") that.updateGuide();
                    setTimeout(() => {
                      that.isGoheadPoint = true;
                      that.showChoseIncoIndex = 1;
                    }, 1000);
                    break;
                  case 7: // 架空电缆
                    // 架空电缆需要判断绘制方式 如果是选择现有杆 那就选择起始点和结束点划线 否则选择起始杆后踩点
                    switch (that.subMitData.drawSet) {
                      case "绘制起始杆":
                        that.lastPointLngAndLat = [];
                        that.startPointId.startId =
                          that.allPointList[j].pointId;
                        that.lastPointLngAndLat.push(
                          Number(that.allPointList[j].longitude)
                        );
                        that.lastPointLngAndLat.push(
                          Number(that.allPointList[j].latitude)
                        );
                        if (isShowAuxiliary === "1") that.updateGuide();
                        that.showChoseIncoIndex = 1;
                        setTimeout(() => {
                          that.isGoheadPoint = true;
                        }, 1000);
                        break;
                      case "选择现有杆":
                        if (that.startPointId.startId === "") {
                          that.startPointTime.startTime = that.allPointList[j].dotTime;
                          that.startPointId.startId = that.allPointList[j].pointId;
                          Toast.success("请选择结束点");
                        } else {
                          that.startPointId.endId =
                            that.allPointList[j].pointId;
                          that.startPointTime.endTime =
                            that.allPointList[j].dotTime;
                          that.paramsChangeSubmit("", "");
                        }
                        break;
                    }
                    break;
                  case 9: // 柱上变压器
                  case 11: // 附属设备
                    const startLng = Number(that.allPointList[j].longitude);
                    const startLat = Number(that.allPointList[j].latitude);
                    that.paramsChangeSubmit(startLat, startLng);
                    setTimeout(() => {
                      that.isGoheadPoint = true;
                    }, 1000);
                    break;
                  case 8: // 柱上变压器
                    if (that.startPointId.startId === "") {
                      if (
                        ["ZXLHZ", "FXLHZ"].includes(moudleType) &&
                        that.allPointList[j].voltage === "10kV"
                      ) {
                        that.startPointId.startId = that.allPointList[j].pointId;
                        that.lastPointLngAndLat = [];
                        that.lastPointLngAndLat.push(Number(that.allPointList[j].longitude));
                        that.lastPointLngAndLat.push(Number(that.allPointList[j].latitude));
                        Toast.success("请挪动辅助线选择结束点方向");
                        that.showChoseIncoIndex = 1;
                        that.drawLXHZStartPoint.lng = Number(
                          that.allPointList[j].longitude
                        );
                        that.drawLXHZStartPoint.lat = Number(
                          that.allPointList[j].latitude
                        );
                      } else {
                        Toast.fail("请选择高压杆塔绘制柱上变压器");
                      }
                    }
                    break;
                  case 10: // 拉线绘制
                    if (that.startPointId.startId === "") {
                      if (["ZXLHZ", "FXLHZ"].includes(moudleType)) {
                        that.startPointId.startId =
                          that.allPointList[j].pointId;
                        that.lastPointLngAndLat = [];
                        that.lastPointLngAndLat.push(
                          Number(that.allPointList[j].longitude)
                        );
                        that.lastPointLngAndLat.push(
                          Number(that.allPointList[j].latitude)
                        );
                        Toast.success("请挪动辅助线选择结束点方向");
                        that.isGoheadPoint = true;
                        that.showChoseIncoIndex = 1;
                        that.drawLXHZStartPoint.lng = Number(
                          that.allPointList[j].longitude
                        );
                        that.drawLXHZStartPoint.lat = Number(
                          that.allPointList[j].latitude
                        );
                      } else {
                        Toast.fail("请选择杆塔绘制拉线");
                      }
                    }
                    break;
                  case 12: // 电缆线路
                    if (that.allPointList[j].moduleType === "DLXLHZ") {
                      that.subMitData.isStart = "0";
                    } else {
                      that.subMitData.isStart = "1";
                    }
                    that.lastPointLngAndLat = [];
                    that.startPointId.startId = that.allPointList[j].pointId;
                    that.lastPointLngAndLat.push(
                      Number(that.allPointList[j].longitude)
                    );
                    that.lastPointLngAndLat.push(
                      Number(that.allPointList[j].latitude)
                    );
                    if (isShowAuxiliary === "1") that.updateGuide();
                    that.showChoseIncoIndex = 2;
                    setTimeout(() => {
                      that.isGoheadPoint = true;
                    }, 1000);
                    break;
                  case 13: // 电缆头
                    if (that.subMitData.type === "终端头") {
                      if (that.allPointList[j].moduleType !== "DLXLHZ") {
                        Toast.fail("当前点不是电缆拐点");
                      } else {
                        const startLngDlt = Number(
                          that.allPointList[j].longitude
                        );
                        const startLatZjt = Number(
                          that.allPointList[j].latitude
                        );
                        that.paramsChangeSubmit(startLatZjt, startLngDlt);
                        setTimeout(() => {
                          that.isGoheadPoint = true;
                        }, 1000);
                      }
                    } else {
                      Toast.fail("请选择电缆线");
                    }
                    break;
                  case 15: // 电缆分支箱
                    if (that.allPointList[j].moduleType === "ZXLHZ" || that.allPointList[j].moduleType === "DLXLHZ") {
                      const startDlLng = Number(that.allPointList[j].longitude);
                      const startDlLat = Number(that.allPointList[j].latitude);
                      that.paramsChangeSubmit(startDlLat, startDlLng);
                    } else {
                      Toast.fail("请选择杆塔或者电缆拐点");
                    }
                    break;
                  case 18: // 配电站房
                    if (that.allPointList.length !== 0) {
                      that.lastPointLngAndLat = [];
                      that.lastPointId = that.allPointList[j].pointId;
                      that.startPointId.startId = that.allPointList[j].pointId;
                      that.lastPointLngAndLat.push(
                        Number(that.allPointList[j].longitude)
                      );
                      that.lastPointLngAndLat.push(
                        Number(that.allPointList[j].latitude)
                      );
                      if (isShowAuxiliary === "1") that.updateGuide();
                      // 这里必须延迟设置isGoheadPoint 否则覆盖物的点击事件和地图的点击事件同时触发了
                      setTimeout(() => {
                        that.isGoheadPoint = true;
                      }, 1000);
                    }
                    break;
                  case 19:
                    // 杆号重排只支持主杆塔绘制
                    if (that.allPointList[j].moduleType === "ZXLHZ") {
                      if (that.startPointId.startId === "") {
                        that.startPointTime.startTime =
                          that.allPointList[j].dotTime;
                        that.startPointId.startId =
                          that.allPointList[j].pointId;
                        Toast.success("请选择重排的结束点");
                      } else {
                        if (that.allPointList[j].moduleType === "ZXLHZ") {
                          that.startPointId.endId =
                            that.allPointList[j].pointId;
                          that.startPointTime.endTime =
                            that.allPointList[j].dotTime;
                          const parasm = {
                            startPointId: that.startPointId.startId,
                            endPointId: that.startPointId.endId,
                            projectId: that.$route.query.childProjectId,
                            pointPrefix: that.addParam.numeral.startPrefix,
                            pointSuffix: that.addParam.numeral.endPrefix,
                            startNumber: that.addParam.numeral.number,
                          };
                          apipost("/t-dtf-app-point/pointGroup", parasm).then(
                            (res) => {
                              if (res.code === 1001) {
                                Toast.success("重排成功");
                                document
                                  .querySelectorAll(".movePointShowAutoLine")
                                  .forEach((e) => e.parentNode.removeChild(e));
                                document
                                  .querySelectorAll(".movePointShowMark")
                                  .forEach((e) => e.parentNode.removeChild(e));
                                document
                                  .querySelectorAll(".markerEveryLines")
                                  .forEach((e) => e.parentNode.removeChild(e));
                                document
                                  .querySelectorAll(".mapMarker")
                                  .forEach((e) => e.parentNode.removeChild(e));
                                document
                                  .querySelectorAll(".mark-distance")
                                  .forEach((e) => e.parentNode.removeChild(e));
                                document
                                  .querySelectorAll(".dlxxj")
                                  .forEach((e) => e.parentNode.removeChild(e));
                                document
                                  .querySelectorAll(".zssbxj")
                                  .forEach((e) => e.parentNode.removeChild(e));
                                document
                                  .querySelectorAll(".mapLineCroow")
                                  .forEach((e) => e.parentNode.removeChild(e));
                                document
                                  .querySelectorAll(".mapLineFourLine")
                                  .forEach((e) => e.parentNode.removeChild(e));
                                that.poIntAllIdList = [];
                                that.setMapCenter();
                                that.initMap();
                                that.isGoheadPoint = false;
                                that.isChooseAfterPoint = false;
                              }
                            }
                          );
                        } else {
                          Toast.fail("请选择主杆塔");
                          that.startPointId.startId = "";
                        }
                      }
                    } else {
                      Toast.fail("请选择主杆塔");
                    }
                    break;
                  // 变电站
                  case 20:
                    that.lastPointLngAndLat = [];
                    that.lastPointId = that.allPointList[j].pointId;
                    that.lastPointLngAndLat.push(
                      Number(that.allPointList[j].longitude)
                    );
                    that.lastPointLngAndLat.push(
                      Number(that.allPointList[j].latitude)
                    );
                    that.startPointId.startId = "";
                    if (isShowAuxiliary === "1") that.updateGuide();
                    that.isChooseAfterPoint = false;
                    that.showChoseIncoIndex = 2;
                    // 这里必须延迟设置isGoheadPoint 否则覆盖物的点击事件和地图的点击事件同时触发了
                    setTimeout(() => {
                      that.isGoheadPoint = true;
                    }, 1000);
                }
              }
            }
          }
        }
      });
      // 回显线的接口
       // 接口改造 apipost
      apipost(getLineUrl, getLineParam).then(function (res) {

        if (type === 1&& state != 'sjline') {

          console.log("先获取现状数据 线");
          that.lineimagesList = [];
          that.drawlineIdList = [];

          that.allSourceLine = res.result;
            that.initMap(0, 'sjline')

        } else  if(type === 0&&state=='sjline'){

          that.allsjLine = res.data;

          console.log("后获取设计数据 线");
        }else{
           that.allsjLine = res.data;
        }
        that.mapAllLineList = that.allSourceLine.concat(that.allsjLine);
        for (let j = 0; j < that.mapAllLineList.length; j++) {
          if (that.mapAllLineList.length === 0) return false;
          if (that.currentIsAfter) {
            if (that.mapAllLineList[j].state === "拆除") {
              continue;
            }
          } else {
            if (that.mapAllLineList[j].state === "新建") {
              continue;
            }
          }
          that.mapAllLineList[j].lonA = Number(that.mapAllLineList[j].lonA);
          that.mapAllLineList[j].latA = Number(that.mapAllLineList[j].latA);
          that.mapAllLineList[j].lonB = Number(that.mapAllLineList[j].lonB);
          that.mapAllLineList[j].latB = Number(that.mapAllLineList[j].latB);
          let PiontIDArrays;
          let imgUrl = ""; // 绘制图片的时候显示的图片地址
          let lineStyle = {}; // 绘制线的配置项
          const lineId = that.mapAllLineList[j].lineId;
          const voltage = that.mapAllLineList[j].voltage;
          const moduleType = that.mapAllLineList[j].moduleType;
          const lineState = that.mapAllLineList[j].state === "Original" ? "原有" : that.mapAllLineList[j].state;
          let imgId;
          let showLineText
          if (that.mapAllLineList[j].source !== "中台") {
            showLineText = that.mapAllLineList[j].lineModel === null ? Number(that.mapAllLineList[j].lineLength).toFixed(0) + "m" : that.mapAllLineList[j].lineModel + "/" + Number(that.mapAllLineList[j].lineLength).toFixed(0) + "m";
          }

          let lineTranslate = [];
          // 16层级的时候杆塔图元是半径是15
          // 第一步 将经纬度转换成平面直角坐标
          const transStartlnglat = mapData.map.project([
            Number(that.mapAllLineList[j].latA),
            Number(that.mapAllLineList[j].lonA),
          ]);
          const transEndlnglat = mapData.map.project([
            Number(that.mapAllLineList[j].latB),
            Number(that.mapAllLineList[j].lonB),
          ]);
          // 中台数据需要单独处理下坐标，中台数据缺少countLine字段，要不PiontIDArrays会undefined
          if (that.mapAllLineList[j].source === "中台") {
            PiontIDArrays = [
              { lng: that.mapAllLineList[j].lonA, lat: that.mapAllLineList[j].latA },
              { lng: that.mapAllLineList[j].lonB, lat: that.mapAllLineList[j].latB }
            ]
          }
          if (moduleType === "DLTDHZ") {
            PiontIDArrays = [
              { lng: that.mapAllLineList[j].lonA, lat: that.mapAllLineList[j].latA },
              { lng: that.mapAllLineList[j].lonB, lat: that.mapAllLineList[j].latB }
            ]
          } else {
            if (that.mapAllLineList[j].countLine !== 1) {
              // countLine 两个杆之间总共有几根线
              // indexLine 两个杆下的第几根线
              switch (that.mapAllLineList[j].indexLine) {
                // 多回路下的第一根线
                case 0:
                  PiontIDArrays = [
                    { lng: that.mapAllLineList[j].lonA, lat: that.mapAllLineList[j].latA },
                    { lng: that.mapAllLineList[j].lonB, lat: that.mapAllLineList[j].latB }
                  ]
                  break;
                // 多回路下的第二根线
                case 1:
                  const twoLine_start = mapData.map.unproject([transStartlnglat.x + 8, transStartlnglat.y + 8])
                  const twoLine_end = mapData.map.unproject([transEndlnglat.x + 8, transEndlnglat.y + 8])
                  PiontIDArrays = [twoLine_start, twoLine_end]
                  break;
                // 多回路下的第三根线
                case 2:
                  const thrLine_start = mapData.map.unproject([transStartlnglat.x - 8, transStartlnglat.y - 8])
                  const thrLine_end = mapData.map.unproject([transEndlnglat.x - 8, transEndlnglat.y - 8])
                  PiontIDArrays = [thrLine_start, thrLine_end]
                  break;
                case 3:
                  PiontIDArrays = [
                    [transformstartLngLat4.lng, transformstartLngLat4.lat],
                    [transformendLngLat4.lng, transformendLngLat4.lat],
                  ];
                  break;
              }
            } else {
              PiontIDArrays = [
                { lng: that.mapAllLineList[j].lonA, lat: that.mapAllLineList[j].latA },
                { lng: that.mapAllLineList[j].lonB, lat: that.mapAllLineList[j].latB }
              ]
            }
          }
          // 这里先判断下线类型 如果是杆塔下的导线用图片展示 否则用不同颜色的线表示
          switch (moduleType) {
            // 光缆绘制
            case "GLHZ":
              lineStyle = {
                "lineColor": "#ffffff",
                "line-translate": that.mapSetting.glhzOffset,
              };
              setLineWithlines();
              break;
            // ZXLHZ主线路绘制  FXLHZ分线路绘制  DXHZ导线绘制 TGBJHZ同杆并架 JKDLHZ架空电缆线路 PDZFHZ配电站房绘制  // 电缆保护管绘制
            case "ZXLHZ":
            case "FXLHZ":
            case "DXHZ":
            case "TGBJHZ":
            case "JKDLHZ":
            case "DLXLHZ":
            case "SBLJHZ":
            case "PDZFHZ":
              switch (voltage) {
                // 10kv导线
                case "10kV":
                  if (lineState === "新建") {
                    lineStyle = {
                      "lineColor": "#FF0000",
                      "lineDasharray": [4, 4],
                    };
                    setLineWithlines();
                  } else if (lineState === "拆除") {
                    lineStyle = {
                      "lineColor": "#000000",
                      "lineDasharray": [2, 2],
                    };
                    setLineWithlines();
                  } else {
                    lineStyle = {
                      "lineColor": "#0052D9",
                    };
                    setLineWithlines();
                  }
                  break;
                case "380V":
                  if (lineState === "新建") {
                    lineStyle = {
                      "lineColor": "#1480EE",
                      "lineDasharray": [2, 2],
                    };
                    setLineWithlines("1");
                  } else if (lineState === "拆除") {
                    lineStyle = {
                      "lineColor": "#000000",
                      "lineDasharray": [2, 2],
                    };
                    setLineWithlines("0");
                  } else {
                    lineStyle = {
                      "lineColor": "#0052D9",
                    };
                    setLineWithlines();
                  }
                  break;
                case "220V":
                  if (lineState === "新建") {
                    lineStyle = {
                      "lineColor": "#17B49B",
                      "lineDasharray": [2, 2],
                    };
                    setLineWithlines("2");
                  } else if (lineState === "拆除") {
                    lineStyle = {
                      "lineColor": "#000000",
                      "lineDasharray": [2, 2],
                    };
                    setLineWithlines("0");
                  } else {
                    lineStyle = {
                      "lineColor": "#0052D9",
                    };
                    setLineWithlines();
                  }
                  break;
              }
              break;
            case "BDZHZ": // 变电站绘制
              switch (voltage) {
                // 10kv导线
                case "10kV":
                case '35kV':
                  if (lineState === "新建") {
                    lineStyle = {
                      "lineColor": "#FF0000",
                      "lineDasharray": [2, 2],
                    };
                    setLineWithlines();
                  } else if (lineState === "拆除") {
                    lineStyle = {
                      "lineColor": "#000000",
                      "lineDasharray": [2, 2],
                    };
                    setLineWithlines("0");
                  } else {
                    setLineWithlines();
                  }
                  break;
                case "380V":
                  if (lineState === "新建") {
                    lineStyle = {
                      "lineColor": "#1480EE",
                      "lineDasharray": [2, 2],
                    };
                    setLineWithlines("1");
                  } else if (lineState === "拆除") {
                    lineStyle = {
                      "lineColor": "#000000",
                      "lineDasharray": [2, 2],
                    };
                    setLineWithlines("0");
                  } else {
                    setLineWithlines();
                  }
                  break;
                case "220V":
                  if (lineState === "新建") {
                    lineStyle = {
                      "lineColor": "#17B49B",
                      "lineDasharray": [2, 2],
                    };
                    setLineWithlines("2");
                  } else if (lineState === "拆除") {
                    lineStyle = {
                      "lineColor": "#000000",
                      "lineDasharray": [2, 2],
                    };
                    setLineWithlines("0");
                  } else {
                    setLineWithlines();
                  }
                  break;
              }
              break;
            case "DLBHGHZ":
              lineStyle = {
                "lineColor": "#5514ee",
                "lineDasharray": [2, 2],
              };
              setLineWithlines();
              break;
            case "DYXHHZ": // 低压下户绘制
              imgUrl = require("@/assets/" + "map/mapmarker/new/dldx_.png");
              lineTranslate = [0, 0];
              imgId = "poi" + j;
              setLineWithImage();
              break;
            case "DLTDHZ":
              const methodName = that.mapAllLineList[j].methodName;
              switch (methodName) {
                case "直埋":
                  imgUrl = require("@/assets/" + "map/mapmarker/new/dltdzm_new.png");
                  lineTranslate = [0, 0];
                  break;
                case "排管":
                  imgUrl = require("@/assets/" + "map/mapmarker/new/dltdpg_new.png");
                  lineTranslate = [0, 0];
                  break;
                case "电缆沟":
                  imgUrl = require("@/assets/" + "map/mapmarker/new/dltddlg_new.png");
                  lineTranslate = [0, 0];
                  break;
                case "隧道":
                  imgUrl = require("@/assets/" + "map/mapmarker/new/dltdsd_new.png");
                  lineTranslate = [0, 0];
                  break;
              }
              imgId = "poi" + j;
              setLineWithImage();
              break;
          }
          // 设置生成的线用图片展示
          function setLineWithImage() {
            if (that.lineimagesList.indexOf(imgId) === -1) {
              that.lineimagesList.push(lineId);
              const linesLngLat = [[PiontIDArrays[0].lng, PiontIDArrays[0].lat], [PiontIDArrays[1].lng, PiontIDArrays[1].lat]]
              var polyMapsPointline = L.polyline(linesLngLat);
              var symbollien = L.pictureLineSymbol({ stroke: true, fill: false, opacity: 1, image: imgUrl, imageWidth: 5, imageHeight: 5 });
              var graphiclineLayer = L.graphic(polyMapsPointline, symbollien, {
                id: lineId
              }).addTo(highlightMapsLayer);
              graphiclineLayer.on('click', function (e) {
                clickChangePoint(e)
              })
              that.mapsAllLinesPoly.push(polyMapsPointline)
              that.mapsAllLinesGraphic.push(graphiclineLayer)
            }
          }
          // 设置生成的线用虚线或者直线
          function setLineWithlines(val) {
            // val 0表示拆除 1表示380v新建  2表示220v新建
            if (that.drawlineIdList.indexOf(lineId) === -1) {
              that.drawlineIdList.push(lineId);
              const linesLngLat = [[PiontIDArrays[0].lng, PiontIDArrays[0].lat], [PiontIDArrays[1].lng, PiontIDArrays[1].lat]]
              var polyMapsPointline = L.polyline(linesLngLat);
              var symbollien = L.simpleLineSymbol(
                {
                  style: 'solid',
                  stroke: true,
                  weight: 2, color: lineStyle.lineColor,
                  fill: false,
                  opacity: 1,
                  lineDash: lineStyle.lineDasharray
                }
              );
              var graphiclineLayer = L.graphic(polyMapsPointline, symbollien, {
                id: lineId,
                showStype: val
              }).addTo(highlightMapsLayer);
              graphiclineLayer.on('click', function (e) {
                clickChangePoint(e)
              })
              that.mapsAllLinesPoly.push(polyMapsPointline)
              that.mapsAllLinesGraphic.push(graphiclineLayer)
            }
          }
          // 点击线进行操作 删除  修改
          // 修改线的方法
          // eslint-disable-next-line no-inner-declarations
          function clickChangePoint(e) {
            // 电缆井是在土建路径上绘制的 会出现点击电缆井覆盖物和土建路径线同时触发的情况 所以需要判断如果点了电缆井覆盖物
            // 就取消线点击事件
            that.isClickLines = true;
            if (that.isClickDlj) return false;
            // 新增杆的时候避免点击到修改线
            if (that.isAddPointUnAddLine) return false;
            that.isGoheadPoint = false;
            if (that.isAddDifLine) {
              const typeArr = [
                "ZXLHZ",
                "FXLHZ",
                "TGBJHZ",
                "DXHZ",
                "GLHZ",
                "JKDLHZ",
              ];
              switch (that.submitType) {
                // 电缆井绘制时候
                case 17:
                  if (moduleType === "DLTDHZ") {
                    that.paramsChangeSubmit(
                      Number(e.latlng.lat),
                      Number(e.latlng.lng)
                    );
                  } else {
                    return Toast.fail("请选择土建路径");
                  }
                  break;
                // 土建路径 // 电缆保护管
                case 16:
                case 14:
                  if (["DLXLHZ", "BDZHZ", "SBLJHZ"].includes(moduleType)) {
                    that.startPointId.startId = that.mapAllLineList[j].startId;
                    that.startPointId.endId = that.mapAllLineList[j].endId;
                    that.paramsChangeSubmit("", "");
                  } else {
                    return Toast.fail("请选择电缆");
                  }
                  break;
                // 交叉跨越
                case 6:
                  if (typeArr.includes(moduleType)) {
                    // 交叉跨越的时候选择当前点位
                    setTimeout(() => {
                      that.isGoheadPoint = true;
                      that.isChooseAfterPoint = true;
                      that.isAddDifLine = true;
                      // 计算交叉跨域落点线在思极地图上与正北方向的弧度
                      const rad = Math.PI / 180;
                      const lat1 = that.mapAllLineList[j].latA * rad;
                      const lat2 = that.mapAllLineList[j].latB * rad;
                      const lon1 = that.mapAllLineList[j].lonA * rad;
                      const lon2 = that.mapAllLineList[j].lonB * rad;
                      const a = Math.sin(lon2 - lon1) * Math.cos(lat2);
                      const b =
                        Math.cos(lat1) * Math.sin(lat2) -
                        Math.sin(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1);
                      // 将弧度转换成角度，交叉跨越的旋转角度90减去与正北的夹角 则为落点线与正东(x轴正半轴)方向的夹角
                      const countAngle = 90 - that.radiansToDegrees(Math.atan2(a, b));
                      // 交叉跨域的旋转角度为输入角度+落点线与x轴的夹角
                      // that.subMitData.angle = Number(that.subMitData.angle) + Number(countAngle);
                      that.subMitData.angle = 0
                      that.paramsChangeSubmit(Number(e.latlng.lat), Number(e.latlng.lng));
                    }, 1000);
                  } else {
                    return Toast.fail("请选择符合规则的线");
                  }
                  break;
              }
            } else {
              // 排杆的功能只能出现在主线路绘制 分支线路绘制 导线绘制上
              let removeLine = null;
              // 这里存在删除一根导线 在删除的时候 线数组改变了 找不到对应的那条数据了
              for (let k = 0; k < that.mapAllLineList.length; k++) {
                if (lineId === that.mapAllLineList[k].lineId) {
                  removeLine = that.mapAllLineList[k];
                }
              }
              const moduleType = removeLine.moduleType;
              const paramsData = {
                moduleType: removeLine.moduleType,
                startId: removeLine.startId,
                endId: removeLine.endId,
                lineId: removeLine.lineId,
                imgId: imgId,
              };
              const params = JSON.stringify(paramsData);
              const typeArr = ["ZXLHZ", "FXLHZ"];
              if (!typeArr.includes(moduleType)) {
                let changeTxt = null;
                switch (moduleType) {
                  case "GLHZ":
                    changeTxt = "修改光缆信息";
                    break;
                  case "JKDLHZ":
                    changeTxt = "修改架空电缆信息";
                    break;
                  case "BDZHZ":
                  // case "DLXLHZ":
                  case "SBLJHZ":
                    changeTxt = "修改电缆信息";
                    break;
                  case "TGBJHZ":
                    changeTxt = "修改同杆并架信息";
                    break;
                  case "DXHZ":
                    changeTxt = "修改导线信息";
                    break;
                  case "DLBHGHZ":
                    changeTxt = "修改电缆保护管信息";
                    break;
                  case "DLTDHZ":
                    changeTxt = "修改土建路径信息";
                    break;
                }
                // 复杂类型比如线的弹出框需要将弹出层加地图上，不能再加到线的graphic
                L.popup().setMapPoint([e.latlng.lng, e.latlng.lat]).setContent(
                  `<div class="" onclick=showLineDetails('${params}')>${changeTxt}</div>
                      <div class="" onclick=removeLines('${params}')>删除</div>
                      <div class="" onclick="hidePop()">取消</div>
                    `
                ).openOn(mapData.map);
              } else {
                if (moduleType === "ZXLHZ" || moduleType === "FXLHZ") {
                  L.popup().setMapPoint([e.latlng.lng, e.latlng.lat]).setContent(
                    `
                        <div class="" onclick=showLineDetails('${params}')>修改导线选型</div>
                          <div class="" onclick=rankGt('${params}')>排杆</div>
                          <div class="" onclick=removeLines('${params}')>删除</div>
                          <div class="" onclick="hidePop()">取消</div>
                        `
                  ).openOn(mapData.map);
                }
              }
            }
          }
        }
      });
    },
    /**
     * 地图层级改变后设置地图的点
     */
    setPointsStyle(val) {
      // 显示和解绑杆塔上的编号，推导的自动选型的型号
      if (this.allPointList.length === 0) return false


      for (let j = 0; j < this.allPointList.length; j++) {
        // 避免重复加载
        switch (this.allPointList[j].source) {
          case '中台':
            if (val >= 19) {
              this.mapsAllPointsGraphic[j].unbindTooltip()
              const pointTypedesc = this.allPointList[j].autoModuleDes === undefined ? '' : this.allPointList[j].autoModuleDes
              //现状数据点名称mark
              const showTexts = `<p class='marks'><span>${this.allPointList[j].moduleType !== '' ? this.allPointList[j].mark : ''}</span><br /><span>${pointTypedesc}</span></p>`
              this.mapsAllPointsGraphic[j].bindTooltip(showTexts, {
                offset: [0, -10],
                permanent: true, // 永久打开弹出框
                direction: 'top', // 提示语的位置
                className: "directiveMarks", // 自定义的显示文字的弹框
              }).openTooltip();
            } else {
              this.mapsAllPointsGraphic[j].unbindTooltip()
            }
            break
          // source等于空或者等于设计的时候 表示是设计类型的数据
          case '':
          case '设计':
            // LDA 让mark文字等于19层的时候显示
            if (val >= 19) {
              // 需要先解绑，避免在大于16层级来回缩放时候，重复绑定多个

              this.mapsAllPointsGraphic[j].unbindTooltip()
              const pointTypedesc = this.allPointList[j].autoModuleDes === undefined ? '' : this.allPointList[j].autoModuleDes
              const showTexts = `<p class='marks'><span>${this.allPointList[j].mark}</span><br /><span>${pointTypedesc}</span></p>`
              this.mapsAllPointsGraphic[j].bindTooltip(showTexts, {
                offset: [0, -10],
                permanent: true, // 永久打开弹出框
                direction: 'top', // 提示语的位置
                className: "directiveMarks", // 自定义的显示文字的弹框
              }).openTooltip();
            } else {
              this.mapsAllPointsGraphic[j].unbindTooltip()
            }
            break
        }
      }
    },
    /**
     * 地图层级改变后设置地图覆盖物和线样式
     * val 地图层级
     */
    setLineStyle(val) {
      const list = this.mapAllLineList;
      const imgList = this.lineimagesList;
      const that = this;
      if (list.length === 0) return false;
      // 这里判断存放图片id和线id数组为空在去渲染线 避免层级缩放太快导致的错误
      for (let j = 0; j < list.length; j++) {
        if (that.currentIsAfter) {
          if (list[j].state === "拆除") {
            continue;
          }
        } else {
          if (list[j].state === "新建") {
            continue;
          }
        }
        list[j].lonA = Number(list[j].lonA);
        list[j].latA = Number(list[j].latA);
        list[j].lonB = Number(list[j].lonB);
        list[j].latB = Number(list[j].latB);
        let PiontIDArrays;
        if (list[j].source === "中台") {
          PiontIDArrays = [
            [
              Number(list[j].lonA),
              Number(list[j].latA),
            ],
            [
              Number(list[j].lonB),
              Number(list[j].latB),
            ],
          ];
        }
        let showLineText
        if (list[j].source !== "中台") {
          showLineText = list.lineModel === null ? Number(list[j].lineLength).toFixed(0) + "m" : list[j].lineModel + "/" + Number(list[j].lineLength).toFixed(0) + "m";
        } else {
          showLineText = Number(list[j].lineLength).toFixed(0) + "m"
        }
        const moduleType = list[j].moduleType;
        /*
          @steps1 判断是不是主线路,分支线路,架空线路类型线
          @steps2 需要将一个杆下的三回线路按照第一根线不偏移 第二根线向上便宜 第三根线向下便宜绘制
          @steps3 计算点与杆塔的切点
          @steps4 根据电压类型绘制不同类型的线
        */
        // 16层级的时候杆塔图元是半径是15
        // 第一步 将经纬度转换成平面直角坐标
        const transStartlnglat = mapData.map.project([Number(list[j].latA), Number(list[j].lonA),]);
        const transEndlnglat = mapData.map.project([Number(list[j].latB), Number(list[j].lonB),]);
        if (that.mapAllLineList[j].source === "中台") {
          PiontIDArrays = [
            { lng: list[j].lonA, lat: list[j].latA },
            { lng: list[j].lonB, lat: list[j].latB }
          ]
        }
        if (moduleType === "DLTDHZ") {
          PiontIDArrays = [
            { lng: list[j].lonA, lat: list[j].latA },
            { lng: list[j].lonB, lat: list[j].latB }
          ]
        } else {
          if (list[j].countLine !== 1) {
            // countLine 两个杆之间总共有几根线
            // indexLine 两个杆下的第几根线
            switch (list[j].indexLine) {
              // 多回路下的第一根线
              case 0:
                PiontIDArrays = [
                  { lng: list[j].lonA, lat: list[j].latA },
                  { lng: list[j].lonB, lat: list[j].latB }
                ]
                break;
              // 多回路下的第二根线
              case 1:
                const twoLine_start = mapData.map.unproject([transStartlnglat.x + 8, transStartlnglat.y + 8])
                const twoLine_end = mapData.map.unproject([transEndlnglat.x + 8, transEndlnglat.y + 8])
                PiontIDArrays = [twoLine_start, twoLine_end]
                break;
              // 多回路下的第三根线
              case 2:
                const thrLine_start = mapData.map.unproject([transStartlnglat.x - 8, transStartlnglat.y - 8])
                const thrLine_end = mapData.map.unproject([transEndlnglat.x - 8, transEndlnglat.y - 8])
                PiontIDArrays = [thrLine_start, thrLine_end]
                break;
              case 3:
                PiontIDArrays = [
                  [transformstartLngLat4.lng, transformstartLngLat4.lat],
                  [transformendLngLat4.lng, transformendLngLat4.lat],
                ];
                break;
            }
          } else {
            PiontIDArrays = [
              { lng: list[j].lonA, lat: list[j].latA },
              { lng: list[j].lonB, lat: list[j].latB }
            ]
          }
        }
        const linesLngLat = [[PiontIDArrays[0].lng, PiontIDArrays[0].lat], [PiontIDArrays[1].lng, PiontIDArrays[1].lat]]
  // LDA 6.18
        that.mapsAllLinesPoly[j].setPoints(linesLngLat)
        that.mapsAllLinesGraphic[j].redraw()
        // 地图层级17级的时候展示距离和相关导线，避免间距太短，样式显示错乱
        if (val > 18) {
          /*给线上绑定线的型号和距离开始*/
          // 计算线段与正北方向对应的旋转度数
          const trnsformAngle = that.getLineAngle([that.mapAllLineList[j].lonA, that.mapAllLineList[j].latA], [that.mapAllLineList[j].lonB, that.mapAllLineList[j].latB])
          if (that.mapsAllLineMarkersIDS.indexOf(list[j].lineId + 'bj') === -1) {
            that.mapsAllLineMarkersIDS.push(list[j].lineId + 'bj')
            let symbol = L.textSymbol({
              text: showLineText,
              fontSize: 14,
              color: "red",
              textAlign: 'center',
              angle: -trnsformAngle,
            });
            const startPonitLon = Number(list[j].lonA) // 连线的起始点经度
            const startPonitLat = Number(list[j].latA) // 连线的起始点纬度
            const endPonitLon = Number(list[j].lonB) // 连线的结束点经度
            const endPonitat = Number(list[j].latB) // 连线的结束点纬度
            const midparamPointLon = Math.abs((startPonitLon - endPonitLon) / 2) // 计算出两点的中间点经度
            const midparamPointLat = Math.abs((startPonitLat - endPonitat) / 2) // 计算两点的中间点纬度
            const midPointLon = startPonitLon > endPonitLon ? midparamPointLon + endPonitLon : midparamPointLon + startPonitLon
            const midPointLat = startPonitLat > endPonitat ? midparamPointLat + endPonitat : midparamPointLat + startPonitLat
            const lineMarker = L.graphic(L.mapPoint(midPointLon, midPointLat), symbol).addTo(
              highlightMapsLayer
            );
            that.mapsAllLineMarkers.push(lineMarker)
          }
          /*给线上绑定线的型号和距离结束*/
          /* 给导线上绘制标记物开始 */
            // LDA 6.18 that.mapsAllLinesGraphic[j]?.attributes.showStyp 加了个问号
          if (that.mapsAllLinesGraphic[j].attributes.showStype !== undefined) {
            // 先判断页面上是不是存在，避免重复添加 graphic的bindTooltip只能是一个，这里需要处理如果绑定杆型了，需要解绑杆型， 在去绑定下面的x，////,//类型，要不会重复绑定杆型
            if (that.mapsAllIdentifyIDS.indexOf(list[j].lineId) === -1) {
              that.mapsAllIdentifyIDS.push(list[j].lineId)
            } else {
              that.mapsAllLinesGraphic[j].unbindTooltip()
            }
            let textShow
            switch (that.mapsAllLinesGraphic[j].attributes.showStype) {
              case "0":
                textShow = `<div class='directiv' style="transform: rotate(${slhptLineAngle}deg)">x</div>`
                break;
              case "1":
                // 表示380v 新建
                textShow = `<div class='directiv' style="transform: rotate(${slhptLineAngle}deg)">////</div>`
                break;
              case "2":
                // 表示220v 新建
                textShow = `<div class='directiv' style="transform: rotate(${slhptLineAngle}deg)">//</div>`
                break;
            }
            that.mapsAllLinesGraphic[j].bindTooltip(textShow, {
              permanent: true, // 永久打开弹出框
              direction: 'center', // 提示语的位置
              className: "directiveLines", // 自定义的显示文字的弹框
            }).openTooltip();
          }
          /* 给导线上绘制标记物结束 */
        } else {
          for (let j = 0; j < that.mapsAllLineMarkers.length; j++) {
            that.mapsAllLineMarkers[j].remove()
          }
          that.mapsAllLineMarkers = []
          that.mapsAllLineMarkersIDS = []
          that.mapsAllIdentifyIDS = []
        }
      }

    },
    choosePmsjt() {
      this.isChoosePdzfIconByPmbzt = true;
      Toast.fail({
        message: "请选择站房图源",
        duration: 1000,
      });
    },
    showPmbztsj(val) {
      if (val !== 3) {
        this.pmbztType = val;
        this.pmpzhtsjVisible = true;
        this.isChoosePdzfIconByPmbzt = false;
      }
      mapData.map.closePopup();
    },
    /**
     * 地图层级改变后改变地图的拉线效果
     */
    setLxPointStyle() {
      if (this.saveAnglePoint.length > 0) {
        // 这里是吧整个拉线的marker全部删了重新加的 思极的setLnglat方法得加source 后期有时间在优化
        for (let j = 0; j < this.saveAnglePoint.length; j++) {
          if (this.saveAnglePoint[j].source !== "中台") {
            for (let k = 0; k < this.mapsAllPointsGraphic.length; k++) {
              if (this.saveAnglePoint[j].pointId === this.mapsAllPointsGraphic[k].attributes.id) {
                const transStartlnglat = mapData.map.project([
                  Number(this.saveAnglePoint[j].latitude),
                  Number(this.saveAnglePoint[j].longitude),
                ]);
                const endPointpmzb = getEndpoint(
                  transStartlnglat,
                  20,
                  Number(this.saveAnglePoint[j].angle)
                );
                // 将平面直角坐标转换成经纬度
                const changeEndLngLat = mapData.map.unproject([
                  Math.abs(endPointpmzb.x),
                  Math.abs(endPointpmzb.y),
                ]);
                this.mapsAllPointsGraphic[k].geometry.x = changeEndLngLat.lng
                this.mapsAllPointsGraphic[k].geometry.y = changeEndLngLat.lat
                this.mapsAllPointsGraphic[k].redraw()
              }
            }
          }
        }
      }

    },
    /**
     * 返回上一级ku
     */
    onClickLeft() {
      this.setLastPoint();
      this.$router.push({
        path: "/index",
      });
      clearInterval(this.mapTimer);
    },
    /**
     * 切换面板const j in
     */
    changeCollapse(val) {
      // this.selectCheck.activeNames =
    },
    /**
     * 排杆功能下拉选
     */
    onConfirmRaneg(type, index, item) {
      const val = item.value;
      switch (type) {
        case 0:
          this.addParam.rangeGt.voltage = val;
          this.settingObj.rangeGt.voltage = false;
          let totowerType = "";
          // 220v和330v都按照0.4kv去查
          const valltege = val === "10kV" ? "10kV" : "0.4kV";
          for (let j = 0; j < this.settingObj.towerType.length; j++) {
            if (this.addParam.rangeGt.legendTypeKey === this.settingObj.towerType[j].value) { totowerType = this.settingObj.towerType[j].type; }
          }
          this.getRangeGt(1, 1, valltege, false);
          break;
        case 1:
          const vallteges =
            this.addParam.rangeGt.valltege === "10kV" ? "10kV" : "0.4kV";
          this.addParam.rangeGt.legendTypeKey = val;
          this.settingObj.rangeGt.legendTypeKey = false;
          this.getRangeGt(1, 1, vallteges);
          break;
        case 2:
          this.addParam.rangeGt.moudle = item.name;
          this.settingObj.rangeGt.moduleId = item.id;
          this.settingObj.rangeGt.moudle = false;
          break;
        case 3:
          this.addParam.rangeGt.legendTypeKey = val;
          this.settingObj.rangeGt.legendTypeKey = false;
          break;
        case 4:
          this.changeRadioObj.isShowZy = false;
          this.changeRadioObj.isShowDf = false;
          this.changeRadioObj.isShowDj = false;
          switch (val) {
            case "等分插入":
              this.changeRadioObj.isShowDf = true;
              break;
            case "等距插入":
              this.changeRadioObj.isShowDj = true;
              break;
            case "自由插入":
              this.changeRadioObj.isShowZy = true;
              break;
          }
          this.addParam.rangeGt.pointNum = "";
          this.addParam.rangeGt.batchType = val;
          this.settingObj.rangeGt.batchType = false;
          break;
      }
    },
    /**
     * 启用 提交
     * @params currentParm 从子组件传过来的数据
     */
    subMitDatas(currentParm) {
      const mapcenter = mapData.map.getCenter();
      this.selectCheck.isShowCpVis = 4; // 关闭选择的页面
      this.showChoseIncoIndex = -1;
      // 清空拉线赋的默认值
      this.drawLXHZStartPoint.lng = 0;
      this.drawLXHZStartPoint.lat = 0;
      $(".map-rightArea").hide();
      this.isReformMark = false;
      mapData.map.closePopup();
      this.submitType = currentParm.type;
      const that = this;
      this.subMitData = currentParm.param;
      this.subMitDataVis = currentParm.visParam;

      if (this.isGoheadPoint) {
        this.setLastPoint();
        this.isGoheadPoint = false;
      }
      switch (this.submitType) {
        // 主线路提交的接口
        case 0:
          const param_one = {
            projectId: this.$route.query.childProjectId, // 工程id
            moduleName: "JKXL", // 写死
            moduleType: "ZXLHZ", // 写死
            startPointId: this.lastPointId, // 开始点编号
            longitude: mapcenter.lng, // "经度",
            latitude: mapcenter.lat, // "纬度",
            voltage: this.subMitData.voltage,
            loopNum: this.subMitData.backLine,
            state: this.subMitData.state,
            pointModule: this.subMitData.towerTypeId,
            pointType: "", // 杆塔类型（直线 转角 耐张 终端） 这部分在选型时候设置
            legendTypeKey: this.subMitData.towerType,
            moduleId: this.subMitData.towerModelId,
            listArr: this.subMitData.listArr,
            note: this.subMitData.message,
            mark: this.subMitData.mark,
            imgFiles: this.subMitData.imgList,
            voiceFiles: this.subMitData.audioList,
            shaoJing: this.subMitData.gggSj,
            equipType: this.subMitData.towerSbModel,
            znhxx: this.subMitData.intelligentize,
            towerHeght: "",
          };
          if (this.subMitData.isShowtowerZJT)
            param_one.towerHeght = this.subMitData.zjtTg;
          if (this.subMitData.isShowtowerGGG)
            param_one.towerHeght = this.subMitData.gggHeight;
          // 判断有没有id决定走更新还是新建
          if (this.isEditPointParam.id === "") {
            // 判断页面上有没有点
            if (this.allPointList.length !== 0) {
              this.startPointId.startId = "";
              this.startPointId.endId = "";
              this.selectCheck.visible = false;
              Toast.success("请选择起始点");
              this.isGoheadPoint = false;
              this.isAddPointUnAddLine = true
              this.isChooseAfterPoint = true;
            } else {
              apipost("/t-dtf-app-point/addPoint", param_one).then(function (res) {
                that.initMap();
                that.isAddMarkerInit = true;
                that.isGoheadPoint = true;
                that.isReformMark = true;
                that.isChooseAfterPoint = false;
                that.selectCheck.visible = false;
                that.showChoseIncoIndex = 1;
              });
            }
          } else {
            param_one.pointId = this.isEditPointParam.id;
            param_one.longitude = this.isEditPointParam.lng;
            param_one.latitude = this.isEditPointParam.lat;
            apipost("/t-dtf-app-point/updatePoint", param_one).then(function (res) {
              // 隐藏点位弹框
              mapData.map.closePopup();
              that.setMapCenter();
              that.initMap();
              that.isGoheadPoint = false;
              that.isChooseAfterPoint = false;
              that.selectCheck.visible = false;
              Toast.success("更新成功!");
            });
          }
          break;
        // 主线路提交的接口  同杆并架提交的接口 同杆并架的需要选择起始杆和结束杆
        // 在点上绑定得点击事件里面去选择起始杆 起始杆内去走打点得逻辑
        case 1: // 分支线路绘制
          that.showChoseIncoIndex = 1;
          // 这里先设置个选择起始点得
          if (this.isEditPointParam.id === "") {
            this.startPointId.startId = "";
            this.startPointId.endId = "";
            this.selectCheck.visible = false;
            Toast.success("请选择起始点");
            this.isGoheadPoint = false;
            this.isChooseAfterPoint = true;
            this.isAddPointUnAddLine = true
            if (this.submitType === 14) {
              this.isAddDifLine = true;
            }
          } else {
            this.paramsChangeSubmit("", "");
          }
          break;
        case 2: // 同杆并架
        case 3: // 导线绘制
        case 21: // 设备连接绘制
        case 4: // 光缆绘制
          if (that.allGtPointList.length === 0) {
            Toast.fail("当前不存在杆塔数据");
          } else {
            if (this.isEditPointParam.id === "") {
              this.startPointId.startId = "";
              this.startPointId.endId = "";
              this.selectCheck.visible = false;
              Toast.success("请选择起始点");
              this.isGoheadPoint = false;
              this.isChooseAfterPoint = true;
              this.isAddPointUnAddLine = true
              if (this.submitType === 14) {
                this.isAddDifLine = true;
              }
            } else {
              this.paramsChangeSubmit("", "");
            }
          }
          break;
        case 5: // 低压下户
          // 这里先设置个选择起始点得
          if (that.allGtPointList.length === 0) {
            Toast.fail("当前不存在杆塔数据");
          } else {
            if (this.isEditPointParam.id === "") {
              this.startPointId.startId = "";
              this.startPointId.endId = "";
              this.selectCheck.visible = false;
              Toast.success("请选择起始点");
              this.isGoheadPoint = true;
              this.isChooseAfterPoint = true;
              this.isAddPointUnAddLine = true
              if (this.submitType === 14) {
                this.isAddDifLine = true;
              }
            } else {
              this.paramsChangeSubmit("", "");
            }
          }
          break;
        case 7: // 架空电缆
        case 15: // 电缆分支箱
        case 8: // 柱上变压器
        case 9: // 柱上设备
        case 10: // 拉线
        case 13: // 电缆头
        case 12: // 电缆线路
          // 这里先设置个选择起始点得
          if (this.isEditPointParam.id === "") {
            this.startPointId.startId = "";
            this.startPointId.endId = "";
            this.selectCheck.visible = false;
            Toast.success("请选择起始点");
            this.isGoheadPoint = false;
            this.isChooseAfterPoint = true;
          } else {
            this.paramsChangeSubmit("", "");
          }
          break;
        case 20: // 配电站
          // 这里先设置个选择起始点得
          this.subMitData.mark = this.isReformMark ? this.transform(this.subMitData.mark) : this.subMitData.mark;
          const param_nightten = {
            moduleType: "BDZHZ",
            moduleName: "TQ",
            projectId: this.$route.query.childProjectId,
            // 'startPointId': this.startPointId.startId,
            startPointId: "",
            longitude: mapcenter.lng,
            latitude: mapcenter.lat,
            state: this.subMitData.state,
            mark: this.subMitData.mark,
            lineCategory: this.subMitData.aisleType,
            plmType: this.subMitData.plmType,
            voltage: this.subMitData.valtage,
            listArr: this.subMitData.listArr,
            imgFiles: this.subMitData.imgList,
            voiceFiles: this.subMitData.audioList,
            note: this.subMitData.loactionMsg,
          };
          // 判断有没有id决定走更新还是新建
          if (this.isEditPointParam.id === "") {
            apipost("/t-dtf-app-point/addPoint", param_nightten).then(function (res) {
              that.setMapCenter();
              that.initMap();
              that.isReformMark = true;
              that.isGoheadPoint = true;
              that.showChoseIncoIndex = 3;
              $(".map-endkc").css({ display: "block" });
            });
          } else {
            param_nightten.pointId = this.isEditPointParam.id;
            param_nightten.longitude = this.isEditPointParam.lng;
            param_nightten.latitude = this.isEditPointParam.lat;
            apipost("/t-dtf-app-point/updatePoint", param_nightten).then(
              function (res) {
                // 隐藏点位弹框
                mapData.map.closePopup();
                that.setMapCenter();
                that.initMap();
                that.isGoheadPoint = false;
                that.isChooseAfterPoint = false;
                that.selectCheck.visible = false;
                Toast.success("更新成功!");
              }
            );
          }
          break;
        case 14: // 电缆保护管
        case 16: // 土建路径
          // 这里先设置个选择起始点得
          if (this.isEditPointParam.id === "") {
            this.startPointId.startId = "";
            this.startPointId.endId = "";
            this.selectCheck.visible = false;
            Toast.success("请选择电缆线路");
            this.isGoheadPoint = false;
            this.isChooseAfterPoint = true;
            this.isAddDifLine = true;
          } else {
            this.paramsChangeSubmit("", "");
          }
          break;
        case 18: // 配电站房
          this.subMitData.mark = this.isReformMark ? this.transform(this.subMitData.mark) : this.subMitData.mark;
          const param_eightten = {
            zfType: this.subMitData.planType,
            faType: this.subMitData.schemeId,
            hbState: this.subMitData.state,
            zfNum: this.subMitData.electNum,
            zfModel: this.subMitData.electModel,
            lineCategory: this.subMitData.aisleType,
            plmType: this.subMitData.plmType,
            voltage: this.subMitData.voltage,
            moduleType: "PDZFHZ",
            moduleName: "TQ",
            projectId: this.$route.query.childProjectId,
            // 'startPointId': this.startPointId.startId,
            endPointId: "",
            longitude: mapcenter.lng,
            latitude: mapcenter.lat,
            listArr: this.subMitData.listArr,
            note: this.subMitData.message,
            mark: this.subMitData.mark,
            imgFiles: this.subMitData.imgList,
            voiceFiles: this.subMitData.audioList,
            dljPosition: this.subMitData.loactionMsg,
          };
          // 判断有没有id决定走更新还是新建
          if (this.isEditPointParam.id === "") {
            // 判断页面上有没有点
            apipost("/t-dtf-app-point/addPoint", param_eightten).then(function (
              res
            ) {
              that.setMapCenter();
              that.initMap();
              that.showChoseIncoIndex = -1;
              that.isGoheadPoint = true;
              that.isChooseAfterPoint = false;
              that.selectCheck.visible = false;
            });
          } else {
            param_eightten.pointId = this.isEditPointParam.id;
            param_eightten.longitude = this.isEditPointParam.lng;
            param_eightten.latitude = this.isEditPointParam.lat;
            apipost("/t-dtf-app-point/updatePoint", param_eightten).then(
              function (res) {
                // 隐藏点位弹框
                mapData.map.closePopup();
                that.setMapCenter();
                that.initMap();
                that.isGoheadPoint = false;
                that.isChooseAfterPoint = false;
                that.selectCheck.visible = false;
                Toast.success("更新成功!");
              }
            );
          }
          break;
        case 6: // 交叉跨越
          // 这里先设置个选择起始点得
          if (this.isEditPointParam.id === "") {
            this.startPointId.startId = "";
            this.startPointId.endId = "";
            this.selectCheck.visible = false;
            Toast.success("请选择要落点的线");
            this.isGoheadPoint = false;
            this.isAddDifLine = true;
            this.isAddPointUnAddLine = false
            this.isChooseAfterPoint = true;
          } else {
            this.paramsChangeSubmit("", "");
          }
          break;
        case 17: // 电缆井
          // 这里先设置个选择起始点得
          if (this.isEditPointParam.id === "") {
            this.startPointId.startId = "";
            this.startPointId.endId = "";
            this.selectCheck.visible = false;
            Toast.success("请选择土建路径");
            this.isGoheadPoint = false;
            this.isAddDifLine = true;
            this.isChooseAfterPoint = true;
          } else {
            this.paramsChangeSubmit("", "");
          }
          break;
      }
    },
    /**
     * 自动排杆时候的自动选型
     */
    getRangePoles(arrays) {
      const that = this;
      for (let j = 0; j < arrays.length; j++) {
        let lineLength = 0;
        switch (arrays[j].loopNum) {
          case "单回":
            lineLength = 0;
            break;
          case "双回":
            lineLength = 1;
            break;
          case "三回":
            lineLength = 2;
            break;
          case "四回":
            lineLength = 3;
            break;
        }
        const params = {
          angle: 0, // 角度
          pointID: arrays[j].pointId,
          arrayMode: "", // 排列方式
          cementMaterialID: arrays[j].moduleId, // 水泥杆物料id
          gtlb: arrays[j].legendTypeKey, // 杆塔类别
          isTerminalPole: false, // 是否终端
          loopNumber: arrays[j].loopNum, // 回路数
          meteorological: this.basicSettingData.qxType, // 气象区
          spanLength: 0, // 总耐张长度
          strainLength: this.basicSettingData.nzLength, // 耐张段长度
          tddj: 0, // 档距
          equipType: arrays[j].equipType, // 设备类型
          voltage: arrays[j].voltage, // 电压等级
          zxArr: [this.basicSettingData.plArray[lineLength][0]], // 基础参数设置直线
          zjArr: [this.basicSettingData.plArray[lineLength][1]], // 基础参数设置转角
          nzArr: [this.basicSettingData.plArray[lineLength][2]], // 基础参数设置耐张
          zdArr: [this.basicSettingData.plArray[lineLength][3]], // 基础参数设置终端
        }; // 自动选型的入参
        if (j === 0 || j === arrays.length - 1) {
          params.isTerminalPole = true;
          params.arrayMode = this.getPlType(
            arrays[j].loopNum,
            "",
            this.basicSettingData.nzLength,
            true
          );
        } else {
          params.isTerminalPole = false;
          params.tddj = mapData.map.distance(
            [arrays[j - 1].longitude, arrays[j - 1].latitude],
            [arrays[j].longitude, arrays[j].latitude]
          );
          params.spanLength = mapData.map.distance(
            [arrays[j - 1].longitude, arrays[j - 1].latitude],
            [arrays[j].longitude, arrays[j].latitude]
          );
          params.arrayMode = this.getPlType(
            arrays[j].loopNum,
            params.angle,
            this.basicSettingData.nzLength,
            false
          );
        }
        apipost("/autoRule/getPole", params).then((res) => {
          if (res.code === 1001) {
            // 在最后一个点设置完自动选型后更新数据
            if (j === arrays.length - 1) {
              that.initMap();
            }
          }
        });
      }
    },
    /**
     * 采集当前点推导上一个杆塔的型号
     */
    getPoles(paramVal, lat, lng) {
      // 202286修改
      const that = this;
      let beginIndex = 2;
      let lineLength = 0;
      // 第一步 先把所有杆塔类型数据取出
      const allGtArr = []; // 所有主分杆塔数组
      const pointType = ["ZXLHZ", "FXLHZ", "JKDLHZ"];
      if (that.allPointList.length !== 0) {
        for (let j = 0; j < that.allPointList.length; j++) {
          if (pointType.includes(that.allPointList[j].moduleType)) {
            allGtArr.push(that.allPointList[j]);
          }
        }
      } else {
        that.initMap();
      }
      switch (paramVal.backLine) {
        case "单回":
          lineLength = 0;
          break;
        case "双回":
          lineLength = 1;
          break;
        case "三回":
          lineLength = 2;
          break;
        case "四回":
          lineLength = 3;
          break;
      }
      const params = {
        angle: 0, // 角度
        pointID: "",
        arrayMode: "", // 排列方式
        cementMaterialID: paramVal.towerModelId, // 水泥杆物料id
        gtlb: paramVal.towerType, // 杆塔类别
        isTerminalPole: true, // 是否终端
        loopNumber: paramVal.backLine, // 回路数
        meteorological: that.basicSettingData.qxType, // 气象区
        spanLength: 0, // 总耐张长度
        strainLength: that.basicSettingData.nzLength, // 耐张段长度
        equipType: paramVal.towerSbModel, // 设备类型
        tddj: 0, // 档距
        voltage: paramVal.voltage, // 电压等级
        zxArr: [that.basicSettingData.plArray[lineLength][0]], // 基础参数设置直线
        zjArr: [that.basicSettingData.plArray[lineLength][1]], // 基础参数设置转角
        nzArr: [that.basicSettingData.plArray[lineLength][2]], // 基础参数设置耐张
        zdArr: [that.basicSettingData.plArray[lineLength][3]], // 基础参数设置终端
      }; // 自动选型的入参
      if (paramVal.towerType === "钢管杆") {
        params.towerHeght = paramVal.gggHeight;
        params.shaoJing = paramVal.gggSj;
      } else if (params.towerType === "窄基塔") {
        params.towerHeght = paramVal.zjtTg;
      }
      // 第二步 推导上一个杆的排列方式 判断如果主分杆塔数组长度是1 说明被推导的杆是终端杆 如果不等于1 说明不是终端杆 需要计算被推导杆的角度
      if (allGtArr.length === 1) {
        params.arrayMode = that.getPlType(
          paramVal.backLine,
          "",
          that.basicSettingData.nzLength,
          true
        );
        params.pointID = allGtArr[allGtArr.length - 1].pointId;
        apipost("/autoRule/getPole", params).then((res) => {
          if (res.code === 1001) {
            if (that.isAddMarkerInit) {
              that.allGtPointList.splice(that.allGtPointList.length - 1, 1);
              that.mapsAllPointsGraphic[that.mapsAllPointsGraphic.length - 1].unbindTooltip()
              that.mapsAllPointsGraphic[that.mapsAllPointsGraphic.length - 1].removeEventListener()
              that.mapsAllPointsGraphic.splice(that.mapsAllPointsGraphic.length - 1, 1);
              that.poIntAllIdList.splice(that.poIntAllIdList.length - 1, 1);
            }
            that.initMap();
          }
        });
        // 调用自动选型接口
      } else if (allGtArr.length > 1) {
        const lat1 = allGtArr[allGtArr.length - 2].latitude;
        const lat2 = lat;
        const lat3 = allGtArr[allGtArr.length - 1].latitude;
        const lng1 = allGtArr[allGtArr.length - 2].longitude;
        const lng2 = lng;
        const lng3 = allGtArr[allGtArr.length - 1].longitude;
        params.pointID = allGtArr[allGtArr.length - 1].pointId;
        params.angle = that.getAngles(
          Number(lng1),
          Number(lat1),
          Number(lng2),
          Number(lat2),
          Number(lng3),
          Number(lat3)
        );
        params.arrayMode = that.getPlType(
          paramVal.backLine,
          params.angle,
          that.basicSettingData.nzLength,
          false
        );
        params.tddj = mapData.map.distance(
          [
            allGtArr[allGtArr.length - 2].longitude,
            allGtArr[allGtArr.length - 2].latitude,
          ],
          [
            allGtArr[allGtArr.length - 1].longitude,
            allGtArr[allGtArr.length - 1].latitude,
          ]
        );
        params.spanLength = getNzdLength(getPointLngLat());
        params.isTerminalPole = false;
        apipost("/autoRule/getPole", params).then((res) => {
          if (res.code === 1001) {
            // 在新踩杆塔点推导上一个点的自动选型型号时候 需要吧上个点位覆盖物删除了 重新更新dom
            if (that.poIntAllIdList.length > 0) {
              //每次更新对应点的时候都需要解绑之前事件removeEventListener 、unbindTooltip
              that.mapsAllPointsGraphic[that.mapsAllPointsGraphic.length - 1].unbindTooltip()
              that.mapsAllPointsGraphic[that.mapsAllPointsGraphic.length - 1].removeEventListener()
              that.mapsAllPointsGraphic.splice(that.mapsAllPointsGraphic.length - 1, 1);
              that.poIntAllIdList.splice(that.poIntAllIdList.length - 1, 1);
            }
            that.initMap();
          }
        });
        /* 计算耐张段总长度
         * 耐张段总长度是基于被推导杆之前的最近的一个耐张属性杆到被推导杆之间的档距和
         */

        // 获取耐张段经过点的经纬度
        function getPointLngLat() {
          const lngLatArr = [[Number(allGtArr[allGtArr.length - 1].longitude), Number(allGtArr[allGtArr.length - 1].latitude)]];
          const lnglat = [Number(allGtArr[allGtArr.length - beginIndex].longitude), Number(allGtArr[allGtArr.length - beginIndex].latitude)];
          lngLatArr.unshift(lnglat);
          if (allGtArr[allGtArr.length - beginIndex].autoIsNaiZhang === "1") {
            beginIndex = 2;
            return lngLatArr;
          } else {
            if (allGtArr.length === beginIndex) {
              const firPoint = [Number(allGtArr[0].longitude), Number(allGtArr[0].latitude)];
              return firPoint;
            } else {
              beginIndex = beginIndex + 1;
              return getPointLngLat();
            }
          }
        }
        function getNzdLength(nzArr) {
          let nzlength = 0;
          if (nzArr.length == 1) {
            return nzlength
          } else {
            for (let k = 0; k < nzArr.length; k++) {
              for (let s = 1; s < nzArr.length; s++) {
                nzlength = nzlength + mapData.map.distance(nzArr[k], nzArr[s]);
              }
            }
            return nzlength.toFixed(2);
          }
        }
      }
    },
    /**
     *  采点方法
     * 存在需要去选择起始杆情况的时候的踩点方法
     * @param lat
     * @param lng
     * @returns {*}
     */
    paramsChangeSubmit(lat, lng) {
      this.isChooseAfterPoint = true;
      const that = this;
      switch (this.submitType) {
        case 0:
          this.subMitData.mark = this.isReformMark ? this.transform(this.subMitData.mark) : this.subMitData.mark;
          const param_zg = {
            projectId: this.$route.query.childProjectId, // 工程id
            moduleName: "JKXL", // 写死
            moduleType: "ZXLHZ", // 写死
            startPointId: this.lastPointId, // 开始点编号
            longitude: lng, // "经度",
            latitude: lat, // "纬度",
            voltage: this.subMitData.voltage,
            loopNum: this.subMitData.backLine,
            state: this.subMitData.state,
            pointModule: this.subMitData.towerTypeId,
            pointType: "", // 杆塔类型（直线 转角 耐张 终端） 这部分在选型时候设置
            legendTypeKey: this.subMitData.towerType,
            moduleId: this.subMitData.towerModelId,
            listArr: this.subMitData.listArr,
            note: this.subMitData.message,
            mark: this.subMitData.mark,
            equipType: this.subMitData.towerSbModel,
            imgFiles: this.subMitData.imgList,
            znhxx: this.subMitData.intelligentize,
            voiceFiles: this.subMitData.audioList,
          };
          // 判断有没有id决定走更新还是新建
          if (this.isEditPointParam.id === "") {
            apipost("/t-dtf-app-point/addPoint", param_zg).then(function (res) {
              if (that.saveLine.isSaveLine) {
                const param = {
                  lonA: that.saveLine.lonA,
                  latA: that.saveLine.latA,
                  startId: that.saveLine.id,
                  projectId: that.$route.query.childProjectId,
                  lonB: lng,
                  latB: lat,
                  endId: res.data,
                  lineLength: mapData.map.distance([that.saveLine.lonA, that.saveLine.latA], [lng, lat]).toFixed(2),
                };
                apipost("/t-dtf-app-point/savePoint", param).then(function (res) {
                  that.saveLine.lonA = "";
                  that.saveLine.latA = "";
                  that.saveLine.id = "";
                  that.saveLine.isSaveLine = false;
                  // 踩点后的事儿 1更新点表 2推导自动选型 3更新点表的选型型号 4查询数据 重新渲染页面
                  that.isGoheadPoint = true;
                  that.isReformMark = true;
                  that.isChooseAfterPoint = false;
                  that.isAddMarkerInit = true;
                  const isShowAuxiliary =
                    sessionStorage.getItem("isShowAuxiliary");
                  if (isShowAuxiliary === "1") that.updateGuide();
                  // 如果选择了线路绘制的智能化选型 就参与推导型号
                  if (that.subMitData.intelligentize) {
                    that.getPoles(that.subMitData, lat, lng);
                  } else {
                    that.initMap();
                  }
                  $(".map-endkc").css({ display: "block" });
                });
              } else {
                // 踩点后的事儿 1更新点表 2推导自动选型 3更新点表的选型型号 4查询数据 重新渲染页面
                that.isGoheadPoint = true;
                that.isReformMark = true;
                that.isChooseAfterPoint = false;
                that.isAddMarkerInit = true;
                const isShowAuxiliary =
                  sessionStorage.getItem("isShowAuxiliary");
                if (isShowAuxiliary === "1") that.updateGuide();
                // 如果选择了线路绘制的智能化选型 就参与推导型号
                if (that.subMitData.intelligentize) {
                  that.getPoles(that.subMitData, lat, lng);
                } else {
                  that.initMap();
                }
                $(".map-endkc").css({ display: "block" });
              }
            });
          } else {
            param_zg.pointId = that.isEditPointParam.id;
            param_zg.longitude = that.isEditPointParam.lng;
            param_zg.latitude = that.isEditPointParam.lat;
            /*
               自动选型步骤
               1.先判断是不是第一个点或者是不是终端点
               2.需要判断起始点是不是耐张类型 计算耐张长度
            */
            apipost("/t-dtf-app-point/updatePoint", param_zg).then(function (
              res
            ) {
              // 隐藏点位弹框
              mapData.map.closePopup();
              that.setMapCenter();
              that.initMap();
              that.isGoheadPoint = false;
              Toast.success("更新成功!");
            });
          }
          break;
        // 分支线路
        case 1:
          this.subMitData.mark = this.isReformMark
            ? this.transform(this.subMitData.mark)
            : this.subMitData.mark;
          const param_one = {
            projectId: this.$route.query.childProjectId, // 工程id
            moduleName: "JKXL", // 写死
            moduleType: "FXLHZ", // 写死
            startPointId: this.lastPointId, // 开始点编号
            equipType: this.subMitData.towerSbModel, // 分支线路的设备类型
            longitude: lng, // "经度",
            latitude: lat, // "纬度",
            voltage: this.subMitData.voltage,
            loopNum: this.subMitData.backLine,
            state: this.subMitData.state,
            pointType: "", // 杆塔类型（直线 转角 耐张 终端） 这部分在选型时候设置
            legendTypeKey: this.subMitData.towerType,
            moduleId: this.subMitData.towerModelId,
            gtType: this.subMitData.branch,
            listArr: this.subMitData.listArr,
            note: this.subMitData.message,
            mark: this.subMitData.mark,
            imgFiles: this.subMitData.imgList,
            voiceFiles: this.subMitData.audioList,
            shaoJing: this.subMitData.gggSj,
            towerHeght: "",
          };
          if (this.subMitDataVis.isShowtowerZJT)
            param_one.towerHeght = this.subMitData.zjtTg;
          if (this.subMitDataVis.isShowtowerGGG)
            param_one.towerHeght = this.subMitData.gggHeight;
          // 判断有没有id决定走更新还是新建
          if (this.isEditPointParam.id === "") {
            apipost("/t-dtf-app-point/addPoint", param_one).then(function (res) {
              that.isGoheadPoint = true;
              that.isChooseAfterPoint = false;
              that.isReformMark = true;
              that.isAddMarkerInit = true;
              const isShowAuxiliary = sessionStorage.getItem("isShowAuxiliary");
              if (isShowAuxiliary === "1") that.updateGuide();
              that.getPoles(that.subMitData, lat, lng);
              $(".map-endkc").css({ display: "block" });
            });
          } else {
            param_one.pointId = this.isEditPointParam.id;
            param_one.longitude = this.isEditPointParam.lng;
            param_one.latitude = this.isEditPointParam.lat;
            apipost("/t-dtf-app-point/updatePoint", param_one).then(function (
              res
            ) {
              // 隐藏点位弹框
              mapData.map.closePopup();
              that.setMapCenter();
              that.initMap();
              that.isGoheadPoint = false;
              that.isChooseAfterPoint = false;
              that.selectCheck.visible = false;
              Toast.success("更新成功!");
            });
          }
          break;
        // 同杆并架
        case 2:
          const param_two = {
            moduleType: "TGBJHZ",
            moduleName: "JKXL",
            startPointId: this.startPointId.startId,
            endPointId: this.startPointId.endId,
            gtType: this.subMitData.lineState,
            voltage: this.subMitData.voltage,
            lineName: this.subMitData.lineName,
            lineState: this.subMitData.state,
            lineModelId: this.subMitData.lineModelId,
            legendTypeKey: "线的类型",
            startDoTime: this.startPointTime.startTime,
            endDoTime: this.startPointTime.endTime,
            projectId: this.$route.query.childProjectId, // 工程id
            note: this.subMitData.message,
          };
          if (this.isEditPointParam.id === "") {
            apipost("/t-dtf-app-point/addPoint", param_two).then(function (res) {
              that.isChooseAfterPoint = false;
              that.isGoheadPoint = false;
              that.setMapCenter();
              that.initMap();
            });
          } else {
            param_two.startPointId = this.isEditLineParam.startId;
            param_two.endPointId = this.isEditLineParam.endId;
            param_two.lineId = this.isEditPointParam.id;
            apipost("/t-dtf-app-point/updateLines", param_two).then(function (
              res
            ) {
              // 隐藏点位弹框
              mapData.map.closePopup();
              Toast.success("更新成功");
              that.setMapCenter();
              that.initMap();
              that.isGoheadPoint = false;
              that.isChooseAfterPoint = false;
              that.selectCheck.visible = false;
            });
          }
          break;
        // 导线型号
        case 3:
          const param_thr = {
            moduleType: "DXHZ",
            moduleName: "JKXL",
            startPointId: this.startPointId.startId,
            endPointId: this.startPointId.endId,
            startDoTime: this.startPointTime.startTime,
            endDoTime: this.startPointTime.endTime,
            projectId: this.$route.query.childProjectId,
            loopNum: this.subMitData.backLine,
            voltage: this.subMitData.voltage,
            listArr: this.subMitData.listArr,
            note: this.subMitData.message,
          };
          if (this.isEditPointParam.id === "") {
            for (let j = 0; j < this.mapAllLineList.length; j++) {
              if (this.mapAllLineList[j].startPointId === this.startPointId.startId && this.mapAllLineList[j].endPointId === this.startPointId.endId
              ) {
                return Toast.fail("当前杆下已存在导线");
              }
            }
            apipost("/t-dtf-app-point/addPoint", param_thr).then(function (res) {
              that.isChooseAfterPoint = false;
              that.isGoheadPoint = false;
              that.setMapCenter();
              that.initMap();
            });
          } else {
            param_thr.startPointId = this.isEditLineParam.startId;
            param_thr.endPointId = this.isEditLineParam.endId;
            param_thr.lineId = this.isEditPointParam.id;
            apipost("/t-dtf-app-point/updateLines", param_thr).then(function (
              res
            ) {
              // 隐藏点位弹框
              mapData.map.closePopup();
              Toast.success("更新成功");
              that.isGoheadPoint = false;
              that.isChooseAfterPoint = false;
              that.selectCheck.visible = false;
            });
          }
          break;
        // 光缆型号
        case 4:
          const arrlist = [];
          for (let j = 0; j < Number(this.addParam.opticCable.opticNum); j++) {
            const obj = {
              lineState: this.addParam.opticCable.opticState,
              lineModelId: this.addParam.opticCable.opticModelId,
              lineLength: this.addParam.opticCable.opticName,
            };
            arrlist.push(obj);
          }
          const param_four = {
            moduleType: "GLHZ",
            moduleName: "JKXL",
            startPointId: this.startPointId.startId,
            endPointId: this.startPointId.endId,
            startDoTime: this.startPointTime.startTime,
            endDoTime: this.startPointTime.endTime,
            glNum: this.addParam.opticCable.opticNum,
            projectId: this.$route.query.childProjectId,
            listArr: arrlist,
            note: this.addParam.opticCable.message,
            imgFiles: this.addParam.opticCable.imgList,
            voiceFiles: this.addParam.opticCable.audioList,
          };
          if (this.isEditPointParam.id === "") {
            for (let j = 0; j < this.mapAllLineList.length; j++) {
              if (
                this.mapAllLineList[j].startPointId ===
                this.startPointId.startId &&
                this.mapAllLineList[j].endPointId === this.startPointId.endId
              ) {
                return Toast.fail("当前杆下已存在导线");
              }
            }
            apipost("/t-dtf-app-point/addPoint", param_four).then(function (
              res
            ) {
              that.isChooseAfterPoint = false;
              that.setMapCenter();
              that.initMap();
            });
          } else {
            param_four.startPointId = this.isEditLineParam.startId;
            param_four.endPointId = this.isEditLineParam.endId;
            param_four.lineId = this.isEditPointParam.id;
            apipost("/t-dtf-app-point/updateLines", param_four).then(function (
              res
            ) {
              // 隐藏点位弹框
              mapData.map.closePopup();
              Toast.success("更新成功");
              that.setMapCenter();
              that.initMap();
              that.isGoheadPoint = false;
              that.isChooseAfterPoint = false;
              that.selectCheck.visible = false;
            });
          }
          break;
        // 低压下户
        case 5:
          this.subMitData.mark = this.isReformMark
            ? this.transform(this.subMitData.mark)
            : this.subMitData.mark;
          const param_five = {
            moduleType: "DYXHHZ",
            moduleName: "JKXL",
            startPointId: this.startPointId.startId,
            longitude: lng,
            latitude: lat,
            hbType: this.subMitData.householdType,
            hbModuleId: this.subMitData.householdModelId,
            hbState: this.subMitData.householdState,
            hbLocation: this.subMitData.householdSite,
            hbNum: this.subMitData.householdNum,
            projectId: this.$route.query.childProjectId,
            note: this.subMitData.message,
            mark: this.subMitData.mark,
            imgFiles: this.subMitData.imgList,
            voiceFiles: this.subMitData.audioList,
            listArr: this.subMitData.listArr,
          };
          if (this.isEditPointParam.id === "") {
            apipost("/t-dtf-app-point/addPoint", param_five).then(function (
              res
            ) {
              that.isGoheadPoint = true;
              that.setMapCenter();
              that.initMap();
              that.isReformMark = true;
              that.isChooseAfterPoint = false;
              $(".map-endkc").css({ display: "block" });
              const isShowAuxiliary = sessionStorage.getItem("isShowAuxiliary");
              if (isShowAuxiliary === "1") that.updateGuide();
            });
          } else {
            param_five.pointId = this.isEditPointParam.id;
            param_five.longitude = this.isEditPointParam.lng;
            param_five.latitude = this.isEditPointParam.lat;
            apipost("/t-dtf-app-point/updatePoint", param_five).then(function (
              res
            ) {
              // 隐藏点位弹框
              mapData.map.closePopup();
              Toast.success("更新成功");
              that.setMapCenter();
              that.initMap();
              that.isGoheadPoint = false;
              that.isChooseAfterPoint = false;
              that.selectCheck.visible = false;
              $(".map-endkc").css({ display: "none" });
            });
          }
          break;
        // 交叉跨域
        case 6:
          this.subMitData.mark = this.isReformMark ? this.transform(this.subMitData.mark) : this.subMitData.mark;
          const param_six = {
            moduleType: "JCKYHZ",
            moduleName: "JKXL",
            longitude: lng,
            latitude: lat,
            crossoverLevel: this.subMitData.leverl ? 1 : 0,
            crossoverType: this.subMitData.type,
            crossoverHigh: this.subMitData.high,
            angle: this.subMitData.angle,
            crossoverWidth: this.subMitData.width,
            projectId: this.$route.query.childProjectId,
            mark: this.subMitData.mark,
            note: this.subMitData.message,
            imgFiles: this.subMitData.imgList,
            voiceFiles: this.subMitData.audioList,
          };
          if (this.isEditPointParam.id === "") {
            apipost("/t-dtf-app-point/addPoint", param_six).then(function (res) {
              that.isGoheadPoint = true;
              that.isChooseAfterPoint = false;
              that.isReformMark = true;
              that.setMapCenter();
              that.initMap();
              that.isAddDifLine = true;
              that.isClickLines = false;
              $(".map-endkc").css({ display: "block" });
            });
          } else {
            param_six.pointId = this.isEditPointParam.id;
            param_six.longitude = this.isEditPointParam.lng;
            param_six.latitude = this.isEditPointParam.lat;
            apipost("/t-dtf-app-point/updatePoint", param_six).then(function (
              res
            ) {
              // 隐藏点位弹框
              mapData.map.closePopup();
              Toast.success("更新成功");
              that.setMapCenter();
              that.poIntAllIdList = [];
              that.initMap();
              that.isGoheadPoint = false;
              that.isChooseAfterPoint = false;
              that.selectCheck.visible = false;
              $(".map-endkc").css({ display: "none" });
            });
          }
          break;
        // 架空电缆
        case 7:
          this.subMitData.mark = this.isReformMark
            ? this.transform(this.subMitData.mark)
            : this.subMitData.mark;
          const param_seven = {
            moduleType: "JKDLHZ",
            moduleName: "JKXL",
            equipType: this.subMitData.towerSbModel,
            drawSettings: this.subMitData.drawSet,
            startPointId: this.startPointId.startId,
            endPointId: this.startPointId.endId,
            startDoTime: this.startPointTime.startTime,
            endDoTime: this.startPointTime.endTime,
            longitude: lng,
            latitude: lat,
            state: this.subMitData.state,
            voltage: this.subMitData.voltage,
            dlNum: this.subMitData.backLine,
            pointModule: this.subMitData.towerModelId,
            projectId: this.$route.query.childProjectId,
            listArr: this.subMitData.listArr,
            note: this.subMitData.message,
            mark: this.subMitData.mark,
            imgFiles: this.subMitData.imgList,
            voiceFiles: this.subMitData.audioList,
          };
          if (this.isEditPointParam.id === "") {
            apipost("/t-dtf-app-point/addPoint", param_seven).then(function (res) {
              if (that.subMitData.drawSet === "绘制起始杆") {
                that.isGoheadPoint = true;
                that.isReformMark = true;
                that.showChoseIncoIndex = 1;
                // 架空电缆的杆塔默认是水泥杆
                that.subMitData.towerType = "水泥杆";
                that.getPoles(that.subMitData, lat, lng);
                $(".map-endkc").css({ display: "block" });
              } else {
                that.isGoheadPoint = false;
                that.initMap();
                $(".map-endkc").css({ display: "none" });
              }
              that.isChooseAfterPoint = false;
            });
          } else {
            if (this.addParam.overheadLine.drawSet === "绘制起始杆") {
              param_seven.pointId = this.isEditPointParam.id;
              param_seven.longitude = this.isEditPointParam.lng;
              param_seven.latitude = this.isEditPointParam.lat;
              apipost("/t-dtf-app-point/updatePoint", param_seven).then(
                function (res) {
                  // 隐藏点位弹框
                  mapData.map.closePopup();
                  Toast.success("更新成功");
                  that.setMapCenter();
                  that.initMap();
                  that.isGoheadPoint = false;
                  that.isChooseAfterPoint = false;
                  that.selectCheck.visible = false;
                  that.settingObj.isDisabledDraw = false;
                }
              );
            } else {
              param_seven.startPointId = this.isEditLineParam.startId;
              param_seven.endPointId = this.isEditLineParam.endId;
              param_seven.lineId = this.isEditPointParam.id;
              apipost("/t-dtf-app-point/updateLines", param_seven).then(
                function (res) {
                  // 隐藏点位弹框
                  mapData.map.closePopup();
                  Toast.success("更新成功");
                  that.setMapCenter();
                  that.initMap();
                  that.isGoheadPoint = false;
                  that.isChooseAfterPoint = false;
                  that.selectCheck.visible = false;
                  that.settingObj.isDisabledDraw = false;
                }
              );
            }
          }
          break;
        // 柱上变压器
        case 8:
          const mapcenterZsb = mapData.map.getCenter();
          // 以地图中心点作为开始点
          const startLnglatZsb = {
            longitude: Number(mapcenterZsb.lng),
            latitude: Number(mapcenterZsb.lat),
          };
          // 以基础点作为结束点
          const endStarLnglatZsb = {
            longitude: that.drawLXHZStartPoint.lng,
            latitude: that.drawLXHZStartPoint.lat,
          };
          const angleZsb = that.bearing(startLnglatZsb, endStarLnglatZsb);
          // 状态 mark 重复
          const param_eight = {
            moduleType: "ZSBYQHZ",
            moduleName: "TQ",
            longitude: lng,
            latitude: lat,
            projectId: this.$route.query.childProjectId,
            state: this.subMitData.state,
            zsbtProgramType: this.subMitData.planTypeId,
            zsbtProgram: this.subMitData.planeId,
            isCht: this.subMitData.wholeSet === "是" ? 1 : 0,
            btTq: this.subMitData.courts,
            btNum: this.subMitData.courtsNum,
            isJd: this.subMitData.isGround === "是" ? 1 : 0,
            note: this.subMitData.message,
            mark: this.subMitData.mark,
            imgFiles: this.subMitData.imgList,
            voiceFiles: this.subMitData.audioList,
            angle: angleZsb,
            // 副杆的参数
            voltage: this.subMitData.voltage,
            loopNum: this.subMitData.backLine,
            pointModule: this.subMitData.towerTypeId,
            pointType: "", // 杆塔类型（直线 转角 耐张 终端） 这部分在选型时候设置
            legendTypeKey: this.subMitData.towerType,
            moduleId: this.subMitData.towerModelId,
            listArr: this.subMitData.listArr,
            shaoJing: this.subMitData.gggSj,
            equipType: this.subMitData.towerSbModel,
          };
          if (this.isEditPointParam.id === "") {
            for (let j = 0; j < this.allPointList.length; j++) {
              if (this.allPointList[j].moduleType === "ZSBYQHZ") {
                if (Number(this.allPointList[j].longitude) === lng) {
                  return Toast.fail("当前杆塔上已存在柱上变压器");
                }
              }
            }
            apipost("/t-dtf-app-point/addPoint", param_eight).then(function (res) {
              that.setMapCenter();
              that.initMap();
              that.isReformMark = true;
              that.showChoseIncoIndex = 1
              that.isChooseAfterPoint = false;
              that.isGoheadPoint = false;
              $(".map-endkc").css({ display: "block" });
            });
          } else {
            param_eight.pointId = this.isEditPointParam.id;
            param_eight.longitude = this.isEditPointParam.lng;
            param_eight.latitude = this.isEditPointParam.lat;
            apipost("/t-dtf-app-point/updatePoint", param_eight).then(function (res) {
              // 隐藏点位弹框
              mapData.map.closePopup();
              Toast.success("更新成功");
              that.setMapCenter();
              that.initMap();
              that.isGoheadPoint = false;
              that.isChooseAfterPoint = false;
              that.selectCheck.visible = false;
            });
          }
          break;
        // 柱上设备绘制
        case 9:
          this.subMitData.mark = this.isReformMark ? this.transform(this.subMitData.mark) : this.subMitData.mark;
          const param_nine = {
            moduleType: "ZSSBHZ",
            moduleName: "TQ",
            longitude: lng,
            latitude: lat,
            state: this.subMitData.state,
            zssbType: this.subMitData.equiType,
            equipBh: this.subMitData.equiNum,
            sbModel: this.subMitData.equiModelId,
            isJd: this.subMitData.isGround === "是" ? 1 : 0,
            projectId: this.$route.query.childProjectId,
            note: this.subMitData.message,
            mark: this.subMitData.mark,
            imgFiles: this.subMitData.imgList,
            voiceFiles: this.subMitData.audioList,
          };
          if (this.isEditPointParam.id === "") {
            let hasZssbNum = 0;
            for (let j = 0; j < this.allPointList.length; j++) {
              if (that.allPointList[j]["moduleType"] === "ZSSBHZ") {
                if (Number(that.allPointList[j].longitude) === lng) {
                  if (hasZssbNum === 2) {
                    return Toast.fail("当前杆塔上已存在两个柱上设备");
                  } else {
                    hasZssbNum = hasZssbNum + 1;
                  }
                }
              }
            }
            apipost("/t-dtf-app-point/addPoint", param_nine).then(function (res) {
              that.setMapCenter();
              that.initMap();
              that.isReformMark = true;
              that.showChoseIncoIndex = 1
              that.isGoheadPoint = true;
              that.isChooseAfterPoint = false;
              $(".map-endkc").css({ display: "block" });
            });
          } else {
            param_nine.pointId = this.isEditPointParam.id;
            param_nine.longitude = this.isEditPointParam.lng;
            param_nine.latitude = this.isEditPointParam.lat;
            apipost("/t-dtf-app-point/updatePoint", param_nine).then(function (
              res
            ) {
              // 隐藏点位弹框
              mapData.map.closePopup();
              Toast.success("更新成功");
              that.setMapCenter();
              that.initMap();
              that.isGoheadPoint = false;
              that.isChooseAfterPoint = false;
              that.selectCheck.visible = false;
            });
          }
          break;
        // 拉线
        case 10:
          const mapcenter = mapData.map.getCenter();
          // 以地图中心点作为开始点
          const startLnglat = {
            longitude: Number(mapcenter.lng),
            latitude: Number(mapcenter.lat),
          };
          // 以基础点作为结束点
          const endStarLnglat = {
            longitude: that.drawLXHZStartPoint.lng,
            latitude: that.drawLXHZStartPoint.lat,
          };
          const angle = that.bearing(startLnglat, endStarLnglat);
          const param_ten = {
            moduleType: "LXHZ",
            moduleName: "TQ",
            longitude: this.drawLXHZStartPoint.lng,
            latitude: this.drawLXHZStartPoint.lat,
            state: this.subMitData.state,
            legendTypeKey: this.subMitData.stayType,
            voltage: this.subMitData.voltage,
            lxProgram: this.subMitData.stayPlanId,
            projectId: this.$route.query.childProjectId,
            note: this.subMitData.message,
            mark: this.subMitData.mark,
            angle: angle,
            imgFiles: this.subMitData.imgList,
            voiceFiles: this.subMitData.audioList,
          };
          if (this.isEditPointParam.id === "") {
            apipost("/t-dtf-app-point/addPoint", param_ten).then(function (res) {
              that.setMapCenter();
              that.initMap();
              that.isReformMark = true;
              that.isChooseAfterPoint = true;
              that.isGoheadPoint = false;
              $(".map-endkc").css({ display: "block" });
              that.showChoseIncoIndex = 1;
            });
          } else {
            param_ten.pointId = this.isEditPointParam.id;
            param_ten.longitude = this.isEditPointParam.lng;
            param_ten.latitude = this.isEditPointParam.lat;
            apipost("/t-dtf-app-point/updatePoint", param_ten).then(function (
              res
            ) {
              // 隐藏点位弹框
              mapData.map.closePopup();
              Toast.success("更新成功");
              that.initMap();
              that.isChooseAfterPoint = false;
              that.selectCheck.visible = false;
              $(".map-endkc").css({ display: "none" });
            });
          }
          break;
        // 电缆线路绘制
        case 12:
          this.subMitData.mark = this.isReformMark ? this.transform(this.subMitData.mark) : this.subMitData.mark;
          const param_twe = {
            moduleType: "DLXLHZ",
            moduleName: "DLXL",
            projectId: this.$route.query.childProjectId,
            startPointId: this.startPointId.startId,
            endPointId: this.startPointId.endId,
            startDoTime: this.startPointTime.startTime,
            endDoTime: this.startPointTime.endTime,
            longitude: lng,
            latitude: lat,
            isStart: this.subMitData.isStart,
            lineCategory: this.subMitData.type,
            lineType: this.subMitData.modelId,
            voltage: this.subMitData.voltage,
            listArr: this.subMitData.listArr,
            zjtModelId: this.subMitData.zjtModelId,
            zdtModelId: this.subMitData.zdtModelId,
            plmType: this.subMitData.plmval,
            mark: this.subMitData.mark,
            note: this.subMitData.message,
          };
          if (this.isEditPointParam.id === "") {
            apipost("/t-dtf-app-point/addPoint", param_twe).then(function (res) {
              that.initMap();
              that.isReformMark = true;
              that.subMitData.isStart = "1";
              that.isGoheadPoint = true;
              that.isChooseAfterPoint = false;
              that.startPointId.startId = that.allPointList[that.allPointList.length - 1].pointId;
              $(".map-endkc").css({ display: "block" });
            });
          } else {
            param_twe.pointId = this.isEditPointParam.id;
            param_twe.longitude = this.isEditPointParam.lng;
            param_twe.latitude = this.isEditPointParam.lat;
            apipost("/t-dtf-app-point/updatePoint", param_twe).then(function (res) {
              // 隐藏点位弹框
              mapData.map.closePopup();
              Toast.success("更新成功");
              that.isGoheadPoint = false;
              that.isChooseAfterPoint = false;
              that.selectCheck.visible = false;
              that.initMap();
              $(".map-endkc").css({ display: "none" });
            });
          }
          break;
        // 电缆头绘制
        case 13:
          this.subMitData.mark = this.isReformMark
            ? this.transform(this.subMitData.mark)
            : this.subMitData.mark;
          const param_thir = {
            moduleType: "DLTHZ",
            moduleName: "ZDT",
            projectId: this.$route.query.childProjectId,
            longitude: lng,
            latitude: lat,
            voltage: this.subMitData.voltage,
            moduleId: this.subMitData.modelId,
            dltType: this.subMitData.type,
            state: this.subMitData.state,
            note: this.subMitData.message,
            mark: this.subMitData.mark,
            imgFiles: this.subMitData.imgList,
            voiceFiles: this.subMitData.audioList,
          };
          if (this.isEditPointParam.id === "") {
            for (let j = 0; j < this.allPointList.length; j++) {
              if (this.allPointList[j].moduleName === "ZDT") {
                if (Number(this.allPointList[j].longitude) === lng) {
                  return Toast.fail("当前点上存在终端头了");
                }
              }
            }
            apipost("/t-dtf-app-point/addPoint", param_thir).then(function (
              res
            ) {
              that.setMapCenter();
              that.initMap();
              that.isReformMark = true;
              that.isGoheadPoint = true;
              that.isChooseAfterPoint = false;
              that.showChoseIncoIndex = 2;
              $(".map-endkc").css({ display: "block" });
            });
          } else {
            param_thir.pointId = this.isEditPointParam.id;
            param_thir.longitude = this.isEditPointParam.lng;
            param_thir.latitude = this.isEditPointParam.lat;
            apipost("/t-dtf-app-point/updatePoint", param_thir).then(function (
              res
            ) {
              // 隐藏点位弹框
              mapData.map.closePopup();
              Toast.success("更新成功");
              that.setMapCenter();
              that.initMap();
              that.isGoheadPoint = false;
              that.isChooseAfterPoint = false;
              that.selectCheck.visible = false;
            });
          }
          break;
        // 电缆保护管绘制
        case 14:
          this.subMitData.mark = this.isReformMark
            ? this.transform(this.subMitData.mark)
            : this.subMitData.mark;
          const param_fourten = {
            moduleType: "DLBHGHZ",
            moduleName: "DLXL",
            projectId: this.$route.query.childProjectId,
            startPointId: this.startPointId.startId,
            endPointId: this.startPointId.endId,
            gcType: this.subMitData.type,
            gcModel: this.subMitData.modelId,
            lineState: this.subMitData.state,
            note: this.subMitData.message,
            mark: this.subMitData.mark,
            imgFiles: this.subMitData.imgList,
            voiceFiles: this.subMitData.audioList,
          };
          if (this.isEditPointParam.id === "") {
            for (let j = 0; j < this.allPointList.length; j++) {
              if (this.allPointList[j].moduleName === "DLBHGHZ") {
                if (Number(this.allPointList[j].longitude) === lng && Number(this.allPointList[j].latitude) === lat) {
                  return Toast.fail("当前路段已经存在电缆保护管了");
                }
              }
            }
            apipost("/t-dtf-app-point/addPoint", param_fourten).then(function (
              res
            ) {
              if (res.code === 1001) {
                that.setMapCenter();
                that.initMap();
                that.isReformMark = true;
                that.isGoheadPoint = false;
                that.isAddDifLine = false;
                that.isChooseAfterPoint = false;
              } else {
                return Toast.fail(res.message);
              }
            });
          } else {
            param_fourten.startPointId = this.isEditLineParam.startId;
            param_fourten.endPointId = this.isEditLineParam.endId;
            param_fourten.lineId = this.isEditPointParam.id;
            apipost("/t-dtf-app-point/updateLines", param_fourten).then(
              function (res) {
                // 隐藏点位弹框
                mapData.map.closePopup();
                Toast.success("更新成功");
                $(".map-endkc").css({ display: "none" });
                that.setMapCenter();
                that.initMap();
                that.isGoheadPoint = false;
                that.isChooseAfterPoint = false;
                that.selectCheck.visible = false;
              }
            );
          }
          break;
        // 电缆分支箱绘制
        case 15:
          this.subMitData.mark = this.isReformMark
            ? this.transform(this.subMitData.mark)
            : this.subMitData.mark;
          const param_fif = {
            moduleType: "DLFZXHZ",
            moduleName: "DLXL",
            projectId: this.$route.query.childProjectId,
            startPointId: this.startPointId.startId,
            endPointId: this.startPointId.endId,
            longitude: lng,
            latitude: lat,
            voltage: this.subMitData.voltage,
            dlfzxName: this.subMitData.branchId,
            state: this.subMitData.state,
            equipBh: this.subMitData.number,
            lineId: this.subMitData.lineName,
            note: this.subMitData.message,
            mark: this.subMitData.mark,
            imgFiles: this.subMitData.imgList,
            voiceFiles: this.subMitData.audioList,
          };
          if (this.isEditPointParam.id === "") {
            for (let j = 0; j < this.allPointList.length; j++) {
              if (this.allPointList[j].moduleType === "DLFZX") {
                if (Number(this.allPointList[j].longitude) === lng) {
                  return Toast.fail("当前点上存在电缆分支箱了");
                }
              }
            }
            apipost("/t-dtf-app-point/addPoint", param_fif).then(function (res) {
              that.setMapCenter();
              that.allPointList = [];
              that.initMap();
              that.isReformMark = true;
              that.isGoheadPoint = false;
              that.isChooseAfterPoint = false;
              $(".map-endkc").css({ display: "block" });
            });
          } else {
            param_fif.pointId = this.isEditPointParam.id;
            param_fif.longitude = this.isEditPointParam.lng;
            param_fif.latitude = this.isEditPointParam.lat;
            apipost("/t-dtf-app-point/updatePoint", param_fif).then(function (
              res
            ) {
              // 隐藏点位弹框
              mapData.map.closePopup();
              Toast.success("更新成功");
              that.setMapCenter();
              that.allPointList = [];
              that.initMap();
              $(".map-endkc").css({ display: "none" });
              that.isGoheadPoint = false;
              that.isChooseAfterPoint = false;
              that.selectCheck.visible = false;
            });
          }
          break;
        // 土建路径绘制
        case 16:
          // 这块添加的时候需要注意  土建路径类别变成了动态读的  2022.12.23
          this.subMitData.mark = this.isReformMark
            ? this.transform(this.subMitData.mark)
            : this.subMitData.mark;
          const param_sixten = {
            moduleType: "DLTDHZ",
            moduleName: "DLXL",
            projectId: this.$route.query.childProjectId,
            startPointId: this.startPointId.startId,
            endPointId: this.startPointId.endId,
            lineName: this.subMitData.name,
            lineState: this.subMitData.state,
            methodName: this.subMitData.laying,
            tdType: this.subMitData.modelId,
            isZddlj: "",
            isDlj: "",
            isHz: "",
            dljNum: "",
            dljSpacing: "",
            note: this.subMitData.message,
            mark: this.subMitData.mark,
            imgFiles: this.subMitData.imgList,
            voiceFiles: this.subMitData.audioList,
          };
          if (this.isEditPointParam.id === "") {
            for (let j = 0; j < this.mapAllLineList.length; j++) {
              if (this.mapAllLineList[j].DLTDHZ === "DLTDHZ") {
                if (
                  this.startPointId.startId ===
                  this.mapAllLineList[j].startId &&
                  this.startPointId.endId === this.mapAllLineList[j].endId
                ) {
                  return Toast.fail("电缆拐点间已存在土建路径");
                }
              }
            }
            apipost("/t-dtf-app-point/addPoint", param_sixten).then(function (
              res
            ) {
              that.setMapCenter();
              that.initMap();
              that.isReformMark = true;
              that.isGoheadPoint = false;
              that.isChooseAfterPoint = false;
              that.isAddDifLine = false;
            });
          } else {
            param_sixten.startPointId = this.isEditLineParam.startId;
            param_sixten.endPointId = this.isEditLineParam.endId;
            param_sixten.lineId = this.isEditPointParam.id;
            apipost("/t-dtf-app-point/updateLines", param_sixten).then(function (
              res
            ) {
              // 隐藏点位弹框
              mapData.map.closePopup();
              Toast.success("更新成功");
              that.mapAllLineList = [];
              that.initMap();
              that.isGoheadPoint = false;
              that.isChooseAfterPoint = false;
              that.selectCheck.visible = false;
              $(".map-endkc").css({ display: "none" });
            });
          }
          break;
        // 电缆井绘制
        case 17:
          this.subMitData.mark = this.isReformMark
            ? this.transform(this.subMitData.mark)
            : this.subMitData.mark;
          const param_sevent = {
            moduleType: "DLJHZ",
            moduleName: "DLXL",
            projectId: this.$route.query.childProjectId,
            state: this.subMitData.state,
            startPointId: this.startPointId.startId,
            endPointId: "",
            longitude: lng,
            latitude: lat,
            dljWay: this.subMitData.operation,
            dljType: this.subMitData.type,
            moduleId: this.subMitData.modelId,
            note: this.subMitData.message,
            mark: this.subMitData.mark,
            imgFiles: this.subMitData.imgList,
            voiceFiles: this.subMitData.audioList,
          };
          if (this.isEditPointParam.id === "") {
            apipost("/t-dtf-app-point/addPoint", param_sevent).then(function (
              res
            ) {
              that.setMapCenter();
              that.initMap();
              that.isReformMark = true;
              that.isGoheadPoint = false;
              that.showChoseIncoIndex = 2;
              that.isChooseAfterPoint = false;
              $(".map-endkc").css({ display: "block" });
            });
          } else {
            param_sevent.pointId = this.isEditPointParam.id;
            param_sevent.longitude = this.isEditPointParam.lng;
            param_sevent.latitude = this.isEditPointParam.lat;
            apipost("/t-dtf-app-point/updatePoint", param_sevent).then(function (res) {
              // 隐藏点位弹框
              mapData.map.closePopup();
              Toast.success("更新成功");
              that.setMapCenter();
              that.allPointList = [];
              that.initMap();
              $(".map-endkc").css({ display: "none" });
              that.isGoheadPoint = false;
              that.isChooseAfterPoint = false;
              that.selectCheck.visible = false;
            });
          }
          break;
        // 配电站房绘制
        case 18:
          this.subMitData.mark = this.isReformMark
            ? this.transform(this.subMitData.mark)
            : this.subMitData.mark;
          const param_eightten = {
            zfType: this.subMitData.planType,
            faType: this.subMitData.schemeId,
            hbState: this.subMitData.state,
            zfNum: this.subMitData.electNum,
            zfModel: this.subMitData.electModelId,
            lineCategory: this.subMitData.aisleType,
            plmType: this.subMitData.plmType,
            voltage: this.subMitData.voltage,
            moduleType: "PDZFHZ",
            moduleName: "TQ",
            projectId: this.$route.query.childProjectId,
            startPointId: this.startPointId.startId,
            endPointId: "",
            longitude: lng,
            latitude: lat,
            listArr: this.subMitData.listArr,
            note: this.subMitData.message,
            mark: this.subMitData.mark,
            imgFiles: this.subMitData.imgList,
            voiceFiles: this.subMitData.audioList,
            dljPosition: this.subMitData.loactionMsg,
          };
          if (this.isEditPointParam.id === "") {
            apipost("/t-dtf-app-point/addPoint", param_eightten).then(function (
              res
            ) {
              that.setMapCenter();
              that.initMap();
              that.showChoseIncoIndex = -1;
              that.isReformMark = true;
              that.isGoheadPoint = true;
              that.isChooseAfterPoint = false;
              $(".map-endkc").css({ display: "block" });
            });
          } else {
            param_eightten.pointId = this.isEditPointParam.id;
            param_eightten.longitude = this.isEditPointParam.lng;
            param_eightten.latitude = this.isEditPointParam.lat;
            apipost("/t-dtf-app-point/updatePoint", param_eightten).then(
              function (res) {
                // 隐藏点位弹框
                mapData.map.closePopup();
                Toast.success("更新成功");
                that.setMapCenter();
                that.allPointList = [];
                that.initMap();
                that.isGoheadPoint = false;
                that.isChooseAfterPoint = false;
                that.selectCheck.visible = false;
              }
            );
          }
          break;
        // 变电站绘制
        case 20:
          this.subMitData.mark = this.isReformMark
            ? this.transform(this.subMitData.mark)
            : this.subMitData.mark;
          // eslint-disable-next-line no-case-declarations
          const param_nightten = {
            moduleType: "BDZHZ",
            moduleName: "TQ",
            projectId: this.$route.query.childProjectId,
            longitude: lng,
            latitude: lat,
            state: this.subMitData.state,
            mark: this.subMitData.mark,
            lineCategory: this.subMitData.aisleType,
            plmType: this.subMitData.plmType,
            voltage: this.subMitData.valtage,
            listArr: this.subMitData.listArr,
            imgFiles: this.subMitData.imgList,
            voiceFiles: this.subMitData.audioList,
            note: this.subMitData.loactionMsg,
          };
          // 判断有没有id决定走更新还是新建
          if (this.isEditPointParam.id === "") {
            apipost("/t-dtf-app-point/addPoint", param_nightten).then(function (
              res
            ) {
              that.setMapCenter();
              that.initMap();
              that.isReformMark = true;
              that.isGoheadPoint = true;
            });
          } else {
            param_nightten.pointId = this.isEditPointParam.id;
            param_nightten.longitude = this.isEditPointParam.lng;
            param_nightten.latitude = this.isEditPointParam.lat;
            apipost("/t-dtf-app-point/updatePoint", param_nightten).then(
              function (res) {
                // 隐藏点位弹框
                mapData.map.closePopup();
                that.setMapCenter();
                that.initMap();
                that.isGoheadPoint = false;
                that.isChooseAfterPoint = false;
                that.selectCheck.visible = false;
                Toast.success("更新成功!");
              }
            );
          }
          break;
        // 电缆线路绘制
        case 21:
          this.subMitData.mark = this.isReformMark
            ? this.transform(this.subMitData.mark)
            : this.subMitData.mark;
          const param_twity = {
            moduleType: "SBLJHZ",
            moduleName: "DLXL",
            projectId: this.$route.query.childProjectId,
            startPointId: this.startPointId.startId,
            endPointId: this.startPointId.endId,
            startDoTime: this.startPointTime.startTime,
            endDoTime: this.startPointTime.endTime,
            longitude: lng,
            latitude: lat,
            isStart: this.subMitData.isStart,
            lineCategory: this.subMitData.type,
            lineType: this.subMitData.modelId,
            voltage: this.subMitData.voltage,
            listArr: this.subMitData.listArr,
            zjtModelId: this.subMitData.zjtModelId,
            zdtModelId: this.subMitData.zdtModelId,
            plmType: this.subMitData.plmval,
            mark: this.subMitData.mark,
            note: this.subMitData.message,
          };
          if (this.isEditPointParam.id === "") {
            apipost("/t-dtf-app-point/addPoint", param_twity).then(function (
              res
            ) {
              that.initMap();
              that.isReformMark = true;
              that.subMitData.isStart = "0";
              that.isGoheadPoint = true;
              that.isChooseAfterPoint = false;
              $(".map-endkc").css({ display: "block" });
            });
          } else {
            param_twe.pointId = this.isEditPointParam.id;
            param_twe.longitude = this.isEditPointParam.lng;
            param_twe.latitude = this.isEditPointParam.lat;
            apipost("/t-dtf-app-point/updatePoint", param_twe).then(function (
              res
            ) {
              // 隐藏点位弹框
              mapData.map.closePopup();
              Toast.success("更新成功");
              that.isGoheadPoint = false;
              that.isChooseAfterPoint = false;
              that.selectCheck.visible = false;
              that.initMap();
              $(".map-endkc").css({ display: "none" });
            });
          }
          break;
      }
    },
    showDraw() {
      this.isReformMark = false;
      this.selectCheck.objVis.showMainLine = false;
      this.selectCheck.objVis.showBranchLine = false;
      this.selectCheck.objVis.showleverHead = false;
      this.selectCheck.objVis.showonductor = false;
      this.selectCheck.objVis.showOpticCable = false;
      this.selectCheck.objVis.showLowDoor = false;
      this.selectCheck.objVis.showCrossOver = false;
      this.selectCheck.objVis.showOverLine = false;
      this.selectCheck.objVis.showTransformer = false;
      this.selectCheck.objVis.showColumnEqu = false;
      this.selectCheck.objVis.showStay = false;
      this.selectCheck.objVis.showFacilities = false;
      this.selectCheck.objVis.showCableLine = false;
      this.selectCheck.objVis.showEquipLine = false;
      this.selectCheck.objVis.showCableHead = false;
      this.selectCheck.objVis.showCableTube = false;
      this.selectCheck.objVis.showCableBranch = false;
      this.selectCheck.objVis.showCableChannels = false;
      this.selectCheck.objVis.showCablePit = false;
      this.selectCheck.objVis.showElectrical = false;
      this.addParam.isShowNav = false;
      this.addParam.isShowStartlnglat = false;
      this.settingObj.isDisabledDraw = false;
      this.settingObj.isShowDrawRaw = false;
      // 新增的时候
      if (this.isEditPointParam.id === "") {
        if (this.allPointList.length === 0) {
          // this.tarbarList = ["架空线路", "台区站房"]
          this.selectCheck.isShowNavBar = true;
          this.selectCheck.objVis.showMainLine = true;
          this.selectCheck.objVis.showElectrical = true;
          this.selectCheck.objVis.showChangeCable = true;
          setTimeout(() => {
            const doms = document.querySelectorAll(".map-selectAreaChos");
            if (this.chooseItems === "架空线路") {
              $(doms[0]).css({ color: "#fff", background: "#526ADE" });
            } else if (this.chooseItems === "台区站房") {
              $(doms[1]).css({ color: "#fff", background: "#526ADE" });
            } else {
              $(doms[2]).css({ color: "#fff", background: "#526ADE" });
            }
          }, 100);
        } else {
          // this.tarbarList = ["架空线路", "台区站房", "电缆线路"]
          this.selectCheck.isShowNavBar = true;
          this.selectCheck.objVis.showMainLine = true;
          this.selectCheck.objVis.showBranchLine = true;
          this.selectCheck.objVis.showleverHead = true;
          this.selectCheck.objVis.showonductor = true;
          this.selectCheck.objVis.showOpticCable = true;
          this.selectCheck.objVis.showLowDoor = true;
          this.selectCheck.objVis.showCrossOver = true;
          this.selectCheck.objVis.showOverLine = true;
          this.selectCheck.objVis.showTransformer = true;
          this.selectCheck.objVis.showColumnEqu = true;
          this.selectCheck.objVis.showStay = true;
          this.selectCheck.objVis.showFacilities = true;
          this.selectCheck.objVis.showCableLine = true;
          this.selectCheck.objVis.showEquipLine = true;
          this.selectCheck.objVis.showCableHead = true;
          this.selectCheck.objVis.showCableTube = true;
          this.selectCheck.objVis.showCableBranch = true;
          this.selectCheck.objVis.showCableChannels = true;
          this.selectCheck.objVis.showCablePit = true;
          this.selectCheck.objVis.showElectrical = true;
          this.selectCheck.objVis.showChangeCable = true;
          setTimeout(() => {
            const doms = document.querySelectorAll(".map-selectAreaChos");
            if (this.chooseItems === "架空线路") {
              $(doms[0]).css({ color: "#fff", background: "#526ADE" });
            } else if (this.chooseItems === "台区站房") {
              $(doms[1]).css({ color: "#fff", background: "#526ADE" });
            } else {
              $(doms[2]).css({ color: "#fff", background: "#526ADE" });
            }
          }, 100);
          if (this.isGoheadPoint) {
            this.setLastPoint();
            this.isGoheadPoint = false;
          }
        }
        this.selectCheck.isShowNavBar = true;
        const isEixstZgt = this.allPointList.filter((item) => {
          return item.moduleType === "ZXLHZ" && item.moduleName === "JKXL";
        });
        this.isEditPointParam = {
          id: "",
          lng: "",
          lat: "",
        }; // 清空编辑的id
        this.submitType = -1; // 重置新建的类型
        this.selectCheck.visible = true;
        this.selectCheck.radio = "";
        this.selectCheck.activeNames = [];
        this.isEditLineParam = {
          startId: "", // 开始点id
          endId: "", // 结束
          moudleType: "",
        };
        /*
             主杆塔查询
             分支杆塔查询
          */
        // // 查询杆塔类型 杆塔类型和电压都写死 按照默认值查询
      }
    },
    showDetails(id, type) {
      // 先把所有的隐藏掉 选择性展示
      this.isShowBackAdds = true;
      mapData.map.closePopup();
      this.selectCheck.objVis.showMainLine = false;
      this.selectCheck.objVis.showBranchLine = false;
      this.selectCheck.objVis.showleverHead = false;
      this.selectCheck.objVis.showonductor = false;
      this.selectCheck.objVis.showOpticCable = false;
      this.selectCheck.objVis.showLowDoor = false;
      this.selectCheck.objVis.showCrossOver = false;
      this.selectCheck.objVis.showOverLine = false;
      this.selectCheck.objVis.showTransformer = false;
      this.selectCheck.objVis.showColumnEqu = false;
      this.selectCheck.objVis.showStay = false;
      this.selectCheck.objVis.showFacilities = false;
      this.selectCheck.objVis.showCableLine = false;
      this.selectCheck.objVis.showEquipLine = false;
      this.selectCheck.objVis.showCableHead = false;
      this.selectCheck.objVis.showCableTube = false;
      this.selectCheck.objVis.showCableBranch = false;
      this.selectCheck.objVis.showCableChannels = false;
      this.selectCheck.objVis.showCablePit = false;
      this.selectCheck.objVis.showElectrical = false;
      this.selectCheck.visible = true;
      const that = this;
      that.selectCheck.isShowNavBar = true;
      const param = {
        projectId: this.$route.query.childProjectId,
        pointId: id,
      };
       // 接口改造 apipost
       console.log(param,'查看设备选型');
      apipost("/t-dtf-app-point/selectPointOne", param).then(function (res) {
        if (res.code === 1001) {
          that.isEditPointParam.id = res.data.pointId;
          that.isEditPointParam.lng = res.data.longitude;
          that.isEditPointParam.lat = res.data.latitude;
          that.addParam.isShowNav = true;
          that.addParam.lngtitude = res.data.longitude;
          that.addParam.lattitude = res.data.latitude;
          that.addParam.highwaNum = res.data.high;
          that.selectCheck.isShowCpVis = 0;
          $(".map-rightArea").show();
          switch (res.data.moduleType) {
            // 主杆塔查询
            case "ZXLHZ":
              that.chooseItems = "架空线路";
              that.selectCheck.objVis.showMainLine = true;
              that.editData = res.data;
              break;
            // 分支线路绘制
            case "FXLHZ":
              that.chooseItems = "架空线路";
              that.selectCheck.objVis.showBranchLine = true;
              that.editData = res.data;
              break;
            // 低压下户绘制
            case "DYXHHZ":
              that.chooseItems = "架空线路";
              that.selectCheck.objVis.showLowDoor = true;
              that.editData = res.data;
              break;
            // 交叉跨越绘制
            case "JCKYHZ":
              that.chooseItems = "架空线路";
              that.selectCheck.objVis.showCrossOver = true;
              that.editData = res.data;
              break;
            // 架空电缆绘制
            case "JKDLHZ":
              that.chooseItems = "架空线路";
              that.selectCheck.objVis.showOverLine = true;
              that.editData = res.data;
              break;
            // 柱上变压器
            case "ZSBYQHZ":
              that.chooseItems = "台区站房";
              that.selectCheck.objVis.showTransformer = true;
              that.editData = res.data;
              break;
            // 柱上设备绘制
            case "ZSSBHZ":
              that.chooseItems = "台区站房";
              that.selectCheck.objVis.showColumnEqu = true;
              that.editData = res.data;
              break;
            // 配电站房绘制
            case "PDZFHZ":
              that.chooseItems = "台区站房";
              that.selectCheck.objVis.showElectrical = true;
              that.editData = res.data;
              break;
            // 变电站绘制
            case "BDZHZ":
              that.chooseItems = "台区站房";
              that.selectCheck.objVis.showChangeCable = true;
              that.editData = res.data;
              break;
            // 拉线绘制
            case "LXHZ":
              that.chooseItems = "架空线路";
              that.selectCheck.objVis.showStay = true;
              that.editData = res.data;
              break;
            // 电缆线绘制
            case "DLXLHZ":
              that.chooseItems = "电缆线路";
              that.selectCheck.objVis.showCableLine = true;
              that.editData = res.data;
              break;
            // 电缆线绘制
            case "SBLJHZ":
              that.chooseItems = "电缆线路";
              that.selectCheck.objVis.showEquipLine = true;
              that.editData = res.data;
              break;
            // 电缆头绘制
            case "DLTHZ":
              that.chooseItems = "电缆线路";
              that.selectCheck.objVis.showCableHead = true;
              that.editData = res.data;
              break;
            // 电缆分支箱修改
            case "DLFZXHZ":
              that.chooseItems = "电缆线路";
              that.selectCheck.objVis.showCableBranch = true;
              that.editData = res.data;
              break;
            // 电缆井修改
            case "DLJHZ":
              that.chooseItems = "电缆线路";
              that.settingObj.isDisabledDraw = true;
              that.editData = res.data;
              break;
          }
        }
      });
    },
    hidePop() {
      mapData.map.closePopup();
      this.isClickDlj = false;
    },
    /**
     * 排杆功能
     */
    rankGt(param) {
      mapData.map.closePopup();
      const datas = JSON.parse(param);
      this.selectCheck.isShowCpVis = 2;
      $(".map-rightArea").show();
      this.addParam.rangeGt.batchType = "自由插入";
      this.addParam.rangeGt.voltage = "10kV";
      this.addParam.rangeGt.state = "新建";
      this.addParam.rangeGt.legendTypeKey = "水泥杆";
      this.addParam.rangeGt.moudle = "";
      this.addParam.rangeGt.moduleId = "";
      this.addParam.rangeGt.pointNum = "";
      this.addParam.rangeGt.lineId = datas.lineId;
      const searchParam = {
        projectId: this.$route.query.childProjectId,
        pointid: datas.startId,
      };
      this.getRangeGt(1, 1, "10kV", true, searchParam);
    },
    /**
     * 删除拉线 拉线是点 但是以线标记物展示 需要删除点及对应的线标记物数据
     */
    removeLXpoint(layerId, imageId, pointId) {
      // 接口改造apipost 特殊
      let params={
        id:pointId
      }
      apipost(`/t-dtf-app-point/delPoint`,params).then((res) => {
        if (res.code === 1001) {
          mapData.map.closePopup();
          $(".mark-distance").remove();
          mapData.map.getLayer(layerId) &&
            mapData.map.removeLayer(layerId) &&
            mapData.map.removeSource(layerId);
          mapData.map.removeImage(imageId);
        }
      });
    },
    removePoint(id, lng, lat) {
      console.log(id,"删除id");
      const that = this;
      // 接口改造apipost 特殊
       let params={
        id:id
      }
      apipost(`/t-dtf-app-point/delPoint`,params).then((res) => {
        if (res.code === 1001) {
          that.isShowAction = false;
          $(".mark-distance").remove();
          for (let j = 0; j < that.mapsAllPointsGraphic.length; j++) {
          console.log(id === that.mapsAllPointsGraphic[j].attributes.id,'dldldldl');
            if (id === that.mapsAllPointsGraphic[j].attributes.id) {

              // 关闭绑定的弹框
              mapData.map.closePopup()

              // 根据id删除对应的图层 删除对应的点的时候会删除和点相关的编号，推导出来的杆塔型号
              that.mapsAllPointsGraphic[j].remove()
              that.mapsAllPointsGraphic.splice(j, 1)
            }
          }
          for (let j = 0; j < that.poIntAllIdList.length; j++) {
            if (that.poIntAllIdList[j] === id) {
              that.poIntAllIdList.splice(j, 1);
            }
          }
          for (let j = 0; j < that.saveAnglePoint.length; j++) {
            if (that.saveAnglePoint[j] === id) {
              that.saveAnglePoint.splice(j, 1);
            }
          }
          for (let j = 0; j < res.data.length; j++) {
            for (let k = 0; k < that.mapAllLineList.length; k++) {
              if (res.data[j] === that.mapAllLineList[k].lineId) {
                that.mapAllLineList.splice(k, 1);
              }
            }

            for (let k = 0; k < that.mapsAllLinesGraphic.length; k++) {
              // LDA循环的res.data[k] === 改为res.data[j]
              if (res.data[j] === that.mapsAllLinesGraphic[k].attributes.id) {
                // 关闭绑定的弹框
                mapData.map.closePopup()
                // 根据id删除对应的图层
                that.mapsAllLinesGraphic[k].remove()
                that.mapsAllLineMarkers[k].remove()
                // 删除对应的沿线标注物
                that.mapsAllLineMarkers.splice(k, 1)
                // 删除对应的沿线标注物的id
                that.mapsAllLineMarkersIDS.splice(k, 1)
                that.mapsAllLinesGraphic.splice(k, 1)
                that.mapsAllLinesPoly.splice(k, 1)
              }
            }


          }

            that.initMap();
          Toast.success("刪除成功!");

        }
      });
    },
    removeLines(paramObj) {
      const params = JSON.parse(paramObj);
      const that = this;
      const typeArrs = ["JKDLHZ", "DXHZ"];
      if (typeArrs.includes(params.moduleType)) {
        switch (params.moduleType) {
          case "JKDLHZ":
            if (params.drawSettings === "选择现有杆") {
              // 架空电缆选择现有杆绘制的数据较特殊 删除会删除两个杆塔之间同类型的所有数据
              Dialog.confirm({
                message: "删除起始杆塔之间所有架空电缆数据，是否确认?",
              })
                .then(() => {
                  this.byIdRemoveLines(params.lineId);
                })
                .catch(() => {
                  // on cancel
                });
            }
            break;
          case "DXHZ":
            Dialog.confirm({
              message: "是否确认删除导线?",
            })
              .then(() => {
                this.byIdRemoveLines(params);
                mapData.map.getLayer(params.lineId + 1) &&
                  mapData.map.removeLayer(params.lineId + 1) &&
                  mapData.map.removeSource(params.lineId + 1);
              })
              .catch(() => {
                // on cancel
              });
            break;
        }
      } else {
        // const params = JSON.parse(paramObj)
        this.byIdRemoveLines(params);
        // mapData.map.getLayer(params.lineId + 1) &&
        //   mapData.map.removeLayer(params.lineId + 1) &&
        //   mapData.map.removeSource(params.lineId + 1);
      }
    },
    byIdRemoveLines(params) {
      const that = this;
      // 接口改造 apipost
      let paramss={id:params.lineId}
      apipost(`/t-dtf-app-point/delLine`,paramss).then((res) => {
        if (res.code === 1001) {
          for (let j = 0; j < that.mapsAllLinesGraphic.length; j++) {
            if (params.lineId === that.mapsAllLinesGraphic[j].attributes.id) {
              // 关闭绑定的弹框
              mapData.map.closePopup()
              // 根据id删除对应的图层
              that.mapsAllLinesGraphic[j].unbindTooltip()
              that.mapsAllLinesGraphic[j].remove()
              that.mapsAllLinesGraphic.splice(j, 1)
              that.mapsAllLinesPoly.splice(j, 1)
            }
          }
          for (let j = 0; j < that.mapsAllLineMarkersIDS.length; j++) {
            that.mapsAllLineMarkers[j].remove()
            that.mapsAllLineMarkersIDS[j].remove()
          }
          for (let j = 0; j < that.mapAllLineList.length; j++) {
            if (that.mapAllLineList[j].lineId === params.lineId) {
              that.mapAllLineList.splice(j, 1);
            }
          }
          for (let j = 0; j < that.lineimagesList.length; j++) {
            if (that.lineimagesList[j] === params.imgId) {
              that.lineimagesList.splice(j, 1);
            }
          }
          Toast.success("刪除成功!");
          mapData.map.closePopup();
        }
      });
    },
    /**
     * 修改线详情 查询线
     * @param startId 开始点id
     * @param endId 结束点id
     * @param type 线类型
     */
    showLineDetails(paramObj) {
      this.isShowBackAdds = true;
      this.selectCheck.isShowNavBar = true;
      mapData.map.closePopup();
      const params = JSON.parse(paramObj);
      const type = params.moduleType;
      const that = this;
      const param = {
        moduleType: type,
        startPointId: params.startId,
        endPointId: params.endId,
      };
       // 接口改造 apipost
      apipost("/t-dtf-app-point/selectLines", param).then((res) => {
        if (res.code === 1001) {
          that.selectCheck.objVis.showMainLine = false;
          that.selectCheck.objVis.showBranchLine = false;
          that.selectCheck.objVis.showleverHead = false;
          that.selectCheck.objVis.showonductor = false;
          that.selectCheck.objVis.showOpticCable = false;
          that.selectCheck.objVis.showLowDoor = false;
          that.selectCheck.objVis.showCrossOver = false;
          that.selectCheck.objVis.showOverLine = false;
          that.selectCheck.objVis.showTransformer = false;
          that.selectCheck.objVis.showColumnEqu = false;
          that.selectCheck.objVis.showStay = false;
          that.selectCheck.objVis.showFacilities = false;
          that.selectCheck.objVis.showCableLine = false;
          that.selectCheck.objVis.showEquipLine = false;
          that.selectCheck.objVis.showCableHead = false;
          that.selectCheck.objVis.showCableTube = false;
          that.selectCheck.objVis.showCableBranch = false;
          that.selectCheck.objVis.showCableChannels = false;
          that.selectCheck.objVis.showCablePit = false;
          that.selectCheck.objVis.showElectrical = false;
          that.selectCheck.visible = true;
          that.isEditPointParam.id = res.data.lineId;
          that.isEditLineParam.startId = res.data.startPointId;
          that.isEditLineParam.endId = res.data.endPointId;
          // 有起始点存在情况的开始点经度
          that.addParam.isShowStartlnglat = true;
          $(".map-rightArea").show();
          switch (type) {
            // 光缆的目前没数据 先隐藏
            case "GLHZ":
              that.chooseItems = "架空线路";
              that.selectCheck.isShowCpVis = 0;
              that.selectCheck.objVis.showOpticCable = true;
              that.editData = res.data;
              break;
            case "ZXLHZ":
              that.chooseItems = "架空线路";
              // 架空线更新线的时候取开始点id 这样直接更新整个点数据
              that.isEditPointParam.id = params.startId;
              that.selectCheck.isShowCpVis = 0;
              that.selectCheck.objVis.showMainLine = true;
              that.isIditLine = true;
              that.editData = res.data;
              break;
            case "FXLHZ":
              that.chooseItems = "架空线路";
              that.selectCheck.isShowCpVis = 0;
              // 架空线更新线的时候取开始点id 这样直接更新整个点数据
              that.isEditPointParam.id = params.startId;
              that.selectCheck.objVis.showBranchLine = true;
              that.isIditLine = true;
              that.editData = res.data;
              break;
            case "JKDLHZ":
              that.chooseItems = "架空线路";
              that.selectCheck.isShowCpVis = 0;
              that.selectCheck.objVis.showOverLine = true;
              that.editData = res.data;
              break;
            case "TGBJHZ":
              that.chooseItems = "架空线路";
              that.selectCheck.isShowCpVis = 0;
              that.selectCheck.objVis.showleverHead = true;
              that.editData = res.data;
              break;
            case "DXHZ":
              that.selectCheck.isShowCpVis = 0;
              that.selectCheck.objVis.showonductor = true;
              that.chooseItems = "架空线路";
              that.editData = res.data;
              break;
            case "DLBHGHZ":
              that.selectCheck.isShowCpVis = 0;
              that.chooseItems = "电缆线路";
              that.selectCheck.objVis.showCableTube = true;
              that.editData = res.data;
              break;
            case "DLTDHZ":
              that.chooseItems = "电缆线路";
              that.selectCheck.isShowCpVis = 0;
              that.selectCheck.objVis.showCableChannels = true;
              that.editData = res.data;
              break;
          }
        }
      });
    },
    /**
     * 获取基础参数
     */
    getBasicSetting() {
      const that = this;
      let params = { userId: this.userId }
    //  接口改造 apipost
      apipost(`/moduleSelection/selectBasicParameter`, params
      ).then(
        function (res) {
          if (res.code === 1001) {
            if (res.data.isAllNull) {
              const obj1 = [
                res.data.pltype.zx1, // 单回直线
                res.data.pltype.zj1, // 单回转角
                res.data.pltype.nz1, // 单回耐张
                res.data.pltype.zd1, // 单回终端
              ];
              const obj2 = [
                res.data.pltype.zx2, // 双回直线
                res.data.pltype.zj2, // 双回转角
                res.data.pltype.nz2, // 双回耐张
                res.data.pltype.zd2, // 双回终端
              ];
              const obj3 = [
                res.data.pltype.zx3, // 三回直线
                res.data.pltype.zj3, // 三回转角
                res.data.pltype.nz3, // 三回耐张
                res.data.pltype.zd3, // 三回终端
              ];
              const obj4 = [
                res.data.pltype.zx4, // 四回直线
                res.data.pltype.zj4, // 四回转角
                res.data.pltype.nz4, // 四回耐张
                res.data.pltype.zd4, // 四回终端
              ];
              that.basicSettingData.plArray.push(obj1, obj2, obj3, obj4); // 排列方式数组
              // 耐张段长度
              that.basicSettingData.nzLength = res.data.others.nzdcd;
            } else {
              Toast.fail({
                message: "基础参数未设置完整，请先更新",
                duration: 500,
              });
              return setTimeout(() => {
                that.$router.push({
                  path: "/setting/basicSetting",
                  query: { childProjectId: that.$route.query.childProjectId },
                });
              }, 1000);
            }
          }
        }
      );
    },
    /**
     * 获取气象区
     */
    getProjcetSetting() {
      const that = this;
      const val = that.$route.query.childProjectId;
      //  接口改造特殊  apipost
      let params ={id:val}
      apipost(`/s-basic-parm/search`,params).then(function (res) {
        if (res.code === 1001) {
          if (res.data === null) {
            Toast.fail({
              message: "工程参数未设置，请先更新",
              duration: 500,
            });
            // return setTimeout(() => {
            //   that.$router.push({
            //     path: '/setting/projectSetting',
            //     query: { 'childProjectId': that.$route.query.childProjectId }
            //   })
            // }, 1000)
            that.projectParmSet = true;
          } else {
            that.isInitMap = true;
            switch (res.data.gcssqxq) {
              case "A气象区":
                that.basicSettingData.qxType = 1;
                break;
              case "B气象区":
                that.basicSettingData.qxType = 2;
                break;
              case "C气象区":
                that.basicSettingData.qxType = 3;
                break;
              case "D1气象区":
                that.basicSettingData.qxType = 4;
                break;
              case "D2气象区":
                that.basicSettingData.qxType = 5;
                break;
              case "E1气象区":
                that.basicSettingData.qxType = 6;
                break;
              case "E2气象区":
                that.basicSettingData.qxType = 7;
                break;
              case "E3气象区":
                that.basicSettingData.qxType = 8;
                break;
            }
          }
        }
      });
    },
    /**
     * 自动选型时候获取排列方式
     * @param LoopNumber 回路数
     * @param AngleRange 角度
     * @param NaizhangLength 耐张长度
     * @param IsFirst 是否终端点(如果会接着自动踩点，当前点就不是终端点)
     * @returns {string|*}
     */
    getPlType(LoopNumber, AngleRange, NaizhangLength, IsFirst) {
      let ArrayMode = "";
      let FinalList = [];
      const SingleList = this.basicSettingData.plArray[0]; // 基础参数排列方式单回
      const doubleList = this.basicSettingData.plArray[1]; // 基础参数排列方式单回
      const TribleList = this.basicSettingData.plArray[2]; // 基础参数排列方式单回
      const QuardraList = this.basicSettingData.plArray[3]; // 基础参数排列方式单回
      switch (LoopNumber) {
        case "单回":
          FinalList = SingleList;
          break;
        case "双回":
          FinalList = doubleList;
          break;
        case "三回":
          FinalList = TribleList;
          break;
        case "四回":
          FinalList = QuardraList;
          break;
      }
      if (IsFirst) {
        ArrayMode = FinalList[3];
      } else {
        if (AngleRange <= 15) {
          const nz = 0;
          if (NaizhangLength > nz) {
            ArrayMode = FinalList[2];
          } else {
            ArrayMode = FinalList[0];
          }
        } else {
          ArrayMode = FinalList[1];
        }
      }
      return ArrayMode;
    },
    // 站内设计
    znsjChange() {
      this.isChoosePdzfIcon = true;
      $(".map-endkc").show();
      Toast.fail({
        message: "请选择站房图源",
        duration: 1000,
      });
    },
  },
};
</script>
<style>
.el-dialog__body {
  padding: 8px 16px !important;
  overflow: auto;
  overflow-x: hidden;
  max-height: 90vh;
}
</style>
<style lang="scss" scoped>
@import "./../../styles/map.css";
@import "./../../styles/globals.css";

::v-deep {
  .van-row {
    .van-field--disabled .van-field__label {
      color: #646566;
    }
  }
  .el-dialog {
    .el-dialog__header {
      margin-right: 0 !important;
    }
  }
}
.zuobiao{
  position: absolute;
  bottom: 50px;
  right: 0;
  z-index: 99999;
}
</style>

