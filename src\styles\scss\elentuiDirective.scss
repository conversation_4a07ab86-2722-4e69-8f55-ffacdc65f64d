/*表单间距*/
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 240px;
}
.el-form-item--small .el-form-item__content,
.el-form-item--small .el-form-item__label {
  width: 240px;
}
.el-form-item__label {
  padding: 0 4px 0 0 !important;
}
.el-select.el-select--small {
  width: 100%;
}
.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 16px;
  margin-right: 0;
}
.el-form-item__label {
  margin-left: 0;
  text-align: right !important;
}
.el-input__inner:hover,
.el-select:hover .el-input__inner {
  border-color: #7c92eb !important;
}
.el-input__inner:focus {
  border-color: #7c92eb !important;
}
.el-year-table td .cell:hover,
.el-year-table td.current:not(.disabled) .cell {
  background: #f0f4ff;
  color: #606266;
}
.el-year-table td.today .cell {
  border: 1px solid #7c92eb;
  color: #7c92eb;
}
.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background: #f0f4ff !important;
}
.el-select-dropdown__item.selected {
  color: #7c92eb !important;
}
.el-table thead {
  height: 48px !important;
  background: #ebedf0 !important;
}
.el-table__row {
  height: 48px !important;
}
tr.current-row > td,
.el-table__body tr:hover > td {
  background: #ebedf0 !important;
}
.el-descriptions--small:not(.is-bordered) .el-descriptions-item__cell {
  padding-bottom: 16px !important;
}
// .el-tree .el-tree-node__content {
//   height: 32px !important;
//   line-height: 32px !important;
// }
.el-tree-node__content > .el-tree-node__expand-icon {
  width: 16px !important;
  padding: 4px !important;
}
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background: #ecf2fe !important;
}

.el-date-table td.today span {
  border: 1px solid #7c92eb;
  color: #7c92eb;
  border-radius: unset;
}
.el-date-table td.current:not(.disabled) span {
  border: 1px solid #7c92eb;
  color: #ffffff;
  background: #526ade !important;
  border-radius: unset;
}
.el-date-table td.available:hover {
  color: #7c92eb !important;
}
.uploadFiles:hover {
  background: #ffffff !important;
  color: #7c92eb !important;
  font-weight: bold;
  border-color: #7c92eb !important;
}
.uploadFiles {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  border: 1px solid #dcdfe6;
  color: #606266;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: none;
  margin: 0;
  transition: 0.1s;
  font-weight: 500;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 4px;
}
/*按钮点击*/
.uploadFiles:focus {
  background: #ffffff !important;
  color: #7c92eb !important;
  font-weight: bold;
  border-color: #7c92eb !important;
}
.upload-demo {
  margin: 10px !important;
}
.el-upload-list__item-name [class^='el-icon'] {
  color: #526ade !important;
}
.el-loading-spinner i {
  color: #526ade;
  font-size: 30px;
}
.el-table--small {
  font-size: 16px !important;
}
.elx-table .elx-body--row.row--checked,
.elx-table .elx-body--row.row--current,
.elx-table .elx-body--row.row--radio {
  background: #ebedf0 !important;
}
.el-table th.el-table__cell > .cell {
  font-size: 16px;
  font-weight: normal;
}
.el-date-table td.end-date span,
.el-date-table td.start-date span {
  background: #526ade !important;
}
.el-dropdown-menu--small .el-dropdown-menu__item {
  display: flex;
  align-items: center;
  justify-content: center;
}
.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:not(.el-buttonDisabled):hover {
  background: #f0f4ff !important;
}
