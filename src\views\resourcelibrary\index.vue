<template>
  <div>
    <div class="main-header">图纸信息维护</div>
    <div class="driver"></div>
    <div class="">
      <div class="item-content">
        <DrawingTool :tabKey="activeName" v-if="activeName === 'drawing'" />
      </div>
      <div class="item-content mainArea">
        <el-container>
          <el-aside
            :style="{ height: tableHeight + 'px' }"
            style="width: 240px; overflow-y: scroll"
          >
            <Treeleft
              :tabKey="activeName"
              v-if="activeName === 'drawing'"
              v-on:treeclick="treeclick"
            />
          </el-aside>
          <el-container>
            <el-main>
              <el-table
                :data="drawingData"
                v-loading="loading"
                :height="tableHeight"
                align="center"
                highlight-current-row
                :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
                style="width: 98%"
                class="widthFull"
              >
                <el-table-column
                  type="index"
                  label="序号"
                  align="center"
                  width="60"
                >
                </el-table-column>
                <el-table-column type="selection" width="55"> </el-table-column>
                <el-table-column
                  prop="DrawingName"
                  label="图纸名称"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  prop="DrawingNo"
                  label="图纸编号"
                  width="100"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  prop="DrawingNo"
                  label="图号"
                  width="100"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  prop=""
                  label="考核类别"
                  width="100"
                  align="center"
                >
                </el-table-column>
                <el-table-column prop="" label="图幅" width="80" align="center">
                </el-table-column>
                <el-table-column prop="" label="顺序" width="80" align="center">
                </el-table-column>
                <el-table-column prop="" label="操作" width="80" align="center">
                </el-table-column>
              </el-table>
              <el-pagination
                style="margin: 10px"
                background
                :current-page="queryParams3.pageNum"
                :page-sizes="pageSize"
                :page-size="queryParams3.pageSize"
                layout="total, sizes, prev, pager, next"
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                :total="total3"
              >
              </el-pagination>
            </el-main>
          </el-container>
        </el-container>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getDrawingList,
  getModuleList,
  getMaterialsProjectList,
} from '@/views/basicParameter/data-static/data.js'
import Treeleft from './components/Treeleft.vue'
import MaterialTool from './components/material-tool.vue'
import ModuleTool from './components/module-tool.vue'
import DrawingTool from './components/drawing-tool.vue'
export default {
  name: 'Drawingmanage',
  components: { Treeleft, MaterialTool, ModuleTool, DrawingTool },
  data () {
    return {
      name: '',
      labelPosition: 'right',
      activeName: 'drawing',
      tableHeight: 0,
      form: {
        selectVersion: 1,
      },
      loading: true,
      // 总条数
      total1: 0,
      total2: 0,
      total3: 0,
      // 查询参数
      queryParams1: {
        pageNum: 1,
        pageSize: 10,
        search: '',
      },
      queryParams2: {
        pageNum: 1,
        pageSize: 10,
        search: '',
      },
      pageSize: [10, 20, 50, 100], //分页页数
      queryParams3: {
        pageNum: 1,
        pageSize: 10,
        search: '',
      },
      materialData: [],
      moduleData: [],
      drawingData: [],
      materialProperty: [],
      rules: {},
      version: [
        { Id: 1, Name: '20221024' },
        { Id: 2, Name: '20220915' },
      ],
    }
  },
  mounted () {
    this.setTablesHeight()
    const that = this
    window.onresize = function () {
      that.setTablesHeight()
    }
  },
  created () {
    this.loading = false
  },
  methods: {
    setTablesHeight () {
      this.$nextTick(() => {
        const tablesAreaHeight = document
          .getElementsByClassName('driver')[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        this.tableHeight = windowsAreaHeight - tablesAreaHeight - 180
      })
    },
    rowIndex ({ row, rowIndex }) {
      //增加索引
      if (this.activeName === 'material') {
        row.index =
          rowIndex +
          1 +
          (this.queryParams1.pageNum - 1) * this.queryParams1.pageSize
      } else if (this.activeName === 'module') {
        row.index =
          rowIndex +
          1 +
          (this.queryParams2.pageNum - 1) * this.queryParams2.pageSize
      } else if (this.activeName === 'drawing') {
        row.index =
          rowIndex +
          1 +
          (this.queryParams3.pageNum - 1) * this.queryParams3.pageSize
      }
    },
    formatterWeight (row, column, cellValue, index) {
      if (cellValue == 0) return ''
      return cellValue.toFixed(2)
    },
    formatterIsDonor (row, column, cellValue, index) {
      if (cellValue === undefined || cellValue === 0) return '甲供'
      else if (cellValue === 1) return '乙供'
      return ''
    },
    treeclick (nodekey, tabkey) {
      if (tabkey === 'material') {
        if (this.queryParams1.search != nodekey) {
          this.queryParams1.pageNum = 1
        }
        this.queryParams1.search = nodekey
        this.getCurrentMaterialsProjectList()
      } else if (tabkey === 'module') {
        if (this.queryParams2.search != nodekey) {
          this.queryParams2.pageNum = 1
        }
        this.queryParams2.search = nodekey
        this.getCurrentModuleList()
      } else if (tabkey === 'drawing') {
        if (this.queryParams3.search != nodekey) {
          this.queryParams3.pageNum = 1
        }
        this.queryParams3.search = nodekey
        this.getCurrentDrawingList()
      }
    },
    getCurrentMaterialsProjectList () {
      this.loading = true
      console.log('获取物料')
      let response = getMaterialsProjectList(this.queryParams1)
      this.materialData = response.rows
      this.total1 = response.total
      this.loading = false
    },
    getCurrentModuleList () {
      this.loading = true
      console.log('获取模块')
      let response = getModuleList(this.queryParams2)
      this.moduleData = response.rows
      this.total2 = response.total
      this.loading = false
    },
    handleCurrentChange (val) {
      this.queryParams3.pageNum = val
      let response = getDrawingList(this.queryParams3)
      this.drawingData = response.rows

      this.total3 = response.total
      this.loading = false
    },
    handleSizeChange (val) {
      this.queryParams3.pageSize = val
      let response = getDrawingList(this.queryParams3)
      this.drawingData = response.rows
      this.total3 = response.total
      this.loading = false
    },
    getCurrentDrawingList () {
      this.loading = true
      console.log('获取图纸')
      let response = getDrawingList(this.queryParams3)
      this.drawingData = response.rows
      console.log(this.drawingData, this.queryParams3, 'kkkkk')
      this.total3 = response.total
      this.loading = false
    },
    handleClick (tab, event) { },
  },
}
</script>

<style scoped lang="scss">
.app-container {
  background-color: #fff;
}
.widthFull {
  width: 100%;
}
aside {
  padding: 8px;
}
.el-main {
  padding: 0;
  padding-left: 16px !important;
}
.content-title {
  position: relative;
  padding-left: 10px;
  color: #526ade;
}
.content-title::after {
  content: "";
  position: absolute;
  left: 0;
  top: 50%; /* 将竖杠放置在元素的垂直中心位置 */
  transform: translateY(-50%);
  width: 5px; /* 竖杠的宽度 */
  height: 17px; /* 竖杠的高度 */
  background-color: #526ade; /* 竖杠的颜色 */
  border-radius: 5px; /* 添加弧度 */
  content: "";
}
::v-deep.widthFull .cell {
  font-size: 14px !important;
}
.mainArea {
  margin: 0 16px 16px 16px;
}
</style>
