<template>
  <div>
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div v-if="showBackAdds" class="maps-zhedNav" @click="backCurrentDom">
            <img
              class="mapites-backImg"
              :src="require('@/assets/' + 'map/settingImg/backAdds.png')"
              alt=""
            />
          </div>
          <span />
          柱上设备
          <div
            v-if="!showBackAdds"
            class="maps-zhedNav"
            @click="showEveryItemSet"
          >
            <img
              v-show="!isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zhedie.png')"
              alt=""
            />
            <img
              v-show="isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zkzhedie.png')"
              alt=""
            />
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img
            class="settingImg"
            :src="require('@/assets/' + 'map/settingImg/useSetting.png')"
            alt=""
          />
          <p>启用</p>
        </div>
      </div>
      <div v-show="isFoldArea" class="pro-addForm">
        <div class="map-showNav">基本信息</div>
        <van-row>
          <van-field
            v-model="columnEqu.mark"
            label="编号"
            placeholder="请输入柱上设备编号"
          />
        </van-row>
        <div class="map-showNav">选型信息</div>
        <van-row>
          <!--柱上设备状态-->
        </van-row>
        <van-row>
          <van-field
            readonly
            clickable
            :value="columnEqu.state"
            label="状态"
            placeholder="请选择状态"
            @click="settingObj.columnEqu.state = true"
          />
          <van-popup
            v-model="settingObj.columnEqu.state"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="状态"
              value-key="key"
              :columns="cableLineState"
              @confirm="onConfirmZssbSel(0, $event)"
              @cancel="settingObj.columnEqu.state = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--柱上设备类型-->
          <van-field
            readonly
            clickable
            :value="columnEqu.equiType"
            label="柱上设备类型"
            placeholder="柱上设备类型"
            @click="settingObj.columnEqu.equiModel = true"
          />
          <van-popup
            v-model="settingObj.columnEqu.equiModel"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="柱上设备类型"
              value-key="moduletypename"
              :columns="euqipType"
              @confirm="onConfirmZssbSel(1, $event)"
              @cancel="settingObj.columnEqu.equiModel = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--柱上设备编号-->
          <van-field
            v-model="columnEqu.equiNum"
            label="设备编号"
            placeholder="请输入设备编号"
          />
        </van-row>
        <van-row>
          <!--柱上设备是否接地-->
          <van-field
            readonly
            clickable
            :value="columnEqu.isGround"
            label="是否接地"
            placeholder="请选择接地状态"
            @click="settingObj.columnEqu.isGround = true"
          />
          <van-popup
            v-model="settingObj.columnEqu.isGround"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="接地状态"
              value-key="key"
              :columns="trueOrFalse"
              @confirm="onConfirmZssbSel(3, $event)"
              @cancel="settingObj.columnEqu.isGround = false"
            />
          </van-popup>
        </van-row>
        <!--柱上设备型号-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="columnEqu.equiModel"
            label="设备型号"
            placeholder="请选择设备型号"
            @click="settingObj.columnEqu.equipType = true"
          />
          <van-popup
            v-model="settingObj.columnEqu.equipType"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="设备型号"
              value-key="moduleName"
              :columns="euqipTypeModel"
              @confirm="onConfirmZssbSel(2, $event)"
              @cancel="settingObj.columnEqu.equipType = false"
            />
          </van-popup>
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { apiget,apipost } from "@/utils/mapRequest";

export default {
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false,
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的id
    editData: {
      type: Object,
      defaults: () => {},
    },
    remodeState: {
      type: Boolean,
      defaults: false,
    },
  },
  data() {
    return {
      isFoldArea: false, // 是否展开区域
      lngtitude: "",
      lattitude: "",
      highNum: "",
      settingObj: {
        // 柱上设备
        columnEqu: {
          state: false, // 状态
          equipType: false, // 设备型号
          equiModel: false, // 变台型号
          isGround: false, // 是否接地
        },
      },
      cableLineState: [], // 电缆线路状态
      lineState: [
        {
          key: "新建",
          value: "新建",
        },
        {
          key: "原有",
          value: "原有",
        },
        {
          key: "拆除",
          value: "拆除",
        },
      ],
      // 柱上设备
      columnEqu: {
        state: "新建", // 状态
        voltage: "", // 电压等级
        equiType: "柱上断路器", // 设备型号
        equiTypeId: "", // 设备型号
        equiModel: "", // 设备型号
        equiModelId: "", // 设备型号id
        equiNum: "zssb001", // 设备编号
        mark: "", // 编号
        isGround: "是", // 是否接地
        imgList: [], // 文件列表
        message: "", // 备注信息
        audioList: [], // 语音列表
      },
      euqipTypeModel: [], // 柱上设备设备型号 从后台读
      euqipType: [], // 柱上设备类型
      trueOrFalse: [
        {
          key: "是",
          value: "是",
        },
        {
          key: "否",
          value: "否",
        },
      ], // 是否
    };
  },
  watch: {
    // 监听改前改后状态
    remodeState: {
      handler(newVal) {
        if (newVal) {
          // 改后不显示拆除的数据
          this.cableLineState = this.lineState.slice(0, 2);
          this.columnEqu.state = "新建";
        } else {
          // 改前不显示新建的数据
          this.cableLineState = this.lineState.slice(1);
          this.columnEqu.state = "原有";
        }
      },
      deep: true,
      immediate: true,
    },
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal;
      },
      deep: true,
      immediate: true,
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal;
        if (data.moudleType === "ZSSBHZ") {
          this.lngtitude = data.longitude;
          this.lattitude = data.latitude;
          this.highNum = data.high;
          this.getAwaitTowerOrLineType(
            0,
            "",
            3,
            "",
            "",
            "10SNGZSSB",
            "",
            "",
            "",
            data.zssbType
          );
          // 查询柱上设备类型
          // 查询柱上设备类型
          this.columnEqu.imgList = [];
          this.columnEqu.audioList = [];
          this.columnEqu.state = data.state;
          this.columnEqu.equiModelId = data.sbModel;
          this.columnEqu.equiModel = data.sbModelSpec;
          this.columnEqu.equiNum = data.equipBh;
          this.columnEqu.equiType = data.zssbType;
          this.columnEqu.isGround = data.isCht === "1" ? "是" : "否";
          this.columnEqu.mark = data.mark; // 编号
          this.columnEqu.message = data.note;
          for (const k in data.imgList) {
            const objs = {
              url: data.imgList[k].path,
              isImage: true,
              isSaveReport: data.imgList[k].isSaveReport,
            };
            this.columnEqu.imgList.push(objs);
          }
          for (const s in data.voiceList) {
            const objs = {
              content: data.voiceList[s].path,
            };
            this.columnEqu.audioList.push(objs);
          }
        }
      },
      deep: true,
    },
  },
  mounted() {
    /* 查询柱上设备类型 */
    this.getFirstTowerOrLineType(0, "", 3, "", "", "10SNGZSSB", "", "");
  },
  methods: {
    /**
     * 获取语音数据
     */
    getAudioData(data) {
      this.columnEqu.audioList = data.aduioList;
    },
    /**
     * 获取上传图片数据
     */
    getImgData(data) {
      this.columnEqu.imgList = data.imgList;
    },
    /**
     * 获取备注信息
     * @param data
     */
    getMsgData(data) {
      this.columnEqu.message = data.message;
    },
    /**
     * 提交数据
     */
    submitData() {
      const parma = {
        type: 9,
        param: this.columnEqu,
        visParam: this.settingObj.columnEqu,
      };
      this.$emit("submitChildData", parma);
    },
    backCurrentDom() {
      this.$emit("backCurrentDom");
      this.getFirstTowerOrLineType(0, "", 3, "", "", "SGZSB", "", "");
      // 柱上变压器
      const stateText = this.remodeState ? "新建" : "原有";
      this.columnEqu.state = stateText;
      this.columnEqu.equiType = "柱上断路器";
      this.columnEqu.equiModel = "";
      this.columnEqu.equiModelId = "";
      this.columnEqu.equiNum = "zssb001";
      this.columnEqu.isGround = "是";
      this.columnEqu.imgList = [];
      this.columnEqu.message = "";
      this.columnEqu.audioList = [];
      this.settingObj.columnEqu.state = false;
      this.settingObj.columnEqu.equipType = false;
      this.settingObj.columnEqu.equiModel = false;
      this.settingObj.columnEqu.isGround = false;
    },
    showEveryItemSet() {
      this.isFoldArea = !this.isFoldArea;
    },
    /**
     * 柱上设备
     */
    onConfirmZssbSel(type, item) {
      const val = item.value;
      switch (type) {
        case 0:
          this.columnEqu.state = val;
          this.settingObj.columnEqu.state = false;
          break;
        case 1:
          this.columnEqu.equiType = item.moduletypename;
          this.columnEqu.equiTypeId = item.moduletypeid;
          this.getTowerOrLineType("", 4, "", item.moduletypekey, "", "", "");
          this.settingObj.columnEqu.equiModel = false;
          break;
        case 2:
          this.columnEqu.equiModel = item.moduleName;
          this.columnEqu.equiModelId = item.moduleID;
          this.settingObj.columnEqu.equipType = false;
          break;
        case 3:
          this.columnEqu.isGround = val;
          this.settingObj.columnEqu.isGround = false;
          break;
      }
    },
    getFirstTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          switch (settype) {
            case 0:
              // 柱上设备类型
              that.columnEqu.equiType = res.data[0].moduletypename;
              that.columnEqu.equiTypeId = res.data[0].moduletypeid;
              that.euqipType = res.data;
              that.getFirstTowerOrLineType(
                1,
                "",
                4,
                "",
                res.data[0].moduletypekey,
                "",
                "",
                ""
              );
              break;
            case 1:
              // 柱上设备型号
              that.columnEqu.equiModel = res.data[0].moduleName;
              that.columnEqu.equiModelId = res.data[0].moduleID;
              that.euqipTypeModel = res.data;
              break;
          }
        }
      });
    },
    getTowerOrLineType(
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          // 柱上设备型号
          that.euqipTypeModel = res.data;
          if (res.data.length === 0) {
            that.columnEqu.equiModel = "";
            that.columnEqu.equiModelId = "";
          } else {
            that.columnEqu.equiModel = res.data[0].moduleName;
            that.columnEqu.equiModelId = res.data[0].moduleID;
          }
        }
      });
    },
    getAwaitTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName,
      selectVal,
      selectLevelTwoVal
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          switch (settype) {
            case 0:
              // 柱上设备类型
              that.euqipType = res.data;
              let euqipType;
              for (const j in res.data) {
                if (selectVal === res.data[j].moduletypename) {
                  euqipType = res.data[j].moduletypekey;
                }
              }
              that.getAwaitTowerOrLineType(1, "", 4, "", euqipType, "", "", "");
              break;
            case 1:
              // 柱上设备型号
              that.euqipTypeModel = res.data;
              break;
          }
        }
      });
    },
  },
};
</script>

<style lang="sass" scoped>
</style>

