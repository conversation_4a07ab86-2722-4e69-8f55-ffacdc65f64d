<template>
  <!-- 结算成果展示 -->
  <div class="index flex-c h100">
    <div class="query-form-box">
      <!-- <el-form
        ref="form"
        :model="form"
        :inline="true"
        @submit.native.prevent
        label-width="120px"
      >
        <el-form-item label="工程名称:">
          <span>{{ projname || '-' }}</span>
        </el-form-item>
        <el-form-item label="建设单位:">
          {{ investmentorg || '-' }}
        </el-form-item>
        <el-form-item label="咨询审核单位:">
          {{ revieworg || '-' }}
        </el-form-item>
      </el-form> -->
      <el-descriptions labelClassName="my-label" size="medium" title="">
        <el-descriptions-item labelClassName="my-label" label="工程名称">{{
          projname || '-'
        }}</el-descriptions-item>
        <el-descriptions-item labelClassName="my-label" label="建设单位">{{
          investmentorg || '-'
        }}</el-descriptions-item>
        <el-descriptions-item labelClassName="my-label" label="咨询审核单位">{{
          revieworg || '-'
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="card-box flex-1 flex-c">
      <div style="margin-bottom: 10px">
        <el-button :loading="btnLoading" @click="download()"
          ><i class="el-icon-download"></i> 全部导出</el-button
        >
      </div>
      <div class="table-content flex-1" ref="tablecontent">
        <div class="table-container-inner">
          <el-table
            v-loading="loading"
            :data="tableData"
            border
            :height="tableHeight"
            style="width: 100%"
            :header-cell-style="{
              'text-align': 'center',
              // background: '#EDF0FC',
              color: '#000',
            }"
            :cell-style="{ 'text-align': 'center' }"
            :row-class-name="xxxTableRowClassName"
            highlight-current-row
          >
            <!-- <el-table-column type="expand">
              <template slot-scope="scope">
                <div
                  class="cg-item"
                  v-for="(item, i) in scope.row.info"
                  :key="i"
                >
                  <span>{{ `${i + 1}. ${item.name}` }}</span>
                  <i
                    :style="{ color: item.upload ? 'green' : 'red' }"
                    :class="
                      item.upload
                        ? 'el-icon-circle-check'
                        : 'el-icon-circle-close'
                    "
                  ></i>
                </div>
              </template>
            </el-table-column> -->
            <el-table-column prop="index" label="序号" width="80" align="center">
            </el-table-column>
            <el-table-column prop="name" label="项目名称" width="400px" align="center">
            </el-table-column>
            <el-table-column prop="info" label="成果形式" align="center">
              <template slot-scope="scope">
                <!-- <div
                  class="flex-box"
                  v-for="(item, i) in scope.row.info"
                  :key="i"
                >
                  <div class="login" :class="item.upload ? 'tick' : ' '"></div>
                  <span>{{ `${i + 1}. ${item.name}` }}</span>
                </div> -->
                <div
                  class="cg-item"
                  v-for="(item, i) in scope.row.info"
                  :key="i"
                  @click="clickCg(scope.row, i)"
                >
                  <i
                    :style="{ color: item.upload ? '#0aaf51' : '#fa3131' }"
                    :class="
                      item.upload
                        ? 'el-icon-circle-check'
                        : 'el-icon-circle-close'
                    "
                  ></i>
                  <el-link>{{ `${i + 1}. ${item.name}` }}</el-link>
                </div>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="" label="操作" width="150">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  style="color:#526ade"
                  @click="add(scope.row)"
                  >操作</el-button
                >
              </template>
            </el-table-column> -->
          </el-table>
        </div>
      </div>
    </div>
    <add v-if="false" :ProCode="ProCode" @getList="getList" ref="add"></add>
    <add-item
      v-if="dialogFormVisible"
      :ProCode="ProCode"
      @getList="getList"
      ref="addItem"
    ></add-item>
  </div>
</template>

<script>
import { getJsData, jscgDownloadAll, resquest } from '@/api/api'
import { tableHeightMixin } from '@/assets/mixin/tableHeightMixin'
import add from './add.vue'
import addItem from './addItem.vue'
export default {
  name: 'jscgzs',
  mixins: [tableHeightMixin],
  components: {
    add,
    addItem,
  },
  data() {
    return {
      // tableHeight: 0,
      // timer: 0,
      form: {
        name: '',
      },
      investmentorg: '',
      projname: '',
      revieworg: '',
      tableData: [
        /*{
          index: '1',
          name: '配电网工程结算审核情况统计表',
          info: [
            {
              name: 'Excel表格',
              upload: false,
              fileList: [
                { name: 'food.jpeg', url: '' },
                { name: 'food2.jpeg', url: '' },
              ],
            },
          ],
        },
        {
          index: '2',
          name: '结算审核报告',
          info: [
            {
              name: 'Word版审核报告',
              upload: false,
              url: '',
            },
            {
              name: 'PDF签字盖章版审核报告',
              upload: true,
              url: '',
            },
            {
              name: '胶装版审核报告（线下）',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '2.1',
          name: '审定结算书',
          info: [
            {
              name: '软件版结算书',
              upload: false,
              url: '',
            },
            {
              name: '结算书',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '2.2',
          name: '甲供物资结算支撑资料',
          info: [
            {
              name: 'ERP物料清单',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '2.3',
          name: '其他费用结算支撑资料',
          info: [
            {
              name: '文物保护评估、环评、洪评',
              upload: false,
              url: '',
            },
            {
              name: '勘察报告，PDF签字盖章版',
              upload: false,
              url: '',
            },
            {
              name: '勘察设计费结算计算说明',
              upload: false,
              url: '',
            },
            {
              name: '监理费结算计算说明',
              upload: false,
              url: '',
            },
            {
              name: '造价咨询费用计算说明',
              upload: false,
              url: '',
            },
            {
              name: '其他',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '2.4',
          name: '施工费结算支撑资料',
          info: [
            {
              name: '设计变更，PDF签字盖章版',
              upload: false,
              url: '',
            },
            {
              name: '工程签证，PDF签字盖章版',
              upload: false,
              url: '',
            },
            {
              name: '其他',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '2.5',
          name: '建设场地征用及清理费结算支撑资料',
          info: [
            {
              name: '协议、赔偿明细、证明等',
              upload: false,
              url: '',
            },
            {
              name: '其他',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '3',
          name: '工程初步设计批复文件（附初步设计评审意见、项目明细表）',
          info: [
            {
              name: '批复文件',
              upload: false,
              url: '',
            },
            {
              name: '项目明细表',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '4',
          name: '工程初步设计批复概算',
          info: [
            {
              name: 'Excel版',
              upload: false,
              url: '',
            },
            {
              name: '软件版',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '5',
          name: '工程开工、竣工验收报告',
          info: [
            {
              name: '开工报告',
              upload: false,
              url: '',
            },
            {
              name: '竣工验收报告',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '6',
          name: '施工招标文件',
          info: [
            {
              name: '公告',
              upload: false,
              url: '',
            },
            {
              name: '文件',
              upload: false,
              url: '',
            },
            {
              name: '技术规范书',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '7',
          name: '施工投标文件',
          info: [
            {
              name: '商务、技术文件等',
              upload: false,
              url: '',
            },
            {
              name: '软件版/Excel版投标文件',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '8',
          name: '施工合同文件',
          info: [
            {
              name: 'Word表单（封面带二维码）',
              upload: false,
              url: '',
            },
            {
              name: 'PDF签字盖章版',
              upload: false,
              url: '',
            },
            {
              name: '中标通知书',
              upload: false,
              url: '',
            },
            {
              name: '框架协议',
              upload: false,
              url: '',
            },
            {
              name: '施工考核单',
              upload: false,
              url: '',
            },
            {
              name: '补充协议',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '9',
          name: '监理审核意见',
          info: [
            {
              name: 'PDF签字盖章版',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '10',
          name: '监理招标文件',
          info: [
            {
              name: '公告、文件和技术规范书',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '11',
          name: '监理投标文件',
          info: [
            {
              name: '商务、技术文件等',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '12',
          name: '监理合同文件',
          info: [
            {
              name: 'Word表单（封面带二维码）',
              upload: false,
              url: '',
            },
            {
              name: 'PDF签字盖章版',
              upload: false,
              url: '',
            },
            {
              name: '中标通知书（PDF、jpg版）',
              upload: false,
              url: '',
            },
            {
              name: '框架协议（PDF、jpg版）',
              upload: false,
              url: '',
            },
            {
              name: '监理考核单（如有）',
              upload: false,
              url: '',
            },
            {
              name: '补充协议（如有）',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '13',
          name: '勘察设计招标文件',
          info: [
            {
              name: '公告、文件和技术规范书',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '14',
          name: '勘察设计投标文件',
          info: [
            {
              name: '商务、技术文件等',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '15',
          name: '勘察设计合同文件',
          info: [
            {
              name: 'Word表单（封面带二维码）',
              upload: false,
              url: '',
            },
            {
              name: 'PDF签字盖章版',
              upload: false,
              url: '',
            },
            {
              name: '中标通知书（PDF、jpg版）',
              upload: false,
              url: '',
            },
            {
              name: '勘察、设计考核单（如有）',
              upload: false,
              url: '',
            },
            {
              name: '补充协议（如有）',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '16',
          name: '结算审核合同文件',
          info: [
            {
              name: 'Word版（封面带二维码）',
              upload: false,
              url: '',
            },
            {
              name: 'PDF签字盖章版',
              upload: false,
              url: '',
            },
            {
              name: '中标通知书',
              upload: false,
              url: '',
            },
            {
              name: '框架协议',
              upload: false,
              url: '',
            },
            {
              name: '考核单',
              upload: false,
              url: '',
            },
            {
              name: '补充协议',
              upload: false,
              url: '',
            },
          ],
        },
        {
          index: '17',
          name: '竣工图纸',
          info: [
            {
              name: 'PDF版、CAD版均可',
              upload: false,
              url: '',
            },
            {
              name: 'Excel版或PDF版图纸目录一份',
              upload: false,
              url: '',
            },
          ],
        }, */
      ],
      loading: false,
      dialogFormVisible: false,
      btnLoading: false,
      downloadAllUrl: resquest + '/tDtfFileModule/downloadAll',
      ProCode: '',
    }
  },
  created() {
    console.log(this.$route.query)
    this.ProCode = this.$route.query.ProCode
  },
  mounted() {
    // this.setTableHeight()
    // window.addEventListener('resize', this.onResize)
    this.getList()
  },
  beforeDestroy() {
    // this.timer && clearTimeout(this.timer)
    // window.removeEventListener('resize', this.onResize)
  },
  methods: {
    // 斑马纹效果
    xxxTableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 == 0) {
        return ''
      } else {
        return 'statistics-warning-row'
      }
    },
    // 设置表格高度
    // setTableHeight() {
    //   this.$nextTick(() => {
    //     let rect = this.$refs.tablecontent.getBoundingClientRect()
    //     this.tableHeight = rect.height
    //   })
    // },
    // onResize() {
    //   this.timer && clearTimeout(this.timer)
    //   this.timer = setTimeout(() => {
    //     this.setTableHeight()
    //   }, 300)
    // },
    // async getList() {
    //   let data = {
    //     ProCode: '1806CB210007-001',
    //   }
    //   const res = await getJsData(data)
    //   this.tableData = res.data.data.tableData
    //   this.investmentorg = res.data.data.investmentorg
    //   this.projname = res.data.data.projname
    //   this.revieworg = res.data.data.revieworg
    //   if (res.data.code == 200) {
    //     // this.tableData = res.data.data.currentPageData
    //     // this.total = res.data.data.totalCount
    //   } else {
    //     // this.$message.info(res.data.msg)
    //   }
    // },
    getList() {
      this.loading = true
      let data = {
        // ProCode: '1806CB210007-001',
        ProCode: this.ProCode,
      }
      getJsData(data)
        .then((res) => {
          this.tableData = res.data.data.tableData
          this.investmentorg = res.data.data.investmentorg
          this.projname = res.data.data.projname
          this.revieworg = res.data.data.revieworg
          // this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },
    add(e) {
      console.log(e)
      this.dialogFormVisible = true // 控制弹出框显示
      this.$nextTick(() => {
        this.$refs.add.init(e) // init（）是子组件函数
      })
    },
    clickCg(row, i) {
      this.dialogFormVisible = true // 控制弹出框显示
      this.$nextTick(() => {
        this.$refs.addItem.init(row, i) // init（）是子组件函数
      })
    },
    download() {
      // this.btnLoading = true
      // let data = {
      //   proCode: '1806CB210007-001',
      // }
      // jscgDownloadAll(data)
      //   .then((res) => {
      //     // if (res) {
      //     //   this.btnLoading = false
      //     //   this.$message.success('导出成功')
      //     // }
      //     this.downloadChange(res.data)
      //   })
      //   .catch(() => {
      //     this.btnLoading = false
      //     this.$message.info('导出失败')
      //   })
      // if (this.tableData.length == 0) {
      //   this.$message.info('暂无数据')
      //   return
      // }
      window.open(`${this.downloadAllUrl}?proCode=${this.ProCode}`)
    },
    downloadId(row) {
      console.log(row)
    },

    //没有titName可以不指定，直接复制下面方法在请求完成调用就行
    downloadChange(data, titName) {
      if (!data) {
        return
      }
      const content = data
      const blob = new Blob([content], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      })
      const fileName = titName ? titName : ''
      if ('download' in document.createElement('a')) {
        // 非IE下载
        var downloadElement = document.createElement('a')
        var href = window.URL.createObjectURL(blob) // 创建下载的链接
        var reg = /^["](.*)["]$/g
        downloadElement.style.display = 'none'
        downloadElement.href = href
        downloadElement.download = decodeURI(fileName.replace(reg, '$1')) // 下载后文件名
        document.body.appendChild(downloadElement)
        downloadElement.click() // 点击下载
        document.body.removeChild(downloadElement) // 下载完成移除元素
        window.URL.revokeObjectURL(href)
      } else {
        // IE10+下载
        navigator.msSaveBlob(blob, fileName)
      }
    },
  },
}
</script>
<style>
.my-label {
  color: #000;
  font-weight: 600;
}
</style>
<style lang="scss" scoped>
// ::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {
//   background-color: rgba(231, 243, 243, 0.7) !important;
// }
.cg-item {
  // margin: 8px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  text-align: left;

  span {
    // width: 200px;
  }

  i {
    // margin-left: 16px;
    margin-right: 16px;
  }
}

.cg-item:last-child {
  margin-bottom: 0;
}

.flex-box {
  display: flex;
  align-items: center;
}

.login {
  position: relative;
  width: 20px;
  height: 20px;
  margin: 0 6px;
  border: 1px solid #e64848 !important;
  border-radius: 2px;
  flex-shrink: 0;
}

.tick {
  border: 1px solid green !important;
}

.tick::after {
  content: ' ';
  position: absolute;
  display: inline-block;
  width: 12px;
  height: 6px;
  border-width: 0 0 2px 2px;
  overflow: hidden;
  border-color: green;
  border-style: solid;
  -webkit-transform: rotate(-50deg);
  transform: rotate(-50deg);
  left: 3px;
  top: 4px;
}
</style>
