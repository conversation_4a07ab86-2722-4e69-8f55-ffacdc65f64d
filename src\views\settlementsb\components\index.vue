<template>
  <div class="setArea">
    <div class="setFormArea">
      <!-- 基础信息 结算信息 概算信息 -->
      <el-card class="box-card">
        <el-descriptions title="基础信息:">
          <el-descriptions-item label="工程编码">{{
            this.foundationXX.code
          }}</el-descriptions-item>
          <el-descriptions-item label="工程名称">{{
            this.foundationXX.projectName
          }}</el-descriptions-item>
          <el-descriptions-item label="地市名称">{{
            this.foundationXX.cityName
          }}</el-descriptions-item>
          <el-descriptions-item label="项目包编码">{{
            this.foundationXX.codeName
          }}</el-descriptions-item>
          <el-descriptions-item label="项目包名称">{{
            this.foundationXX.xmmc
          }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions title="结算信息(单位:元)">
          <span slot="title" v-if="ProCode.tgzt == '6'">
            <div style="display: flex; align-items: center">
              <span>结算信息(单位:元)</span
              ><el-upload
                class="upload-demo"
                accept=".word,.xlsx,.xls,txt,bmp,.docx,.doc"
                :before-upload="beforeDesignUpload"
                :show-file-list="false"
                :on-success="DesignSuccess"
                :on-error="DesignError"
                :file-list="JSbookList.file"
                :http-request="JSbookListUpload"
              >
                <el-button class="blue-btn"
                  ><i class="el-icon-upload2"></i> 导入结算书
                </el-button>
              </el-upload>
            </div>
          </span>
        </el-descriptions>
        <el-form ref="formJS" :model="formJS" :inline="true" :rules="rules">
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="结算报审结算费总额:"
                prop="sgjsf"
                label-width="175px"
              >
                <el-input
                  v-model="formJS.sgjsf"
                  clearable
                  @input="handleNumericInput('sgjsf')"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="结算报审建筑工程费:"
                prop="bstjf"
                label-width="175px"
              >
                <el-input
                  v-model="formJS.bstjf"
                  clearable
                  @input="handleNumericInput('bstjf')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="结算报审安装工程费:"
                prop="bsazf"
                label-width="175px"
              >
                <el-input
                  v-model="formJS.bsazf"
                  clearable
                  @input="handleNumericInput('bsazf')"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="结算报审其他费用:"
                prop="bstf"
                label-width="175px"
              >
                <el-input
                  v-model="formJS.bstf"
                  clearable
                  @input="handleNumericInput('bstf')"
                ></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item label="结算报审基本预备费:" prop="jbybf" label-width="175px">
                <el-input v-model="formJS.jbybf" clearable @input="handleNumericInput('jbybf')"></el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item
                label="结算报审设备购置费:"
                prop="sbgzf"
                label-width="175px"
              >
                <el-input
                  v-model="formJS.sbgzf"
                  clearable
                  @input="handleNumericInput('sbgzf')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="结算报审动态投资:"
                prop="ssdttz"
                label-width="175px"
              >
                <el-input
                  v-model="formJS.ssdttz"
                  clearable
                  @input="handleNumericInput('ssdttz')"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="结算报审甲供设备费:"
                prop="bsjgsbf"
                label-width="175px"
              >
                <el-input
                  v-model="formJS.bsjgsbf"
                  clearable
                  @input="handleNumericInput('bsjgsbf')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="结算报审甲供材料费:"
                prop="bsjgclf"
                label-width="175px"
              >
                <el-input
                  v-model="formJS.bsjgclf"
                  clearable
                  @input="handleNumericInput('bsjgclf')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="计划审核日期:"
                prop="jhshrq"
                label-width="175px"
              >
                <el-date-picker
                  v-model="formJS.jhshrq"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="结算报审日期:"
                prop="sendDate"
                label-width="175px"
              >
                <el-date-picker
                  v-model="formJS.sendDate"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="完工报告固定资产信息:" label-width="175px">
                <el-input
                  v-model="formJS.wgbggdzcxx"
                  clearable
                  oninput="value=value.replace(/[！@#￥%……&*（）——+~!@#$$%^&*()_+=]+/g,'')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="结算报审静态投资:"
                prop="ssjttz"
                label-width="175px"
              >
                <el-input
                  v-model="formJS.ssjttz"
                  clearable
                  @input="handleNumericInput('ssjttz')"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row> </el-row>
        </el-form>
        <el-descriptions title="概算信息(单位:元)"> </el-descriptions>
        <el-form ref="formGS" :model="formGS" :inline="true" :rules="rules">
          <el-row>
            <el-col :span="8">
              <el-form-item label="总概算费用:" prop="hj" label-width="175px">
                <el-input
                  v-model="formGS.hj"
                  clearable
                  @input="handleNumericInputGS('hj')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="建筑工程费:"
                prop="jzgcf"
                label-width="175px"
              >
                <el-input
                  v-model="formGS.jzgcf"
                  clearable
                  @input="handleNumericInputGS('jzgcf')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="安装工程费:"
                prop="azgcf"
                label-width="175px"
              >
                <el-input
                  v-model="formGS.azgcf"
                  clearable
                  @input="handleNumericInputGS('azgcf')"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="设备购置费:"
                prop="sbgzf"
                label-width="175px"
              >
                <el-input
                  v-model="formGS.sbgzf"
                  clearable
                  @input="handleNumericInputGS('sbgzf')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="其他费用:" prop="qtfy" label-width="175px">
                <el-input
                  v-model="formGS.qtfy"
                  clearable
                  @input="handleNumericInputGS('qtfy')"
                ></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item label="基本预备费:" prop="jbfy" label-width="175px">
                <el-input v-model="formGS.jbfy" clearable @input="handleNumericInputGS('jbfy')"></el-input>
              </el-form-item>
            </el-col> -->
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="设计费:" prop="sjf" label-width="175px">
                <el-input
                  v-model="formGS.sjf"
                  clearable
                  @input="handleNumericInputGS('sjf')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="监理费:" prop="jlf" label-width="175px">
                <el-input
                  v-model="formGS.jlf"
                  clearable
                  @input="handleNumericInputGS('jlf')"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      <el-card class="box-card">
        <el-descriptions title="设计物料清单:"> </el-descriptions>
        <div
          style="margin-bottom: 10px"
          class="buttonUpDon"
          v-show="ProCode.tgzt == '6'"
        >
          <el-upload
            class="upload-demo"
            :data="DesignData()"
            accept=".word,.xlsx,.xls,txt,bmp,.docx,.doc"
            :before-upload="beforeDesignUpload"
            :show-file-list="false"
            :on-success="DesignSuccess"
            :on-error="DesignError"
            :file-list="DesignList.file"
            :http-request="uploadImportJs"
          >
            <el-button class="blue-btn"
              ><i class="el-icon-upload2"></i> 物料导入
            </el-button>
          </el-upload>
          <el-button class="directiveBtn" @click="DesignDown"
            ><i class="el-icon-download"></i> 模板下载</el-button
          >
        </div>
        <template>
          <el-table
            :data="tableDataDesign"
            :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
            height="200"
            border
            style="width: 100%"
          >
            <el-table-column
              type="index"
              :index="DesignindexMethod"
              label="序号"
              align="center"
              :resizable="false"
              width="60"
            >
            </el-table-column>
            <el-table-column prop="bm" label="物料编码" align="center">
            </el-table-column>
            <el-table-column prop="wlms" label="物料描述" align="center">
            </el-table-column>
            <el-table-column prop="sl" label="数量" align="center">
            </el-table-column>
            <el-table-column prop="dw" label="单位" align="center">
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button
                  @click="DesignDet(scope.row)"
                  class="el-button"
                  type="text"
                  size="small"
                  v-show="ProCode.tgzt == '6'"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            style="margin: 10px 0px"
            background
            :current-page="this.formDesign.pageIndex"
            :page-size="this.formDesign.pageSize"
            :page-sizes="pageSizeDesign"
            layout="total, sizes, prev, pager, next"
            @current-change="DesignCurrentChange"
            @size-change="DesignSizeChange"
            :total="DesignTotal"
          >
          </el-pagination>
        </template>
      </el-card>
      <el-card class="box-card">
        <el-descriptions title="申报料单:"> </el-descriptions>
        <div
          style="margin-bottom: 10px"
          class="buttonUpDon"
          v-show="ProCode.tgzt == '6'"
        >
          <el-upload
            class="upload-demo"
            :data="ReportData()"
            accept=".word,.xlsx,.xls,txt,bmp,.docx,.doc"
            :before-upload="beforeDesignUpload"
            :show-file-list="false"
            :on-success="ReportSuccess"
            :on-error="ReportError"
            :file-list="ReportList.file"
            :http-request="uploadImportSBLD"
          >
            <el-button class="blue-btn"
              ><i class="el-icon-upload2"></i> 物料导入
            </el-button>
          </el-upload>
          <el-button class="directiveBtn" @click="ReportDown"
            ><i class="el-icon-download"></i> 模板下载</el-button
          >
        </div>
        <template>
          <el-table
            :data="tableDataReport"
            :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
            height="200"
            border
            style="width: 100%"
          >
            <el-table-column
              type="index"
              :index="ReportindexMethod"
              label="序号"
              align="center"
              :resizable="false"
              width="60"
            >
            </el-table-column>
            <el-table-column prop="bm" label="物料编码" align="center">
            </el-table-column>
            <el-table-column prop="wlms" label="物料描述" align="center">
            </el-table-column>
            <el-table-column prop="sl" label="数量" align="center">
            </el-table-column>
            <el-table-column prop="dw" label="单位" align="center">
            </el-table-column>
            <el-table-column prop="dj" label="单价" align="center">
            </el-table-column>
            <el-table-column prop="zj" label="总价" align="center">
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button
                  @click="ReportDet(scope.row)"
                  class="el-button"
                  type="text"
                  size="small"
                  v-show="ProCode.tgzt == '6'"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            style="margin: 10px 0px"
            background
            :current-page="this.formReport.pageIndex"
            :page-sizes="pageSizeReport"
            :page-size="this.formReport.pageSize"
            layout="total, sizes, prev, pager, next"
            @current-change="ReportCurrentChange"
            @size-change="ReportSizeChange"
            :total="ReportTotal"
          >
          </el-pagination>
        </template>
      </el-card>
      <el-card class="box-card">
        <el-descriptions title="移入料单:"> </el-descriptions>
        <div
          style="margin-bottom: 10px"
          class="buttonUpDon"
          v-show="ProCode.tgzt == '6'"
        >
          <el-upload
            class="upload-demo"
            :data="MoveInData()"
            accept=".word,.xlsx,.xls,txt,bmp,.docx,.doc"
            :before-upload="beforeDesignUpload"
            :show-file-list="false"
            :on-success="MoveInSuccess"
            :on-error="MoveInError"
            :file-list="MoveInList.file"
            :http-request="MoveInupload"
          >
            <el-button class="blue-btn"
              ><i class="el-icon-upload2"></i> 物料导入
            </el-button>
          </el-upload>
          <el-button class="directiveBtn" @click="MoveInDown"
            ><i class="el-icon-download"></i> 模板下载</el-button
          >
        </div>
        <template>
          <el-table
            :data="tableDataMoveIn"
            :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
            height="200"
            border
            style="width: 100%"
          >
            <el-table-column
              type="index"
              :index="MoveInindexMethod"
              label="序号"
              align="center"
              :resizable="false"
              width="60"
            >
            </el-table-column>
            <el-table-column prop="bm" label="物料编码" align="center">
            </el-table-column>
            <el-table-column prop="wlms" label="物料描述" align="center">
            </el-table-column>
            <el-table-column prop="sl" label="数量" align="center">
            </el-table-column>
            <el-table-column prop="dw" label="单位" align="center">
            </el-table-column>
            <el-table-column prop="dj" label="单价" align="center">
            </el-table-column>
            <el-table-column prop="zj" label="总价" align="center">
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button
                  @click="MoveInDet(scope.row)"
                  class="el-button"
                  type="text"
                  size="small"
                  v-show="ProCode.tgzt == '6'"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            style="margin: 10px 0px"
            background
            :current-page="this.formMoveIn.pageIndex"
            :page-sizes="pageSizeMoveIn"
            :page-size="this.formMoveIn.pageSize"
            layout="total, sizes, prev, pager, next"
            @current-change="MoveInCurrentChange"
            @size-change="MoveInSizeChange"
            :total="MoveInTotal"
          >
          </el-pagination>
        </template>
      </el-card>
      <el-card class="box-card">
        <el-descriptions title="移出料单:"> </el-descriptions>
        <div
          style="margin-bottom: 10px"
          class="buttonUpDon"
          v-show="ProCode.tgzt == '6'"
        >
          <el-upload
            class="upload-demo"
            :data="MoveOutData()"
            accept=".xls"
            :before-upload="beforeDesignUpload"
            :show-file-list="false"
            :on-success="MoveOutSuccess"
            :on-error="MoveOutError"
            :file-list="MoveOutList.file"
            :http-request="MoveOutupload"
          >
            <el-button class="blue-btn"
              ><i class="el-icon-upload2"></i> 物料导入
            </el-button>
          </el-upload>
          <el-button class="directiveBtn" @click="MoveOutDown"
            ><i class="el-icon-download"></i> 模板下载</el-button
          >
        </div>
        <template>
          <el-table
            :data="tableDataMoveOut"
            :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
            height="200"
            border
            style="width: 100%"
          >
            <el-table-column
              type="index"
              :index="MoveOutindexMethod"
              label="序号"
              align="center"
              :resizable="false"
              width="60"
            >
            </el-table-column>
            <el-table-column prop="bm" label="物料编码" align="center">
            </el-table-column>
            <el-table-column prop="wlms" label="物料描述" align="center">
            </el-table-column>
            <el-table-column prop="sl" label="数量" align="center">
            </el-table-column>
            <el-table-column prop="dw" label="单位" align="center">
            </el-table-column>
            <el-table-column prop="dj" label="单价" align="center">
            </el-table-column>
            <el-table-column prop="zj" label="总价" align="center">
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button
                  @click="MoveOutDet(scope.row)"
                  class="el-button"
                  type="text"
                  size="small"
                  v-show="ProCode.tgzt == '6'"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            style="margin: 10px 0px"
            background
            :current-page="this.formMoveOut.pageIndex"
            :page-sizes="pageSizeMoveOut"
            :page-size="this.formMoveOut.pageSize"
            layout="total, sizes, prev, pager, next"
            @current-change="MoveOutCurrentChange"
            @size-change="MoveOutSizeChange"
            :total="MoveOutTotal"
          >
          </el-pagination>
        </template>
      </el-card>
      <el-card class="box-card">
        <el-descriptions title="出库料单:"> </el-descriptions>
        <div
          style="margin-bottom: 10px"
          class="buttonUpDon"
          v-show="ProCode.tgzt == '6'"
        >
          <el-upload
            class="upload-demo"
            :data="boundData()"
            accept=".xls"
            :before-upload="beforeDesignUpload"
            :show-file-list="false"
            :on-success="boundSuccess"
            :on-error="boundError"
            :file-list="boundList.file"
            :http-request="ckldOutupload"
          >
            <el-button class="blue-btn"
              ><i class="el-icon-upload2"></i> 物料导入
            </el-button>
          </el-upload>
          <el-button class="directiveBtn" @click="boundDown"
            ><i class="el-icon-download"></i> 模板下载</el-button
          >
        </div>
        <template>
          <el-table
            :data="tableDatabound"
            :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
            height="200"
            border
            style="width: 100%"
          >
            <el-table-column
              type="index"
              :index="boundindexMethod"
              label="序号"
              align="center"
              :resizable="false"
              width="60"
            >
            </el-table-column>
            <el-table-column prop="bm" label="物料编码" align="center">
            </el-table-column>
            <el-table-column prop="wlms" label="物料描述" align="center">
            </el-table-column>
            <el-table-column prop="sl" label="数量" align="center">
            </el-table-column>
            <el-table-column prop="dw" label="单位" align="center">
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button
                  @click="boundDet(scope.row)"
                  class="el-button"
                  type="text"
                  size="small"
                  v-show="ProCode.tgzt == '6'"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            style="margin: 10px 0px"
            background
            :current-page="this.formbound.pageIndex"
            :page-sizes="pageSizebound"
            :page-size="this.formbound.pageSize"
            layout="total, sizes, prev, pager, next"
            @current-change="boundCurrentChange"
            @size-change="boundSizeChange"
            :total="boundTotal"
          >
          </el-pagination>
        </template>
      </el-card>
      <el-card class="box-card">
        <el-descriptions title="退料单:"> </el-descriptions>
        <div
          style="margin-bottom: 10px"
          class="buttonUpDon"
          v-show="ProCode.tgzt == '6'"
        >
          <el-upload
            class="upload-demo"
            :data="RefundData()"
            accept=".word,.xlsx,.xls,txt,bmp,.docx,.doc"
            :before-upload="beforeDesignUpload"
            :show-file-list="false"
            :on-success="RefundSuccess"
            :on-error="RefundError"
            :file-list="RefundList.file"
            :http-request="Refundupload"
          >
            <el-button class="blue-btn"
              ><i class="el-icon-upload2"></i> 物料导入
            </el-button>
          </el-upload>
          <el-button class="directiveBtn" @click="RefundDown"
            ><i class="el-icon-download"></i> 模板下载</el-button
          >
        </div>
        <template>
          <el-table
            :data="tableDataRefund"
            :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
            height="200"
            border
            style="width: 100%"
          >
            <el-table-column
              type="index"
              :index="RefundindexMethod"
              label="序号"
              align="center"
              :resizable="false"
              width="60"
            >
            </el-table-column>
            <el-table-column prop="bm" label="物料编码" align="center">
            </el-table-column>
            <el-table-column prop="wlms" label="物料描述" align="center">
            </el-table-column>
            <el-table-column prop="sl" label="数量" align="center">
            </el-table-column>
            <el-table-column prop="dw" label="单位" align="center">
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button
                  @click="RefundDet(scope.row)"
                  class="el-button"
                  type="text"
                  size="small"
                  v-show="ProCode.tgzt == '6'"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            style="margin: 10px 0px"
            background
            :current-page="this.formRefund.pageIndex"
            :page-sizes="pageSizeRefund"
            :page-size="this.formRefund.pageSize"
            layout="total, sizes, prev, pager, next"
            @current-change="RefundCurrentChange"
            @size-change="RefundSizeChange"
            :total="RefundTotal"
          >
          </el-pagination>
        </template>
      </el-card>
      <!-- 签证信息 -->
      <el-card class="box-card">
        <el-descriptions title="签证信息:"> </el-descriptions>
        <div
          style="margin-bottom: 10px"
          class="buttonUpDon"
          v-show="ProCode.tgzt == '6'"
        >
          <el-upload
            class="upload-demo"
            :data="VisaData()"
            accept=".word,.xlsx,.xls,txt,bmp,.docx,.doc"
            :before-upload="beforeDesignUpload"
            :show-file-list="false"
            :on-success="VisaSuccess"
            :on-error="VisaError"
            :file-list="VisaList.file"
            :http-request="Visaupload"
          >
            <el-button class="blue-btn"
              ><i class="el-icon-upload2"></i> 物料导入
            </el-button>
          </el-upload>
          <el-button class="directiveBtn" @click="VisaDown"
            ><i class="el-icon-download"></i> 模板下载</el-button
          >
        </div>
        <template>
          <el-table
            :data="tableDataVisa"
            :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
            height="200"
            border
            style="width: 100%"
          >
            <el-table-column
              type="index"
              :index="VisaindexMethod"
              label="序号"
              align="center"
              :resizable="false"
              width="60"
            >
            </el-table-column>
            <el-table-column prop="qzbt" label="签证标题" align="center">
            </el-table-column>
            <el-table-column prop="qzyy" label="签证原因" align="center">
            </el-table-column>
            <el-table-column prop="qzygje" label="签证预估金额" align="center">
            </el-table-column>
            <el-table-column
              prop="sgxmjld"
              label="施工项目经理名称"
              align="center"
            >
            </el-table-column>
            <el-table-column prop="sgdwmc" label="施工单位名称" align="center">
            </el-table-column>
            <el-table-column prop="jldwmc" label="监理单位名称" align="center">
            </el-table-column>
            <el-table-column prop="qzsy" label="签证事由" align="center">
            </el-table-column>
            <el-table-column prop="sdje" label="审定金额" align="center">
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button
                  @click="VisaDet(scope.row)"
                  class="el-button"
                  type="text"
                  size="small"
                  v-show="ProCode.tgzt == '6'"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            style="margin: 10px 0px"
            background
            :current-page="this.formVisa.pageIndex"
            :page-sizes="pageSizeVisa"
            :page-size="this.formVisa.pageSize"
            layout="total, sizes, prev, pager, next"
            @current-change="VisaCurrentChange"
            @size-change="VisaSizeChange"
            :total="VisaTotal"
          >
          </el-pagination>
        </template>
      </el-card>
      <!-- 设计变更 -->
      <el-card class="box-card">
        <el-descriptions title="设计变更:"> </el-descriptions>
        <div
          style="margin-bottom: 10px"
          class="buttonUpDon"
          v-show="ProCode.tgzt == '6'"
        >
          <el-upload
            class="upload-demo"
            :data="ChangeData()"
            accept=".word,.xlsx,.xls,txt,bmp,.docx,.doc"
            :before-upload="beforeDesignUpload"
            :show-file-list="false"
            :on-success="ChangeSuccess"
            :on-error="ChangeError"
            :file-list="ChangeList.file"
            :http-request="Changeupload"
          >
            <el-button class="blue-btn"
              ><i class="el-icon-upload2"></i> 物料导入
            </el-button>
          </el-upload>
          <el-button class="directiveBtn" @click="ChangeDown"
            ><i class="el-icon-download"></i> 模板下载</el-button
          >
        </div>
        <template>
          <el-table
            :data="tableDataChange"
            :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
            height="200"
            border
            style="width: 100%"
          >
            <el-table-column
              type="index"
              :index="ChangeindexMethod"
              label="序号"
              align="center"
              :resizable="false"
              width="60"
            >
            </el-table-column>
            <el-table-column prop="bgje" label="变更金额" align="center">
            </el-table-column>
            <el-table-column prop="bgdw" label="变更单位" align="center">
            </el-table-column>
            <el-table-column prop="xmjl" label="项目经理" align="center">
            </el-table-column>
            <el-table-column prop="bgsj" label="变更时间" align="center">
            </el-table-column>
            <el-table-column prop="bglx" label="变更类型" align="center">
            </el-table-column>
            <el-table-column prop="bhsje" label="不含税金额" align="center">
            </el-table-column>
            <el-table-column prop="jldwmc" label="监理单位名称" align="center">
            </el-table-column>
            <el-table-column prop="sdje" label="审定金额" align="center">
            </el-table-column>
            <el-table-column prop="gclms" label="工程量描述" align="center">
            </el-table-column>
            <el-table-column prop="bgyy" label="变更原因" align="center">
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button
                  @click="ChangeDet(scope.row)"
                  class="el-button"
                  type="text"
                  size="small"
                  v-show="ProCode.tgzt == '6'"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            style="margin: 10px 0px"
            background
            :current-page="this.formChange.pageIndex"
            :page-sizes="pageSizeChange"
            :page-size="this.formChange.pageSize"
            layout="total, sizes, prev, pager, next"
            @current-change="ChangeCurrentChange"
            @size-change="ChangeSizeChange"
            :total="ChangeTotal"
          >
          </el-pagination>
        </template>
      </el-card>
      <el-card class="box-card">
        <el-descriptions title="分包费用:"> </el-descriptions>
        <el-form ref="formFBCost" :model="formFBCost" :inline="true">
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="专业分包工程量:"
                prop="zyfbgcl"
                label-width="150px"
              >
                <el-input
                  v-model="formFBCost.zyfbgcl"
                  clearable
                  @input="handleNumericInputFB('zyfbgcl')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="送审专业分包费用:"
                prop="sszyfbfy"
                label-width="150px"
              >
                <el-input
                  v-model="formFBCost.sszyfbfy"
                  clearable
                  @input="handleNumericInputFB('sszyfbfy')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="审定专业分包费用:"
                prop="sdzybfy"
                label-width="150px"
              >
                <el-input
                  v-model="formFBCost.sdzybfy"
                  clearable
                  @input="handleNumericInputFB('sdzybfy')"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="审定专业分包费用(折扣后):"
                prop="sdzyfbfyzk"
                label-width="150px"
              >
                <el-input
                  v-model="formFBCost.sdzyfbfyzk"
                  clearable
                  @input="handleNumericInputFB('sdzyfbfyzk')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="劳务分包总工期:"
                prop="lwfbzgq"
                label-width="150px"
              >
                <el-input
                  v-model="formFBCost.lwfbzgq"
                  clearable
                  oninput="value=value.replace(/[！@#￥%……&*（）——+~!@#$$%^&*()_+=]+/g,'')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="送审劳务分包费用:"
                prop="sslwbfy"
                label-width="150px"
              >
                <el-input
                  v-model="formFBCost.sslwbfy"
                  clearable
                  @input="handleNumericInputFB('sslwbfy')"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="审定劳务分包费用:"
                prop="sdlwfbfy"
                label-width="150px"
              >
                <el-input
                  v-model="formFBCost.sdlwfbfy"
                  clearable
                  @input="handleNumericInputFB('sdlwfbfy')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="分包占比:" prop="sdsgf" label-width="150px">
                <el-input
                  v-model="formFBCost.sdsgf"
                  clearable
                  oninput="value=value.replace(/[！@#￥……&*（）——+~!@#$^&*()_+=]+/g,'')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="审定施工费:" prop="fbzb" label-width="150px">
                <el-input
                  v-model="formFBCost.fbzb"
                  clearable
                  @input="handleNumericInputFB('fbzb')"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      <!-- 附件上传 -->
      <!-- <el-card class="box-card">
        <el-descriptions title="附件上传:"> </el-descriptions>
        <template>
          <el-table :data="tableDataAnnex" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }" height="200"
            border style="width: 100%">
            <el-table-column type="index" :index="AnnexindexMethod" label="序号" align="center" :resizable="false"
              width="60">
            </el-table-column>
            <el-table-column prop="type" label="上传内容" align="center">
            </el-table-column>
            <el-table-column prop="name" label="文件名称" align="center">
            </el-table-column>
            <el-table-column label="操作" width="" align="center">
              <template slot-scope="scope">
                <div style="
                    display: flex;
                    justify-content: center;
                    align-items: center;
                  ">
                 
                  <el-upload class="upload-demo" :data="AnnexData()" :before-upload="beforeDesignUploadSb"
                    :show-file-list="false" :on-success="AnnexSuccess" :on-error="AnnexError"
                    :file-list="AnnexList.file" :http-request="AnnexUpload">
                    <span @click="AnnexUp(scope.row)" style="color: #526ade; cursor: pointer">上传</span>
                  </el-upload>
                  <span @click="AnnexDon(scope.row)" style="color: #526ade; cursor: pointer">下载</span>
                  <span @click="AnnexDet(scope.row)"
                    style="color: #526ade; cursor: pointer; margin-left: 12px">删除</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination style="margin: 10px 0px" background :current-page="this.formAnnex.pageIndex"
            :page-sizes="pageSize" :page-size="this.formAnnex.pageSize" layout="total, sizes, prev, pager, next"
            @current-change="handleCurrentChange" @size-change="handleSizeChange" :total="AnnexTotal">
          </el-pagination>
        </template>
      </el-card> -->
    </div>
    <div class="setFooter" v-show="ProCode.tgzt == '6'">
      <span
        slot="footer"
        class="dialog-footer"
        style="float: right; margin: 10px 10px 10px 0px"
      >
        <el-button @click="feederDialogQuery">保 存</el-button>
        <el-button class="blue-btn" @click="submiteDatas" :disabled="submitDisl">提 交</el-button>
      </span>
    </div>
  </div>
</template>

<script>
import {
  getSB,
  resquest,
  getDet,
  getVisa,
  getChange,
  ChangeDet,
  VisaDet,
  getFBCost,
  FBCostSave,
  AnnexDet,
  getAnnex,
  getGSInformation,
  GSSave,
  getJSInformation,
  submitDeclare,
  importFileJs,
  downFileWl,
  fjImportFileJs,
  saveSBForm, fjDownFileJs,
  importSettlement
} from '@/api/api'
import axios from 'axios'
export default {
  components: {},
  props: {
    ProCode: {
      default: '',
    },
    sbOrSh: {
      default: ''
    }
  },
  data () {
    return {
      tempUrl: "",
      submitDisl:true,
      rules: {
        bstjf: [{
          required: true,
          message: '请输入送审建筑工程费',
          trigger: 'blur'
        }],
        sgjsf: [{
          required: true,
          message: '请输入送审结算费总额',
          trigger: 'blur'
        }],
        bsazf: [{
          required: true,
          message: '请输入送审安装工程费',
          trigger: 'blur'
        }],
        bstf: [{
          required: true,
          message: '请输入送审其他费用',
          trigger: 'blur'
        }],
        jbybf: [{
          required: true,
          message: '请输入送审基本预备费',
          trigger: 'blur'
        }],
        sbgzf: [{
          required: true,
          message: '请输入设备购置费',
          trigger: 'blur'
        }],
        bsjgsbf: [{
          required: true,
          message: '请输入送审甲供设备费',
          trigger: 'blur'
        }],
        bsjgclf: [{
          required: true,
          message: '请输入送审甲供材料费',
          trigger: 'blur'
        }],
        jhshrq: [{
          required: true,
          message: '请选择计划审核日期',
          trigger: 'change'
        }],
        sendDate: [{
          required: true,
          message: '请输入送审日期',
          trigger: 'blur'
        }],
        wgbggdzcxx: [{
          required: true,
          message: '请输入完工报告固定资产信息',
          trigger: 'blur'
        }],
        zyfbgcl: [{
          required: true,
          message: '请输入专业分包工程量',
          trigger: 'blur'
        }],
        sszyfbfy: [{
          required: true,
          message: '请输入送审专业分包费用',
          trigger: 'blur'
        }],
        sdzybfy: [{
          required: true,
          message: '请输入审定专业分包费用',
          trigger: 'blur'
        }],
        sdzyfbfyzk: [{
          required: true,
          message: '请输入审定专业分包费用（折扣后）',
          trigger: 'blur'
        }],
        lwfbzgq: [{
          required: true,
          message: '请输入劳务分包总工期',
          trigger: 'blur'
        }],
        sslwbfy: [{
          required: true,
          message: '请输送审劳务分包费用',
          trigger: 'blur'
        }],
        sdlwfbfy: [{
          required: true,
          message: '请输入审定劳务分包费用',
          trigger: 'blur'
        }],
        sdsgf: [{
          required: true,
          message: '请输入分包占比',
          trigger: 'blur'
        }],
        fbzb: [{
          required: true,
          message: '请输入审定施工费',
          trigger: 'blur'
        }],
        jbfy: [{
          required: true,
          message: '请输入基本预备费',
          trigger: 'blur'
        }],
        qtfy: [{
          required: true,
          message: '请输入其他费用',
          trigger: 'blur'
        }],
        azgcf: [{
          required: true,
          message: '请输入安装工程费',
          trigger: 'blur'
        }],
        jzgcf: [{
          required: true,
          message: '请输入建筑工程费',
          trigger: 'blur'
        }],
        hj: [{
          required: true,
          message: '请输入总概算费用',
          trigger: 'blur'
        }],
        sjf: [{
          required: true,
          message: '请输入设计费',
          trigger: 'blur'
        }],
        jlf: [{
          required: true,
          message: '请输入监理费',
          trigger: 'blur'
        }],
        ssjttz: [{
          required: true,
          message: '请输入送审静态投资',
          trigger: 'blur'
        }],
        ssdttz: [{
          required: true,
          message: '请输入送审动态投资',
          trigger: 'blur'
        }],
      },
      url: resquest,
      saveType: '', // 记录保存阶段
      isSaveData: false,
      formJS: { //表单参数
        gcjsid: '',
        sgjsf: '',
        bstjf: '',
        bsazf: '',
        bstf: '',
        jbybf: '',
        sbgzf: '',
        bsjgsbf: '',
        bsjgclf: '',
        jhshrq: '',
        sendDate: '',
        wgbggdzcxx: '',
        xmbm: '',
        code: '',
        ssjttz: '',
        ssdttz: ''
      },
      formFBCost: { ///分包费用
        procode: '',
        zyfbgcl: '',
        sszyfbfy: '',
        sdzybfy: '',
        sdzyfbfyzk: '',
        lwfbzgq: '',
        sslwbfy: '',
        sdlwfbfy: '',
        sdsgf: '',
        fbzb: '',
        code: ''
      },
      formGS: { //概算信息
        procode: '',
        hj: '',
        jzgcf: '',
        azgcf: '',
        sbgzf: '',
        qtfy: '',
        jbfy: '',
        sjf: '',
        jlf: '',
      },
      foundationXX: {
        codeName: ''
      },
      cuttentProCode: '',
      jsdwOptions: [], //批次名次下拉数据
      tableData: [],
      tableHeight: 0,
      dialogProblem: false, //编辑
      dialogSB: false, //申报
      tableDatas: [],
      //设计物料清单
      formDesign: {
        type: '设计',
        taskid: '',
        pageSize: 5,
        pageIndex: 1,
        code: ''
      },
      tableDataDesign: [],
      DesignTotal: 0,
      pageSizeDesign: [5, 10, 20, 50, 100],
      downloadAllUrlDesign: resquest + '/tDtfProject/downFilewl',
      DesignList: {
        file: [], // 文件上传
      },
      JSbookList: {
        file: []
      },
      //申报料单
      formReport: {
        type: '领料',
        taskid: '',
        pageSize: 5,
        pageIndex: 1,
        code: ''
      },
      tableDataReport: [],
      ReportTotal: 0,
      pageSizeReport: [5, 10, 20, 50, 100],
      ReportList: {
        file: [], // 文件上传
      },
      //移入料单
      formMoveIn: {
        type: '移入',
        taskid: '',
        pageSize: 5,
        pageIndex: 1,
        code: ''
      },
      tableDataMoveIn: [],
      MoveInTotal: 0,
      pageSizeMoveIn: [5, 10, 20, 50, 100],
      MoveInList: {
        file: [], // 文件上传
      },
      //移出料单
      formMoveOut: {
        type: '移出',
        taskid: '',
        pageSize: 5,
        pageIndex: 1,
        code: ''
      },
      tableDataMoveOut: [],
      MoveOutTotal: 0,
      pageSizeMoveOut: [5, 10, 20, 50, 100],
      MoveOutList: {
        file: [], // 文件上传
      },
      //出库料单
      formbound: {
        type: '出库',
        taskid: '',
        pageSize: 5,
        pageIndex: 1,
        code: ''
      },
      tableDatabound: [],
      boundTotal: 0,
      pageSizebound: [5, 10, 20, 50, 100],
      boundList: {
        file: [], // 文件上传
      },
      //退料单
      formRefund: {
        type: '退料',
        taskid: '',
        pageSize: 5,
        pageIndex: 1,
        code: ''
      },
      tableDataRefund: [],
      RefundTotal: 0,
      pageSizeRefund: [5, 10, 20, 50, 100],
      RefundList: {
        file: [], // 文件上传
      },
      //签证
      formVisa: {
        code: '',
        pageSize: 5,
        pageIndex: 1
      },
      tableDataVisa: [],
      VisaTotal: 0,
      pageSizeVisa: [5, 10, 20, 50, 100],
      VisaList: {
        file: [], // 文件上传
      },
      //设计变更
      formChange: {
        code: '',
        pageSize: 5,
        pageIndex: 1
      },
      tableDataChange: [],
      ChangeTotal: 0,
      pageSizeChange: [5, 10, 20, 50, 100],
      ChangeList: {
        file: [], // 文件上传
      },
      // 附件
      formAnnex: {
        code: '',
        pageSize: 5,
        pageIndex: 1
      },
      tableDataAnnex: [],
      AnnexTotal: 0,
      pageSize: [5, 10, 20, 50, 100], //分页页数
      downloadAllUrlAnnex: resquest + '/tDtfProject/downFile3',
      AnnexList: {
        file: [], // 文件上传
      },
      isSaveSuccess: false
    }
  },
  mounted () {
    console.log(this.ProCode.tgzt, '父传子值')

    this.getHeight()
    this.getList()
    this.FBCost()
    window.addEventListener('resize', this.getHeight)
  },
  watch: {
    saveType: {
      handler (newVal, oldVal) {
        const that = this
        console.log(newVal.length, "nnnn")
        // 每保存成功一个模块，字符串长度加1
        if (newVal.length === 3) {
          if (this.tableDatabound.length === 0) {
            // this.$message.warning('请至少上传一条出库料单')
            this.isSaveSuccess = true
          } else if (this.tableDataAnnex.every((item) => { item.name !== '' })) {
            this.$message.warning('请上传附件')
          }
          else {
            // that.$message.success('保存成功')
            this.isSaveSuccess = false
            this.saveType = ''
            this.isSaveData = false
          }
        } else {
          // if(this.isSaveData) {
          // 	that.$message.warning('请补充录入信息')
          // }
        }
      },
      immediate: true
    }
  },
  computed: {
    //概算信息数据格式
    isFormats () {
      for (let i in this.formGS) {
        if (
          i == "hj" ||
          i == "jzgcf" ||
          i == "azgcf" ||
          i == "sbgzf" ||
          i == "qtfy" ||
          i == "jbfy" ||
          i == "sjf" ||
          i == "jlf"
        ) {
          if (isNaN(Number(this.formGS[i]))) {
            return false
          }
        }
      }
      return true
    },
    //结算信息数据格式
    isJsFormats () {
      for (let i in this.formJS) {
        if (
          i == "sgjsf" ||
          i == "bstjf" ||
          i == "bsazf" ||
          i == "bstf" ||
          i == "jbybf" ||
          i == "sbgzf" ||
          i == "bsjgsbf" ||
          i == "bsjgclf"
        ) {
          if (isNaN(Number(this.formJS[i]))) {
            return false
          }
        }
      }
      return true
    }
  },
  methods: {
    creatChunk (file, chunkSize) {
      const result = []

      for (let i = 0; i < file.size; i += chunkSize) {

        result.push(file.slice(i, i + chunkSize))

      }
      return result
    },
    // 校验方法是否为i小数点和英文
    handleNumericInput (field) {
      let value = this.formJS[field]
      // 过滤非数字和多余的点
      let cleanedValue = value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')

      // 去除前导零，但保留小数点前的一个零
      if (cleanedValue.startsWith('0.') || cleanedValue === '0') {
        this.formJS[field] = cleanedValue
      } else {
        this.formJS[field] = cleanedValue.replace(/^0+/, '')
      }
      if (['bstjf', 'bsazf', 'bstf', 'sbgzf'].includes(field)) {
        this.calculateTotal()
      }
    },
    calculateTotal () {
      const { bstjf, bsazf, bstf, sbgzf } = this.formJS
      const total = parseFloat(bstjf) + parseFloat(bsazf) + parseFloat(bstf) + parseFloat(sbgzf)
      this.formJS.sgjsf = isNaN(total) ? '0' : total.toFixed(0) 
      this.formJS.ssdttz = isNaN(total) ? '0' : total.toFixed(0)
    },
    handleNumericInputGS (field) {
      let value = this.formGS[field]
      // 过滤非数字和多余的点
      let cleanedValue = value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')

      // 去除前导零，但保留小数点前的一个零
      if (cleanedValue.startsWith('0.') || cleanedValue === '0') {
        this.formGS[field] = cleanedValue
      } else {
        this.formGS[field] = cleanedValue.replace(/^0+/, '')
      }
    },
    handleNumericInputFB (field) {
      let value = this.formFBCost[field]
      // 过滤非数字和多余的点
      let cleanedValue = value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')

      // 去除前导零，但保留小数点前的一个零
      if (cleanedValue.startsWith('0.') || cleanedValue === '0') {
        this.formFBCost[field] = cleanedValue
      } else {
        this.formFBCost[field] = cleanedValue.replace(/^0+/, '')
      }
    },
    uploadList (type, files, taskid) {
      let tempUrl = ""
      const loading = this.$loading({
        lock: true,
        text: '上传中',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)'
      })
      const chunkArr = this.creatChunk(files.file, 10 * 1024 * 1024)
      let chunkIndex = 0
      const uploadNextChunk = () => {
        if (chunkIndex >= chunkArr.length) {
          console.log('All chunks uploaded successfully')
          loading.close()
          return
        }
        const file = chunkArr[chunkIndex]

        const formData1 = new FormData()
        formData1.append('file', file)
        formData1.append('code', taskid)
        formData1.append('type', type)
        formData1.append('chunkNumber', chunkIndex)
        formData1.append('totalChunks', chunkArr.length)
        formData1.append('fileName', files.file.name)
        formData1.append('tempDirectory', tempUrl)
        console.log(`Uploading chunk ${chunkIndex} of ${chunkArr.length}`)
        importFileJs(formData1).then(res => {
          console.log(res, chunkIndex, "测试返回结果")

          if (chunkIndex === 0 && res) {
            tempUrl = res.data.result
            console.log('Temp URL received:', tempUrl)
          }

          if (['上传成功', 'success', "保存数据成功"].includes(res.data.result)) {
            this.$message.success("上传成功")
            this.getList()
            loading.close()
          }
          chunkIndex++
          uploadNextChunk()
        }).catch(error => {
          console.error('Error uploading chunk:', error)
          this.$message.warning("上传失败，请重试")
          loading.close()
        })
      }
      uploadNextChunk()


      // console.log(files, 'filessss')
      // const formData = new FormData()
      // formData.append('code', taskid)
      // formData.append('type', type)
      // formData.append('file', files.file)
      // console.log(formData.get('code'), formData.get('file'), '上传文件')
      // importFileJs(formData).then(res => {
      //   console.log(res)
      //   this.getList()
      // })
    },
    // 导入结算书
    JSbookListUpload (files) {
      let tempUrl = ""
      const loading = this.$loading({
        lock: true,
        text: '上传中',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)'
      })
      const chunkArr = this.creatChunk(files.file, 10 * 1024 * 1024)
      let chunkIndex = 0
      const uploadNextChunk = () => {
        if (chunkIndex >= chunkArr.length) {
          console.log('All chunks uploaded successfully')
          loading.close()
          return
        }
        const file = chunkArr[chunkIndex]

        const formData1 = new FormData()
        formData1.append('code', this.ProCode.code)
        formData1.append('file', file)
        formData1.append('type', '0')
        formData1.append('chunkNumber', chunkIndex)
        formData1.append('totalChunks', chunkArr.length)
        formData1.append('fileName', files.file.name)
        formData1.append('tempDirectory', tempUrl)
        console.log(`Uploading chunk ${chunkIndex} of ${chunkArr.length}`)
        importSettlement(formData1).then(res => {
          console.log(res, chunkIndex, "测试返回结果")

          if (chunkIndex === 0 && res) {
            tempUrl = res.data.result
            console.log('Temp URL received:', tempUrl)
          }
          if (['上传成功', 'success'].includes(res.data.result)) {
            this.$message.success("上传成功")
            getJSInformation(this.ProCode.code)
              .then((res) => {
                if (res.data.result === '') { } else {
                  // this.formJS = res.data.result
                  this.formJS.sgjsf = res.data.result.sgjsf
                  this.formJS.bstjf = res.data.result.bstjf
                  this.formJS.bsazf = res.data.result.bsazf
                  this.formJS.bstf = res.data.result.bstf
                  this.formJS.jbybf = res.data.result.jbybf
                  this.formJS.sbgzf = res.data.result.sbgzf
                  this.formJS.ssdttz = res.data.result.ssdttz
                  this.formJS.ssjttz = res.data.result.ssjttz
                  this.$message.success("导入成功")
                }
              })
            loading.close()
          }
          chunkIndex++
          uploadNextChunk()
        }).catch(error => {
          console.error('Error uploading chunk:', error)
          this.$message.warning("上传失败，请重试")
          loading.close()
        })
      }
      uploadNextChunk()


      // const formData = new FormData()
      // formData.append('code', this.ProCode.code)
      // formData.append('type', '0')
      // formData.append('file', files.file)
      // importSettlement(formData).then(res => {
      //   console.log(res, '上传成功')
      //   getJSInformation(this.ProCode.code)
      //     .then((res) => {
      //       if (res.data.result === '') { } else {
      //         // this.formJS = res.data.result
      //         this.formJS.sgjsf = res.data.result.sgjsf
      //         this.formJS.bstjf = res.data.result.bstjf
      //         this.formJS.bsazf = res.data.result.bsazf
      //         this.formJS.bstf = res.data.result.bstf
      //         this.formJS.jbybf = res.data.result.jbybf
      //         this.formJS.sbgzf = res.data.result.sbgzf
      //         this.formJS.ssdttz = res.data.result.ssdttz
      //         this.formJS.ssjttz = res.data.result.ssjttz
      //         this.$message.success("导入成功")
      //       }
      //     })
      //     .catch(() => { this.$message.error('上传失败，请稍后再试') })
      // }).catch(() => { this.$message.error("上传失败") })
      console.log('导入结算书', files)

    },
    uploadImportJs (files) {
      this.uploadList('设计', files, this.formDesign.taskid)
    },
    uploadImportSBLD (files) {
      // console.log(files,'filessss');
      //   const formData=new FormData()
      //   formData.append('code',this.formReport.taskid)
      //   formData.append('type','领料')
      //   formData.append('file',files.file)
      //   console.log(formData.get('code'),formData.get('file'),'申报料单');
      //   importFileJs(formData).then(res=>{
      //       console.log(res);
      //   })
      this.uploadList('领料', files, this.formReport.taskid)
    },
    MoveInupload (files) {
      //  const formData=new FormData()
      //     formData.append('code',this.formMoveIn.taskid)
      //     formData.append('type','移入')
      //     formData.append('file',files.file)
      //     console.log(formData.get('code'),formData.get('file'),'申报料单');
      //     importFileJs(formData).then(res=>{
      //         console.log(res);
      //     })
      this.uploadList('移入', files, this.formMoveIn.taskid)
    },
    MoveOutupload (files) {
      this.uploadList('移出', files, this.formMoveOut.taskid)
    },
    ckldOutupload (files) {
      this.uploadList('出库', files, this.formbound.taskid)
    },
    Refundupload (files) {
      this.uploadList('退料', files, this.formRefund.taskid)
    },
    Visaupload (files) {
      this.uploadList('签证', files, this.formVisa.code)
    },
    Changeupload (files) {
      this.uploadList('变更', files, this.formChange.code)
    },
    creatChunk (file, chunkSize) {
      const result = []

      for (let i = 0; i < file.size; i += chunkSize) {

        result.push(file.slice(i, i + chunkSize))

      }
      return result
    },
    AnnexUpload (files) {
      let tempUrl = ""
      const loading = this.$loading({
        lock: true,
        text: '上传中',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)'
      })
      const chunkArr = this.creatChunk(files.file, 10 * 1024 * 1024)
      let chunkIndex = 0
      const uploadNextChunk = () => {
        if (chunkIndex >= chunkArr.length) {
          console.log('All chunks uploaded successfully')
          loading.close()
          return
        }
        const file = chunkArr[chunkIndex]
        const formData1 = new FormData()
        formData1.append('file', file)
        formData1.append('code', this.formAnnex.code)
        formData1.append('chunkNumber', chunkIndex)
        formData1.append('totalChunks', chunkArr.length)
        formData1.append('fileName', files.file.name)
        formData1.append('tempDirectory', tempUrl)
        console.log(`Uploading chunk ${chunkIndex} of ${chunkArr.length}`)
        fjImportFileJs(formData1).then(res => {
          console.log(res, chunkIndex, "测试返回结果")

          if (chunkIndex === 0 && res) {
            tempUrl = res.data.result
            console.log('Temp URL received:', tempUrl)
          }
          if (['上传成功', 'success'].includes(res.data.result)) {
            this.$message.success("上传成功")
            loading.close()

          }
          chunkIndex++
          uploadNextChunk()
        }).catch(error => {
          console.error('Error uploading chunk:', error)
          this.$message.warning("上传失败，请重试")
          loading.close()
        })

      }
      uploadNextChunk()
    },

    FBCost () {
      // 分包单位
      this.formFBCost.procode = this.ProCode.code
      getFBCost(this.ProCode.code)
        .then((res) => {
          if (res.data.result === '') { } else {
            this.formFBCost = res.data.result
          }
        })
        .catch(() => { })
      // 概算信息
      getGSInformation(this.ProCode.code)
        .then((res) => {
          console.log(res, '概算信息')
          if (res.data.result === '') {

          } else {
            this.formGS = res.data.result
          }
        })
        .catch(() => { })
      // 结算信息
      getJSInformation(this.ProCode.code)
        .then((res) => {
          if (res.data.result === '') { } else {
            this.formJS = res.data.result
          }
        })
        .catch(() => { })

    },
    // 查询
    getList () {
      this.foundationXX = this.ProCode
      // 每个表格传taskid
      this.formDesign.taskid = this.foundationXX.code
      this.formReport.taskid = this.foundationXX.code
      this.formMoveIn.taskid = this.foundationXX.code
      this.formMoveOut.taskid = this.foundationXX.code
      this.formbound.taskid = this.foundationXX.code
      this.formRefund.taskid = this.foundationXX.code
      this.formVisa.code = this.foundationXX.code
      this.formChange.code = this.foundationXX.code
      this.formAnnex.code = this.foundationXX.code
      // 截取字符
      const data = this.foundationXX.code
      var nstr = data.split('-')
      this.foundationXX.codeName = nstr[nstr.length - 2]
      //设计物料清单
      getSB(this.formDesign)
        .then((res) => {
          console.log(res.data, "设计")
          this.tableDataDesign = res.data.result
          this.DesignTotal = res.data.count
        })
        .catch(() => { })
      // 申报料单
      getSB(this.formReport)
        .then((res) => {
          // console.log('申报料单', res.data)
          this.tableDataReport = res.data.result
          this.ReportTotal = res.data.count
        })
        .catch(() => { })
      // 移入料单
      getSB(this.formMoveIn)
        .then((res) => {
          console.log('移入料单', res)
          this.tableDataMoveIn = res.data.result
          this.MoveInTotal = res.data.count
        })
        .catch(() => { })
      // 移出料单
      getSB(this.formMoveOut)
        .then((res) => {
          // console.log('移出料单', res.data)
          this.tableDataMoveOut = res.data.result
          this.MoveOutTotal = res.data.count
        })
        .catch(() => { })
      // 出库料单
      getSB(this.formbound)
        .then((res) => {
          console.log('出库料单', res.data)
          this.tableDatabound = res.data.result
          this.boundTotal = res.data.count
        })
        .catch(() => { })
      // 退料单
      getSB(this.formRefund)
        .then((res) => {
          // console.log('退料单', res.data)
          this.tableDataRefund = res.data.result
          this.RefundTotal = res.data.count
        })
        .catch(() => { })
      // 签证信息
      getVisa(this.formVisa)
        .then((res) => {
          console.log(res.data.result, "签证信息")
          this.tableDataVisa = res.data.result
          this.VisaTotal = res.data.count
        })
        .catch(() => { })
      // 设计变更
      getChange(this.formChange)
        .then((res) => {
          console.log(res, "   // 设计变更")
          this.tableDataChange = res.data.result
          this.ChangeTotal = res.data.count
        })
        .catch(() => { })
      // 附件
      getAnnex(this.formAnnex)
        .then((res) => {
          console.log('附件', res.data)
          this.tableDataAnnex = res.data.result
          this.AnnexTotal = res.data.count
        })
        .catch(() => { })
    },
    // ↓在methods里面(窗体大小改变计算表格高度)
    getHeight () {
      this.tableHeight = (window.innerHeight - 320)
    },
    // 重置
    clearForm () {

    },
    // 退料单
    RefundCurrentChange (val) {
      this.formRefund.pageIndex = val
      this.getList()
      // table列表序号索引
    },
    RefundSizeChange (val) {
      this.formRefund.pageSize = val
      this.getList()
    },
    RefundindexMethod (index) {
      return (this.formRefund.pageIndex - 1) * 5 + index + 1
    },
    RefundDown () {
      // var link = document.createElement('a')
      // link.style.display = "none"
      // link.href = `${this.downloadAllUrlDesign}?id=wl`
      // link.setAttribute("download", name)
      // document.body.appendChild(link)
      // link.click()
      this.downFileList('wl', '退料单模板.xls')
    },
    // 上传传参
    RefundData () {
      return {
        code: this.formRefund.taskid,
        type: '退料',
      }
    },
    //上传成功
    RefundSuccess (response) {
      if (response.result == "保存数据失败") {
        this.$message.error('上传失败，检查料单里字段是否为空、无、特殊字符！')
      } else {
        this.$message.success('上传成功')
        this.getList()
      }
    },
    // 上传失败
    RefundError () {
      this.$message.error('上传失败')
    },
    // 删除表格
    RefundDet (row) {
      getDet(row.qdid)
        .then((res) => {
          if (res.data.message == 'success') {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error('删除失败')
          }
        })
        .catch(() => { })
    },
    // 出库料单
    boundCurrentChange (val) {
      this.formbound.pageIndex = val
      this.getList()
    },
    boundSizeChange (val) {
      this.formbound.pageSize = val
      this.getList()
    },
    boundindexMethod (index) {
      return (this.formbound.pageIndex - 1) * 5 + index + 1
    },
    boundDown () {
      // var link = document.createElement('a')
      // link.style.display = "none"
      // link.href = `${this.downloadAllUrlDesign}?id=wl`
      // link.setAttribute("download", name)
      // document.body.appendChild(link)
      // link.click()
      this.downFileList('wl', '出库料单.xls')
    },
    // 上传传参
    boundData () {
      return {
        code: this.formbound.taskid,
        type: '出库',
      }
    },
    //上传成功
    boundSuccess (response) {
      if (response.result == "保存数据失败") {
        this.$message.error('上传文件失败，请检查文件数据是否有误！')
      } else {
        this.$message.success('上传成功')
        this.getList()
      }
    },
    // 上传失败
    boundError () {
      this.$message.error('上传失败')
    },
    // 删除表格
    boundDet (row) {
      getDet(row.qdid)
        .then((res) => {
          if (res.data.message == 'success') {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error('删除失败')
          }
        })
        .catch(() => { })
    },
    // 移除料单
    MoveOutCurrentChange (val) {
      this.formMoveOu.pageIndex = val
      this.getList()
    },
    MoveOutSizeChange (val) {
      this.formMoveOut.pageSize = val
      this.getList()
    },
    MoveOutindexMethod (index) {
      return (this.formMoveOut.pageIndex - 1) * 5 + index + 1
    },
    MoveOutDown () {
      // var link = document.createElement('a')
      // link.style.display = "none"
      // link.href = `${this.downloadAllUrlDesign}?id=qtwl`
      // link.setAttribute("download", name)
      // document.body.appendChild(link)
      // link.click()
      this.downFileList('qtwl', '移出料单.xls')
    },
    // 上传传参
    MoveOutData () {
      return {
        code: this.formMoveOut.taskid,
        type: '移出',
      }
    },
    //上传成功
    MoveOutSuccess (response) {
      if (response.result == "保存数据失败") {
        this.$message.error('上传文件失败，请检查文件数据是否有误！')
      } else {
        this.$message.success('上传成功')
        this.getList()
      }
    },
    // 上传失败
    MoveOutError () {
      this.$message.error('上传失败')
    },
    // 删除表格
    MoveOutDet (row) {
      getDet(row.qdid)
        .then((res) => {
          if (res.data.message == 'success') {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error('删除失败')
          }
        })
        .catch(() => { })
    },
    // 申报料单
    ReportCurrentChange (val) {
      this.formReport.pageIndex = val
      this.getList()
    },
    ReportSizeChange (val) {
      this.formReport.pageSize = val
      this.getList()
    },
    ReportindexMethod (index) {
      return (this.formReport.pageIndex - 1) * 5 + index + 1
    },
    ReportDown () {
      this.downFileList('wl', '申报料单模板.xls')
      // var link = document.createElement('a')
      // link.style.display = "none"
      // link.href = `${this.downloadAllUrlDesign}?id=wl`
      // link.setAttribute("download", name)
      // document.body.appendChild(link)
      // link.click()
    },
    // 上传传参
    ReportData () {
      return {
        code: this.formReport.taskid,
        type: '领料',
      }
    },
    //上传成功
    ReportSuccess (response) {
      if (response.result == "保存数据失败") {
        this.$message.error('上传文件失败，请检查文件数据是否有误！')
      } else {
        this.$message.success('上传成功')
        this.getList()
      }
    },
    // 上传失败
    ReportError () {
      this.$message.error('上传失败')
    },
    // 删除表格
    ReportDet (row) {
      getDet(row.qdid)
        .then((res) => {
          if (res.data.message == 'success') {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error('删除失败')
          }
        })
        .catch(() => { })
    },
    // 设计物料清单
    DesignCurrentChange (val) {
      this.formDesign.pageIndex = val
      this.getList()
    },
    DesignSizeChange (val) {
      this.formDesign.pageSize = val
      this.getList()
    },
    DesignindexMethod (index) {
      return (this.formDesign.pageIndex - 1) * 5 + index + 1
    },
    async downFileList (id, fileName) {
      let params = { id: id }
      const res = await downFileWl(params)
      console.log(res, "下载模板----------------------------------------")
      const blob = new Blob([res.data])
      let link = document.createElement('a')
      link.download = fileName
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)
    },
    // 下载
    DesignDown () {
      this.downFileList('qtwl', '物料模板.xls')
    },
    // 上传传参
    DesignData () {
      return {
        code: this.formDesign.taskid,
        type: '设计',
      }
    },
    //上传成功
    DesignSuccess (response) {
      console.log(response, "上传")
      if (response.result == "保存数据失败") {
        this.$message.error('上传文件失败，请检查文件数据是否有误！')
      } else {
        this.$message.success('上传成功')
        this.getList()
      }

    },
    // 上传失败
    DesignError () {
      this.$message.error('上传失败')
    },
    // 上传前判断
    beforeDesignUpload (file) {
      let fileName = file.name
      let pos = fileName.lastIndexOf('.')
      let lastName = fileName.substring(pos, fileName.length)
      const ext = lastName.toLowerCase()
      console.log(ext, this.AnnexList.file)
      if (ext !== '.doc' && ext !== '.docx' && ext != '.xls' && ext != '.xlsx' && ext != '.txt' && ext !=
        '.bmp' && ext != '.word') {
        console.log("文件必须为.doc")
        this.$message.error('文件必须为.doc')
        return false
      }
    },
    beforeDesignUploadSb (file) {
      let fileName = file.name
      let pos = fileName.lastIndexOf('.')
      let lastName = fileName.substring(pos, fileName.length)
      const ext = lastName.toLowerCase()
      console.log(ext, this.AnnexList.file)
      if (ext == '.xlsx' || ext == '.xls') {
        console.log("文件不能为excel文件")
        this.$message.error('文件不能为excel文件')
        return false
      }
    },
    // 删除表格
    DesignDet (row) {
      getDet(row.qdid)
        .then((res) => {
          if (res.data.message == 'success') {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error('删除失败')
          }
        })
        .catch(() => { })
    },
    // 移入料单
    MoveInCurrentChange (val) {
      this.formMoveIn.pageIndex = val
      this.getList()
    },
    MoveInSizeChange (val) {
      this.formMoveIn.pageSize = val
      this.getList()
    },
    MoveInindexMethod (index) {
      return (this.formMoveIn.pageIndex - 1) * 10 + index + 1
    },
    MoveInDown () {
      // var link = document.createElement('a')
      // link.style.display = "none"
      // link.href = `${this.downloadAllUrlDesign}?id=qtwl`
      // link.setAttribute("download", name)
      // document.body.appendChild(link)
      // link.click()
      this.downFileList('qtwl', '移入料单模板.xls')
    },
    // 上传传参
    MoveInData () {
      return {
        code: this.formMoveIn.taskid,
        type: '移入',
      }
    },
    //上传成功
    MoveInSuccess (response) {
      if (response.result == "保存数据失败") {
        this.$message.error('上传文件失败，请检查文件数据是否有误！')
      } else {
        this.$message.success('上传成功')
        this.getList()
      }
    },
    // 上传失败
    MoveInError () {
      this.$message.error('上传失败')
    },
    // 删除表格
    MoveInDet (row) {
      getDet(row.qdid)
        .then((res) => {
          if (res.data.message == 'success') {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error('删除失败')
          }
        })
        .catch(() => { })
    },
    // 签证
    VisaSizeChange (val) {
      this.formVisa.pageSize = val
      this.getList()
    },
    VisaCurrentChange (val) {
      this.formVisa.pageIndex = val
      this.getList()
    },
    VisaindexMethod (index) {
      return (this.formVisa.pageIndex - 1) * 5 + index + 1
    },
    VisaDown () {
      // var link = document.createElement('a')
      // link.style.display = "none"
      // link.href = `${this.downloadAllUrlDesign}?id=qz`
      // link.setAttribute("download", name)
      // document.body.appendChild(link)
      // link.click()
      this.downFileList('qz', '签证模板.xls')
    },
    // 上传传参
    VisaData () {
      return {
        code: this.formVisa.code,
        type: '签证',
      }
    },
    //上传成功
    VisaSuccess (response) {
      if (response.result == "保存数据失败") {
        this.$message.error('上传文件失败，请检查文件数据是否有误！')
      } else {
        this.$message.success('上传成功')
        this.getList()
      }
    },
    // 上传失败
    VisaError () {
      this.$message.error('上传失败')
    },
    // 表格删除
    VisaDet (row) {
      console.log(row)
      VisaDet(row.qzid)
        .then((res) => {
          if (res.data.message == 'success') {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error('删除失败')
          }
        })
        .catch(() => { })
    },
    // 设计变更
    ChangeCurrentChange (val) {
      this.formChange.pageIndex = val
      this.getList()
    },
    ChangeSizeChange (val) {
      this.formChange.pageSize = val
      this.getList()
    },
    ChangeindexMethod (index) {
      return (this.formChange.pageIndex - 1) * 5 + index + 1
    },
    ChangeDown () {
      // var link = document.createElement('a')
      // link.style.display = "none"
      // link.href = `${this.downloadAllUrlDesign}?id=bg`
      // link.setAttribute("download", name)
      // document.body.appendChild(link)
      // link.click()
      this.downFileList('bg', '设计变更.xls')
    },
    // 上传传参
    ChangeData () {
      return {
        code: this.formChange.code,
        type: '变更',
      }
    },
    //上传成功
    ChangeSuccess (response) {
      if (response.result == "保存数据失败") {
        this.$message.error('上传文件失败，请检查文件数据是否有误！')
      } else {
        this.$message.success('上传成功')
        this.getList()
      }
    },
    // 上传失败
    ChangeError () {
      this.$message.error('上传失败')
    },
    // 表格删除
    ChangeDet (row) {
      ChangeDet(row.sjbgid)
        .then((res) => {
          if (res.data.message == 'success') {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error('删除失败')
          }
        })
        .catch(() => { })
    },

    handleSizeChange (val) {
      this.formAnnex.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.formAnnex.pageIndex = val
      this.getList()
    },
    AnnexindexMethod (index) {
      return (this.formAnnex.pageIndex - 1) * 5 + index + 1
    },
    // 附件下载
    async AnnexDon (row) {
      // fjDownFileJs
      let params = { id: row.id }
      const res = await fjDownFileJs(params)
      console.log(res, "下载模板----------------------------------------")
      const blob = new Blob([res.data])

      let link = document.createElement('a')
      link.download = row.name
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)

      // console.log(`${this.downloadAllUrlAnnex}?id=${row.id}`)
      // var link = document.createElement('a')
      // link.style.display = "none"
      // link.href = `${this.downloadAllUrlAnnex}?id=${row.id}`
      // link.setAttribute("download", name)
      // document.body.appendChild(link)
      // link.click()
    },
    // 附件上传
    AnnexUp (row) {
      this.formAnnex.code = row.id
    },
    // 附件删除
    AnnexDet (row) {
      AnnexDet(row.id)
        .then((res) => {
          console.log(res)
          if (res.data.message == 'success') {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error('删除失败')
          }
        })
        .catch(() => { })
    },
    // 上传传参
    AnnexData () {
      return {
        code: this.formAnnex.code,
      }
    },
    //上传成功
    AnnexSuccess () {
      this.$message.success('上传成功')
      this.getList()
    },
    // 上传失败
    AnnexError () {
      this.$message.error('上传失败')
    },
    // 编辑按钮
    handleEdit () {
      this.dialogProblem = true
    },
    // 提交
    handleSubt () {

    },
    // 编辑保存
    dialogQuery () {
      this.dialogProblem = false
    },
    // 申报
    handleaddress () {
      this.dialogSB = true
    },

    operation () {
      this.dialogSB = true
    },
    // 保存
    feederDialogQuery () {
      this.formFBCost.procode = this.ProCode.code
      this.formGS.procode = this.ProCode.code
      FBCostSave(this.formFBCost).then((res) => {
        if (res.data.message == 'success') {
          this.saveType = this.saveType + '1'
          this.isSaveData = true
        } else {
          this.$message.error('保存失败')
        }
      })

      this.formJS.xmbm = this.ProCode.code
      this.formJS.gcjsid = this.ProCode.code
      // this.formJS.sendDate = this.formJS.sendDate + ' 00:00:00'
      // this.formJS.jhshrq = this.formJS.jhshrq + ' 00:00:00'
      console.log('this.formJS', this.formJS)
      this.$refs['formJS'].validate((valid) => {
        if (!this.isJsFormats) return this.$message.warning("请检查结算信息数据是否填写正确")
        if (!this.isFormats) return this.$message.warning("请检查概算信息数据是否填写正确")
        if (this.tableDatabound.length == 0) return this.$message.warning("至少上传一条出库料单")
        if (valid) {
          saveSBForm(this.formJS).then(res => {
            if (res.data.message == 'success') {
              this.saveType = this.saveType + '3'
              this.isSaveData = true
              this.$refs['formGS'].validate((valid) => {
                if (valid) {
                  GSSave(this.formGS).then((res) => {
                    if (res.data.message == 'success') {
                      this.saveType = this.saveType + '2'
                      this.isSaveData = true
                      this.$message.success('保存成功')
                      this.submitDisl=false
                    } else {
                      this.$message.error('保存失败')
                    }
                  })
                    .catch(() => { })
                } else {
                  return this.$message.warning('请先输入概算信息必填内容')
                }
              })
            } else {
              this.$message.error('保存失败')
            }
          })
        } else {
          return this.$message.warning('请先输入结算信息必填项')
        }
      })


    },
    submiteDatas () {
      console.log('===', this.ProCode.code)
      console.log('===', this.ProCode)

      this.$refs['formJS'].validate((valid) => {
        if (valid) {
          this.$refs['formGS'].validate((valid) => {
            if (valid) {
              if (!this.isJsFormats) return this.$message.warning("请检查结算信息数据是否填写正确")
              if (!this.isFormats) return this.$message.warning("请检查概算信息数据是否填写正确")
              if (this.tableDatabound.length == 0) return this.$message.warning("至少上传一条出库料单")
              Promise.all([saveSBForm(this.formJS), GSSave(this.formGS)]).then(([res1,res2])=>{
                if(res1.data.message == 'success'&&res2.data.message == 'success'){
                  setTimeout(() => {
                     submitDeclare(this.ProCode.code).then((res) => {
                            if (res.status === 200) {
                              // this.getList()
                              this.$emit('cloaseDialogVis', false)
                              this.$message.success('提交成功')
                            }
                          })
                  }, 500);

                }else{
                   this.$message.error('保存失败')
                }
              })
              // saveSBForm(this.formJS).then(res => {
              //   if (res.data.message == 'success') {
              //     this.saveType = this.saveType + '3'
              //     this.isSaveData = true
              //     this.$refs['formGS'].validate((valid) => {
              //       if (valid) {
              //         GSSave(this.formGS).then((res) => {
              //           if (res.data.message == 'success') {
              //             this.saveType = this.saveType + '2'
              //             this.isSaveData = true
              //             submitDeclare(this.ProCode.code).then((res) => {
              //               if (res.status === 200) {
              //                 // this.getList()
              //                 this.$emit('cloaseDialogVis', false)
              //                 this.$message.success('提交成功')
              //               }
              //             })
              //           } else {
              //             this.$message.error('保存失败')
              //           }
              //         })
              //           .catch(() => { })
              //       } else {
              //         return this.$message.warning('请先输入概算信息必填内容')
              //       }
              //     })
              //   } else {
              //     this.$message.error('保存失败')
              //   }
              // })



            } else {
              return this.$message.warning('请先输入概算信息必填内容')
            }
          })

        } else {
          return this.$message.warning('请先输入结算信息必填项')
        }
      })

    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-descriptions-item__label:not(.is-bordered-label) {
  margin-left: 50px;
}

.box-card {
  margin-bottom: 10px;
}

.directiveBtn {
  height: 34px;
  margin-top: 10px;
}

.buttonUpDon {
  display: flex;
}

.upload-demo {
  margin-right: 10px;
}

.setFormArea {
  height: 75vh;
  overflow-y: scroll;
  margin-bottom: 20px;
}

.setFooter {
  position: absolute;
  bottom: 0;
  right: 30px;
}
</style>
