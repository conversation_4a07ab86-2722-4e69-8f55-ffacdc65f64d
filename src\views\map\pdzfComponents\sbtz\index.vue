<template>
  <div class="sbtz">
    <el-tabs v-model="activeName">
      <el-tab-pane label="导线" name="导线">
        <el-table
          :data="dxtableData"
          max-height="500"
          v-horizontal-scroll="'always'"
          >
          <el-table-column
            prop="sbmc"
            align="center"
            label="设备名称"
            width="180"
            >
          </el-table-column>
          <el-table-column
            prop="sscs"
            align="center"
            label="所属城市"
            width="180"
            >
          </el-table-column>
          <el-table-column
            prop="dydj"
            align="center"
            width="180"
            label="电压等级">
          </el-table-column>
          <el-table-column
            prop="gdqy"
            align="center"
            width="180"
            label="供电区域">
          </el-table-column>
          <el-table-column
            prop="qsgt"
            align="center"
            width="180"
            label="起始杆塔">
          </el-table-column>
          <el-table-column
            prop="zzgt"
            align="center"
            width="180"
            label="终止杆塔">
          </el-table-column>
          <el-table-column
            prop="plfs"
            align="center"
            width="180"
            label="排列方式">
          </el-table-column>
          <el-table-column
            prop="dxcd"
            align="center"
            width="180"
            label="导线长度">
          </el-table-column>
          <el-table-column
          prop="gcbh"
          align="center"
          width="180"
          label="工程编号">
        </el-table-column>
        <el-table-column
          prop="gcmc"
          align="center"
          width="400px"
          label="工程名称">
        </el-table-column>
          <el-table-column
            prop="sbxh"
            align="center"
            width="180"
            label="设备型号">
          </el-table-column>
          <el-table-column
            prop="dxlx"
            align="center"
            width="180"
            label="导线类型">
          </el-table-column>
          <el-table-column
            prop="dxjm"
            align="center"
            width="180"
            label="导线截面">
          </el-table-column>
          <el-table-column
            prop="edzll"
            align="center"
            width="180"
            label="额定载流量">
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="电缆段" name="电缆段">
        <el-table
          :data="dldTableData"
          max-height="500"
          style="width: 100%">
          <el-table-column
            prop="sscs"
            align="center"
            width="180"
            label="所属城市"
          >
          </el-table-column>
          <el-table-column
            prop="sscs"
            align="center"
            width="180"
            label="所属馈线"
          >
          </el-table-column>
          <el-table-column
            prop="dydj"
            align="center"
            width="180"
            label="电压等级">
          </el-table-column>
          <el-table-column
            prop="gdqy"
            align="center"
            width="180"
            label="供电区域">
          </el-table-column>
          <el-table-column
            prop="fsfs"
            align="center"
            width="180"
            label="敷设方式">
          </el-table-column>
          <el-table-column
            prop="dlcd"
            align="center"
            width="180"
            label="电缆长度">
          </el-table-column>
          <el-table-column
            prop="fjcd"
            align="center"
            label="附加长度">
          </el-table-column>
          <el-table-column
            prop="gcbh"
            align="center"
            label="工程编号">
          </el-table-column>
          <el-table-column
            prop="gcmc"
            align="center"
            width="400px"
            label="工程名称">
          </el-table-column>
          <el-table-column
            prop="sbxh"
            align="center"
            label="设备型号">
          </el-table-column>
          <el-table-column
            prop="eddy"
            align="center"
            label="额定电压">
          </el-table-column>
          <el-table-column
            prop="jmj"
            align="center"
            label="截面积">
          </el-table-column>
          <el-table-column
            prop="edzll"
            align="center"
            label="额定载流量">
          </el-table-column>
          <el-table-column
            prop="xxcl"
            align="center"
            label="线芯材料">
          </el-table-column>
          <el-table-column
            prop="xs"
            align="center"
            label="芯数">
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="杆塔" name="杆塔">
        <el-table
          :data="gtTableData"
          max-height="500"
          style="width: 100%">
          <el-table-column
            prop="gtbh"
            align="center"
            width="180"
            label="杆塔编号"
          >
          </el-table-column>
          <el-table-column
            prop="ssds"
            align="center"
            width="180"
            label="所属地市"
          >
          </el-table-column>
          <el-table-column
            prop="sskx"
            align="center"
            width="180"
            label="所属馈线">
          </el-table-column>
          <el-table-column
            prop="dydj"
            width="180"
            align="center"
            label="电压等级">
          </el-table-column>
          <el-table-column
            prop="dqtz"
            width="180"
            align="center"
            label="地区特征">
          </el-table-column>
          <el-table-column
            prop="sfzd"
            width="180"
            align="center"
            label="是否终端">
          </el-table-column>
          <el-table-column
            prop="dj"
            width="180"
            align="center"
            label="档距">
          </el-table-column>
          <el-table-column
            prop="gcbh"
            width="180"
            align="center"
            label="工程编号">
          </el-table-column>
          <el-table-column
            prop="gcmc"
            width="400px"
            align="center"
            label="工程名称">
          </el-table-column>
          <el-table-column
            prop="sbxh"
            width="180"
            align="center"
            label="设备型号">
          </el-table-column>
          <el-table-column
            prop="gtcz"
            width="180"
            align="center"
            label="杆塔材质">
          </el-table-column>
          <el-table-column
            prop="gtxz"
            width="180"
            align="center"
            label="gtxz">
          </el-table-column>
          <el-table-column
            prop="gtg"
            width="180"
            align="center"
            label="杆塔高">
          </el-table-column>
          <el-table-column
            prop="jcxs"
            width="180"
            align="center"
            label="基础形式">
          </el-table-column>
          <el-table-column
            prop="sftg"
            width="180"
            align="center"
            label="是否同杆">
          </el-table-column>
          <el-table-column
            prop="tghs"
            width="180"
            align="center"
            label="同杆回数">
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="配电室" name="配电室">
        <el-table
          :data="pdsTableData"
          max-height="500"
          style="width: 100%">
          <el-table-column
            prop="sscs"
            align="center"
            label="所属城市"
          >
          </el-table-column>
          <el-table-column
            prop="dydj"
            align="center"
            label="电压等级">
          </el-table-column>
          <el-table-column
            prop="gdqy"
            align="center"
            label="供电区域">
          </el-table-column>
          <el-table-column
            prop="gcbh"
            align="center"
            label="工程编号">
          </el-table-column>
          <el-table-column
            prop="gcmc"
            align="center"
            width="400px"
            label="工程名称">
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="终端头" name="终端头">
        <el-table
          :data="zdtTableData"
          max-height="500"
          style="width: 100%">
          <el-table-column
            prop="sscs"
            align="center"
            label="所属城市"
          >
          </el-table-column>
          <el-table-column
            prop="sskx"
            align="center"
            label="所属馈线">
          </el-table-column>
          <el-table-column
            prop="dydj"
            align="center"
            label="电压等级">
          </el-table-column>
          <el-table-column
            prop="gcbh"
            align="center"
            label="工程编号">
          </el-table-column>
          <el-table-column
            prop="gcmc"
            align="center"
            width="400px"
            label="工程名称">
          </el-table-column>
          <el-table-column
            prop="sbxh"
            align="center"
            label="设备型号">
          </el-table-column>
          <el-table-column
            prop="zdlx"
            align="center"
            label="终端类型">
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  import { apipost } from '@/utils/mapRequest'
  export default {
    components: {},
    data() {
      return {
        activeName: '导线',
        dxtableData: [], // 导线
        dldTableData: [], // 电缆段
        gtTableData: [], // 杆塔
        pdsTableData: [], // 配电室
        zdtTableData: [] // 终端头
      };
    },
    methods: {},
    mounted() {
      let params={id:this.$route.query.childProjectId}
      apipost(`/mapModel/queryLedger`,params).then((res) => {
         this.dxtableData = res.result.dx
         this.dldTableData = res.result.dld
         this.gtTableData = res.result.gt
         this.pdsTableData = res.result.pds
         this.zdtTableData = res.result.zdt
      })
    },
  }

</script>

<style lang="sass" scoped>
</style>

