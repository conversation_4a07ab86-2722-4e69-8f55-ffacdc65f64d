
.setting .setting-exploration {
  height: 4rem;
  border-bottom: 1px solid #e5e2e2;
  padding: 0 1.5rem;
  font-size: 0.8rem;
  background: #ffffff;
}

.setting .setting-nav, .setting-info {
  display: flex;
  padding-top: 0.5rem;
}

.setting .setting-nav, .setting-info {
  display: flex;
}

.setting .setting-nav .setting-eqType {
  text-align: right;
  margin-right: 0.8rem;
}

.setting .setting-nav .setting-eqName, .setting-eqType {
  flex: 1;
}

.setting .setting-info .setting-lineName {
  flex: 1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}

.setting .setting-info .setting-setBtn {
  width: 3rem;
  text-align: right;
}

.setting-table {
  margin: 0 0.2rem;
}

.setting-scaleTable {
  width: 100vw;
  text-align: center;
  font-size: 1rem;
}

.setting-table th {
  border: 0.01rem solid #e3dfdf;
}

.setting-echarts {
  display: none;
  height: calc(100vh - 9rem);
  position: relative;
}

.setting-echarts .setting-echartTitle {
  height: 3rem;
  line-height: 3rem;
  font-size: 0.9rem;
  padding-left: 1rem;
}

.setting-echarts .setting-echartsArea {
  position: absolute;
  width: 100%;
  height: 80%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.van-tabs__wrap {
  position: fixed;
  height: 3rem;
  width: 100vw;
  z-index: 99;
}

.van-nav-bar__right {
  color: white;
}

.setting-dialog {
  position: fixed;
  top: 0;
  z-index: 1000;
  width: 63vw;
  height: 61vh;
  background: white;
  display: none;
}

.setting-dialog .setting-diaTitle {
  height: 3rem;
  line-height: 3rem;
  text-align: center;
  font-size: 1rem;
  color: #ffffff;
  background: #526ADE;
  width: 100%;
}
.proinfo-scaleTable{
  border: 1px solid #f1e9e9;
}
.setting-dialog .setting-diaselect {
  height: 3rem;
  line-height: 3rem;
  text-align: center;
  font-size: 1rem;
  margin: 0 1rem;
}

/*.setting-diaselect .setting-diaitems {*/
/*  height: calc(100vh - 7rem);*/
/*  overflow-y: scroll;*/
/*  position: absolute;*/
/*  left: 0;*/
/*  top: 3rem;*/
/*  width: 100%;*/
/*}*/

.setting-diaselect .setting-diaitems .van-checkbox {
  height: 3rem;
  line-height: 3rem;
}

.setting-tableBtn {
  background: #d7d6d6;
}

.pro-navright {
  height: 2rem;
  line-height: 2rem;
  padding-left: 0.5rem;
}

.pro-addFieldLeng .van-cell__title {
  width: 6.5rem;
}

.van-swipe-cell .van-swipe-cell__right {
  top: -10px;
}

.van-swipe-cell .van-swipe-cell__right .van-button__content {
  padding-top: 0.2rem;
}
.civicomputedSelect .van-cell__title{
  width: 7rem;
}
.computedSelect .van-cell__title{
  width: 8.5rem;
}
.topcomputedSelect .van-cell__title{
  width: 9rem;
}
.project-label{
  padding-left: 1rem;
  background: #fff;
  font-size: 0.8rem;
}
.setting-input{
  display: flex;
  background: #ffffff;
}
.setting-input input{
  border: 1px solid #c1bdbd;
  width: 100%;
  height: 2rem;
  text-align: center;
  border-radius: 0.5rem;
}
.setting-input .setting-inputLeft,.setting-inputRig {
  width: 3rem;
}
.setting-input .setting-inputMid {
  font-size: 1.5rem;
  width: 1rem;
  text-align: center;
}
.pro-addTitle{
  font-size: 16px;
}
.setting-tootip{
  background-image: url("~@/assets/setting/tootipback.jpg");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.setting-tootip .setting-tootipMain{
  /*margin: 2rem 4rem;*/
  padding: 1.2rem 1.2rem;
  font-size: 0.9rem;
}
.setting-tootip .setting-tootipItem{
  line-height: 1.5rem;

}
.setting-tootip .setting-tootipItem .setting-content{
  margin-left: 0.5rem;
}
.setting-tootip .setting-tootipItem .setting-title{
  color: #2828f3;
  float: left;
}
.project-addEveryArea .van-popup{
  position: fixed;
  width: 40%;
  right: 13px;
  bottom: 25rem;
  left: 290px;
}
