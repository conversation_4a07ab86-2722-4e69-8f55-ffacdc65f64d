define(["./when-b60132fc","./FrustumGeometry-9d84ae9b","./buildModuleUrl-4e1b81e7","./Cartographic-3309dd0d","./Check-7b2a090c","./Math-119be1a3","./Cartesian2-47311507","./PrimitiveType-a54dc62f","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-3a88ba31","./FeatureDetection-c3b71206","./GeometryAttributes-252e9929","./Plane-7ae8294c","./VertexFormat-6446fca0"],(function(e,t,a,r,n,u,c,i,o,b,d,m,s,f,y,G,C,p){"use strict";return function(a,r){return e.defined(r)&&(a=t.FrustumGeometry.unpack(a,r)),t.FrustumGeometry.createGeometry(a)}}));
