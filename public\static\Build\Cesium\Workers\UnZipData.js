define(["./createTaskProcessorWorker","./pako_inflate-f73548c4","./CompressedTextureBuffer-290a1ff4","./when-b60132fc","./PixelFormat-34cafcee","./RuntimeError-4a5c8994","./S3MPixelFormat-4f2b7689","./WebGLConstants-4ae0db90"],(function(e,r,t,n,a,i,f,E){"use strict";var u,o,s=1,_=2,c={};c[0]=a.PixelFormat.RGB_DXT1,c[s]=a.PixelFormat.RGBA_DXT3,c[_]=a.PixelFormat.RGBA_DXT5;var d,T=0;function l(e){var r=e.data,f=r.byteLength,E=new Uint8Array(r,e.offset),s=d._malloc(f);!function(e,r,t,n){var a,i=t/4,f=n%4,E=new Uint32Array(e.buffer,0,(n-f)/4),u=new Uint32Array(r.buffer);for(a=0;a<E.length;a++)u[i+a]=E[a];for(a=n-f;a<n;a++)r[t+a]=e[a]}(E,d.HEAPU8,s,f);var _=d._crn_get_dxt_format(s,f),l=c[_];if(!n.defined(l))throw new i.RuntimeError("Unsupported compressed format.");var m,y=d._crn_get_levels(s,f),U=d._crn_get_width(s,f),p=d._crn_get_height(s,f),A=0;for(m=0;m<y;++m)A+=a.PixelFormat.compressedTextureSizeInBytes(l,U>>m,p>>m);if(T<A&&(n.defined(u)&&d._free(u),u=d._malloc(A),o=new Uint8Array(d.HEAPU8.buffer,u,A),T=A),d._crn_decompress(s,f,u,A,0,y),d._free(s),n.defaultValue(e.bMipMap,!1)){var B=o.slice(0,A);return new t.CompressedTextureBuffer(l,U,p,B)}var w=a.PixelFormat.compressedTextureSizeInBytes(l,U,p),P=o.subarray(0,w),g=new Uint8Array(w);return g.set(P,0),new t.CompressedTextureBuffer(l,U,p,g)}function m(e){var r=new DataView(e),t=0,n=r.getUint32(t,!0);t+=Uint32Array.BYTES_PER_ELEMENT;var a=r.getUint32(t,!0);t+=Uint32Array.BYTES_PER_ELEMENT;var i=r.getUint32(t,!0);t+=Uint32Array.BYTES_PER_ELEMENT;var f=r.getUint32(t,!0);t+=Uint32Array.BYTES_PER_ELEMENT;var E=r.getUint32(t,!0);t+=Uint32Array.BYTES_PER_ELEMENT;var u=l({data:e.slice(t,t+E)}).bufferView,o=new ArrayBuffer(t+u.byteLength),s=new Uint8Array(o),_=new Uint32Array(o);return t=0,_[0]=n,t+=Uint32Array.BYTES_PER_ELEMENT,_[1]=a,t+=Uint32Array.BYTES_PER_ELEMENT,_[2]=i,t+=Uint32Array.BYTES_PER_ELEMENT,_[3]=f,t+=Uint32Array.BYTES_PER_ELEMENT,_[4]=u.byteLength,t+=Uint32Array.BYTES_PER_ELEMENT,s.set(u,t),o}function y(e,t){for(var n=e.data,a=[],i=0;i<n.length;i++){var E,u=n[i];try{var o=new Uint8Array(u.zipBuffer);E=r.pako.inflate(o).buffer,new DataView(E).getUint32(0,!0)===f.S3MPixelFormat.CRN_DXT5&&(E=m(E)),t.push(E),a.push({unzipBuffer:E,name:u.name})}catch(r){u.unzipLength===u.zippedLength&&(E=u.zipBuffer.buffer,e.isCRN&&(E=m(E)),t.push(E),a.push({unzipBuffer:E,name:u.name}));continue}}return{data:a}}function U(){self.onmessage=e(y),self.postMessage(!0)}return function(e){var r=e.data.webAssemblyConfig;if(n.defined(r))return require([r.modulePath],(function(e){n.defined(r.wasmBinaryFile)?(n.defined(e)||(e=self.Module),d=e,U()):(d=e,U())}))}}));
