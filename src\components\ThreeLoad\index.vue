<template>
    <canvas id="mycanvas" v-bind:style="canvasSize">
      <!--此处放预览的内容-->
    </canvas>
  </template>
  
  <script>
  
  import { previewDraw } from '@/utils/threeLoad'
  
  export default {
    name: 'ThreeLoad',
    props: [ 'canvasDataBind', 'height', 'width' ],
    data(){
      return{
          canvasSize:{
          "width": this.width,//"615px !important",
          "height": this.height//"268px !important"
        }
      }
    },
    mounted() {
        previewDraw('mycanvas', JSON.stringify(this.canvasDataBind));
    },
    methods:{
      Draw(data) {
        previewDraw('mycanvas', JSON.stringify(data));
      }
    }
  }
  </script>
  
  <style scoped lang="scss">
    // #mycanvas {
    //     width: 615px !important;
    //     height: 268px !important;
    // }
  </style>
  