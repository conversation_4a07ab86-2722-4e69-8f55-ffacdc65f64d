<!DOCTYPE html>
<html>

<head>
  <script>
    window._interceptConfig = {
      collection: true,
      click: true, //页面点击事件信息采集开关
      page: true, //页面跳转加载事件信息采集开关
      error: true, //异常信息采集开关
      http: true, //通讯信息采集开关
      name: "pwgcszhyjybj_n_prd.pms-sd-pgject-web-bhne-wb", //系统编号，被采集信息的系统名称。前端简称
      url: "http://apm.sd.sgcc.com.cn:19411/api/v2/spansWeb"//数据上报服务地址，根据实际情况输入
    }
  </script>
  <script src="http://apm.sd.sgcc.com.cn:19411/assets/js/intercept.min.js" async></script>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <meta name="viewport"
    content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
  <!-- 忽略将页面中的数字识别为电话号码 -->
  <meta name="format-detection" content="telephone=no" />
  <!-- 内网思极 -->
  <script src='http://map-sd.sgcc.com.cn/maps?v=3.0.0'></script>
  <!-- 外网 -->
  <!-- <script src='https://map.sgcc.com.cn/maps?v=3.0.0'></script> -->
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <link rel='stylesheet' href='./luckysheet/plugins/plugins.css' />
  <link rel='stylesheet' href='./luckysheet/css/luckysheet.css' />
  <link rel='stylesheet' href='./luckysheet/assets/iconfont/iconfont.css' />
  <script src="./luckysheet/plugins/js/plugin.js"></script>
  <script src="./config.js"></script>
  <script src="<%= BASE_URL %>./luckysheet/luckysheet.umd.js"></script>
  <script type="text/javascript" src="./static/Build/Cesium/Cesium.js"></script>
  <script src="./nbgis.mini.js"></script>
  <script src="./nbgis.mini.css"></script>
  <script src="./static/js//sm21.js"></script>
  <script src="./static/js/three.min.js"></script>
  <script src="./static/js/sm2.js"></script>

  <title>
    <%= htmlWebpackPlugin.options.title %>
  </title>
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>
<script src="./config.js"></script>