<template>
  <div>
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div class="pro-leftTitle">
            <div
              v-if="showBackAdds"
              class="maps-zhedNav"
              @click="backCurrentDom"
            >
              <img
                class="mapites-backImg"
                :src="require('@/assets/' + 'map/settingImg/backAdds.png')"
                alt=""
              />
            </div>
            <span />
            架空电缆
            <div
              v-if="!showBackAdds"
              class="maps-zhedNav"
              @click="showEveryItemSet"
            >
              <img
                v-show="!isFoldArea"
                class="mapites-zhed"
                :src="require('@/assets/' + 'map/settingImg/zhedie.png')"
                alt=""
              />
              <img
                v-show="isFoldArea"
                class="mapites-zhed"
                :src="require('@/assets/' + 'map/settingImg/zkzhedie.png')"
                alt=""
              />
            </div>
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img
            class="settingImg"
            :src="require('@/assets/' + 'map/settingImg/useSetting.png')"
            alt=""
          />
          <p>启用</p>
        </div>
      </div>
      <div v-show="isFoldArea" class="pro-addForm">
        <div class="map-showNav">基本信息</div>
        <van-row>
          <van-field
            v-model="overheadLine.mark"
            label="编号"
            placeholder="请输入杆塔编号"
          />
        </van-row>
        <div class="map-showNav">选型信息</div>
        <!--架空电缆绘制设置-->
        <van-row
          :style="{ pointerEvents: settingObj.isDisabledDraw ? 'none' : '' }"
        >
          <van-field
            :disabled="settingObj.isDisabledDraw"
            readonly
            clickable
            :value="overheadLine.drawSet"
            label="绘制设置"
            placeholder="请选择绘制设置"
            @click="settingObj.overheadLine.drawSet = true"
          />
          <van-popup
            v-model="settingObj.overheadLine.drawSet"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              :disabled="settingObj.isDisabledDraw"
              title="绘制设置"
              value-key="key"
              :columns="drawSet"
              @confirm="onConfirmJkdlSel(3, '', $event)"
              @cancel="settingObj.overheadLine.drawSet = false"
            />
          </van-popup>
        </van-row>
        <!--架空电缆状态-->
        <van-row v-show="settingObj.isShowDrawRaw">
          <van-field
            readonly
            clickable
            :value="overheadLine.state"
            label="状态"
            placeholder="请选择状态"
            @click="settingObj.overheadLine.state = true"
          />
          <van-popup
            v-model="settingObj.overheadLine.state"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="状态"
              value-key="key"
              :columns="cableLineState"
              @confirm="onConfirmJkdlSel(6, '', $event)"
              @cancel="settingObj.overheadLine.state = false"
            />
          </van-popup>
        </van-row>
        <!--架空电缆状态-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="overheadLine.voltage"
            label="电压"
            placeholder="请选择电压等级"
            @click="settingObj.overheadLine.voltage = true"
          />
          <van-popup
            v-model="settingObj.overheadLine.voltage"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="电压"
              value-key="key"
              :columns="voltage"
              @confirm="onConfirmJkdlSel(0, '', $event)"
              @cancel="settingObj.overheadLine.voltage = false"
            />
          </van-popup>
        </van-row>
        <!--架空电缆回路数-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="overheadLine.backLine"
            label="回路数"
            placeholder="请选择回路数"
            @click="settingObj.overheadLine.backLine = true"
          />
          <van-popup
            v-model="settingObj.overheadLine.backLine"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="回路数"
              value-key="key"
              :columns="backLine"
              @confirm="onConfirmJkdlSel(1, '', $event)"
              @cancel="settingObj.overheadLine.backLine = false"
            />
          </van-popup>
        </van-row>
        <!--架空电缆杆塔型号-->
        <van-row v-show="settingObj.isShowDrawRaw">
          <van-field
            readonly
            clickable
            :value="overheadLine.towerModel"
            label="杆塔型号"
            placeholder="请选择杆塔型号"
            @click="settingObj.overheadLine.towerModel = true"
          />
          <van-popup
            v-model="settingObj.overheadLine.towerModel"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="杆塔型号"
              value-key="name"
              :columns="jkdltowerModel"
              @confirm="onConfirmJkdlSel(2, '', $event)"
              @cancel="settingObj.overheadLine.towerModel = false"
            />
          </van-popup>
        </van-row>
        <!--架空电缆设备类型-->
        <van-row v-if="settingObj.isShowDrawRaw">
          <van-field
            readonly
            clickable
            :value="overheadLine.towerSbModel"
            label="设备类型"
            placeholder="选择设备类型"
            @click="settingObj.overheadLine.towerSbModel = true"
          />
          <van-popup
            v-model="settingObj.overheadLine.towerSbModel"
            round
            position="bottom"
          >
            <van-picker
              title="设备类型"
              show-toolbar
              value-key="key"
              :columns="towerSbModel"
              @confirm="onConfirBranchSelc(11, '', $event)"
              @cancel="settingObj.overheadLine.towerSbModel = false"
            />
          </van-popup>
        </van-row>
        <!--架空线路线路 -->
        <div :style="{ width: '100%', overflowX: 'scroll' }">
          <table
            border="1"
            class="map-tables"
            cellspacing="0"
            cellpadding="0"
            align="center"
          >
            <tr>
              <th :style="{ width: '4rem' }">线路名称</th>
              <th :style="{ width: '4rem' }">状态</th>
              <th :style="{ width: '8rem' }">电缆型号</th>
              <th>操作</th>
            </tr>
            <tr v-for="(item, index) in overheadLine.listArr">
              <td>
                <van-field
                  v-model="item.lineName"
                  placeholder="请输入线路名称"
                />
              </td>
              <td>
                <van-field
                  readonly
                  clickable
                  class="pickerSelect"
                  :value="item.lineState"
                  placeholder="线路状态"
                  @click="
                    settingObj.overheadLine.listArr[index].lineStateVis = true
                  "
                />
                <van-popup
                  v-model="settingObj.overheadLine.listArr[index].lineStateVis"
                  round
                  position="bottom"
                >
                  <van-picker
                    title="线路状态"
                    show-toolbar
                    value-key="key"
                    :columns="mainLineState"
                    @confirm="onConfirmJkdlSel(4, index, $event)"
                    @cancel="
                      settingObj.overheadLine.listArr[
                        index
                      ].lineStateVis = false
                    "
                  />
                </van-popup>
              </td>
              <td>
                <van-field
                  readonly
                  clickable
                  :value="item.lineModel"
                  placeholder="电缆型号"
                  @click="
                    settingObj.overheadLine.listArr[index].lineModelVis = true
                  "
                />
                <van-popup
                  v-model="settingObj.overheadLine.listArr[index].lineModelVis"
                  round
                  position="bottom"
                >
                  <van-picker
                    title="电缆型号"
                    show-toolbar
                    value-key="name"
                    :columns="jkdldxxhType"
                    @confirm="onConfirmJkdlSel(5, index, $event)"
                    @cancel="
                      settingObj.overheadLine.listArr[
                        index
                      ].lineModelVis = false
                    "
                  />
                </van-popup>
              </td>
              <td :style="{ width: '4rem', textAlign: 'center' }">
                <span
                  :style="{ color: 'red' }"
                  @click="removeMainLine(3, index)"
                  >删除</span
                >
              </td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { apiget ,apipost} from "@/utils/mapRequest";

export default {
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false,
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的id
    editData: {
      type: Object,
      defaults: () => {},
    },
    // 电缆型号
    dlxlList: {
      type: Array,
      defaults: () => [],
    },
    // 杆塔型号
    gtList: {
      type: Array,
      defaults: () => [],
    },
    remodeState: {
      type: Boolean,
      defaults: false,
    },
  },
  data() {
    return {
      isFoldArea: false,
      drawSet: [
        {
          key: "选择现有杆",
          value: "选择现有杆",
        },
        {
          key: "绘制起始杆",
          value: "绘制起始杆",
        },
      ], // 绘制设置
      backLine: [
        {
          key: "单回",
          value: "单回",
        },
        {
          key: "双回",
          value: "双回",
        },
        {
          key: "三回",
          value: "三回",
        },
        {
          key: "四回",
          value: "四回",
        },
      ], // 回路
      cableLineState: [], // 电缆线路状态
      lineState: [
        {
          key: "新建",
          value: "新建",
        },
        {
          key: "原有",
          value: "原有",
        },
        {
          key: "拆除",
          value: "拆除",
        },
      ],
      settingObj: {
        isDisabledDraw: false,
        // 架空电缆
        overheadLine: {
          voltage: false, // 电压等级
          backLine: false, // 回路数
          towerModel: false, // 杆塔型号
          drawSet: false, // 绘制设置
          state: false, // 架空电缆状态
          listArr: [
            {
              lineStateVis: false, // 线路状态
              lineModelVis: false, // 线路型号
            },
          ],
        },
      },
      voltage: [
        {
          key: "10kV",
          value: "10kV",
        },
        {
          key: "0.4kV",
          value: "0.4kV",
        },
      ], // 电压等级
      // 架空线路
      overheadLine: {
        state: "新建", // 架空电缆状态
        voltage: "10kV", // 电压等级
        towerSbModel: "不带杆上设备", // 设备类型
        backLine: "单回", // 回路数
        towerModel: "", // 杆塔型号
        towerModelType: "", // 杆塔型号
        towerModelId: "", // 杆塔型号id
        drawSet: "选择现有杆", // 绘制设置
        imgList: [], // 文件列表
        message: "", // 备注信息
        audioList: [], // 语音列表
        listArr: [
          {
            voltage: "", // 电压等级
            lineName: "线路一", // 线路名称
            lineState: "新建", // 线路状态
            lineModel: "", // 线路型号
            lineModelId: "", // 线路型号id
          },
        ],
      },
      jkdltowerModel: [], // 架空电缆杆塔型号
      towerSbModel: [
        {
          key: "不带杆上设备",
          value: "不带杆上设备",
        },
        {
          key: "带单侧隔离刀闸",
          value: "带单侧隔离刀闸",
        },
        {
          key: "带单侧隔离刀闸、断路器",
          value: "带单侧隔离刀闸、断路器",
        },
        {
          key: "带单侧隔离刀闸、断路器、关口计量",
          value: "带单侧隔离刀闸、断路器、关口计量",
        },
      ], // 杆塔设备类型
      jkdldxxhType: [], // 导线导线型号
      mainLineState: [], // 杆塔状态状态
      gtState: [
        {
          key: "新建",
          value: "新建",
        },
        {
          key: "原有",
          value: "原有",
        },
        {
          key: "改造",
          value: "改造",
        },
        {
          key: "拆除",
          value: "拆除",
        },
      ],
    };
  },
  watch: {
    // 监听改前改后状态
    remodeState: {
      handler(newVal) {
        if (newVal) {
          // 改后不显示拆除的数据
          this.cableLineState = this.lineState.slice(0, 2);
          this.mainLineState = this.gtState.slice(0, 3);
          this.overheadLine.state = "新建";
          for (const j in this.overheadLine.listArr) {
            this.overheadLine.listArr[j].lineState = "新建";
          }
        } else {
          // 改前不显示新建的数据
          this.cableLineState = this.lineState.slice(1);
          this.mainLineState = this.gtState.slice(1);
          this.overheadLine.state = "原有";
          for (const j in this.overheadLine.listArr) {
            this.overheadLine.listArr[j].lineState = "原有";
          }
        }
      },
      deep: true,
      immediate: true,
    },
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal;
      },
      deep: true,
      immediate: true,
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal;
        if (data.moudleType === "JKDLHZ") {
          if (data.drawSettings === "绘制起始杆") {
            // 查询电缆线路类型
            const valltege = data.voltage === "10kV" ? "10kV" : "0.4kV";
            this.getAwaitTowerOrLineType(0, 1, 1, "", "", "", "", valltege);
            this.getAwaitTowerOrLineType(1, 4, 1, "", "", "", "", valltege);
            this.settingObj.visible = true;
            this.selectCheck.objVis.showOverLine = true;
            this.this.overheadLine.state = data.state;
            this.this.overheadLine.voltage = data.voltage;
            this.this.overheadLine.backLine = data.dlNum;
            this.this.overheadLine.towerModelId = data.moduleId;
            this.this.overheadLine.towerModel = data.pointModule;
            this.this.overheadLine.drawSet = data.drawSettings;
            this.this.overheadLine.towerSbModel = data.equipType;
            this.settingObj.overheadLine.listArr = [];
            this.this.overheadLine.listArr = [];
            this.this.overheadLine.imgList = [];
            this.this.overheadLine.audioList = [];
            this.this.overheadLine.mark = data.mark; // 编号
            this.this.overheadLine.message = data.note;
            this.settingObj.isDisabledDraw = true;
            for (const k in data.imgList) {
              const objs = {
                url: data.imgList[k].path,
                isImage: true,
                isSaveReport: data.imgList[k].isSaveReport,
              };
              this.this.overheadLine.imgList.push(objs);
            }
            for (const s in data.voiceList) {
              const objs = {
                content: data.voiceList[s].path,
              };
              this.this.overheadLine.audioList.push(objs);
            }
            for (const j in data.listArr) {
              const obj = {
                lineName: data.listArr[j].lineName, // 线路名称
                lineState: data.listArr[j].state, // 线路状态
                lineModel: data.listArr[j].lineModel, // 导线型号
                lineModelId: data.listArr[j].moduleId, // 导线id
              };
              const isShow_one = {
                lineStateVis: false, // 主线路状态
                lineModelVis: false, // 主线路导线型号
              };
              this.settingObj.overheadLine.listArr.push(isShow_one);
              this.thisoverheadLine.listArr.push(obj);
            }
          }
        }
      },
      deep: true,
    },
    dlxlList: {
      handler(newVal) {
        this.jkdldxxhType = newVal;
        this.overheadLine.listArr[0].lineModel = newVal[0].name;
        this.overheadLine.listArr[0].lineModelId = newVal[0].id;
      },
      deep: true,
    },
    gtList: {
      handler(newVal) {
        this.jkdltowerModel = newVal;
        this.overheadLine.towerModel = newVal[0].name;
        this.overheadLine.towerModelId = newVal[0].id;
      },
      deep: true,
    },
  },
  mounted() {},
  methods: {
    /**
     * 获取语音数据
     */
    getAudioData(data) {
      this.overheadLine.audioList = data.aduioList;
    },
    /**
     * 获取上传图片数据
     */
    getImgData(data) {
      this.overheadLine.imgList = data.imgList;
    },
    /**
     * 获取备注信息
     * @param data
     */
    getMsgData(data) {
      this.overheadLine.message = data.message;
    },
    /**
     * 架空电缆
     */
    onConfirmJkdlSel(type, index, item) {
      const val = item.value;
      switch (type) {
        case 0:
          this.overheadLine.voltage = val;
          // 220v和330v都按照0.4kv去查
          const valltege = val === "10kV" ? "10kV" : "0.4kV";
          // 查询杆塔类型
          this.getFirstTowerOrLineType(0, 1, 1, "", "", "", "", valltege);
          this.getFirstTowerOrLineType(1, 4, 1, "", "", "", "", valltege);
          this.settingObj.overheadLine.voltage = false;
          break;
        case 1:
          this.overheadLine.backLine = val;
          let length = 0;
          if (val === "单回") {
            length = 1;
          } else if (val === "双回") {
            length = 2;
          } else if (val === "三回") {
            length = 3;
          } else {
            length = 4;
          }
          this.overheadLine.listArr = [];
          const stateText = this.remodeState ? "新建" : "原有";
          this.settingObj.overheadLine.listArr = [];
          for (var i = 0; i < length; i++) {
            const item_one = {
              lineName: "线路" + (Number(i) + 1), // 线路名称
              lineState: stateText, // 线路状态
              lineModel: this.jkdldxxhType[0].name, // 导线型号
              lineModelId: this.jkdldxxhType[0].id, // 导线id
              voltage: this.overheadLine.voltage,
            };
            const isShow_one = {
              lineStateVis: false, // 主线路状态
              lineModelVis: false, // 主线路导线型号
            };
            this.overheadLine.listArr.push(item_one);
            this.settingObj.overheadLine.listArr.push(isShow_one);
          }
          this.settingObj.overheadLine.backLine = false;
          break;
        case 2:
          this.overheadLine.towerModel = item.name;
          this.overheadLine.towerModelId = item.id;
          this.settingObj.overheadLine.towerModel = false;
          break;
        case 3:
          this.overheadLine.drawSet = val;
          if (val === "选择现有杆") {
            this.settingObj.isShowDrawRaw = false;
            this.overheadLine.towerModel = "";
            this.overheadLine.towerModelId = "";
            this.overheadLine.towerSbModel = "";
          } else {
            this.settingObj.isShowDrawRaw = true;
            this.overheadLine.towerModel = this.jkdltowerModel[0].name;
            this.overheadLine.towerModelId = this.jkdltowerModel[0].id;
            this.overheadLine.towerSbModel = this.towerSbModel[0].key;
          }
          this.settingObj.overheadLine.drawSet = false;
          break;
        case 4:
          this.overheadLine.listArr[index].lineState = val;
          this.settingObj.overheadLine.listArr[index].lineStateVis = false;
          break;
        case 5:
          this.overheadLine.listArr[index].lineModel = item.name;
          this.overheadLine.listArr[index].lineModelId = item.id;
          this.settingObj.overheadLine.listArr[index].lineModelVis = false;
          break;
        case 6:
          this.overheadLine.state = val;
          this.settingObj.overheadLine.state = false;
          break;
      }
    },
    submitData() {
      const parma = {
        type: 7,
        param: this.overheadLine,
        visParam: this.settingObj.overheadLine,
      };
      this.$emit("submitChildData", parma);
    },
    showEveryItemSet() {
      this.isFoldArea = !this.isFoldArea;
    },
    backCurrentDom() {
      this.$emit("backCurrentDom");
      this.jkdltowerModel = this.gtList;
      this.overheadLine.towerModel = this.gtList[0].name;
      this.overheadLine.towerModelId = this.gtList[0].id;
      this.jkdldxxhType = this.dlxlList;
      this.overheadLine.listArr[0].lineModel = this.dlxlList[0].name;
      this.overheadLine.listArr[0].lineModelId = this.dlxlList[0].id;
      // 架空线路
      const stateText = this.remodeState ? "新建" : "原有";
      this.overheadLine.state = stateText;
      this.overheadLine.voltage = "10kV";
      this.overheadLine.backLine = "单回";
      this.overheadLine.towerModel = "";
      this.overheadLine.towerModelType = "";
      this.overheadLine.towerModelId = "";
      this.overheadLine.drawSet = "选择现有杆";
      this.overheadLine.towerSbModel = "不带杆上设备";
      this.overheadLine.imgList = [];
      this.overheadLine.message = "";
      this.overheadLine.audioList = [];
      this.overheadLine.listArr = [
        {
          lineName: "线路一", // 线路名称
          lineState: stateText, // 线路状态
          lineModel: "", // 线路型号
          lineModelId: "", // 线路型号id
        },
      ];
      this.settingObj.overheadLine.voltage = false;
      this.settingObj.overheadLine.backLine = false;
      this.settingObj.overheadLine.towerModel = false;
      this.settingObj.overheadLine.drawSet = false;
      this.settingObj.overheadLine.state = false;
      this.settingObj.overheadLine.listArr = [
        {
          lineStateVis: false, // 线路状态
          lineModelVis: false, // 线路型号
        },
      ];
    },
    /**
     * 第一次初始化得时候查询各种型号赋默认值
     * 这里是为了减少修改方法这么处理了
     * @params settype 请求的具体某个模块的类型
     * @params type 请求的来自于基础参数设置当中的四类的类型 1: 水泥杆 2: 导线 3: 拉线 4: 电缆
     * @params moduleType 请求的不来自于基础参数当中的数据类型 打点类型（1：地图页查询用户参数设置、打点时选择水泥杆、导线、拉线、电缆
     2：绘制光缆3：柱上变压器方案类别、柱上设备类型、通道类别、通道类别、电缆井类型4：下户线型号、户表型号、柱上变压器方案、柱上设备型号、箱变方案类别、站房型号、环网箱方案类别、站房型号、环网室方案类别、开关站方案类别、配电室方案类别、通道信号、终端头型号、中间头型号、管材型号、电缆分支箱、电缆井型号）
     * @params materialsTypeKey 物料key
     * @params moduleTypeKey  模块key
     * @params parentKey 父类Key
     * @params moduleCode 模块code
     * @params moduleName 模块名称
     * @params voltage 请求的模块电压
     */
    getFirstTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          switch (settype) {
            case 0:
              if (voltage === "10kV") {
                // 架空电缆杆塔型号
                that.jkdltowerModel = res.data.tenSNG;
                that.overheadLine.towerModel = res.data.tenSNG[0].name;
                that.overheadLine.towerModelId = res.data.tenSNG[0].id;
              } else {
                // 架空电缆杆塔型号
                that.jkdltowerModel = res.data.lowSNG;
                that.overheadLine.towerModel = res.data.lowSNG[0].name;
                that.overheadLine.towerModelId = res.data.lowSNG[0].id;
              }
              break;
            case 1:
              if (voltage === "10kV") {
                // 架空电缆电缆型号
                that.jkdldxxhType = res.data.tenDl;
                that.overheadLine.listArr[0].lineModel = res.data.tenDl[0].name;
                that.overheadLine.listArr[0].lineModelId = res.data.tenDl[0].id;
              } else {
                // 架空电缆电缆型号
                that.jkdldxxhType = res.data.lowDl;
                that.overheadLine.listArr[0].lineModel = res.data.lowDl[0].name;
                that.overheadLine.listArr[0].lineModelId = res.data.lowDl[0].id;
              }
              break;
          }
        }
      });
    },
    getAwaitTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName,
      selectVal,
      selectLevelTwoVal
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          // 架空电缆电缆型号
          switch (settype) {
            case 0:
              // 架空电缆杆塔型号
              if (voltage === "10kV") {
                that.jkdltowerModel = res.data.tenSNG;
              } else {
                that.jkdltowerModel = res.data.lowSNG;
              }
              break;
            case 1:
              // 架空电缆电缆型号
              if (voltage === "10kV") {
                that.jkdldxxhType = res.data.tenDl;
              } else {
                that.jkdldxxhType = res.data.lowDl;
              }
              break;
          }
        }
      });
    },
  },
};
</script>

<style lang="sass" scoped>
</style>

