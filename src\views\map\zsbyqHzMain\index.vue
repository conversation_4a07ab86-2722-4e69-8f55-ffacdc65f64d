<template>
  <div>
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div v-if="showBackAdds" class="maps-zhedNav" @click="backCurrentDom">
            <img
              class="mapites-backImg"
              :src="require('@/assets/' + 'map/settingImg/backAdds.png')"
              alt=""
            />
          </div>
          <span />
          柱上变台绘制
          <div
            v-if="!showBackAdds"
            class="maps-zhedNav"
            @click="showEveryItemSet"
          >
            <img
              v-show="!isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zhedie.png')"
              alt=""
            />
            <img
              v-show="isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zkzhedie.png')"
              alt=""
            />
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img
            class="settingImg"
            :src="require('@/assets/' + 'map/settingImg/useSetting.png')"
            alt=""
          />
          <p>启用</p>
        </div>
      </div>
      <div v-show="isFoldArea" class="pro-addForm">
        <div class="map-showNav">基本信息</div>
        <van-row>
          <van-field
            v-model="transformer.mark"
            label="编号"
            placeholder="请输入柱上变压器编号"
          />
        </van-row>
        <div class="map-showNav">选型信息</div>
        <van-row>
          <!--柱上变压器状态-->
          <van-field
            readonly
            clickable
            :value="transformer.state"
            label="状态"
            placeholder="请选择状态"
            @click="settingObj.transformer.state = true"
          />
          <van-popup
            v-model="settingObj.transformer.state"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="状态"
              value-key="key"
              :columns="mainLineState"
              @confirm="onConfirmZsbySel(0, $event)"
              @cancel="settingObj.transformer.state = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--柱上变压器是否成套变化-->
          <van-field
            readonly
            clickable
            :value="transformer.wholeSet"
            label="是否成套化"
            placeholder="请选择成套变化"
            @click="settingObj.transformer.wholeSet = true"
          />
          <van-popup
            v-model="settingObj.transformer.wholeSet"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="成套化"
              value-key="key"
              :columns="trueOrFalse"
              @confirm="onConfirmZsbySel(3, $event)"
              @cancel="settingObj.transformer.wholeSet = false"
            />
          </van-popup>
        </van-row>
        <!--柱上变压器柱上变台方案类别-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="transformer.planType"
            label="柱上变台方案类别"
            placeholder="柱上变台方案类别"
            @click="settingObj.transformer.planType = true"
          />
          <van-popup
            v-model="settingObj.transformer.planType"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="柱上变台方案类别"
              value-key="moduletypename"
              :columns="planType"
              @confirm="onConfirmZsbySel(1, $event)"
              @cancel="settingObj.transformer.planType = false"
            />
          </van-popup>
        </van-row>
        <!--柱上变压器柱上变台方案-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="transformer.plane"
            label="柱上变台方案"
            placeholder="请选择柱上变台方案"
            @click="settingObj.transformer.plane = true"
          />
          <van-popup
            v-model="settingObj.transformer.plane"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="柱上变台方案"
              value-key="moduleName"
              :columns="transformerPlan"
              @confirm="onConfirmZsbySel(2, $event)"
              @cancel="settingObj.transformer.plane = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--柱上变压器变台台区-->
          <van-field
            v-model="transformer.courts"
            label="变台台区"
            placeholder="请输入变台台区"
          />
        </van-row>
        <van-row>
          <!--柱上变压器变台编号-->
          <van-field
            v-model="transformer.courtsNum"
            label="变台编号"
            placeholder="请输入变台编号"
          />
        </van-row>
        <!--柱上变压器是否接地-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="transformer.isGround"
            label="是否接地"
            placeholder="请选择接地状态"
            @click="settingObj.transformer.isGround = true"
          />
          <van-popup
            v-model="settingObj.transformer.isGround"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="接地状态"
              value-key="key"
              :columns="trueOrFalse"
              @confirm="onConfirmZsbySel(4, $event)"
              @cancel="settingObj.transformer.isGround = false"
            />
          </van-popup>
        </van-row>
        <div class="map-showNav">副杆基本信息</div>
        <van-row>
          <van-field
            v-model="mainLine.mark"
            label="编号"
            placeholder="请输入杆塔编号"
          />
        </van-row>
        <div class="map-showNav">副杆选型信息</div>
        <van-row>
          <van-field
            readonly
            clickable
            :value="mainLine.voltage"
            label="电压等级"
            placeholder="选择电压等级"
            @click="settingObj.mainLine.voltageVis = true"
          />
          <van-popup
            v-model="settingObj.mainLine.voltageVis"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="电压等级"
              value-key="key"
              :columns="allVoltage"
              @confirm="onConfirmSelect(0, '', $event)"
              @cancel="settingObj.mainLine.voltageVis = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--主线路回路数-->
          <van-field
            readonly
            clickable
            :value="mainLine.backLine"
            label="回路数"
            placeholder="选择回路数"
            @click="settingObj.mainLine.backLineVis = true"
          />
          <van-popup
            v-model="settingObj.mainLine.backLineVis"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="回路数"
              value-key="key"
              :columns="backLine"
              @confirm="onConfirmSelect(1, '', $event)"
              @cancel="settingObj.mainLine.backLineVis = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--主线路状态-->
          <van-field
            readonly
            clickable
            :value="mainLine.state"
            label="状态"
            placeholder="选择状态"
            @click="settingObj.mainLine.stateVis = true"
          />
          <van-popup
            v-model="settingObj.mainLine.stateVis"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="主线路状态"
              value-key="key"
              :columns="mainLineState"
              @confirm="onConfirmSelect(2, '', $event)"
              @cancel="settingObj.mainLine.stateVis = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--主线路杆塔型号-->
          <van-field
            readonly
            clickable
            :value="mainLine.towerType"
            label="杆塔类型"
            placeholder="选择杆塔类型"
            @click="settingObj.mainLine.towerModelVis = true"
          />
          <van-popup
            v-model="settingObj.mainLine.towerModelVis"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="杆塔类型"
              value-key="key"
              :columns="towerType"
              @confirm="onConfirmSelect(3, '', $event)"
              @cancel="settingObj.mainLine.towerModelVis = false"
            />
          </van-popup>
        </van-row>
        <van-row v-if="settingObj.mainLine.isShowtowerGGG">
          <van-field
            readonly
            clickable
            :value="mainLine.gggHeight"
            label="杆高"
            placeholder="选择杆高"
            @click="settingObj.mainLine.towerGGGVis = true"
          />
          <van-popup
            v-model="settingObj.mainLine.towerGGGVis"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="杆高"
              value-key="key"
              :columns="towerGGGGtHeight"
              @confirm="onConfirmSelect(8, '', $event)"
              @cancel="settingObj.mainLine.towerGGGVis = false"
            />
          </van-popup>
        </van-row>
        <van-row v-if="settingObj.mainLine.isShowtowerGGG">
          <van-field
            readonly
            clickable
            :value="mainLine.gggSj"
            label="稍经"
            placeholder="选择稍经"
            @click="settingObj.mainLine.towerSjVis = true"
          />
          <van-popup
            v-model="settingObj.mainLine.towerSjVis"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="稍经"
              value-key="key"
              :columns="towerSJHeight"
              @confirm="onConfirmSelect(9, '', $event)"
              @cancel="settingObj.mainLine.towerSjVis = false"
            />
          </van-popup>
        </van-row>
        <van-row v-if="settingObj.mainLine.isShowtowerZJT">
          <van-field
            readonly
            clickable
            :value="mainLine.zjtTg"
            label="塔高"
            placeholder="选择塔高"
            @click="settingObj.mainLine.towerZJTVis = true"
          />
          <van-popup
            v-model="settingObj.mainLine.towerZJTVis"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="塔高"
              value-key="key"
              :columns="towerSJHeight"
              @confirm="onConfirmSelect(10, '', $event)"
              @cancel="settingObj.mainLine.towerGGGVis = false"
            />
          </van-popup>
        </van-row>
        <!--主线路杆塔型号-->
        <van-row v-if="settingObj.mainLine.isShowtowerMoudle">
          <van-field
            readonly
            clickable
            :value="mainLine.towerModel"
            label="杆塔型号"
            placeholder="选择杆塔型号"
            @click="settingObj.mainLine.towerTypeVis = true"
          />
          <van-popup
            v-model="settingObj.mainLine.towerTypeVis"
            round
            position="bottom"
          >
            <van-picker
              title="杆塔型号"
              show-toolbar
              value-key="name"
              :columns="towerModel"
              @confirm="onConfirmSelect(4, '', $event)"
              @cancel="settingObj.mainLine.towerTypeVis = false"
            />
          </van-popup>
        </van-row>
        <!--主线路杆塔设备类型-->
        <van-row v-if="settingObj.mainLine.isShowtowerMoudle">
          <van-field
            readonly
            clickable
            :value="mainLine.towerSbModel"
            label="设备类型"
            placeholder="选择设备类型"
            @click="settingObj.mainLine.towerSbModel = true"
          />
          <van-popup
            v-model="settingObj.mainLine.towerSbModel"
            round
            position="bottom"
          >
            <van-picker
              title="设备类型"
              show-toolbar
              value-key="key"
              :columns="towerSbModel"
              @confirm="onConfirmSelect(11, '', $event)"
              @cancel="settingObj.mainLine.towerSbModel = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <div :style="{ width: '100%', overflowX: 'scroll' }">
            <!--主线路-->
            <table
              border="1"
              class="map-tables"
              cellspacing="0"
              cellpadding="0"
              align="center"
            >
              <tr>
                <th :style="{ width: '7rem' }">线路名称</th>
                <th :style="{ width: '7rem' }">状态</th>
                <th :style="{ width: '14rem' }">导线型号</th>
                <th>操作</th>
              </tr>
              <tr v-for="(item, index) in mainLine.listArr">
                <td>
                  <van-field
                    v-model="item.lineName"
                    placeholder="请输入线路名称"
                  />
                </td>
                <td>
                  <van-field
                    readonly
                    clickable
                    :value="item.lineState"
                    class="pickerSelect"
                    placeholder="线路状态"
                    @click="
                      settingObj.mainLine.listArr[index].lineStateVis = true
                    "
                  />
                  <van-popup
                    v-model="settingObj.mainLine.listArr[index].lineStateVis"
                    round
                    position="bottom"
                  >
                    <van-picker
                      title="线路状态"
                      show-toolbar
                      value-key="key"
                      :columns="cableLineState"
                      @confirm="onConfirmSelect(5, index, $event)"
                      @cancel="
                        settingObj.mainLine.listArr[index].lineStateVis = false
                      "
                    />
                  </van-popup>
                </td>
                <td>
                  <van-field
                    readonly
                    clickable
                    :value="item.lineModel"
                    placeholder="选导线型号"
                    @click="
                      settingObj.mainLine.listArr[index].lineModelVis = true
                    "
                  />
                  <van-popup
                    v-model="settingObj.mainLine.listArr[index].lineModelVis"
                    round
                    position="bottom"
                  >
                    <van-picker
                      title="导线型号"
                      show-toolbar
                      value-key="name"
                      :columns="lineType"
                      @confirm="onConfirmSelect(6, index, $event)"
                      @cancel="
                        settingObj.mainLine.listArr[index].lineModelVis = false
                      "
                    />
                  </van-popup>
                </td>
                <td :style="{ width: '4rem', textAlign: 'center' }">
                  <span :style="{ color: 'red' }" @click="removeMainLine(index)"
                    >删除</span
                  >
                </td>
              </tr>
            </table>
          </div>
        </van-row>
        <!--基本信息 2022.7.27新增-->
        <div v-if="isShowNav" class="map-showNav">其他信息</div>
        <van-row v-if="isShowNav">
          <!--经度-->
          <van-field v-model="lngtitude" label="经度" disabled />
        </van-row>
        <van-row v-if="isShowNav">
          <!--纬度-->
          <van-field v-model="lattitude" label="纬度" disabled />
        </van-row>
        <van-row v-if="isShowNav">
          <!--高程-->
          <van-field v-model="highNum" label="高程" disabled />
        </van-row>
        <div v-if="isShowNav" class="map-showNav">其他信息</div>
        <van-row v-if="isShowNav">
          <!--经度-->
          <van-field v-model="lngtitude" label="经度" disabled />
        </van-row>
        <van-row v-if="isShowNav">
          <!--纬度-->
          <van-field v-model="lattitude" label="纬度" disabled />
        </van-row>
        <van-row v-if="isShowNav">
          <!--高程-->
          <van-field v-model="highNum" label="高程" disabled />
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { apiget,apipost } from "@/utils/mapRequest";
export default {
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false,
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false,
    },
    // 杆塔型号默认值
    gtList: {
      type: Array,
      defaults: () => [],
    },
    // 导线型号默认值
    dxList: {
      type: Array,
      defaults: () => [],
    },
    // 编辑的id
    editData: {
      type: Object,
      defaults: () => {},
    },
    remodeState: {
      type: Boolean,
      defaults: false,
    },
  },
  data() {
    return {
      isFoldArea: false, // 是否展开区域
      lngtitude: "",
      lattitude: "",
      highNum: "",
      lineType: [], // 导线型号
      cableLineState: [], // 电缆线路状态
      lineState: [
        {
          key: "新建",
          value: "新建",
        },
        {
          key: "原有",
          value: "原有",
        },
        {
          key: "拆除",
          value: "拆除",
        },
      ],
      backLine: [
        {
          key: "单回",
          value: "单回",
        },
        {
          key: "双回",
          value: "双回",
        },
        {
          key: "三回",
          value: "三回",
        },
        {
          key: "四回",
          value: "四回",
        },
      ], // 回路
      towerModel: [], // 杆塔型号
      towerType: [
        {
          key: "水泥杆",
          value: "水泥杆",
          type: "SNG",
        },
        {
          key: "水泥双杆",
          value: "水泥双杆",
          type: "SNSG",
        },
        {
          key: "钢管杆",
          value: "钢管杆",
          type: "GGG",
        },
        {
          key: "窄基塔",
          value: "窄基塔",
          type: "ZJT",
        },
        {
          key: "低压水泥杆",
          value: "低压水泥杆",
          type: "DYSNG",
        },
      ], // 杆塔类型
      allVoltage: [
        {
          key: "10kV",
          value: "10kV",
        },
        {
          key: "380V",
          value: "380V",
        },
        {
          key: "220V",
          value: "220V",
        },
      ], // 电压等级
      mainLineState: [], // 电缆线路状态
      gtState: [
        {
          key: "新建",
          value: "新建",
        },
        {
          key: "原有",
          value: "原有",
        },
        {
          key: "改造",
          value: "改造",
        },
        {
          key: "拆除",
          value: "拆除",
        },
      ],
      towerGGGGtHeight: [
        {
          key: "10",
          value: "10",
        },
        {
          key: "13",
          value: "13",
        },
        {
          key: "16",
          value: "16",
        },
      ], // 钢管杆杆高
      towerSJHeight: [
        {
          key: "310",
          value: "310",
        },
        {
          key: "350",
          value: "350",
        },
        {
          key: "390",
          value: "390",
        },
      ], // 钢管杆稍经
      towerSbModel: [
        {
          key: "不带杆上设备",
          value: "不带杆上设备",
        },
        {
          key: "带单侧隔离刀闸",
          value: "带单侧隔离刀闸",
        },
        {
          key: "带单侧隔离刀闸、断路器",
          value: "带单侧隔离刀闸、断路器",
        },
        {
          key: "带单侧隔离刀闸、断路器、关口计量",
          value: "带单侧隔离刀闸、断路器、关口计量",
        },
      ], // 杆塔设备类型
      settingObj: {
        // 柱上变压器
        transformer: {
          state: false, // 状态
          planType: false, // 方案类别
          plane: false, // 变台方案
          wholeSet: false, // 是否成套变化
          isGround: false, // 是否接地
        },
        // 主线路
        mainLine: {
          voltageVis: false, // 电压等级
          backLineVis: false, // 回路数
          stateVis: false, // 状态
          towerTypeVis: false, // 杆塔类型
          towerModelVis: false, // 主杆塔型号
          towerSbModel: false, // 设备类型
          towerGGGVis: false, // 钢管杆高度
          towerSjVis: false, // 钢管杆稍经
          towerZJTVis: false, // 窄基塔塔高
          isShowtowerGGG: false, // 是否展示主杆塔钢管杆的杆高和稍经
          isShowtowerZJT: false, // 是否展示主杆塔窄基塔的塔高
          isShowtowerMoudle: true, // 是否展示主杆塔窄基塔的塔高
          listArr: [
            {
              lineStateVis: false, // 主线路状态
              lineModelVis: false, // 主线路导线型号600
            },
          ],
        },
      },
      // 柱上变压器
      transformer: {
        state: "新建", // 状态
        planType: "", // 方案类别
        planTypeId: "", // 方案类别id
        plane: "", // 变台方案
        planeId: "", // 变台方案
        wholeSet: "是", // 是否成套变化
        courts: "1", // 变台台区
        courtsNum: "1", // 变台编号
        isGround: "是", // 是否接地
        imgList: [], // 文件列表
        message: "", // 备注信息
        audioList: [], // 语音列表
      },
      // 副杆线路
      mainLine: {
        voltage: "10kV", // 电压等级
        mark: "",
        backLine: "单回", // 回路数
        state: "新建", // 状态
        towerType: "水泥杆", // 杆塔类型
        towerModel: "不带杆上设备", // 导线型号
        towerTypeId: "", // 导线类型id
        towerModelId: "", // 杆塔type值
        towerSbModel: "不带杆上设备", // 设备类型
        imgList: [], // 文件列表
        message: "", // 备注信息
        audioList: [], // 语音列表
        gggHeight: "", // 钢管杆高度
        gggSj: "", // 钢管杆稍经
        zjtTg: "", // 窄基塔塔高
        listArr: [
          {
            lineName: "线路1", // 线路名称
            lineState: "新建", // 线路状态
            lineModel: "", // 导线型号
            lineModelId: "", // 导线id
            projectId: this.$route.query.childProjectId, // 工程id
          },
        ],
      },
      trueOrFalse: [
        {
          key: "是",
          value: "是",
        },
        {
          key: "否",
          value: "否",
        },
      ], // 是否
      planType: [], // 柱上变台方案类别 这里从后台读
      transformerPlan: [],
    };
  },
  watch: {
    // 监听改前改后状态
    remodeState: {
      handler(newVal) {
        if (newVal) {
          // 改后不显示拆除的数据
          this.cableLineState = this.lineState.slice(0, 2);
          this.mainLineState = this.gtState.slice(0, 3);
          this.transformer.state = "新建";
          this.mainLine.state = "新建";
          for (const j in this.mainLine.listArr) {
            this.mainLine.listArr[j].lineState = "新建";
          }
        } else {
          // 改前不显示新建的数据
          this.cableLineState = this.lineState.slice(1);
          this.mainLineState = this.gtState.slice(1);
          this.transformer.state = "原有";
          this.mainLine.state = "原有";
          for (const j in this.mainLine.listArr) {
            this.mainLine.listArr[j].lineState = "原有";
          }
        }
      },
      deep: true,
      immediate: true,
    },
    gtList: {
      handler(newVal) {
        // 这块取父页面传过来的数据 因为存在多个模块请求一样的数据 避免模块间重复请求接口获取数据
        this.towerModel = newVal;
        this.mainLine.towerModel = newVal[0].name;
        this.mainLine.towerModelId = newVal[0].id;
      },
      deep: true,
    },
    dxList: {
      handler(newVal) {
        // 这块取父页面传过来的数据 因为存在多个模块请求一样的数据 避免模块间重复请求接口获取数据
        this.lineType = newVal;
        this.mainLine.listArr[0].lineModel = newVal[0].name;
        this.mainLine.listArr[0].lineModelId = newVal[0].id;
      },
      deep: true,
    },
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal;
      },
      deep: true,
      immediate: true,
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal;
        if (data.moudleType === "ZSBYQHZ") {
          const valatege = data.voltage === "10kV" ? "10kV" : "0.4kV";
          this.lngtitude = data.longitude;
          this.lattitude = data.latitude;
          this.highNum = data.high;
          this.transformer.imgList = [];
          this.transformer.audioList = [];
          this.transformer.state = data.state;
          this.transformer.planTypeId = data.zsbtProgramType;
          this.transformer.planType = data.zsbtProgramTypeSpec;
          this.transformer.planeId = data.zsbtProgram;
          this.transformer.plane = data.zsbtProgramSpec;
          this.transformer.wholeSet = data.isCht === "1" ? "是" : "否";
          this.transformer.courts = data.btTq;
          this.transformer.courtsNum = data.btNum;
          this.transformer.isGround = data.isJd === "1" ? "是" : "否";
          this.transformer.mark = data.mark; // 编号
          this.transformer.message = data.note;
          // 副杆数据回显
          this.getAwaitTowerOrLineType(0, 1, 1, "", "", "", "", valatege);
          // 查询导线型号
          this.getAwaitTowerOrLineType(1, 2, 1, "", "", "", "", valatege);
          this.settingObj.mainLine.listArr = [];
          this.mainLine.voltage = data.voltage; // 电压
          this.mainLine.backLine = data.loopNum; // 回路数
          this.mainLine.state = data.state; // 状态
          this.mainLine.towerModelId = data.moduleId;
          this.mainLine.towerType = data.legendTypeKey;
          this.mainLine.towerModel = data.pointModule;
          this.mainLine.mark = data.mark; // 编号
          this.mainLine.towerSbModel = data.equipType; // 设备类型
          this.mainLine.message = data.note;
          for (const k in data.imgList) {
            const objs = {
              url: data.imgList[k].path,
              isImage: true,
              isSaveReport: data.imgList[k].isSaveReport,
            };
            this.transformer.imgList.push(objs);
          }
          for (const s in data.voiceList) {
            const objs = {
              content: data.voiceList[s].path,
            };
            this.transformer.audioList.push(objs);
          }
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.getFirstTowerOrLineType(0, "", 3, "", "", "SGZSB", "", "");
  },
  methods: {
    /**
     * 提交数据
     */
    submitData() {
      const parma = {
        type: 8,
        param: this.transformer,
        visParam: this.settingObj.transformer,
      };
      this.$emit("submitChildData", parma);
    },
    backCurrentDom() {
      this.$emit("backCurrentDom");
      this.getFirstTowerOrLineType(0, "", 3, "", "", "SGZSB", "", "");
      // 柱上变压器
      const stateText = this.remodeState ? "新建" : "原有";
      this.transformer.state = stateText;
      this.transformer.planType = this.planType[0].moduletypename;
      this.transformer.planTypeId = this.planType[0].moduletypeid;
      this.transformer.plane = this.transformerPlan[0].moduleName;
      this.transformer.planeId = this.transformerPlan[0].moduleID;
      this.transformer.wholeSet = "是";
      this.transformer.courts = "1";
      this.transformer.courtsNum = "1";
      this.transformer.isGround = "是";
      this.transformer.imgList = [];
      this.transformer.message = "";
      this.transformer.audioList = [];
      this.mainLine.voltage = "10kV"; // 电压
      this.mainLine.backLine = "单回"; // 回路数
      this.mainLine.state = stateText; // 状态
      this.mainLine.towerModelId = this.towerModel[0].id;
      this.mainLine.towerType = "水泥杆";
      this.mainLine.towerModel = this.towerModel[0].name;
      this.mainLine.mark = ""; // 编号
      this.mainLine.towerSbModel = "towerSbModel"; // 设备类型
      this.mainLine.message = "";
      this.settingObj.transformer.state = false;
      this.settingObj.transformer.planType = false;
      this.settingObj.transformer.plane = false;
      this.settingObj.transformer.wholeSet = false;
      this.settingObj.transformer.isGround = false;
    },
    showEveryItemSet() {
      this.isFoldArea = !this.isFoldArea;
    },
    getFirstTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          switch (settype) {
            case 0:
              // 柱上变台方案类别
              that.planType = res.data;
              that.transformer.planType = res.data[0].moduletypename;
              that.transformer.planTypeId = res.data[0].moduletypeid;
              /* 柱上变台方案*/
              that.getFirstTowerOrLineType(
                1,
                "",
                4,
                "",
                res.data[0].moduletypekey,
                "",
                "",
                ""
              );
              break;
            case 1:
              // 柱上变台方案
              that.transformer.plane = res.data[0].moduleName;
              that.transformer.planeId = res.data[0].moduleID;
              that.transformerPlan = res.data;
              break;
            case 2:
              if (voltage === "10kV") {
                // 主线路杆塔型号
                that.towerModel = res.data.tenSNG;
                that.mainLine.towerModel = res.data.tenSNG[0].name;
                that.mainLine.towerModelId = res.data.tenSNG[0].id;
              } else {
                // 主线路杆塔型号
                that.towerModel = res.data.lowSNG;
                that.mainLine.towerModel = res.data.lowSNG[0].name;
                that.mainLine.towerModelId = res.data.lowSNG[0].id;
              }
              break;
            case 3:
              if (voltage === "10kV") {
                // 主线路导线型号
                that.lineType = res.data.tenDx;
                that.mainLine.listArr[0].lineModel = res.data.tenDx[0].name;
                that.mainLine.listArr[0].lineModelId = res.data.tenDx[0].id;
              } else {
                // 主线路导线型号
                that.lineType = res.data.lowDx;
                that.mainLine.listArr[0].lineModel = res.data.lowDx[0].name;
                that.mainLine.listArr[0].lineModelId = res.data.lowDx[0].id;
              }
              break;
          }
        }
      });
    },
    /**
     * 主线路单选框的添加
     */
    onConfirmSelect(type, index, item) {
      const val = item.value;
      switch (type) {
        case 0:
          this.mainLine.voltage = val;
          for (const j in this.mainLine.listArr) {
            this.mainLine.listArr[j].voltage = val;
          }
          this.mainLine.towerModel = "";
          this.mainLine.towerModelId = "";
          // 220v和330v都按照0.4kv去查
          const valltege = val === "10kV" ? "10kV" : "0.4kV";
          // 查询杆塔类型
          this.getFirstTowerOrLineType(2, 1, 1, "", "", "", "", valltege);
          // 查询电缆线路类型
          this.getFirstTowerOrLineType(3, 2, 1, "", "", "", "", valltege);
          this.settingObj.mainLine.voltageVis = false;
          break;
        case 1:
          this.mainLine.backLine = val;
          // eslint-disable-next-line no-case-declarations
          let length = 0;
          if (val === "单回") {
            length = 1;
          } else if (val === "双回") {
            length = 2;
          } else if (val === "三回") {
            length = 3;
          } else {
            length = 4;
          }
          this.mainLine.listArr = [];
          this.settingObj.mainLine.listArr = [];
          const stateText = this.remodeState ? "新建" : "原有";
          for (var i = 0; i < length; i++) {
            const item_one = {
              lineName: "线路" + (Number(i) + 1), // 线路名称
              lineState: stateText, // 线路状态
              lineModel: this.lineType[0].name, // 导线型号
              lineModelId: this.lineType[0].id, // 导线id
              voltage: this.mainLine.voltage,
              projectId: this.$route.query.childProjectId, // 工程id
            };
            const isShow_one = {
              lineStateVis: false, // 主线路状态
              lineModelVis: false, // 主线路导线型号
            };
            this.mainLine.listArr.push(item_one);
            this.settingObj.mainLine.listArr.push(isShow_one);
          }
          this.settingObj.mainLine.backLineVis = false;
          break;
        case 2:
          this.mainLine.state = val;
          this.settingObj.mainLine.stateVis = false;
          break;
        case 3:
          const vallteges = this.mainLine.voltage === "10kV" ? "10kV" : "0.4kV";
          this.mainLine.towerType = val;
          this.mainLine.towerTypeId = item.type;
          this.settingObj.mainLine.towerModelVis = false;
          this.mainLine.zjtTg = "";
          this.mainLine.gggHeight = "";
          this.mainLine.gggSj = "";
          switch (val) {
            // 钢管杆展示杆高和稍经 窄基塔显示塔高 其余类型不展示
            case "钢管杆":
              this.settingObj.mainLine.isShowtowerGGG = true;
              this.settingObj.mainLine.isShowtowerZJT = false;
              this.settingObj.mainLine.isShowtowerMoudle = false;
              this.mainLine.towerModel = "";
              this.mainLine.towerModelId = "";
              this.mainLine.towerSbModel = "";
              break;
            case "窄基塔":
              this.settingObj.mainLine.isShowtowerGGG = false;
              this.settingObj.mainLine.isShowtowerZJT = true;
              this.settingObj.mainLine.isShowtowerMoudle = false;
              this.mainLine.towerModel = "";
              this.mainLine.towerSbModel = "";
              break;
            case "水泥杆":
            case "水泥双杆":
            case "低压水泥杆":
              // 查询杆塔类型
              this.getFirstTowerOrLineType(2, 1, 1, "", "", "", "", vallteges);
              this.settingObj.mainLine.isShowtowerGGG = false;
              this.settingObj.mainLine.isShowtowerZJT = false;
              this.settingObj.mainLine.isShowtowerMoudle = true;
              this.mainLine.towerModel = this.towerModel[0].name;
              this.mainLine.towerModelId = this.towerModel[0].id;
              this.mainLine.towerSbModel = this.towerSbModel[0].key;
              this.mainLine.gggHeight = "";
              this.mainLine.zjtTg = "";
              this.mainLine.zjtTg = "";
              break;
          }
          break;
        case 4:
          this.mainLine.towerModel = item.name;
          this.mainLine.towerModelId = item.id;
          this.settingObj.mainLine.towerTypeVis = false;
          break;
        case 5:
          this.mainLine.listArr[index].lineState = val;
          this.settingObj.mainLine.listArr[index].lineStateVis = false;
          break;
        case 6:
          this.mainLine.listArr[index].lineModel = item.name;
          this.mainLine.listArr[index].lineModelId = item.id;
          this.settingObj.mainLine.listArr[index].lineModelVis = false;
          break;
        case 8:
          this.mainLine.gggHeight = val;
          this.settingObj.mainLine.towerGGGVis = false;
          break;
        case 9:
          this.mainLine.gggSj = val;
          this.settingObj.mainLine.towerSjVis = false;
          break;
        case 10:
          this.mainLine.zjtTg = val;
          this.settingObj.mainLine.towerZJTVis = false;
          break;
        case 11:
          this.mainLine.towerSbModel = val;
          this.settingObj.mainLine.towerSbModel = false;
          break;
      }
    },
    getAwaitTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName,
      selectVal,
      selectLevelTwoVal
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          switch (settype) {
            case 0:
              // 主线路杆塔型号
              if (voltage === "10kV") {
                that.towerModel = res.data.tenSNG;
              } else {
                that.towerModel = res.data.lowSNG;
              }
              break;
            case 1:
              // 主线路导线型号
              if (voltage === "10kV") {
                that.lineType = res.data.tenDx;
              } else {
                that.lineType = res.data.lowDx;
              }
              break;
          }
        }
      });
    },
    /**
     * 柱上变台方案类别
     */
    onConfirmZsbySel(type, item) {
      const val = item.value;
      switch (type) {
        case 0:
          this.transformer.state = val;
          this.settingObj.transformer.state = false;
          break;
        case 1:
          this.transformer.planType = item.moduletypename;
          this.transformer.planTypeId = item.moduletypeid;
          this.getFirstTowerOrLineType(
            1,
            "",
            4,
            "",
            item.moduletypekey,
            "",
            "",
            ""
          );
          this.settingObj.transformer.planType = false;
          break;
        case 2:
          this.transformer.plane = item.moduleName;
          this.transformer.planeId = item.moduleID;
          this.settingObj.transformer.plane = false;
          break;
        case 3:
          this.transformer.wholeSet = val;
          this.settingObj.transformer.wholeSet = false;
          break;
        case 4:
          this.transformer.isGround = val;
          this.settingObj.transformer.isGround = false;
          break;
      }
    },
    /**
     * 获取语音数据
     */
    getAudioData(data) {
      this.transformer.audioList = data.aduioList;
    },
    /**
     * 获取上传图片数据
     */
    getImgData(data) {
      this.transformer.imgList = data.imgList;
    },
    /**
     * 获取备注信息
     * @param data
     */
    getMsgData(data) {
      this.transformer.message = data.message;
    },
    getAwaitTowerOrLineType(moduleType, materialsTypeKey) {
      const that = this;
      const param = {
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        userId: this.userId,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          that.settingObj.transformerPlan = res.data;
          if (res.data.length !== 0) {
            that.transformer.plane = res.data[0].moduleName;
            that.transformer.planeId = res.data[0].moduleID;
          } else {
            that.transformer.plane = "";
            that.transformer.planeId = "";
          }
        }
      });
    },
  },
};
</script>

<style lang="sass" scoped>
</style>

