<template>
  <div class="onlineDeg">
    <div class="onlineDegmainBox">
      <div class="leftTree" v-show="leftTreeIsshow">
        <el-tree :data="data" :highlight-current="true" :props="defaultProps" @node-click="handleNodeClick" :default-expand-all="true">
          <span slot-scope="{ node, data }" class="eltree-template eltreeParents">
            <div class="eltreeParents-childLeft">
              <img v-if="
                  [
                    taskID,
                    '7',
                    '8',
                    '9',
                    '10',
                    '11',
                    '12',
                    '13',
                    '14',
                  ].includes(data.id)
                " class="eltree-image" :src="require('@/assets/onlineDesign/wjj.png')" alt="" />
              <img v-else class="eltree-image" :src="require('@/assets/onlineDesign/treefile.png')" alt="" />
              <span style="font-size: 0.75rem">{{ data.label }}</span>
            </div>
          </span>
        </el-tree>
      </div>
      <div class="rightMap" v-loading.fullscreen.lock="fullscreenLoading">
        <div class="topNav">
          <img :src="require('@/assets/onlineDesign/back.png')" v-show="leftTreeIsshow" :style="{ width: '24px', borderRadius: '24px' }" @click="leftTreeIsshow = !leftTreeIsshow" alt="" />
          <i class="el-icon-s-unfold" style="font-size: 22px" v-show="!leftTreeIsshow" @click="leftTreeIsshow = !leftTreeIsshow"></i>
          <el-dropdown @command="handleCommandPro" v-for="(item, index) in meunbarData" :key="index">
            <div v-show="item.children.length != 0">
              <el-link type="primary" :underline="false">{{ item.menuName
                }}<i class="el-icon-arrow-down el-icon--right"></i></el-link>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="val" v-for="(val, i) in item.children" :key="i">{{ val.menuName }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </div>
            <div v-show="item.children.length == 0">
              <el-link type="primary" :underline="false" @click="goDetail(item.menuName)">
                <span v-if="item.menuName !== '关闭'">{{ item.menuName }}</span>
              </el-link>
            </div>
          </el-dropdown>
          <div class="closeBtn" @click="goDetail('关闭')">
            <img :src="require('@/assets/onlineDesign/close.png')" alt="" />
            关闭
          </div>
        </div>
        <div class="mapBox">
          <iframe v-show="!isShowIframe" :src="`${iframeUrl}changemaps?childProjectId=${taskID}&mapsType=${mapsType}&imgSize=${insertMapInfo.insertImgVal}&mapZoom=${insertMapInfo.insertMapZoomVal}`" style="width: 100%; height: 100%"
            frameborder="0" />
          <iframe :src="browseUrl" v-show="isShowIframe" class="previewPdf" width="100%" height="100%" frameborder="0" scrolling="yes" style="margin-left: 5px; border: 1px solid lightgray">
          </iframe>
          <div class="closePdf" v-show="isShowIframe" @click="closePdf">
            关闭
          </div>
        </div>
      </div>
    </div>
    <div v-show="false" ref="pdf">
      <el-table :data="gridData" style="width: 100%">
        <el-table-column v-for="(item, index) in columns" :key="index" :prop="item.porp" :label="item.label" width="180">
        </el-table-column>
      </el-table>
    </div>
    <el-dialog title="主要设备材料清单" :visible.sync="dialogVisible" width="80%">
      <dialog-table v-loading="loadingMainMetrils" :data="gridData" :columns="columns"></dialog-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="derivedInfo" size="medium">导出材料清单</el-button>
        <el-button @click="dialogVisible = false" size="medium">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="拆旧物资清册" :visible.sync="oldInventoryVisible" width="80%">
      <dialog-table :data="oldInventory" :columns="oldInventoryColumns"></dialog-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="derivedOldInvent" size="medium">导出清单</el-button>
        <el-button @click="oldInventoryVisible = false" size="medium">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="基础一览图" :visible.sync="basicMapDataShow" width="80%">
      <dialog-table :data="basicMapData" :columns="basicMapDataColumns"></dialog-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="derivedbasicMapData" size="medium">导出清单</el-button>
        <el-button @click="basicMapDataShow = false" size="medium">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="利旧物资表" :visible.sync="showsupplieslist" width="80%">
      <dialog-table :data="supplieslist" :hasIndex="true" :columns="supplieslistColumns"></dialog-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="derivedsupplieslist" size="medium">导出表</el-button>
        <el-button @click="showsupplieslist = false" size="medium">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="杆型一览图表" :visible.sync="showbarTypeview" width="80%">
      <dialog-table :data="barTypeviewData" :hasIndex="true" :columns="barTypeviewColumns"></dialog-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="derivedbarTypeview" size="medium">导出表</el-button>
        <el-button @click="showbarTypeview = false" size="medium">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="电缆附属设施统计表" :visible.sync="showaccessories" width="80%">
      <dialog-table :data="accessoriesData" :hasIndex="true" :columns="accessoriesColumns"></dialog-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="derivedaccessories" size="medium">导出表</el-button>
        <el-button @click="showaccessories = false" size="medium">关闭</el-button></span>
    </el-dialog>
    <el-dialog title="铁附件明细" :visible.sync="showdetailofIron" width="80%">
      <dialog-table :data="detailofIronData" :hasIndex="true" :columns="detailofIronColumns" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="deriveddetailofIron" size="medium">导出表</el-button>
        <el-button @click="showdetailofIron = false" size="medium">关闭</el-button></span>
    </el-dialog>
    <el-dialog title="电缆明细表" :visible.sync="showCabledetail" width="80%">
      <dialog-table :data="cabledetailData" :hasIndex="true" :columns="cabledetailColumns"></dialog-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="derivedcabledetail" size="medium">导出表</el-button>
        <el-button @click="showCabledetail = false" size="medium">关闭</el-button></span>
    </el-dialog>
    <el-dialog title="钢管杆明细表" :visible.sync="showSteelpipe" width="80%">
      <dialog-table :data="steelpipeData" :columns="steelpipeColumns" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="derivedsteelpipe" size="medium">导出表</el-button>
        <el-button @click="showSteelpipe = false" size="medium">关闭</el-button></span>
    </el-dialog>
    <el-dialog title="ERP物资采购清册" :visible.sync="showERPpsteel" width="80%">
      <dialog-table :data="ERPpsteelData" :columns="ERPpsteelColumns" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="derivedERPpsteel" size="medium">导出表</el-button>
        <el-button @click="showERPpsteel = false" size="medium">关闭</el-button></span>
    </el-dialog>
    <el-dialog title="杆塔明细表" :visible.sync="showPoletower" width="80%">
      <dialog-table :data="PoletowerData" :columns="PoletowerColumns" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="derivedPoletower" size="medium">导出表</el-button>
        <el-button @click="showPoletower = false" size="medium">关闭</el-button></span>
    </el-dialog>
    <el-dialog title="工程规模统计表" :visible.sync="showProjectscale" width="80%">
      <dialog-table :data="ProjectscaleData" :columns="ProjectscaleColumns" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="derivedProjectscale" size="medium">导出表</el-button>
        <el-button @click="showProjectscale = false" size="medium">关闭</el-button></span>
    </el-dialog>
    <el-dialog title="应力弧垂表" :visible.sync="isShowss" width="80%">
      <el-table :data="tableDataYl" style="width: 100%">
        <el-table-column prop="span" label="档距" width="150">
        </el-table-column>
        <el-table-column v-for="(item, index) in tableHeader" :key="index" :label="item.label1" align="center">
          <el-table-column :label="item.label2" width="150" align="center">
            <el-table-column :label="item.label3" width="150" align="center">
              <el-table-column :prop="item.prop" :label="item.label4" width="150" align="center">
              </el-table-column>
            </el-table-column>
          </el-table-column>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="derivedStresssag" size="medium">导出表</el-button>
        <el-button @click="isShowss = false" size="medium">关闭</el-button></span>
    </el-dialog>
    <el-dialog title="CIM、SVG模型映射表配置" :visible.sync="showCIMSVG" width="80%">
      <el-table :data="simsvgData" style="width: 100%">
        <el-table-column prop="deviceCode" label="设备代码" align="center">
        </el-table-column>
        <el-table-column prop="deviceName" label="设备名称" align="center">
        </el-table-column>
        <el-table-column prop="deviceType" label="设备类型" align="center">
          <template slot-scope="scope">
            <el-input size="mini" placeholder="输入类型" v-model="scope.row.deviceType" />
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" align="center">
          <template slot-scope="scope">
            <el-input size="mini" placeholder="输入备注" v-model="scope.row.remark" />
          </template>
        </el-table-column>
        <el-table-column>
          <template slot-scope="scope">
            <el-button size="mini" type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pageFooter">
        <el-pagination background layout="prev, pager, next" :total="simsvgData.length">
        </el-pagination>
        <span>
          <el-button type="primary" @click="savesimsvg" size="small">保存</el-button>
          <el-button @click="showCIMSVG = false" size="small">关闭</el-button></span>
      </div>
    </el-dialog>
    <!--插入图幅-->
    <el-dialog title="选择插入图幅大小" :visible.sync="mapsInsertImg" width="20%">
      <el-form :model="insertMapInfo" ref="insertImgSize" label-width="100px" class="demo-ruleForm">
        <el-form-item label="图幅大小" prop="region">
          <el-select v-model="insertMapInfo.insertImgVal" placeholder="请选择图幅大小">
            <el-option v-for="item in insertMapInfo.insertImgSize" :label="item.value" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitImgSize" size="medium">确定</el-button>
        <el-button @click="mapsInsertImg = false" size="medium">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog :title="titleStr" :visible.sync="AutoMatchDrawing" width="40%">
      <el-table :data="tzData" stripe>
        <el-table-column type="index" label="序号" align="center" width="60">
        </el-table-column>
        <el-table-column prop="drawName" label="图纸名称"> </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="SaveToDirectory" size="medium">保存到成果目录</el-button>
        <el-button @click="AutoMatchDrawing = false" size="medium">关闭</el-button>
      </span>
    </el-dialog>
    <!--插入比例-->
    <el-dialog title="选择插入地图比例大小" :visible.sync="mapsInsertZoom" width="23%">
      <el-form :model="insertMapInfo" ref="insertImgSize" label-width="150px" class="demo-ruleForm">
        <el-form-item label="地图比例尺大小(m)" prop="region">
          <el-select v-model="insertMapInfo.insertMapZoomVal" placeholder="请选择地图比例尺">
            <el-option v-for="item in insertMapInfo.mapZomm" :label="item.value" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitMapZoom" size="medium">确定</el-button>
        <el-button @click="mapsInsertZoom = false" size="medium">关闭</el-button>
      </span>
    </el-dialog>
    <!--图纸目录-->
    <el-dialog title="图纸目录" :visible.sync="mapsTzVisible" size="small" top="12vh">
      <el-table :data="mapPicturData" stripe>
        <el-table-column prop="th" label="序号" width="80"> </el-table-column>
        <el-table-column prop="th" label="图号"> </el-table-column>
        <el-table-column prop="tm" label="图名"> </el-table-column>
        <!-- <el-table-column prop="tygcmc" width="300px" label="套用工程名称及图号"></el-table-column> -->
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="SaveToMapCg" size="medium">保存到成果目录</el-button>
        <el-button @click="mapsTzVisible = false" size="medium">关闭</el-button>
      </span>
    </el-dialog>
    <!--线夹匹配-->
    <el-dialog title="线夹匹配" :visible.sync="mapsxjppVisible" size="small" top="12vh">
      <el-table :data="mapsxjppData" v-if="mapsxjppVisible" :span-method="objectSpanMethod" border @selection-change="handleSelecmapsxjppData">
        stripe>
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column prop="xjlb" label="线夹类别"> </el-table-column>
        <el-table-column prop="ggxh" label="规格型号"> </el-table-column>
        <el-table-column prop="sydxjt" label="适用导线截图"> </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="mapsxjppVisible = false" size="medium">关闭</el-button>
      </span>
    </el-dialog>
    <!--成果上报-->
    <el-dialog title="成果上报" :visible.sync="reportResultShow" width="40%">
      <el-table :data="reportResultData" stripe>
        <el-table-column prop="type" label="类型" width="200">
        </el-table-column>
        <el-table-column prop="state" label="状态" width="80"></el-table-column>
        <el-table-column prop="progress" label="进度">
          <template slot-scope="scope">
            <el-progress :text-inside="true" :stroke-width="15" :percentage="scope.row.progress" :color="customColors"></el-progress>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="reportResultSubmit" size="medium">生成并提交</el-button>
        <el-button @click="reportResultShow = false" size="medium">关闭</el-button>
      </span>
    </el-dialog>
    <!--差异化绘图管理-->
    <el-dialog title="差异化绘图管理" :visible.sync="differentiationShow" width="80%">
      <el-dialog width="30%" title="提示" :visible.sync="innerVisible" append-to-body>
        切换杆塔类型会导致当前杆塔类型的个性化规则清空，是否确定？
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="innerVisibleYes" size="medium">是</el-button>
          <el-button @click="innerVisibleNo" size="medium">否</el-button>
        </span>
      </el-dialog>
      <template>
        <el-radio-group v-model="difRadio" @change="difRadioChange">
          <el-radio label="钢管杆">钢管杆</el-radio>
          <el-radio label="窄基塔">窄基塔</el-radio>
        </el-radio-group>
      </template>
      <el-divider></el-divider>
      <div class="toolBar">
        <el-button icon="el-icon-circle-plus-outline" @click="increaseData">增加</el-button>
        <el-button icon="el-icon-delete-solid" @click="deletedData">删除</el-button>
      </div>
      <el-table :data="differentiationData" stripe border @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column label="回路数">
          <template slot-scope="scope">
            <el-select v-model="scope.row.loopNumber" placeholder="请选择">
              <el-option v-for="item in loopNumberData" :key="item.value" :label="item.text" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="杆塔类型">
          <template slot-scope="scope">
            <el-select v-model="scope.row.gtType" placeholder="请选择">
              <el-option v-for="item in gtTypeData" :key="item.value" :label="item.text" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="最小角度">
          <template slot-scope="scope">
            <el-input v-model="scope.row.miniAngle" placeholder="最小角度"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="最大角度">
          <template slot-scope="scope">
            <el-input v-model="scope.row.maxiAngle" placeholder="最大角度"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="杆高">
          <template slot-scope="scope">
            <el-select v-model="scope.row.towerHeight" placeholder="请选择">
              <el-option v-for="item in towerHeightData" :key="item.value" :label="item.text" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="稍径" v-if="shaojingShow">
          <template slot-scope="scope">
            <el-select v-model="scope.row.shaojing" placeholder="请选择">
              <el-option v-for="item in shaojingData" :key="item.value" :label="item.text" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="模块型号">
          <template slot-scope="scope">
            <el-select v-model="scope.row.modules" placeholder="请选择" @visible-change="modulesChange(scope.row)">
              <el-option v-for="item in modulesData" :key="item.value" :label="item.text" :value="item.value">
              </el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="differentiationSave" size="medium">保存</el-button>
        <el-button @click="differentiationShow = false" size="medium">关闭</el-button>
      </span>
    </el-dialog>
    <!--插入图例-->
    <el-dialog title="图例" :visible.sync="drawLegend" width="40%">
      <el-table :data="legendData" stripe border :show-header="false">
        <el-table-column label="图元" width="80">
          <template slot-scope="scope">
            <el-image :src="scope.row.legend"> </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="legendName" label="图元名称"> </el-table-column>
        <el-table-column label="图元" width="80">
          <template slot-scope="scope">
            <el-image :src="scope.row.legend1">
              <div slot="error" class="image-slot"></div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="legendName1" label="图元名称"> </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer" center>
        <el-button @click="drawLegend = false" size="medium" round>关闭</el-button>
      </span>
    </el-dialog>
    <!--插入比例-->
    <el-dialog title="选择插入地图比例大小" :visible.sync="mapsInsertZoom" width="23%">
      <el-form :model="insertMapInfo" ref="insertImgSize" label-width="150px" class="demo-ruleForm">
        <el-form-item label="地图比例尺大小(m)" prop="region">
          <el-select v-model="insertMapInfo.insertMapZoomVal" placeholder="请选择地图比例尺">
            <el-option v-for="item in insertMapInfo.mapZomm" :label="item.value" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitMapZoom" size="medium">确定</el-button>
        <el-button @click="mapsInsertZoom = false" size="medium">关闭</el-button>
      </span>
    </el-dialog>
    <!--图纸目录-->
    <!-- <el-dialog
      title="图纸目录"
      :visible.sync="mapsTzVisible"
      size="small"
      top="12vh"
  >
      <el-table :data="mapPicturData" stripe>
        <el-table-column type="index" label="序号" width="80"> </el-table-column>
        <el-table-column prop="th" label="图号"> </el-table-column>
        <el-table-column prop="tm" label="图名"> </el-table-column>

      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="SaveToMapCg" size="medium"
          >保存到成果目录</el-button
        >
        <el-button @click="mapsTzVisible = false" size="medium">关闭</el-button>
      </span>
    </el-dialog> -->
    <!--标注管理-->
    <el-dialog title="标注管理" :visible.sync="tagManageVisible" size="small" top="12vh" width="90%">
      <div class="item-content">
        <el-container style="height: 380px; border: 1px solid #eee">
          <el-aside width="100px" style="background-color: rgb(238, 241, 246)">
            <Treeleft v-on:treeclick="tagTreeclick" />
          </el-aside>
          <el-container>
            <el-main>
              <el-table :data="tagData" v-loading="loading" height="270" max-height="500" align="center">
                <el-table-column label="是否显示" align="center">
                  <template slot-scope="scope">
                    <el-checkbox v-model="scope.row.IsShow"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column prop="TagName" label="标注项目">
                </el-table-column>
                <el-table-column label="字体样式">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.FontStyle" placeholder="请选择">
                      <el-option v-for="item in fontStyleData" :key="item.value" :label="item.text" :value="item.value">
                      </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="字体颜色">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.FontColor" placeholder="请选择">
                      <el-option v-for="item in fontColorData" :key="item.value" :label="item.text" :value="item.value">
                      </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="字体高度">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.FontHeight" placeholder="字体高度"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="角度" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.Angle" placeholder="角度"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="距离" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.Distance" placeholder="距离"></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="AttachmentPoint" label="标注对齐方式">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.AttachmentPoint" placeholder="请选择">
                      <el-option v-for="item in AttachmentPointData" :key="item.value" :label="item.text" :value="item.value">
                      </el-option>
                    </el-select>
                  </template>
                </el-table-column>
              </el-table>
            </el-main>
          </el-container>
        </el-container>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="medium">保存</el-button>
        <el-button @click="tagManageVisible = false" size="medium">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="预览" width="80%" :visible.sync="excelShow">
      <vue-office-excel :src="urlExcel" style="height: 500px; overflow-y: scroll" @rendered="renderedHandler" @error="errorHandler" />
    </el-dialog>
    <el-dialog title="运行态svg展示" width="90%" :visible.sync="operatingsvgShow">
      <div style="width: 100%; height: 500px; overflow: auto">
        <img src="@/assets/img/10kV五中线运行态单线图.svg" alt="" style="width: 400vw; height: 400vh" />
      </div>
    </el-dialog>
    <el-dialog title="设计态svg展示" width="90%" :visible.sync="designSvgShow">
      <div style="width: 100%; height: 500px; overflow: auto">
        <img src="@/assets/img/10kV五中线_设计态单线图.svg" alt="" style="width: 400vw; height: 400vh" />
      </div>
    </el-dialog>
    <el-dialog title="cim展示" width="90%" :visible.sync="cimShow">
      <div style="width: 100%; height: 70vh; overflow-y: auto" class="cimsBox">
        <pre>
  <code >{{cimXml}}</code>
 </pre>
        <!-- <iframe
          src="/cims/10kVcim.xml"
          frameborder="0"
          style="width: 100%; height: 100%"
        ></iframe> -->
      </div>
    </el-dialog>
    <el-dialog width="95%" title="基础参数设置" :visible.sync="showBasic">
      <BasicParametersSetting></BasicParametersSetting>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="medium" @click="saveBasicParame">保存</el-button>
        <el-button @click="showBasic = false" size="medium">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog width="95%" title="标准化信息管理" :visible.sync="showResource">
      <ResourceLibrary></ResourceLibrary>
    </el-dialog>
    <el-dialog width="95%" title="自动选型管理" :visible.sync="showautoChange">
      <autoChangeType></autoChangeType>
    </el-dialog>
    <el-dialog width="95%" title="图纸信息管理" :visible.sync="showDrawing">
      <Drawingmanage></Drawingmanage>
    </el-dialog>

    <el-dialog width="80%" height="800px" top="2vh" class="jjDialog" :visible.sync="qdjjComVisible" title="造价编制" @close="removeSessProject">
      <iframe :src="`${iframeUrl}qdjj?projectId=${taskID}`" style="width: 100%; height: 100%" />
    </el-dialog>
  </div>
</template>

<script>
import DialogTable from "./components/table.vue";
import Treeleft from "./components/Treeleft.vue";
import {
  leftMenu,
  topMenu,
  selectExcelFile,
  updateIsShow,
  getMeterilsData,
  saveMeterilsData,
  wacthSbExcelFile,
  generateMapJson,pushMaterialData
} from "@/api/api.js";
import { base64ToBlob } from "@/utils/toBlob.js";
import ScreenShort from "js-web-screen-shot";
import VueOfficeExcel from "@vue-office/excel";
import "@vue-office/excel/lib/index.css";
import BasicParametersSetting from "@/views/basicParameter/basicParameter.vue";
import ResourceLibrary from "@/views/resourcelibrary/materialsAmod.vue";
import autoChangeType from "@/views/resourcelibrary/autoChangeType.vue";
import Drawingmanage from "@/views/resourcelibrary/index.vue";
import axios from "axios";
import { cimXml } from "@/assets/xmltext/cim.js";
export default {
  components: {
    DialogTable,
    Treeleft,
    BasicParametersSetting,
    ResourceLibrary,
    autoChangeType,
    Drawingmanage,
    VueOfficeExcel,
  },
  data() {
    return {
      // 左侧深度菜单
      data: [],
      showBasic: false,
      showBilling: false, //技经提资
      showResource: false,
      showautoChange: false,
      showDrawing: false,
      loading: false,
      browseUrl: "",
      isShowIframe: false,
      tagData: [],
      qdjjComVisible: false,
      mapsxjppVisible: false, //
      mapsxjppData: [
        {
          xjlb: "耐张线夹-螺栓型",
          ggxh: "耐张线夹-螺栓型，NLL-1",
          sydxjt: "25-50",
        },
        {
          xjlb: "耐张线夹-螺栓型",
          ggxh: "耐张线夹-螺栓型，NLL-2",
          sydxjt: "70-95",
        },
        {
          xjlb: "耐张线夹-螺栓型",
          ggxh: "耐张线夹-螺栓型，NLL-3",
          sydxjt: "120-150",
        },
        {
          xjlb: "耐张线夹-螺栓型",
          ggxh: "耐张线夹-螺栓型，NLL-4",
          sydxjt: "185-240",
        },
        {
          xjlb: "耐张线夹-楔形绝缘（NXL）",
          ggxh: "耐张线夹-楔形绝缘，NXL-1",
          sydxjt: "25-50",
        },
        {
          xjlb: "耐张线夹-楔形绝缘（NXL）",
          ggxh: "耐张线夹-楔形绝缘，NXL-2",
          sydxjt: "70-95",
        },
        {
          xjlb: "耐张线夹-楔形绝缘（NXL）",
          ggxh: "耐张线夹-楔形绝缘，NXL-3",
          sydxjt: "120-150",
        },
        {
          xjlb: "耐张线夹-楔形绝缘（NXL）",
          ggxh: "耐张线夹-楔形绝缘，NXL-4",
          sydxjt: "185-240",
        },
        {
          xjlb: "耐张线夹-楔形绝缘（NXJG）",
          ggxh: "耐张线夹-楔型绝缘,NXJG-1",
          sydxjt: "35-50",
        },
        {
          xjlb: "耐张线夹-楔形绝缘（NXJG）",
          ggxh: "耐张线夹-楔型绝缘,NXJG-2",
          sydxjt: "70-95",
        },
        {
          xjlb: "耐张线夹-楔形绝缘（NXJG）",
          ggxh: "耐张线夹-楔型绝缘,NXJG-3",
          sydxjt: "120-150",
        },
        {
          xjlb: "耐张线夹-楔形绝缘（NXJG）",
          ggxh: "耐张线夹-楔型绝缘,NXJG-4",
          sydxjt: "185-240",
        },
        {
          xjlb: "楔形并沟线夹",
          ggxh: "接续金具-楔型并沟线夹，JXD-1",
          sydxjt: "35-50",
        },
        {
          xjlb: "楔形并沟线夹",
          ggxh: "接续金具-楔型并沟线夹，JXD-3",
          sydxjt: "70-95",
        },
        {
          xjlb: "楔形并沟线夹",
          ggxh: "接续金具-楔型并沟线夹，JXD-6",
          sydxjt: "120-150",
        },
        {
          xjlb: "楔形并沟线夹",
          ggxh: "接续金具-楔型并沟线夹，JXD-10",
          sydxjt: "185-240",
        },
      ],
      fontColorData: [
        { value: "ByBlock", text: "红色" },
        { value: "#FFFFFF", text: "白色" },
        { value: "#0000FF", text: "蓝色" },
        { value: "#FF00FF", text: "洋红色" },
        { value: "#FFFF00", text: "黄色" },
        { value: "#00FF00", text: "绿色" },
      ],
      fontStyleData: [
        { value: "宋体", text: "宋体" },
        { value: "仿宋", text: "仿宋" },
        { value: "微软雅黑", text: "微软雅黑" },
        { value: "新宋体", text: "新宋体" },
        { value: "楷体", text: "楷体" },
        { value: "黑体", text: "黑体" },
      ],
      AttachmentPointData: [
        { value: "TopLeft", text: "左上" },
        { value: "TopCenter", text: "中上" },
        { value: "TopRight", text: "右上" },
        { value: "MiddleLeft", text: "左中" },
        { value: "MiddleCenter", text: "正中" },
        { value: "MiddleRight", text: "右中" },
        { value: "BottomLeft", text: "左下" },
        { value: "BottomCenter", text: "中下" },
        { value: "BottomRight", text: "右下" },
      ],
      /*主要设备清单start*/
      dialogVisible: false,
      mapsType: 0, // 传递给maps的类型
      mapsImgSize: {}, // 传递给maps的框选的大小
      insertMapInfo: {
        insertImgVal: "", // 插入到地图的图幅值
        insertMapZoomVal: "", // 插入的地图比例尺大小
        mapZomm: [
          {
            id: "1",
            value: "1:50",
          },
          {
            id: "2",
            value: "1:100",
          },
          {
            id: "3",
            value: "1:200",
          },
          {
            id: "4",
            value: "1:500",
          },
          {
            id: "5",
            value: "1:1000",
          },
        ],
        insertImgSize: [
          {
            id: "1",
            value: "A0-841x1189标准",
          },
          {
            id: "2",
            value: "A1-594x841标准",
          },
          {
            id: "3",
            value: "A2-594x420标准",
          },
          {
            id: "4",
            value: "A3-420x297标准",
          },
          {
            id: "5",
            value: "A4-210x297标准",
          },
          {
            id: "6",
            value: "A5-297x210标准",
          },
        ], // 插入到地图的图幅选项
      },
      mapPicturData: [
        {
          th: "图1",
          tm: "线路路径图",
          tygcmc: "图1_天津河西10kV五中线延伸工程_线路路径图",
        },
        {
          th: "图2",
          tm: "杆塔型式一览图",
          tygcmc: "图2_天津河西10kV五中线延伸工程_杆塔型式一览图",
        },
        {
          th: "图3",
          tm: "基础形式一览图",
          tygcmc: "图3_天津河西10kV五中线延伸工程_基础形式一览图",
        },
        {
          th: "图4",
          tm: "DA-M-12（NJ1-4）单回45°～90拉线终端水泥单杆",
          tygcmc:
            "图4_天津河西10kV五中线延伸工程_DA-M-12（NJ1-4）单回45°～90拉线终端水泥单杆",
        },
        {
          th: "图5",
          tm: "DA-M-12（NJ1-4）单回45°～90拉线终端水泥杆型一览图",
          tygcmc:
            "图5_天津河西10kV五中线延伸工程_DA-M-12（NJ1-4）单回45°～90拉线终端水泥杆型一览图",
        },
        {
          th: "图6",
          tm: "Z-M-12（Z1-2）单回直线水泥单杆杆型一览图",
          tygcmc:
            "图7_天津河西10kV五中线延伸工程_Z-M-12（Z1-2）单回直线水泥单杆杆型一览图",
        },
        {
          th: "图7",
          tm: "Z-M-12（Z1-2）单回直线水泥单杆组装图",
          tygcmc:
            "图8_天津河西10kV五中线延伸工程_Z-M-12（Z1-2）单回直线水泥单杆组装图",
        },
        {
          th: "图8",
          tm: "Z-M-12（Z1-2）单回直线水泥单杆组装图",
          tygcmc:
            "图8_天津河西10kV五中线延伸工程_Z-M-12（Z1-2）单回直线水泥单杆组装图",
        },
        {
          th: "图9",
          tm: "绝缘线用10kV绝缘子串组装图  (10kV)金具组装",
          tygcmc:
            "图9_天津河西10kV五中线延伸工程_绝缘线用10kV绝缘子串组装图  (10kV)金具组装",
        },
        {
          th: "图10",
          tm: "拉线棒加工图  (10kV)金具组装",
          tygcmc:
            "图10_天津河西10kV五中线延伸工程_拉线棒加工图  (10kV)金具组装",
        },
        {
          th: "图11",
          tm: "1500mm耐张横担加工图  (10kV)耐张横担",
          tygcmc:
            "图11_天津河西10kV五中线延伸工程_1500mm耐张横担加工图  (10kV)耐张横担",
        },
        {
          th: "图12",
          tm: "1900mm耐张横担加工图（一） (10kV)耐张横担",
          tygcmc:
            "图12_天津河西10kV五中线延伸工程_1900mm耐张横担加工图（一） (10kV)耐张横担",
        },
        {
          th: "图13",
          tm: "1900mm耐张横担加工图（二） (10kV)耐张横担",
          tygcmc:
            "图13_天津河西10kV五中线延伸工程_1900mm耐张横担加工图（二） (10kV)耐张横担",
        },
      ], // 图纸目录
      excelShow: false,
      excelData: "",
      urlExcel: "",
      /*主要设备材料清单*/
      tagManageVisible: false,
      gridData: [{
        Materialcode: "500083391", //物料编码
        Materialdescr: "智能一体化电源系统,DC220V,80A", //物料描述
        unit: "面", //单位
        Unitprice: "0", //单价
        num: "9", //数量
        materialtype: "配电自动化", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500050299", //物料编码
        Materialdescr: "M20x350", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "2", //数量
        materialtype: "金具", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500078764", //物料编码
        Materialdescr: "M18x80", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "4", //数量
        materialtype: "金具", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500050316", //物料编码
        Materialdescr: "M20x100", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "4", //数量
        materialtype: "金具", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500078799", //物料编码
        Materialdescr: "M18x100", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "2", //数量
        materialtype: "金具", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500020748", //物料编码
        Materialdescr: "NUT-1", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "1", //数量
        materialtype: "拉线金具-UT型线夹", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500020749", //物料编码
        Materialdescr: "NUT-2", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "1", //数量
        materialtype: "拉线金具-UT型线夹", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500020760", //物料编码
        Materialdescr: "JK-1", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "12", //数量
        materialtype: "拉线金具-钢线卡子", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500020761", //物料编码
        Materialdescr: "JK-2", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "12", //数量
        materialtype: "拉线金具-钢线卡子", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500029690", //物料编码
        Materialdescr: "NX-1", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "3", //数量
        materialtype: "拉线金具-钢线卡子", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500029689", //物料编码
        Materialdescr: "NX-2", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "12", //数量
        materialtype: "拉线金具-锲型线夹", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500028065", //物料编码
        Materialdescr: "U-16", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "12", //数量
        materialtype: "联结金具-U型挂环", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "", //物料编码
        Materialdescr: "U-25", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "1", //数量
        materialtype: "联结金具-U型挂环", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500020411", //物料编码
        Materialdescr: "PD-12", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "2", //数量
        materialtype: "联结金具-平行挂板", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500020353", //物料编码
        Materialdescr: "QP-7", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "3", //数量
        materialtype: "联结金具-球头挂环", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500020369", //物料编码
        Materialdescr: "W-7B", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "3", //数量
        materialtype: "联结金具-碗头挂板", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500020399", //物料编码
        Materialdescr: "Z-7", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "3", //数量
        materialtype: "联结金具-直角挂板", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500063113", //物料编码
        Materialdescr: "—8×80,D200,普通型", //物料描述
        unit: "块", //单位
        Unitprice: "0", //单价
        num: "1", //数量
        materialtype: "抱箍", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500063114", //物料编码
        Materialdescr: "—8×80,D210,普通型", //物料描述
        unit: "块", //单位
        Unitprice: "0", //单价
        num: "1", //数量
        materialtype: "抱箍", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500075216", //物料编码
        Materialdescr: "—8×80,D190,加强型", //物料描述
        unit: "块", //单位
        Unitprice: "0", //单价
        num: "2", //数量
        materialtype: "抱箍", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500018778", //物料编码
        Materialdescr: "—8×80,D200,加强型", //物料描述
        unit: "块", //单位
        Unitprice: "0", //单价
        num: "2", //数量
        materialtype: "抱箍", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500063081", //物料编码
        Materialdescr: "拉线保护套", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "2", //数量
        materialtype: "其他物料", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500027409", //物料编码
        Materialdescr: "400x800x200", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "1", //数量
        materialtype: "其他物料", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500027410", //物料编码
        Materialdescr: "500x1000x200", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "1", //数量
        materialtype: "其他物料", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500019527", //物料编码
        Materialdescr: "φ16x3100mm 双耳", //物料描述
        unit: "根", //单位
        Unitprice: "0", //单价
        num: "1", //数量
        materialtype: "其他物料", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500118948", //物料编码
        Materialdescr: "φ22x3100mm,双耳", //物料描述
        unit: "根", //单位
        Unitprice: "0", //单价
        num: "1", //数量
        materialtype: "其他物料", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500113018", //物料编码
        Materialdescr: "∠80×8 1700mm 不计孔距", //物料描述
        unit: "根", //单位
        Unitprice: "0", //单价
        num: "1", //数量
        materialtype: "其他物料", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500118948", //物料编码
        Materialdescr: "∠75×8 1500mm 不计孔距", //物料描述
        unit: "套", //单位
        Unitprice: "0", //单价
        num: "1", //数量
        materialtype: "其他物料", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500082759", //物料编码
        Materialdescr: "18D220", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "1", //数量
        materialtype: "铁附件", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500019207", //物料编码
        Materialdescr: "60x6 D190 单杆顶", //物料描述
        unit: "个", //单位
        Unitprice: "0", //单价
        num: "1", //数量
        materialtype: "铁附件", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500122799", //物料编码
        Materialdescr: "FZS-10/5", //物料描述
        unit: "只", //单位
        Unitprice: "0", //单价
        num: "3", //数量
        materialtype: "绝缘子", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500065920", //物料编码
        Materialdescr: "截面mm2:35 镀锌", //物料描述
        unit: "米", //单位
        Unitprice: "0", //单价
        num: "0.006", //数量
        materialtype: "钢绞线", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500065944", //物料编码
        Materialdescr: "钢绞线 截面mm2:80镀锌", //物料描述
        unit: "米", //单位
        Unitprice: "0", //单价
        num: "0.012", //数量
        materialtype: "钢绞线", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500061767", //物料编码
        Materialdescr: "JKLGYJ-10-95/15", //物料描述
        unit: "米", //单位
        Unitprice: "0", //单价
        num: "0.315", //数量
        materialtype: "架空绝缘导线", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500013972", //物料编码
        Materialdescr: "非预应力,整根杆,12m,190mm,M，无根部法兰", //物料描述
        unit: "基", //单位
        Unitprice: "0", //单价
        num: "2", //数量
        materialtype: "锥形水泥杆", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500122849", //物料编码
        Materialdescr: "FXBW-10/70", //物料描述
        unit: "只", //单位
        Unitprice: "0", //单价
        num: "3", //数量
        materialtype: "交流棒形悬式复合绝缘子", //物资类型
        remark: "", //备注
      },
      {
        Materialcode: "500017322", //物料编码
        Materialdescr: "JH10-90", //物料描述
        unit: "只", //单位
        Unitprice: "0", //单价
        num: "2", //数量
        materialtype: "拉紧绝缘子", //物资类型
        remark: "", //备注
      }],
      loadingMainMetrils: false,
      // columns: [
      //   {
      //     porp: "erpcode",
      //     label: "物料编码",
      //   },
      //   {
      //     porp: "wlms",
      //     label: "物料描述",
      //   },
      //   {
      //     porp: "unit",
      //     label: "单位",
      //   },
      //   {
      //     porp: "dj",
      //     label: "单价",
      //   },
      //   {
      //     porp: "num",
      //     label: "数量",
      //   },
      //   {
      //     porp: "wllx",
      //     label: "物资类型",
      //   },
      //   {
      //     porp: "remark",
      //     label: "备注",
      //   },
      // ],
      columns: [
        {
          porp: "Materialcode",
          label: "物料编码",
        },
        {
          porp: "Materialdescr",
          label: "物料描述",
        },
        {
          porp: "unit",
          label: "单位",
        },
        {
          porp: "Unitprice",
          label: "单价",
        },
        {
          porp: "num",
          label: "数量",
        },
        {
          porp: "materialtype",
          label: "物资类型",
        },
        {
          porp: "remark",
          label: "备注",
        },
      ],
      /*主要设备清单end*/
      /*拆旧物资清册start*/
      oldInventory: [
        {
          encoding: "500065813", //物料编码
          description: "", //描述
          unit: "千米", //单位
          unitprice: "0", //单价
          num: "3", //数量
        },
        {
          encoding: "500050316", //物料编码
          description: "", //描述
          unit: "个", //单位
          unitprice: "0", //单价
          num: "12", //数量
        },
        {
          encoding: "500063081", //物料编码
          description: "", //描述
          unit: "个", //单位
          unitprice: "0", //单价
          num: "6", //数量
        },
        {
          encoding: "500027409", //物料编码
          description: "", //描述
          unit: "个", //单位
          unitprice: "0", //单价
          num: "6", //数量
        },
        {
          encoding: "500019527", //物料编码
          description: "", //描述
          unit: "根", //单位
          unitprice: "0", //单价
          num: "6", //数量
        },
        {
          encoding: "500065920", //物料编码
          description: "", //描述
          unit: "吨", //单位
          unitprice: "0", //单价
          num: "0.036", //数量
        },
        {
          encoding: "500020760", //物料编码
          description: "", //描述
          unit: "个", //单位
          unitprice: "0", //单价
          num: "72", //数量
        },
        {
          encoding: "500029690", //物料编码
          description: "", //描述
          unit: "个", //单位
          unitprice: "0", //单价
          num: "18", //数量
        },
        {
          encoding: "500020748", //物料编码
          description: "", //描述
          unit: "个", //单位
          unitprice: "0", //单价
          num: "6", //数量
        },
        {
          encoding: "500020411", //物料编码
          description: "", //描述
          unit: "个", //单位
          unitprice: "0", //单价
          num: "6", //数量
        },
        {
          encoding: "500017322", //物料编码
          description: "", //描述
          unit: "只", //单位
          unitprice: "0", //单价
          num: "6", //数量
        },
        {
          encoding: "500063113", //物料编码
          description: "", //描述
          unit: "块", //单位
          unitprice: "0", //单价
          num: "8", //数量
        },
        {
          encoding: "500063114", //物料编码
          description: "", //描述
          unit: "块", //单位
          unitprice: "0", //单价
          num: "2", //数量
        },
        {
          encoding: "500013972", //物料编码
          description: "", //描述
          unit: "基", //单位
          unitprice: "0", //单价
          num: "4", //数量
        },
        {
          encoding: "500078799", //物料编码
          description: "", //描述
          unit: "个", //单位
          unitprice: "0", //单价
          num: "12", //数量
        },
        {
          encoding: "500050299", //物料编码
          description: "", //描述
          unit: "个", //单位
          unitprice: "0", //单价
          num: "12", //数量
        },
        {
          encoding: "500075216", //物料编码
          description: "", //描述
          unit: "块", //单位
          unitprice: "0", //单价
          num: "8", //数量
        },
        {
          encoding: "500118948", //物料编码
          description: "", //描述
          unit: "套", //单位
          unitprice: "0", //单价
          num: "6", //数量
        },
        {
          encoding: "500122849", //物料编码
          description: "", //描述
          unit: "只", //单位
          unitprice: "0", //单价
          num: "4", //数量
        },
        {
          encoding: "500020353", //物料编码
          description: "", //描述
          unit: "个", //单位
          unitprice: "0", //单价
          num: "4", //数量
        },
        {
          encoding: "500020369", //物料编码
          description: "", //描述
          unit: "个", //单位
          unitprice: "0", //单价
          num: "4", //数量
        },
        {
          encoding: "500020399", //物料编码
          description: "", //描述
          unit: "个", //单位
          unitprice: "0", //单价
          num: "4", //数量
        },
        {
          encoding: "500129321", //物料编码
          description: "", //描述
          unit: "个", //单位
          unitprice: "0", //单价
          num: "4", //数量
        },
        {
          encoding: "500019179", //物料编码
          description: "", //描述
          unit: "个", //单位
          unitprice: "0", //单价
          num: "2", //数量
        },
        {
          encoding: "500122799", //物料编码
          description: "", //描述
          unit: "只", //单位
          unitprice: "0", //单价
          num: "4", //数量
        },
        {
          encoding: "500129328", //物料编码
          description: "", //描述
          unit: "付", //单位
          unitprice: "0", //单价
          num: "12", //数量
        },
      ],
      // 控制显隐
      oldInventoryVisible: false,
      oldInventoryColumns: [
        { porp: "encoding", label: "物料编码" },
        { porp: "description", label: "描述" },
        { porp: "unit", label: "单位" },
        { porp: "unitprice", label: "单价" },
        { porp: "num", label: "数量" },
      ],
      /*拆旧物资清册end*/
      /*基础一览图start*/
      basicMapDataShow: false,
      basicMapData: [
        {
          baseType: "水泥杆直埋式基础", //基础类型
          besuitableFor: "水泥杆", //适用于
          num: "5", //数量
        },
        {
          baseType: "拉盘直埋式基础",
          besuitableFor: "拉盘",
          num: "4",
        },
      ],
      basicMapDataColumns: [
        { porp: "baseType", label: "基础类型" },
        { porp: "besuitableFor", label: "水泥杆" },
        { porp: "num", label: "数量" },
      ],
      /*基础一览图end*/
      /*利旧物资表start*/

      showsupplieslist: false,
      supplieslist: [
        {
          materialClass: "金具", //物料类别
          materialName: "双头螺丝", //物料名称,
          materialSpecification: "M20x100", //物料规格
          oldQuantity: "0", //利旧数量
          demolishOldQuantity: "12", //拆除利旧数量
          unit: "个", //单位
          agelimit: "2023", //年限
          remark: "", //备注
        },
        {
          materialClass: "拉线金具-UT型线夹", //物料类别
          materialName: "UT线夹", //物料名称,
          materialSpecification: "NUT-1", //物料规格
          oldQuantity: "0", //利旧数量
          demolishOldQuantity: "6", //拆除利旧数量
          unit: "个", //单位
          agelimit: "2023", //年限
          remark: "", //备注
        },
        {
          materialClass: "拉线金具-钢线卡子", //物料类别
          materialName: "拉线金具-钢线卡子", //物料名称,
          materialSpecification: "JK-1", //物料规格
          oldQuantity: "0", //利旧数量
          demolishOldQuantity: "72", //拆除利旧数量
          unit: "个", //单位
          agelimit: "2023", //年限
          remark: "", //备注
        },
        {
          materialClass: "拉线金具-锲型线夹", //物料类别
          materialName: "拉线金具-锲型线夹", //物料名称,
          materialSpecification: "NX-1", //物料规格
          oldQuantity: "0", //利旧数量
          demolishOldQuantity: "18", //拆除利旧数量
          unit: "个", //单位
          agelimit: "2023", //年限
          remark: "", //备注
        },
        {
          materialClass: "联结金具-U型挂环", //物料类别
          materialName: "拉线U型环", //物料名称,
          materialSpecification: "U-16", //物料规格
          oldQuantity: "0", //利旧数量
          demolishOldQuantity: "6", //拆除利旧数量
          unit: "个", //单位
          agelimit: "2023", //年限
          remark: "", //备注
        },
        {
          materialClass: "联结金具-平行挂板", //物料类别
          materialName: "联结金具-平行挂板", //物料名称,
          materialSpecification: "PD-12", //物料规格
          oldQuantity: "0", //利旧数量
          demolishOldQuantity: "6", //拆除利旧数量
          unit: "个", //单位
          agelimit: "2023", //年限
          remark: "", //备注
        },
        {
          materialClass: "抱箍", //物料类别
          materialName: "半圆抱箍", //物料名称,
          materialSpecification: "—8×80,D200,普通型", //物料规格
          oldQuantity: "0", //利旧数量
          demolishOldQuantity: "8", //拆除利旧数量
          unit: "块", //单位
          agelimit: "2023", //年限
          remark: "", //备注
        },
        {
          materialClass: "抱箍", //物料类别
          materialName: "半圆抱箍", //物料名称,
          materialSpecification: "—8×80,D210,普通型", //物料规格
          oldQuantity: "0", //利旧数量
          demolishOldQuantity: "2", //拆除利旧数量
          unit: "块", //单位
          agelimit: "2023", //年限
          remark: "", //备注
        },
        {
          materialClass: "抱箍", //物料类别
          materialName: "半圆抱箍", //物料名称,
          materialSpecification: "—8×80,D220,普通型", //物料规格
          oldQuantity: "0", //利旧数量
          demolishOldQuantity: "2", //拆除利旧数量
          unit: "块", //单位
          agelimit: "2023", //年限
          remark: "", //备注
        },
        {
          materialClass: "其他物料", //物料类别
          materialName: "拉线保护套", //物料名称,
          materialSpecification: "拉线保护套", //物料规格
          oldQuantity: "0", //利旧数量
          demolishOldQuantity: "6", //拆除利旧数量
          unit: "个", //单位
          agelimit: "2023", //年限
          remark: "", //备注
        },
        {
          materialClass: "其他物料", //物料类别
          materialName: "拉盘", //物料名称,
          materialSpecification: "400x800x200", //物料规格
          oldQuantity: "0", //利旧数量
          demolishOldQuantity: "6", //拆除利旧数量
          unit: "个", //单位
          agelimit: "2023", //年限
          remark: "", //备注
        },
        {
          materialClass: "其他物料", //物料类别
          materialName: "拉线棒", //物料名称,
          materialSpecification: "φ16x3100mm 双耳", //物料规格
          oldQuantity: "0", //利旧数量
          demolishOldQuantity: "6", //拆除利旧数量
          unit: "根", //单位
          agelimit: "2023", //年限
          remark: "", //备注
        },
        {
          materialClass: "钢绞线", //物料类别
          materialName: "钢绞线", //物料名称,
          materialSpecification: "截面mm2:35 镀锌", //物料规格
          oldQuantity: "0", //利旧数量
          demolishOldQuantity: "0.038", //拆除利旧数量
          unit: "吨", //单位
          agelimit: "2023", //年限
          remark: "", //备注
        },
        {
          materialClass: "架空绝缘导线", //物料类别
          materialName: "架空绝缘导线", //物料名称,
          materialSpecification: "JKTRJY-10/35", //物料规格
          oldQuantity: "0", //利旧数量
          demolishOldQuantity: "0.945", //拆除利旧数量
          unit: "千米", //单位
          agelimit: "2023", //年限
          remark: "", //备注
        },
        {
          materialClass: "拉紧绝缘子", //物料类别
          materialName: "拉紧绝缘子", //物料名称,
          materialSpecification: "JH10-90", //物料规格
          oldQuantity: "0", //利旧数量
          demolishOldQuantity: "6", //拆除利旧数量
          unit: "只", //单位
          agelimit: "2023", //年限
          remark: "", //备注
        },
      ],
      supplieslistColumns: [
        { porp: "materialClass", label: "物料类别" },
        { porp: "materialName", label: "物料名称" },
        { porp: "materialSpecification", label: "物料规格" },
        { porp: "oldQuantity", label: "利旧数量" },
        { porp: "demolishOldQuantity", label: "拆除利旧数量" },
        { porp: "unit", label: "单位" },
        { porp: "remark", label: "备注" },
      ],
      /*利旧物资表end*/
      /*杆型一览图start*/
      showbarTypeview: false,
      barTypeviewData: [
        {
          rodType: "DA-H-12", //杆型
          shaftWeight: "", //杆身重量
          anchorbolt: "", //地脚螺栓/钢管杆重量
          universalpolenum: "1,5", //通用杆号
          remark: "", //备注
        },
        {
          rodType: "NJIA-N-12", //杆型
          shaftWeight: "", //杆身重量
          anchorbolt: "", //地脚螺栓/钢管杆重量
          universalpolenum: "2", //通用杆号
          remark: "", //备注
        },
        {
          rodType: "NJIA-N-12", //杆型
          shaftWeight: "", //杆身重量
          anchorbolt: "", //地脚螺栓/钢管杆重量
          universalpolenum: "3,4", //通用杆号
          remark: "", //备注
        },
      ],
      barTypeviewColumns: [
        { porp: "rodType", label: "杆型" },
        { porp: "shaftWeight", label: "杆身重量" },
        { porp: "anchorbolt", label: "地脚螺栓/钢管杆重量" },
        { porp: "universalpolenum", label: "通用杆号" },
        { porp: "remark", label: "备注" },
      ],

      /*杆型一览图end*/
      /*电缆附属设施统计表start*/
      showaccessories: false,
      accessoriesData: [
        {
          accessoryType: "电缆标志桩", //附件类别
          materialName: "", //物料名称
          userChannelType: "全部类别通道", //使用通道类别
          num: "27", //数量,
          unit: "个", //单位
          calculationFormula: "每条电缆通道路径长度/电缆标志桩间距", //计算公式
        },
        {
          accessoryType: "电缆警示带", //附件类别
          materialName: "电缆警示带", //物料名称
          userChannelType: "全部类别通道", //使用通道类别
          num: "43", //数量,
          unit: "米", //单位
          calculationFormula: "每条电缆通道路径长度*该路径电缆根数", //计算公式
        },
        {
          accessoryType: "电缆管枕数量", //附件类别
          materialName: "", //物料名称
          userChannelType: "新建的排管敷设 B-1和低压排管 方案", //使用通道类别
          num: "570", //数量,
          unit: "个", //单位
          calculationFormula:
            "((每条电缆通道路径长度/电缆管枕计算参 数)+1)*敷设方式最大电缆根数(仅统计新建的排 管敷设B-1方案和低压排管方案)", //计算公式
        },
      ],
      accessoriesColumns: [
        { porp: "accessoryType", label: "电缆警示带" },
        { porp: "materialName", label: "电缆警示带" },
        { porp: "userChannelType", label: "全部类别通道" },
        { porp: "num", label: "数量" },
        { porp: "unit", label: "单位" },
        { porp: "calculationFormula", label: "计算公式" },
      ],
      /*电缆附属设施统计表end*/
      /*铁附件明细表start*/
      showdetailofIron: false,
      detailofIronData: [
        {
          ERPcoding: "", //ERP编码
          materialName: "", //物料名称
          type: "", //类型
          materialSpecifi: "", //物料规格
          cureID: "", //固化ID
          units: "", //单位
          designDosage: "", //设计用量
          singleweight: "", //单重(kg)
          totalweight: "", //总重(kg)
          natureofSupply: "", //供应性质
        },
      ],
      detailofIronColumns: [
        { porp: "ERPcoding", label: "ERP编码" },
        { porp: "materialName", label: "物料名称" },
        { porp: "type", label: "类型" },
        { porp: "materialSpecifi", label: "物料规格" },
        { porp: "cureID", label: "固化ID" },
        { porp: "units", label: "单位" },
        { porp: "designDosage", label: "单重(kg)" },
        { porp: "totalweight", label: "总重(kg)" },
        { porp: "natureofSupply", label: "供应性质" },
      ],
      /*铁附件明细表end*/
      /*电缆明细表start*/
      showCabledetail: false,
      cabledetailData: [
        {
          cableName: "线路1", //电缆名称
          cableType: "ZC-YJLV22-10kV-3*70", //电缆型号
          originDevicename: "", //起始设备名称
          terminationDevicename: "", //终止设备名称
          newLength: "392", //新建长度
          layinoldways: "0", //利旧敷设
          strippingLength: "0", //拆旧长度
          indoorTerminal: "2", //户内终端
          equipmentTerminal: "0", //设备终端
          intermediateJoint: "0", //中间接头
          cablebuttbox: "0", //电缆对接箱
          cableBranchbox: "", //电缆分支箱
        },
        {
          cableName: "线路1", //电缆名称
          cableType: "ZC-YJLV22-10kV-3*70", //电缆型号
          originDevicename: "", //起始设备名称
          terminationDevicename: "", //终止设备名称
          newLength: "1025", //新建长度
          layinoldways: "0", //利旧敷设
          strippingLength: "0", //拆旧长度
          indoorTerminal: "2", //户内终端
          equipmentTerminal: "0", //设备终端
          intermediateJoint: "0", //中间接头
          cablebuttbox: "0", //电缆对接箱
          cableBranchbox: "", //电缆分支箱
        },
        {
          cableName: "线路1", //电缆名称
          cableType: "ZC-YJLV22-10kV-3*70", //电缆型号
          originDevicename: "", //起始设备名称
          terminationDevicename: "", //终止设备名称
          newLength: "1365", //新建长度
          layinoldways: "0", //利旧敷设
          strippingLength: "0", //拆旧长度
          indoorTerminal: "2", //户内终端
          equipmentTerminal: "0", //设备终端
          intermediateJoint: "0", //中间接头
          cablebuttbox: "0", //电缆对接箱
          cableBranchbox: "", //电缆分支箱
        },
        {
          cableName: "线路1", //电缆名称
          cableType: "ZC-YJLV22-10kV-3*70", //电缆型号
          originDevicename: "", //起始设备名称
          terminationDevicename: "", //终止设备名称
          newLength: "296", //新建长度
          layinoldways: "0", //利旧敷设
          strippingLength: "0", //拆旧长度
          indoorTerminal: "2", //户内终端
          equipmentTerminal: "0", //设备终端
          intermediateJoint: "0", //中间接头
          cablebuttbox: "0", //电缆对接箱
          cableBranchbox: "", //电缆分支箱
        },
        {
          cableName: "线路1", //电缆名称
          cableType: "ZC-YJLV22-10kV-3*70", //电缆型号
          originDevicename: "", //起始设备名称
          terminationDevicename: "", //终止设备名称
          newLength: "271", //新建长度
          layinoldways: "0", //利旧敷设
          strippingLength: "0", //拆旧长度
          indoorTerminal: "2", //户内终端
          equipmentTerminal: "0", //设备终端
          intermediateJoint: "0", //中间接头
          cablebuttbox: "0", //电缆对接箱
          cableBranchbox: "", //电缆分支箱
        },
        {
          cableName: "线路1", //电缆名称
          cableType: "ZC-YJLV22-10kV-3*70", //电缆型号
          originDevicename: "", //起始设备名称
          terminationDevicename: "", //终止设备名称
          newLength: "285", //新建长度
          layinoldways: "0", //利旧敷设
          strippingLength: "0", //拆旧长度
          indoorTerminal: "2", //户内终端
          equipmentTerminal: "0", //设备终端
          intermediateJoint: "0", //中间接头
          cablebuttbox: "0", //电缆对接箱
          cableBranchbox: "", //电缆分支箱
        },
        {
          cableName: "线路1", //电缆名称
          cableType: "ZC-YJLV22-10kV-3*70", //电缆型号
          originDevicename: "", //起始设备名称
          terminationDevicename: "", //终止设备名称
          newLength: "323", //新建长度
          layinoldways: "0", //利旧敷设
          strippingLength: "0", //拆旧长度
          indoorTerminal: "2", //户内终端
          equipmentTerminal: "0", //设备终端
          intermediateJoint: "0", //中间接头
          cablebuttbox: "0", //电缆对接箱
          cableBranchbox: "", //电缆分支箱
        },
      ],
      cabledetailColumns: [
        { porp: "cableName", label: "电缆名称" },
        { porp: "cableType", label: "电缆型号" },
        { porp: "originDevicename", label: "起始设备名称" },
        { porp: "terminationDevicename", label: "终止设备名称" },
        { porp: "newLength", label: "新建长度" },
        { porp: "layinoldways", label: "利旧敷设" },
        { porp: "indoorTerminal", label: "户内终端" },
        { porp: "equipmentTerminal", label: "设备终端" },
        { porp: "intermediateJoint", label: "中间接头" },
        { porp: "cablebuttbox", label: "电缆对接箱" },
        { porp: "cableBranchbox", label: "电缆分支箱" },
      ],

      /*电缆明细表end*/
      /*钢管杆明细表start*/
      showSteelpipe: false,
      steelpipeData: [
        {
          rodType: "GN31-10", //杆型
          totalWeight: "2155.16", //单基总重(kg)
          sectionItower: "1737.4", //杆塔Ⅰ段(kg)
          footHeavy: "417.76", //螺栓脚钉垫圈重(kg)
          IIallupweight: "0", //杆塔Ⅱ段总重
          climbingWeight: "0", // 爬梯重(kg)
          armTotalweight: "", //横担总重（kg）
          cardinalNum: "5", //基数
          allupweight: "10775.8", //总重
        },
      ],
      steelpipeColumns: [
        { porp: "rodType", label: "杆型" },
        { porp: "totalWeight", label: "单基总重(kg)" },
        { porp: "sectionItower", label: "杆塔Ⅰ段(kg)" },
        { porp: "footHeavy", label: "螺栓脚钉垫圈重(kg)" },
        { porp: "IIallupweight", label: "杆塔Ⅱ段总重" },
        { porp: "climbingWeight", label: "爬梯重(kg)" },
        { porp: "armTotalweight", label: "横担总重（kg）" },
        { porp: "cardinalNum", label: "基数" },
        { porp: "allupweight", label: "总重" },
      ],
      /*钢管杆明细表end*/
      /*ERPERP物资采购清册start*/
      showERPpsteel: false,
      ERPpsteelData: [
        {
          materialcode: "500028065", //物料编码
          materialname: "U-16", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "8", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "9906-500020383-00004", //技术规范书ID
        },
        {
          materialcode: "500028065", //物料编码
          materialname: "U-21", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "1", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "9906-500020383-00004", //技术规范书ID
        },
        {
          materialcode: "500083391", //物料编码
          materialname: "智能一体化电源系统,DC220V,80A", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "216", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "面", //计量单位
          technicaID: "G00G-500083391-00001", //技术规范书ID
        },
        {
          materialcode: "500031947", //物料编码
          materialname: "3×70,户内终端,冷缩,铝", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "2", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "套", //计量单位
          technicaID: "5216-500073469-00002", //技术规范书ID
        },
        {
          materialcode: "500063081", //物料编码
          materialname: "拉线保护套", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "9", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "", //技术规范书ID
        },
        {
          materialcode: "500027409", //物料编码
          materialname: "400x800x200", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "9", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "G00G-500063048-00001", //技术规范书ID
        },
        {
          materialcode: "500050299", //物料编码
          materialname: "M20x350", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "14", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "0699-500081584-00001", //技术规范书ID
        },
        {
          materialcode: "500050316", //物料编码
          materialname: "M20x100", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "18", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "0699-500081584-00001", //技术规范书ID
        },
        {
          materialcode: "500078799", //物料编码
          materialname: "M18x100", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "14", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "0699-500081584-00001", //技术规范书ID
        },
        {
          materialcode: "500063113", //物料编码
          materialname: "—8×80,D200,普通型", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "9", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "块", //计量单位
          technicaID: "G00G-500118948-00001", //技术规范书ID
        },
        {
          materialcode: "500063114", //物料编码
          materialname: "—8×80,D210,普通型", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "3", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "块", //计量单位
          technicaID: "G00G-500118948-00001", //技术规范书ID
        },
        {
          materialcode: "500063102", //物料编码
          materialname: "—6×80,D200,普通型", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "1", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "块", //计量单位
          technicaID: "G00G-500118948-00001", //技术规范书ID
        },
        {
          materialcode: "500063117", //物料编码
          materialname: "—8×80,D220,普通型", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "2", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "块", //计量单位
          technicaID: "G00G-500118948-00001", //技术规范书ID
        },
        {
          materialcode: "500075216", //物料编码
          materialname: "—8×80,D190,加强型", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "8", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "块", //计量单位
          technicaID: "0699-500018767-00002", //技术规范书ID
        },
        {
          materialcode: "500019527", //物料编码
          materialname: "φ16x3100mm 双耳", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "8", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "根", //计量单位
          technicaID: "0699-500018767-00002", //技术规范书ID
        },
        {
          materialcode: "500019527", //物料编码
          materialname: "φ20x3100mm,双耳", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "1", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "根", //计量单位
          technicaID: "0699-500018767-00002", //技术规范书ID
        },
        {
          materialcode: "500118948", //物料编码
          materialname: "∠75×8 1500mm 不计孔距", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "6", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "套", //计量单位
          technicaID: "G00G-500118948-00001", //技术规范书ID
        },
        {
          materialcode: "500118948", //物料编码
          materialname: "∠80×8 1900mm 不计孔距", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "1", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "套", //计量单位
          technicaID: "G00G-500118948-00001", //技术规范书ID
        },
        {
          materialcode: "500019179", //物料编码
          materialname: "80x8 D190 双杆顶", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "3", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "0699-500018767-00002", //技术规范书ID
        },
        {
          materialcode: "500020748", //物料编码
          materialname: "NUT-1", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "8", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "9906-500020383-00004", //技术规范书ID
        },
        {
          materialcode: "500020749", //物料编码
          materialname: "NUT-2", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "1", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "9906-500020383-00004", //技术规范书ID
        },
        {
          materialcode: "500020760", //物料编码
          materialname: "JK-1", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "96", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "9906-500020383-00004", //技术规范书ID
        },
        {
          materialcode: "500020761", //物料编码
          materialname: "JK-2   ", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "12", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "9906-500020383-00004", //技术规范书ID
        },
        {
          materialcode: "500029690", //物料编码
          materialname: "NX-1", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "24", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "9906-500020383-00004", //技术规范书ID
        },
        {
          materialcode: "500029689", //物料编码
          materialname: "NX-2", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "3", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "9906-500020383-00004", //技术规范书ID
        },
        {
          materialcode: "500020411", //物料编码
          materialname: "PD-12", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "9", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "9906-500020383-00004", //技术规范书ID
        },
        {
          materialcode: "500020353", //物料编码
          materialname: "QP-7", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "24", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "9906-500020383-00004", //技术规范书ID
        },
        {
          materialcode: "500020369", //物料编码
          materialname: "W-7B", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "24", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "9906-500020383-00004", //技术规范书ID
        },
        {
          materialcode: "500020399", //物料编码
          materialname: "Z-7", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "24", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "个", //计量单位
          technicaID: "9906-500020383-00004", //技术规范书ID
        },
        {
          materialcode: "500129328", //物料编码
          materialname: "JC-6", //物料名称
          owningWBScode: "", //所属WBS编码
          quantityRequired: "18", //需求数量
          evaluationPrice: "0", //评估价格
          unitmeasurement: "付", //计量单位
          technicaID: "9906-500020383-00004", //技术规范书ID
        },
      ],
      ERPpsteelColumns: [
        { porp: "materialcode", label: "物料编码" },
        { porp: "materialname", label: "物料名称" },
        { porp: "owningWBScode", label: "所属WBS编码" },
        { porp: "quantityRequired", label: "需求数量" },
        { porp: "evaluationPrice", label: "评估价格" },
        { porp: "unitmeasurement", label: "计量单位" },
        { porp: "technicaID", label: "技术规范书ID" },
      ],
      /*ERPERP物资采购清册end*/
      /*杆塔明细表start*/
      showPoletower: false,
      PoletowerData: [
        {
          poleCode:
            "7ed1ac16-1208-4496-88a7-6442006f50ed,7c7199e0-f489-4dbe-a6f1-b73409b64a6a_", //杆塔编号
          rodDesignation: "", //杆型代号
          rodheadDesignation: "", //杆头代号
          barcondition: "", //杆段情况
          material: "", //材质
          status: "原有", //状态
          landform: "平地", //地形
          geological: "普通土", //地质情况
        },
        {
          poleCode: "线路1_", //杆塔编号
          rodDesignation: "", //杆型代号
          rodheadDesignation: "", //杆头代号
          barcondition: "", //杆段情况
          material: "", //材质
          status: "", //状态
          landform: "", //地形
          geological: "", //地质情况
        },
        {
          poleCode: "线路1_1", //杆塔编号
          rodDesignation: "Z-M-12", //杆型代号
          rodheadDesignation: "Z1-2", //杆头代号
          barcondition: "锥形水泥杆", //杆段情况
          material: "水泥", //材质
          status: "新建", //状态
          landform: "平地", //地形
          geological: "普通土", //地质情况
        },
        {
          poleCode: "DA-M-12", //杆塔编号
          rodDesignation: "NJ1-4", //杆型代号
          rodheadDesignation: "锥形水泥杆", //杆头代号
          barcondition: "锥形水泥杆", //杆段情况
          material: "水泥", //材质
          status: "新建", //状态
          landform: "平地", //地形
          geological: "普通土", //地质情况
        },
      ],
      PoletowerColumns: [
        { porp: "poleCode", label: "杆塔编号" },
        { porp: "rodDesignation", label: "杆型代号" },
        { porp: "rodheadDesignation", label: "杆头代号" },
        { porp: "barcondition", label: "杆段情况" },
        { porp: "material", label: "材质" },
        { porp: "status", label: "状态" },
        { porp: "landform", label: "地形" },
        { porp: "geological", label: "地质情况" },
      ],
      /*杆塔明细表end*/
      /*应力弧垂表start*/
      tableHeader: [
        {
          label1: "",
          label2: "气温<%%dC>",
          label3: "风速<m/s>",
          label4: "覆冰<mm>",
          prop: "type",
        },
        {
          label1: "高温",
          label2: "40",
          label3: "0",
          label4: "0",
          prop: "gw",
        },
        {
          label1: "低温",
          label2: "-20",
          label3: "0",
          label4: "0",
          prop: "dw",
        },
        {
          label1: "安装",
          label2: "-10",
          label3: "0",
          label4: "10",
          prop: "az",
        },
        {
          label1: "外过",
          label2: "15",
          label3: "10",
          label4: "0",
          prop: "wg",
        },
        {
          label1: "内过",
          label2: "10",
          label3: "15",
          label4: "0",
          prop: "ng",
        },
        {
          label1: "大风",
          label2: "-5",
          label3: "30",
          label4: "0",
          prop: "df",
        },
        {
          label1: "覆冰",
          label2: "-5",
          label3: "15",
          label4: "20",
          prop: "fb",
        },
        {
          label1: "平均",
          label2: "10",
          label3: "0",
          label4: "0",
          prop: "pj",
        },
      ],
      tableDataYl: [
        {
          span: "30",
          type: "应力<MPa>",
          gw: "6.527",
          dw: "27.465",
          az: "18.316",
          wg: "9.217",
          ng: "10.72",
          df: "20.434",
          fb: "38.936",
          pj: "9.994",
        },
        {
          span: "30",
          type: "弧垂<m>",
          gw: "0.564",
          dw: "0.134",
          az: "0.204",
          wg: "0.406",
          ng: "0.373",
          df: "0.291",
          fb: "0.413",
          pj: "0.368",
        },

        {
          span: "35",
          type: "应力<MPa>",
          gw: "6.527",
          dw: "20.129",
          az: "14.808",
          wg: "9.183",
          ng: "10.447",
          df: "18.674",
          fb: "38.936",
          pj: "9.705",
        },
        {
          span: "35",
          type: "弧垂<m>",
          gw: "0.721",
          dw: "0.249",
          az: "0.344",
          wg: "0.555",
          ng: "0.521",
          df: "0.433",
          fb: "0.562",
          pj: "0.516",
        },
        {
          span: "40",
          type: "应力<MPa>",
          gw: "7.284",
          dw: "15.803",
          az: "12.925",
          wg: "9.159",
          ng: "10.268",
          df: "17.563",
          fb: "38.936",
          pj: "9.519",
        },
        {
          span: "40",
          type: "弧垂<m>",
          gw: "0.898",
          dw: "0.414",
          az: "0.515",
          wg: "0.727",
          ng: "0.692",
          df: "0.602",
          fb: "0.733",
          pj: "0.687",
        },

        {
          span: "45",
          type: "应力<MPa>",
          gw: "7.545",
          dw: "13.537",
          az: "11.851",
          wg: "9.143",
          ng: "10.146",
          df: "16.832",
          fb: "38.936",
          pj: "9.391",
        },
        {
          span: "45",
          type: "弧垂<m>",
          gw: "1.098",
          dw: "0.612",
          az: "0.711",
          wg: "0.922",
          ng: "0.886",
          df: "0.795",
          fb: "0.928",
          pj: "0.882",
        },
        {
          span: "50",
          type: "应力<MPa>",
          gw: "7.752",
          dw: "12.262",
          az: "11.185",
          wg: "9.131",
          ng: "10.058",
          df: "16.33",
          fb: "38.936",
          pj: "9.301",
        },
        {
          span: "50",
          type: "弧垂<m>",
          gw: "1.319",
          dw: "0.834",
          az: "0.93",
          wg: "1.139",
          ng: "1.104",
          df: "1.011",
          fb: "1.146",
          pj: "1.099",
        },

        {
          span: "55",
          type: "应力<MPa>",
          gw: "7.918",
          dw: "11.473",
          az: "10.74",
          wg: "9.122",
          ng: "9.993",
          df: "15.97",
          fb: "38.936",
          pj: "9.234",
        },
        {
          span: "55",
          type: "弧垂<m>",
          gw: "1.563",
          dw: "1.078",
          az: "1.172",
          wg: "1.38",
          ng: "1.344",
          df: "1.251",
          fb: "1.387",
          pj: "1.34",
        },
        {
          span: "60",
          type: "应力<MPa>",
          gw: "8.052",
          dw: "10.947",
          az: "10.428",
          wg: "9.115",
          ng: "9.944",
          df: "15.704",
          fb: "38.936",
          pj: "9.184",
        },
        {
          span: "60",
          type: "弧垂<m>",
          gw: "1.828",
          dw: "1.345",
          az: "1.437",
          wg: "1.644",
          ng: "1.608",
          df: "1.515",
          fb: "1.65",
          pj: "1.603",
        },

        {
          span: "65",
          type: "应力<MPa>",
          gw: "8.163",
          dw: "10.575",
          az: "10.198",
          wg: "9.11",
          ng: "9.906",
          df: "15.502",
          fb: "38.936",
          pj: "9.145",
        },
        {
          span: "65",
          type: "弧垂<m>",
          gw: "2.117",
          dw: "1.634",
          az: "1.724",
          wg: "1.93",
          ng: "1.894",
          df: "1.801",
          fb: "1.937",
          pj: "1.89",
        },
        {
          span: "70",
          type: "应力<MPa>",
          gw: "8.254",
          dw: "10.301",
          az: "10.024",
          wg: "9.105",
          ng: "9.876",
          df: "15.344",
          fb: "38.936",
          pj: "9.114",
        },
        {
          span: "70",
          type: "弧垂<m>",
          gw: "2.428",
          dw: "1.945",
          az: "2.034",
          wg: "2.239",
          ng: "2.203",
          df: "2.11",
          fb: "2.246",
          pj: "2.199",
        },

        {
          span: "75",
          type: "应力<MPa>",
          gw: "8.331",
          dw: "10.093",
          az: "9.889",
          wg: "9.102",
          ng: "9.851",
          df: "15.218",
          fb: "38.936",
          pj: "9.089",
        },
        {
          span: "75",
          type: "弧垂<m>",
          gw: "2.761",
          dw: "2.279",
          az: "2.367",
          wg: "2.572",
          ng: "2.536",
          df: "2.442",
          fb: "2.579",
          pj: "2.531",
        },
        {
          span: "80",
          type: "应力<MPa>",
          gw: "8.395",
          dw: "9.93",
          az: "9.781",
          wg: "9.099",
          ng: "9.831",
          df: "15.117",
          fb: "38.936",
          pj: "9.068",
        },
        {
          span: "80",
          type: "弧垂<m>",
          gw: "3.118",
          dw: "2.636",
          az: "2.723",
          wg: "2.927",
          ng: "2.891",
          df: "2.797",
          fb: "2.934",
          pj: "2.886",
        },

        {
          span: "85",
          type: "应力<MPa>",
          gw: "8.45",
          dw: "9.8",
          az: "9.694",
          wg: "9.097",
          ng: "9.815",
          df: "15.033",
          fb: "38.936",
          pj: "9.051",
        },
        {
          span: "85",
          type: "弧垂<m>",
          gw: "3.497",
          dw: "3.015",
          az: "3.101",
          wg: "3.305",
          ng: "3.269",
          df: "3.175",
          fb: "3.312",
          pj: "3.265",
        },
        {
          span: "90",
          type: "应力<MPa>",
          gw: "8.497",
          dw: "9.694",
          az: "9.623",
          wg: "9.095",
          ng: "9.801",
          df: "14.964",
          fb: "38.936",
          pj: "9.037",
        },
        {
          span: "90",
          type: "弧垂<m>",
          gw: "3.899",
          dw: "3.417",
          az: "3.503",
          wg: "3.706",
          ng: "3.67",
          df: "3.576",
          fb: "3.713",
          pj: "3.666",
        },

        {
          span: "95",
          type: "应力<MPa>",
          gw: "8.537",
          dw: "9.607",
          az: "9.563",
          wg: "9.093",
          ng: "9.789",
          df: "14.906",
          fb: "38.936",
          pj: "9.025",
        },
        {
          span: "95",
          type: "弧垂<m>",
          gw: "4.323",
          dw: "3.842",
          az: "3.927",
          wg: "4.13",
          ng: "4.094",
          df: "4",
          fb: "4.137",
          pj: "4.09",
        },
        {
          span: "100",
          type: "应力<MPa>",
          gw: "8.572",
          dw: "9.534",
          az: "9.513",
          wg: "9.091",
          ng: "9.779",
          df: "14.856",
          fb: "38.936",
          pj: "9.015",
        },
        {
          span: "100",
          type: "弧垂<m>",
          gw: "4.771",
          dw: "4.29",
          az: "4.374",
          wg: "4.577",
          ng: "4.541",
          df: "4.447",
          fb: "4.584",
          pj: "4.537",
        },

        {
          span: "105",
          type: "应力<MPa>",
          gw: "8.603",
          dw: "9.472",
          az: "9.47",
          wg: "9.09",
          ng: "9.77",
          df: "14.814",
          fb: "38.936",
          pj: "9.006",
        },
        {
          span: "105",
          type: "弧垂<m>",
          gw: "5.241",
          dw: "4.76",
          az: "4.845",
          wg: "5.047",
          ng: "5.011",
          df: "4.917",
          fb: "5.054",
          pj: "5.006",
        },
        {
          span: "110",
          type: "应力<MPa>",
          gw: "8.629",
          dw: "9.42",
          az: "9.434",
          wg: "9.089",
          ng: "9.763",
          df: "14.778",
          fb: "38.936",
          pj: "8.999",
        },
        {
          span: "110",
          type: "弧垂<m>",
          gw: "5.735",
          dw: "5.253",
          az: "5.338",
          wg: "5.54",
          ng: "5.504",
          df: "5.41",
          fb: "5.547",
          pj: "5.499",
        },

        {
          span: "115",
          type: "应力<MPa>",
          gw: "8.653",
          dw: "9.374",
          az: "9.402",
          wg: "9.088",
          ng: "9.756",
          df: "14.746",
          fb: "38.936",
          pj: "8.992",
        },
        {
          span: "115",
          type: "弧垂<m>",
          gw: "6.251",
          dw: "5.77",
          az: "5.853",
          wg: "6.056",
          ng: "6.019",
          df: "5.926",
          fb: "6.063",
          pj: "6.015",
        },
        {
          span: "120",
          type: "应力<MPa>",
          gw: "8.674",
          dw: "9.335",
          az: "9.375",
          wg: "9.087",
          ng: "9.751",
          df: "14.718",
          fb: "38.936",
          pj: "8.986",
        },
        {
          span: "120",
          type: "弧垂<m>",
          gw: "6.79",
          dw: "6.309",
          az: "6.392",
          wg: "6.594",
          ng: "6.558",
          df: "6.464",
          fb: "6.601",
          pj: "6.554",
        },
      ],
      isShowss: false,
      /*应力弧垂表end*/
      /*simsvg start*/
      showCIMSVG: false,
      simsvgData: [
        {
          deviceCode: "101", //设备代码
          deviceName: "导线", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "103", //设备代码
          deviceName: "杆塔", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "103001", //设备代码
          deviceName: "绝缘子", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "103002", //设备代码
          deviceName: "金具", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "103005", //设备代码
          deviceName: "拉线", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "110", //设备代码
          deviceName: "柱上变压器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "111", //设备代码
          deviceName: "柱上断路器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "112", //设备代码
          deviceName: "柱上负荷开关", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "113", //设备代码
          deviceName: "柱上隔离开关", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "114", //设备代码
          deviceName: "柱上重合器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "115", //设备代码
          deviceName: "柱上跌落式熔断器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "116", //设备代码
          deviceName: "线路避雷器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "118", //设备代码
          deviceName: "柱上电容器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "119", //设备代码
          deviceName: "柱上电压互感器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "120", //设备代码
          deviceName: "柱上电流互感器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "121", //设备代码
          deviceName: "柱上组合互感器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "122", //设备代码
          deviceName: "柱上分段器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "124", //设备代码
          deviceName: "自动调压器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "190", //设备代码
          deviceName: "附属设施", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "201", //设备代码
          deviceName: "电缆段", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "2010098", //设备代码
          deviceName: "中压电缆段拐点", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "202", //设备代码
          deviceName: "电缆终端", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "203", //设备代码
          deviceName: "电缆接头", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "204", //设备代码
          deviceName: "电缆分支箱", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "207", //设备代码
          deviceName: "电缆分界室", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "302", //设备代码
          deviceName: "配电变压器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "303", //设备代码
          deviceName: "所用变", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "305", //设备代码
          deviceName: "断路器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "306", //设备代码
          deviceName: "隔离开关", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "307", //设备代码
          deviceName: "负荷开关", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "309", //设备代码
          deviceName: "熔断器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "311", //设备代码
          deviceName: "母线", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "312", //设备代码
          deviceName: "电抗器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "313", //设备代码
          deviceName: "电流互感器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "314", //设备代码
          deviceName: "电压互感器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "315", //设备代码
          deviceName: "组合互感器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "316", //设备代码
          deviceName: "电力电容器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "318", //设备代码
          deviceName: "避雷器", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "322", //设备代码
          deviceName: "开关柜", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "323", //设备代码
          deviceName: "箱式变电站", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "324", //设备代码
          deviceName: "环网柜", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
        {
          deviceCode: "336", //设备代码
          deviceName: "站内电缆", //设备名称
          deviceType: "", //设备类型
          remark: "", //备注
        },
      ],
      simsvgColumns: [
        { porp: "deviceCode", label: "设备代码" },
        { porp: "deviceName", label: "设备名称" },
        { porp: "deviceType", label: "设备类型" },
        { porp: "remark", label: "备注" },
      ],

      /*simsvg end*/
      /*工程规模统计表start*/
      showProjectscale: false,
      ProjectscaleData: [
        {
          projectName: "", //项目名称
          owningProject: "", //所属项目包名称
          itemclassify: "", //项目分类
          constructionEffect: "", //建设成效
          projectDescription: "", //项目简述
          totalInvestment: "", //总投资（万元）
          voltageLevel: "", //电压等级（千伏)
          numberofcables: "", //中压架空电缆条数（条)
          Numberofbranches: "", //其中：分支条数（条）
          Length: "", //长度（千米）
          itemdefinition: "", //项目定义
        },
        {
          projectName: "", //项目名称
          owningProject: "", //所属项目包名称
          itemclassify: "", //项目分类
          constructionEffect: "", //建设成效
          projectDescription: "", //项目简述
          totalInvestment: "", //总投资（万元）
          voltageLevel: "", //电压等级（千伏)
          numberofcables: "1", //中压架空电缆条数（条)
          Numberofbranches: "1", //其中：分支条数（条）
          Length: "0.1", //长度（千米）
          itemdefinition: "B气象区", //项目定义
        },
      ],
      ProjectscaleColumns: [
        { porp: "projectName", label: "项目名称" },
        { porp: "owningProject", label: "所属项目包名称" },
        { porp: "itemclassify", label: "项目分类" },
        { porp: "constructionEffect", label: "建设成效" },
        { porp: "projectDescription", label: "项目简述" },
        { porp: "totalInvestment", label: "总投资（万元）" },
        { porp: "voltageLevel", label: "电压等级（千伏)" },
        { porp: "numberofcables", label: "中压架空电缆条数（条)" },
        { porp: "Numberofbranches", label: "其中：分支条数（条）)" },
        { porp: "Length", label: "长度（千米）" },
        { porp: "itemdefinition", label: "项目定义" },
      ],
      /*工程规模统计表end*/
      // 菜单数据
      meunbarData: [],
      /*自动匹配图纸、批量打印start*/
      titleStr: "",
      AutoMatchDrawing: false,
      tzData: [
        { drawName: "1500mm耐张横担加工图  (10kV)耐张横担" },
        { drawName: "1900mm耐张横担加工图（二） (10kV)耐张横担" },
        { drawName: "1900mm耐张横担加工图（一） (10kV)耐张横担" },
        { drawName: "DA-M-12（NJ1-4）单回45°～90拉线终端水泥单杆" },
        { drawName: "DA-M-12（NJ1-4）单回45°～90拉线终端水泥杆型一览图" },
        { drawName: "LX-8型单拉线布置示意图及配置表" },
        { drawName: "Z-M-12（Z1-2）单回直线水泥单杆杆型一览图" },
        { drawName: "Z-M-12（Z1-2）单回直线水泥单杆组装图" },
        { drawName: "绝缘线用10kV绝缘子串组装图  (10kV)金具组装" },
        { drawName: "拉线棒加工图  (10kV)金具组装" },
      ],
      /*自动匹配图纸、批量打印end*/
      /*成果上报 start*/
      reportResultShow: false,
      customColors: [
        { color: "#f56c6c", percentage: 30 },
        { color: "#409eff", percentage: 95 },
        { color: "#67c23a", percentage: 100 },
      ],
      reportResultData: [
        { type: "结构化数据生成", state: "未完成", progress: 0 },
        { type: "非结构化数据生成", state: "未完成", progress: 0 },
        { type: "架空线路标准报文生成", state: "未完成", progress: 0 },
        { type: "电缆线路标准报文生成", state: "未完成", progress: 0 },
        { type: "配电站房标准报文生成", state: "未完成", progress: 0 },
      ],
      /*成果上报 end*/
      /*差异化绘图管理start*/
      difRadio: "钢管杆",
      shaojingShow: true,
      differentiationShow: false,
      innerVisible: false, //内嵌
      differentiationData: [],
      loopNumberData: [
        { value: "单回", text: "单回" },
        { value: "双回", text: "双回" },
      ],
      gtTypeData: [
        { value: "直线", text: "直线" },
        { value: "耐张", text: "耐张" },
        { value: "转角", text: "转角" },
        { value: "终端", text: "终端" },
      ],
      towerHeightData: [],
      gggHeightData: [
        { value: "10m", text: "10m" },
        { value: "13m", text: "13m" },
      ],
      ttHeightData: [
        { value: "13m", text: "13m" },
        { value: "15m", text: "15m" },
        { value: "18m", text: "18m" },
      ],
      shaojingData: [
        { value: "270mm", text: "270mm" },
        { value: "310mm", text: "310mm" },
        { value: "350mm", text: "350mm" },
      ],
      modulesData: [],
      modulesTypeData: [
        { towerHeight: "10m", shaojing: "270mm", modules: "单回钢管杆GN27-10" },
        {
          towerHeight: "10m",
          shaojing: "270mm",
          modules: "单回终端钢管杆GN27-10",
        },
        { towerHeight: "10m", shaojing: "310mm", modules: "单回钢管杆GN31-10" },
        {
          towerHeight: "10m",
          shaojing: "310mm",
          modules: "单回终端钢管杆GN31-10",
        },
        { towerHeight: "10m", shaojing: "350mm", modules: "单回钢管杆GN35-10" },
        {
          towerHeight: "10m",
          shaojing: "350mm",
          modules: "单回终端钢管杆GN35-10",
        },
        { towerHeight: "13m", shaojing: "270mm", modules: "单回钢管杆GN27-13" },
        {
          towerHeight: "13m",
          shaojing: "270mm",
          modules: "单回终端钢管杆GN27-13",
        },
        { towerHeight: "13m", shaojing: "270mm", modules: "双回钢管杆GN27-13" },
        {
          towerHeight: "13m",
          shaojing: "270mm",
          modules: "双回终端钢管杆GN27-13",
        },
        { towerHeight: "13m", shaojing: "310mm", modules: "单回钢管杆GN31-13" },
        {
          towerHeight: "13m",
          shaojing: "310mm",
          modules: "单回终端钢管杆GN31-13",
        },
        { towerHeight: "13m", shaojing: "310mm", modules: "双回钢管杆GN31-13" },
        {
          towerHeight: "13m",
          shaojing: "310mm",
          modules: "双回终端钢管杆GN31-13",
        },
        { towerHeight: "13m", shaojing: "350mm", modules: "单回钢管杆GN35-13" },
        {
          towerHeight: "13m",
          shaojing: "350mm",
          modules: "单回终端钢管杆GN35-13",
        },
        { towerHeight: "13m", shaojing: "350mm", modules: "双回钢管杆GN35-13" },
        {
          towerHeight: "13m",
          shaojing: "350mm",
          modules: "双回终端钢管杆GN35-13",
        },
        { towerHeight: "13m", shaojing: "", modules: "ZJT-Z1-13" },
        { towerHeight: "13m", shaojing: "", modules: "ZJT-J1-13" },
        { towerHeight: "13m", shaojing: "", modules: "ZJT-SZ1-13" },
        { towerHeight: "13m", shaojing: "", modules: "ZJT-SJ1-13" },
        { towerHeight: "15m", shaojing: "", modules: "ZJT-Z1-15" },
        { towerHeight: "15m", shaojing: "", modules: "ZJT-J1-15" },
        { towerHeight: "15m", shaojing: "", modules: "ZJT-SZ1-15" },
        { towerHeight: "15m", shaojing: "", modules: "ZJT-SJ1-15" },
        { towerHeight: "18m", shaojing: "", modules: "ZJT-Z1-18" },
        { towerHeight: "18m", shaojing: "", modules: "ZJT-J1-18" },
        { towerHeight: "18m", shaojing: "", modules: "ZJT-SZ1-18" },
        { towerHeight: "18m", shaojing: "", modules: "ZJT-SJ1-18" },
      ],
      selectRow: [],
      /*差异化绘图管理end*/
      /*插入图例start*/
      drawLegend: false,
      legendData: [
        {
          legend: require("@/assets/resourceFile/LegendPicture/水泥杆新建.jpg"),
          legendName: "水泥杆新建",
          legend1: require("@/assets/resourceFile/LegendPicture/水泥杆原有.jpg"),
          legendName1: "水泥杆原有",
        },
        {
          legend: require("@/assets/resourceFile/LegendPicture/10kV单回架空线新建.jpg"),
          legendName: "10kV单回架空线新建",
          legend1: require("@/assets/resourceFile/LegendPicture/10kV单回架空线原有.jpg"),
          legendName1: "10kV单回架空线原有",
        },
        {
          legend: require("@/assets/resourceFile/LegendPicture/普通单拉线新建.jpg"),
          legendName: "普通单拉线新建.jpg",
          egend1: "",
          legendName1: "",
        },
      ],
      /*插入图例end*/
      defaultProps: {
        children: "children",
        label: "label",
      },
      leftTreeIsshow: true,
      mapsInsertImg: false, // 插入到地图的矩形边框大小
      mapsInsertZoom: false, // 插入到地图的比例尺大小
      mapsTzVisible: false, // 图纸目录
      /*数据刷新的Loading 加载*/
      fullscreenLoading: false,
      taskID: "475",
      kxid: this.$route.query.kxid,
      iframeUrl: "",
      watchLocal: null,
      isReloadTreeData: null,
      eventValue: "", //用于传给后端换取base64文件流
      screenShotHandler: undefined,
      imageBase64: [],
      operatingsvgShow: false, //运行态svg展示
      designSvgShow: false, //设计态svg展示
      cimShow: false, //cim文件展示
      cimXml: cimXml,
      projectNames: "",
    };
  },
  mounted() {
    // this.iframeUrl = window.wgParameter.iframeUrl;
    this.iframeUrl = process.env.VUE_APP_API_iframeUrl;
    // 顶部菜单栏
    topMenu().then((res) => {

      this.meunbarData = res.data.data;
      this.meunbarData[0].children.push({
        menuName:"主设备材料清单推送",
        menuId:'22'
      })
      console.log(this.meunbarData[0].children, "112131231");
    });
    this.taskID = this.$route.query.taskID;
    // 左侧深度菜单
    leftMenu(this.taskID).then((res) => {
      console.log(res.data.data, "参数");
      this.data.push(res.data.data);
      this.projectNames = res.data.data.label;
    });
    const that = this;
    window.addEventListener("message", function (event) {
      //父接收子数据 刷新传给iframe得值
      if (event.data == "hideIframe") {
        that.mapsType = -1;
      }
      if (event.data == "hidejjBjIframe") {
        that.qdjjComVisible = false;
        that.addLeftMeun("工程造价书_造价报表", 4);
      }
    });
  },
  methods: {
    // 点击左侧深度菜单
    toiframe() {
      const iframe = this.$refs.iframeref;
      iframe.contentWindow.postMessage(
        {
          childProjectId: this.taskID,
        },
        "*"
      );
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex % 4 === 0) {
          return {
            rowspan: 4,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },
    renderedHandler() {
      console.log("渲染完成");
    },
    errorHandler() {
      console.log("渲染失败");
    },
    handleSelecmapsxjppData(val) {
      this.selecmapsxjppData = val;
    },
    innerVisibleYes() {
      if (this.difRadio === "钢管杆") {
        this.shaojingShow = true;
      } else if (this.difRadio === "窄基塔") {
        this.shaojingShow = false;
      }
      this.towerHeightData = [];
      this.differentiationData = [];
      this.addDifData(true);
      this.innerVisible = false;
    },
    difRadioChange(value) {
      this.innerVisible = true;
    },
    deletedData() {
      this.selectRow.forEach((row) => {
        const index = this.differentiationData.indexOf(row);
        if (index !== -1) {
          this.differentiationData.splice(index, 1); // 删除该行数据
        }
      });
    },
    innerVisibleNo() {
      if (this.difRadio === "钢管杆") {
        this.difRadio = "窄基塔";
      } else if (this.difRadio === "窄基塔") {
        this.difRadio = "钢管杆";
      }
      this.innerVisible = false;
    },
    handleSelectionChange(val) {
      this.selectRow = val;
    },
    handleNodeClick(data) {
      console.log(data);
      if (data.label.includes(".")) {
        const arr = data.label.split(".");
        if (arr.length === 2 && (arr[1] === "pdf" || arr[1] === "dwg")) {
          this.browseUrl =
            window.wgParameter.pdfsUrls + "?file=" + arr[0] + ".pdf";
          this.isShowIframe = true;
        } else {
          if (arr[0] == "工程造价书_造价报表") {
            this.urlExcel = window.wgParameter.isNetwork
              ? "../web/工程造价书_造价报表.xlsx"
              : "web/工程造价书_造价报表.xlsx"; // window.wgParameter.pdfsUrls + '?file=' + arr[0] + ".xlsx";
            this.excelShow = true;
          } else {
            this.previewExcel(data.label.split('.')[0])
          }
        }
      }
    },
    increaseData() {
      this.addDifData(false);
    },
    closePdf() {
      this.browseUrl = "";
      this.isShowIframe = false;
    },
    savesimsvg() {
      console.log("保存");
      this.showCIMSVG = false;
      this.$message({
        message: "保存成功",
        type: "success",
      });
    }, // 选择图幅
    submitImgSize() {
      for (let j in this.insertMapInfo.insertImgSize) {
        if (
          this.insertMapInfo.insertImgVal ===
          this.insertMapInfo.insertImgSize[j].id
        ) {
          this.mapsImgSize = {
            width: this.insertMapInfo.insertImgSize[j].width,
            heiht: this.insertMapInfo.insertImgSize[j].hight,
          };
        }
      }
      this.mapsInsertImg = false;
      this.mapsType = 1;
    },
    submitMapZoom() {
      this.mapsInsertZoom = false;
      this.mapsType = 6;
    },
    // 推送材料数据
    pushMaterial(){
      let params={
        taskid:this.taskID
      }
      pushMaterialData(params).then(res=>{
        console.log(res.msg);
      })
    },
    // 导入
    handleCommandPro(val) {
      console.log(val.menuName);
      switch (val.menuName) {
         case "主设备材料清单推送":
          this.pushMaterial()
          break;
        case "主要设备材料清单":
          this.showMainequipment(val);
          break;
        case "技经提资":
          this.qdjjComVisible = true;
          sessionStorage.setItem("projectName", this.projectNames);
          break;
        case "自动匹配图纸":
        case "批量打印":
          this.showDrawDialog(val.menuName);
          break;
        case "数据刷新":
          this.DataRefresh();
          break;
        case "插入图例":
          this.DrawLegend();
          break;
        case "成果上报":
          this.reportResult();
          break;
        case "差异化绘图管理":
          this.differentiation();
          break;
        case "铁附件明细表":
          this.detailofIronShow(val);
          break;
        case "电缆清册":
          this.cabledetailShow(val);
          break;
        case "钢管杆明细表":
          this.steelpipeShow(val);
          break;
        case "ERP物资采购清册":
          this.ERPpsteelShow(val);
          break;
        case "杆塔明细表":
          this.PoletowerShow(val);
          break;
        case "应力弧垂表":
          this.StresssagShow(val);
          break;
        case "标准化信息管理":
          this.showResource = true;
          break;
        case "图纸信息管理":
          this.showDrawing = true;
          break;
        case "自动选型管理":
          this.showautoChange = true;
          break;
        case "CIM、SVG模型映射表配置":
          this.showCIMSVG = true;
          break;
        case "工程规模统计表":
          this.ProjectscaleShow(val);
          break;
        case "插入图框":
          this.mapsInsertImg = true;
          this.insertMapInfo.insertImgVal = "";
          break;
        case "插入图例":
          this.mapsType = 2;
          break;
        case "配电间隔管理":
          this.mapsType = 3;
          break;
        case "平面布置图设计":
          this.mapsType = 4;
          break;
        case "电气一次设计":
          this.mapsType = 5;
          break;
        case "修改比例":
          this.mapsInsertZoom = true;
          this.insertMapInfo.insertMapZoomVal = "";
          break;
        case "图纸目录":
          this.mapsTzVisible = true;
          break;
        case "标注管理":
          this.showTagManage();
          break;
        case "电缆附件统计表":
          this.accessoriesshow(val);
          break;
        case "利旧物资清册":
          this.supplieslistShow(val);
          break;
        case "基础一览图":
          this.showbasicMapData(val);
          break;
        case "杆型一览图":
          this.barTypeviewshow(val);
          break;
        case "拆旧物资清册":
          this.showOldInvent(val);
          break;
        case "基础参数设置":
          this.showBasic = true;
          break;
        case "格式刷":
          this.mapsType = 7;
          break;
        case "图纸分幅":
          this.printscreen();
          break;
        case "线夹匹配":
          this.mapsxjppVisible = true;
          break;
        case "编号重拍":
          this.mapsType = 8;
          break;
        case "运行态现状图沿布":
        case "移动勘测成果沿布":
          this.mapsType = 9;
          break;
        case "运行态svg展示":
          this.showOPSvg(val);
          break;
        case "设计态svg展示":
          console.log("设计态svg展示111");
          this.showdesignSvg(val);
          break;
        case "设计态cim查看":
          this.showCimXml(val);
          break;
        case "查看工程台账信息":
          this.mapsType = 10;
          break;
        case "保存":
          this.$message({
            showClose: true,
            message: "保存成功",
            type: "success",
          });
          break;
      }
    },
    removeSessProject() {
      // console.log("清空")
      // sessionStorage.removeItem()
    },
    showOPSvg(val) {
      console.log("运行态svg展示", val);
      this.operatingsvgShow = true;
    },
    showdesignSvg(val) {
      console.log("设计太", val);
      this.designSvgShow = true;
    },
    showCimXml(val) {
      this.cimShow = true;
      // axios.get("/cims/10kVcim.xml").then(res => {
      //   console.log(res.data, "cimmmm")
      //   this.cimXml = JSON.parse(JSON.stringify(res.data))
      // })
    },
    //无二级点击事件
    goDetail(val) {
      this.$router.push({
        path: this.$route.query.routerType,
      });
    },
    /*成果上报-页面*/
    reportResult() {
      this.reportResultData[0].progress = 0;
      this.reportResultData[1].progress = 0;
      this.reportResultData[2].progress = 0;
      this.reportResultData[3].progress = 0;
      this.reportResultData[4].progress = 0;
      this.reportResultData[0].state = "未完成";
      this.reportResultData[1].state = "未完成";
      this.reportResultData[2].state = "未完成";
      this.reportResultData[3].state = "未完成";
      this.reportResultData[4].state = "未完成";
      this.reportResultShow = true;
    },
    /*成果上报-提交 start*/
    reportResultSubmit() {
      this.pgJGHSJStart();
      this.pgFJGHSJStart();
      this.pgJKXLBZBWStart();
      this.pgDLXLBZBWStart();
      this.pgPDZFBZBWStart();
      let params = { id: this.taskID, kxid: this.kxid }
      generateMapJson(params).then(res => {
        console.log(res, "成果上报成功");
      })
      const state = this.reportResultData.every(e => e.progress === 100)
      const timer = setInterval(() => {

        if (this.reportResultData.every(e => e.progress === 100)) {
          this.reportResultShow=false
            this.$message({
            showClose: true,
            message: "上传成功",
            type: "success",
          });
          clearInterval(timer)
        }
      }, 500)
    },
    pgJGHSJStart() {
      if (this.reportResultData[0].progress === 0) {
        setTimeout(() => {
          this.reportResultData[0].progress += 20;
        }, 500);
      } else if (
        this.reportResultData[0].progress >= 100 ||
        this.reportResultData[0].progress + 20 >= 100
      ) {
        this.reportResultData[0].state = "提交成功";
        this.reportResultData[0].progress = 100;
      } else {
        this.reportResultData[0].progress += 20;
      }
      if (this.reportResultData[0].progress < 100) {
        setTimeout(() => {
          this.pgJGHSJStart();
        }, 500);
      }
    },
    pgFJGHSJStart() {
      if (this.reportResultData[1].progress === 0) {
        setTimeout(() => {
          this.reportResultData[1].progress += 20;
        }, 550);
      } else if (
        this.reportResultData[1].progress >= 100 ||
        this.reportResultData[1].progress + 20 >= 100
      ) {
        this.reportResultData[1].state = "提交成功";
        this.reportResultData[1].progress = 100;
      } else {
        this.reportResultData[1].progress += 20;
      }

      if (this.reportResultData[1].progress < 100) {
        setTimeout(() => {
          this.pgFJGHSJStart();
        }, 550);
      }
    },
    pgJKXLBZBWStart() {
      if (this.reportResultData[2].progress === 0) {
        setTimeout(() => {
          this.reportResultData[2].progress += 20;
        }, 250);
      } else if (
        this.reportResultData[2].progress >= 100 ||
        this.reportResultData[2].progress + 20 >= 100
      ) {
        this.reportResultData[2].state = "提交成功";
        this.reportResultData[2].progress = 100;
      } else {
        this.reportResultData[2].progress += 20;
      }

      if (this.reportResultData[2].progress < 100) {
        setTimeout(() => {
          this.pgJKXLBZBWStart();
        }, 250);
      }
    },
    pgDLXLBZBWStart() {
      if (this.reportResultData[3].progress === 0) {
        setTimeout(() => {
          this.reportResultData[3].progress += 20;
        }, 275);
      } else if (
        this.reportResultData[3].progress >= 100 ||
        this.reportResultData[3].progress + 20 >= 100
      ) {
        this.reportResultData[3].state = "提交成功";
        this.reportResultData[3].progress = 100;
      } else {
        this.reportResultData[3].progress += 20;
      }

      if (this.reportResultData[3].progress < 100) {
        setTimeout(() => {
          this.pgDLXLBZBWStart();
        }, 275);
      }
    },
    pgPDZFBZBWStart() {
      if (this.reportResultData[4].progress === 0) {
        setTimeout(() => {
          this.reportResultData[4].progress += 20;
        }, 240);
      } else if (
        this.reportResultData[4].progress >= 100 ||
        this.reportResultData[4].progress + 20 >= 100
      ) {
        this.reportResultData[4].state = "提交成功";
        this.reportResultData[4].progress = 100;
      } else {
        this.reportResultData[4].progress += 20;
      }

      if (this.reportResultData[4].progress < 100) {
        setTimeout(() => {
          this.pgPDZFBZBWStart();
        }, 240);
      }
    },
    /*成果上报-提交 end*/
    /*差异化绘图管理start*/
    differentiation() {
      this.differentiationData = [];
      this.addDifData(true);
      this.differentiationShow = true;
    },
    differentiationSave() {
      this.differentiationShow = false;
    },
    addDifData(boo) {
      if (this.difRadio === "钢管杆") {
        this.differentiationData.push({
          loopNumber: "单回",
          gtType: "直线",
          miniAngle: "",
          maxiAngle: "",
          towerHeight: "10m",
          shaojing: "270mm",
          modules: "单回钢管杆GN27-10",
        });
        if (boo) {
          this.towerHeightData.push(...this.gggHeightData);
          this.modulesData = this.modulesTypeData
            .filter(
              (item) => item.towerHeight === "10m" && item.shaojing === "270mm"
            )
            .map((item) => ({ text: item.modules, value: item.modules }));
        }
      } else if (this.difRadio === "窄基塔") {
        this.differentiationData.push({
          loopNumber: "单回",
          gtType: "直线",
          miniAngle: "",
          maxiAngle: "",
          towerHeight: "13m",
          shaojing: "",
          modules: "ZJT-Z1-13",
        });
        if (boo) {
          this.towerHeightData.push(...this.ttHeightData);
          this.modulesData = this.modulesTypeData
            .filter(
              (item) => item.towerHeight === "13m" && item.shaojing === ""
            )
            .map((item) => ({ text: item.modules, value: item.modules }));
        }
      }
    },
    modulesChange(row) {
      this.modulesData = this.modulesTypeData
        .filter(
          (item) =>
            item.towerHeight === row.towerHeight &&
            item.shaojing === row.shaojing
        )
        .map((item) => ({ text: item.modules, value: item.modules }));
    },
    /*差异化绘图管理end*/
    showMainequipment(val) {
      this.dialogVisible = true;
      this.eventValue = val.eventValue;
      // this.loadingMainMetrils = true;
      // getMeterilsData(this.taskID).then((res) => {
      //   this.gridData = res.data.result;
      //   this.loadingMainMetrils = false;
      // });
    },
    showOldInvent(val) {
      this.oldInventoryVisible = true;
      console.log("显示设备拆旧清单");
      this.eventValue = val.eventValue;
    },
    showbasicMapData(val) {
      console.log("显示基础一览图");
      this.basicMapDataShow = true;
      this.eventValue = val.eventValue;
    },
    supplieslistShow(val) {
      console.log("利旧物资清册");
      this.showsupplieslist = true;
      this.eventValue = val.eventValue;
    },
    barTypeviewshow(val) {
      console.log("杆型一览图");
      this.showbarTypeview = true;
      this.eventValue = val.eventValue;
    },
    accessoriesshow(val) {
      console.log("电缆附属设施统计表");
      this.showaccessories = true;
      this.eventValue = val.eventValue;
    },
    detailofIronShow(val) {
      console.log("铁附件明细", val.eventValue);
      this.showdetailofIron = true;
      this.eventValue = val.eventValue;
    },
    cabledetailShow(val) {
      console.log("电缆明细表", val.eventValue);
      this.eventValue = val.eventValue;
      this.showCabledetail = true;
    },
    steelpipeShow(val) {
      console.log("钢管杆明细表", val.eventValue);
      this.eventValue = val.eventValue;
      this.showSteelpipe = true;
    },
    ERPpsteelShow(val) {
      console.log("ERP物资采购清册", val.eventValue);

      this.eventValue = val.eventValue;
      this.showERPpsteel = true;
    },
    PoletowerShow(val) {
      console.log("杆塔明细表", val.eventValue);
      this.eventValue = val.eventValue;
      this.showPoletower = true;
    },
    StresssagShow(val) {
      console.log("应力弧垂表", val.eventValue);
      this.eventValue = val.eventValue;
      this.isShowss = true;
    },
    ProjectscaleShow(val) {
      this.eventValue = val.eventValue;
      //工程规模统计表

      leftMenu(this.taskID).then((res) => {
        console.log(res.data.data.label, "参数");
        this.showProjectscale = true;
        this.ProjectscaleData[0].itemclassify = res.data.data.label;
        this.ProjectscaleData[1].itemclassify = res.data.data.label;
        this.ProjectscaleData[1].constructionEffect = res.data.data.label;
      });
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch((_) => { });
    },
    derivedInfo() {
      // saveMeterilsData(this.taskID).then((res) => {
      //   if (res.status === 200) {
      //     // 导出设备材料
      //     this.dialogVisible = false;
      //     this.addLeftMeun(this.eventValue, "1");
      //   }
      // });
       this.dialogVisible = false;
          this.addLeftMeun(this.eventValue, "1");
    },
    derivedOldInvent() {
      // 导出拆旧清册
      this.oldInventoryVisible = false;
      this.addLeftMeun(this.eventValue, "1");
    },
    derivedbasicMapData() {
      //基础览图导出
      this.basicMapDataShow = false;
      this.addLeftMeun(this.eventValue, "2");
    },
    derivedsupplieslist() {
      // 利旧物资表导出
      this.showsupplieslist = false;
      this.addLeftMeun(this.eventValue, "3");
    },
    derivedbarTypeview() {
      //杆型一览图导出
      this.showbarTypeview = false;
      this.addLeftMeun(this.eventValue, "2");
    },
    derivedaccessories() {
      //电缆附属设施统计表
      this.showaccessories = false;
      this.addLeftMeun(this.eventValue, "3");
    },
    deriveddetailofIron() {
      //导出铁附件明细
      this.showdetailofIron = false;
      this.addLeftMeun(this.eventValue, "3");
    },
    derivedcabledetail() {
      //导出电缆明细表
      this.showCabledetail = false;
      this.addLeftMeun(this.eventValue, "1");
    },
    derivedsteelpipe() {
      //导出钢管杆明细表
      this.showSteelpipe = false;
      this.addLeftMeun(this.eventValue, "3");
    },
    derivedERPpsteel() {
      //ERP物资采购清册
      this.showERPpsteel = false;
      this.addLeftMeun(this.eventValue, "3");
    },
    derivedPoletower() {
      //导出杆塔明细表
      this.showPoletower = false;
      this.addLeftMeun(this.eventValue, "2");
    },
    derivedStresssag() {
      //导出应力弧垂表
      this.isShowss = false;
      this.addLeftMeun(this.eventValue, "3");
    },
    derivedProjectscale() {
      //工程规模统计表
      this.showProjectscale = false;
      this.addLeftMeun(this.eventValue, "3");
    },

    // 判断添加到那个目录下
    /***
     *  menuName文件名称
     * type判断添加到那个目录下
     * type=1 设备材料清单 2 电气设计图纸 3 附件
     */
    addLeftMeun(menuName, type) {
      console.log(menuName, "dkdk");
      updateIsShow(menuName).then((res) => {
        console.log(res, "dddddd");
        leftMenu(this.taskID).then((res) => {
          console.log(res.data.data, "参数");
          this.data = [];
          this.data.push(res.data.data);
          // this.data[0].children[0].children.forEach((item, index) => {
          //   if (type == "3" && item.label == "附件") {
          //     item.children.push({
          //       id: new Date().getTime(),
          //       label: menuName + ".xls",
          //       children: [],
          //     });
          //   } else if (type == "2" && item.label == "电气设计图纸") {
          //     if (!item.children[0]?.label.includes(menuName)) {

          //     }
          //   } else if (type == "1" && item.label == "设备材料清单") {
          //     if (!item.children[0]?.label.includes(menuName)) {
          //       item.children.push({
          //         id: new Date().getTime(),
          //         label: menuName + ".xls",
          //         children: [],
          //       });
          //     }
          //   } else if (type == "4" && item.label == "工程造价") {
          //     if (!item.children[0]?.label.includes(menuName)) {
          //       item.children.push({
          //         id: new Date().getTime(),
          //         label: menuName + ".xlsx",
          //         children: [],
          //       });
          //     }
          //   }
          // });
        });
      });
    },
    //预览excel方法
    previewExcel(value) {
      console.log("value", value);
      // wacthSbExcelFile(value).then((res) => {
      //   let blob = new Blob([res.data], { type: "application/xls" }); //文件流处理
      //   this.urlExcel = window.URL.createObjectURL(res.data);
      //   this.excelShow = true;
      // });

      selectExcelFile(value).then((res) => {
        this.urlExcel = base64ToBlob(res.data.data[0].fileFlow)
        this.excelShow = true
      })
    },
    /*自动匹配图纸、批量打印-页面*/
    showDrawDialog(str) {
      this.titleStr = str;
      this.AutoMatchDrawing = true;
    },
    /*自动匹配图纸、批量打印-保存到成果目录*/
    SaveToDirectory() {
      let suffix = "";
      if (this.titleStr === "自动匹配图纸") {
        suffix = ".pdf";
      } else if (this.titleStr === "批量打印") {
        suffix = ".dwg";
      }
      for (let i = 0; i < this.data[0].children[0].children.length; i++) {
        const item = this.data[0].children[0].children[i];
        if (item.label === "附件") {
          for (let j = 0; j < this.tzData.length; j++) {
            if (
              item.children
                .map((obj) => obj.label)
                .indexOf(this.tzData[j].drawName + suffix) === -1
            ) {
              item.children.push({
                id: new Date().getTime(),
                label: this.tzData[j].drawName + suffix,
                children: [],
              });
            }
          }
        }
      }
      this.AutoMatchDrawing = false;
    },
    SaveToMapCg() {
      this.mapsTzVisible = false;
      const obj = {
        children: [
          {
            label: "图纸目录.pdf",
          },
        ],
        label: "图纸目录",
      };
      this.data[0].children[0].children.push(obj);
    },
    /*数据刷新录*/
    DataRefresh() {
      this.fullscreenLoading = true;
      setTimeout(() => {
        this.fullscreenLoading = false;
      }, 2000);
    },
    /*插入图例*/
    DrawLegend() {
      this.drawLegend = true;
    },
    showTagManage() {
      this.tagManageVisible = true;
    },
    tagTreeclick(tags) {
      this.tagData = tags;
    },

    //开始截图
    printscreen() {
      this.imageBase64 = [];
      // eslint-disable-next-line no-unused-vars
      var timer = setTimeout(() => {
        // eslint-disable-next-line no-unused-vars
        this.screenShotHandler = new ScreenShort(
          {
            enableWebRtc: true, //是否显示选项框
            level: 999999999, //层级级别
            completeCallback: this.callback,
            saveCompleteCallback: this.saveCallback,
            closeCallback: this.closeFn,
            hiddenToolIco: {
              square: true,
              round: true,
              rightTop: true,
              brush: true,
              mosaicPen: true,
              text: true,
              separateLine: true,
              undo: true,
            },
          },
          0
        );
      });
    },
    saveCallback(base64data) {
      this.imageBase64.push(base64data.base64);
    },
    /**
     * 生成图片
     * 截图确认按钮回调函数
     */
    callback(base64data) {
      this.imageBase64.forEach((element) => {
        let _this = this;
        var image = document.createElement("img");
        image.onload = () => {
          _this.convertToBlob(image);
        };
        image.src = element;
      });
    },
    closeFn() {
      this.screenShotHandler.destroyComponents();
    },
    convertToBlob(img) {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      // 调整画布大小与图片相同
      canvas.width = img.naturalWidth;
      canvas.height = img.naturalHeight;
      // 将图片绘制到画布上
      ctx.drawImage(img, 0, 0);
      // 获取Blob对象
      const blob = canvas.toBlob((blob) => {
        if (typeof window !== "undefined" && typeof URL !== "undefined") {
          // 生成临时URL链接
          var link = document.createElement("a");
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "image.png";
          link.click();
        } else {
          console.error("Your browser does not support Blob URLs");
        }
      });
    },
    handleDelete(index, row) {
      this.simsvgData.splice(index, 1);
    },
    saveBasicParame() {
      this.showBasic = false;
      this.$message({
        message: "保存成功",
        type: "success",
      });
    },
    previewXml() {
      axios.get("/cims/10kV五中线_cim.xml").then((res) => {
        console.log(res, "cimmmm");
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-tree {
  padding-top: 10px;
  padding-bottom: 10px;
  .el-tree-node__expand-icon.expanded {
    color: #333333;
  }
  .el-tree-node__content {
    margin-bottom: 10px;
    height: unset !important;
  }
}
::v-deep .el-link.el-link--primary {
  color: #526ade;
  font-size: 16px;
  margin-left: 15px;
}
.closeBtn {
  display: flex;
  align-items: center;
  position: absolute;
  right: 20px;
  color: #526ade;
  cursor: pointer;
  img {
    width: 16px;
    height: 16px;
    border-radius: 20px;
    margin-right: 5px;
  }
}
.closePdf {
  position: absolute;
  top: 67px;
  right: 190px;
  cursor: pointer;
  color: red;
}
.onlineDeg {
  background: #ebebeb;
  box-sizing: border-box;
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  .onlineDegmainBox {
    display: flex;
    width: 100%;
    height: 100vh;
    background-color: #ebebeb;
    .leftTree {
      width: 17vw !important;
      box-sizing: border-box;
      height: calc(100vh - 20px);
      padding: 10px;
      ::v-deep .el-tree {
        height: 100%;
        overflow-y: scroll;
      }
    }
    .rightMap {
      box-sizing: border-box;
      flex: 1;
      display: flex;
      flex-direction: column;
      background: #ebebeb;
      .topNav {
        display: flex;
        height: 60px;
        line-height: 60px;
        align-items: center;
        & > * {
          margin: 0 10px;
        }
      }
      .mapBox {
        flex: 1;
        box-sizing: border-box;
        border: 1px solid #ccc;
      }
    }
  }
}
.eltreeParents-childLeft {
  display: flex;
  align-items: flex-start;
  img {
    width: 14px;
    line-height: 18px !important;
  }
  span {
    margin-left: 4px;
    white-space: break-spaces;
    line-height: 18px;
    font-size: 14px;
    color: #333333;
    font-family: Source Han Sans CN, Source Han Sans CN;
  }
}
::v-deep .jjDialog {
  .el-dialog__body {
    height: 80vh;
  }
}
.table-html-wrap {
  width: 100%;
  height: 680px;
  overflow: scroll;
  ::v-deep(table) {
    border-collapse: collapse;
    tr {
      &:first-child {
        td {
          &:first-child {
            background-color: #fff;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
          }
        }
      }
      &:nth-child(2) {
        background-color: #fff;
        color: #000;
      }
      &:nth-child(3) {
        background-color: #fff;
        td {
          &:nth-child(9) {
            text-align: center;
          }
          &:nth-child(9),
          &:nth-child(10) {
            color: #000;
          }
        }
      }
      td {
        border: 1px solid #000000;
        white-space: wrap;
        text-align: left;
        min-width: 150px;
        height: 30px;
        padding: 4px;
      }
    }
  }
}
.eltree-image {
  line-height: 20px;
}
::deep .eltreeParents-childLeft {
  line-height: 25px;
}
.cimsBox {
  width: 100%;
}
.pageFooter {
  display: flex;
  height: 3rem;
  align-items: center;
  justify-content: right;
}
::v-deep .el-dialog__body {
  padding: 24px !important;
}
::v-deep .el-dialog__footer {
  padding: 16px 24px !important;
}
</style>
