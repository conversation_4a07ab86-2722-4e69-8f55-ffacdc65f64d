<template>
  <div class="query-form-box">
    <el-form :inline="true" ref="form" :model="form" label-width="120px">
      <el-row>
        <el-col :span="5">
          <el-form-item label="版本信息:" prop="">
            <el-select v-model="form.selectVersion" placeholder="请选择">
              <el-option
                v-for="item in version"
                :key="item.Id"
                :label="item.Name"
                :value="item.Id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="driver"></div>
    <div class="cp-tableInfo">
      <div class="cp-btns">
        <div class="cp-handleBtn" @click="handleClick">
          <span class="cp-icons">
            <i class="iconfont icon-tianjiaxinxi" />
          </span>
          <span class="cp-texts">
            <img
              style="width: 16px; height: 16px"
              :src="require('@/assets/main/mantAgree.png')"
              alt=""
            />
            <span style="margin-left: 5px"> 导入 </span>
          </span>
        </div>
      </div>
      <div class="cp-btns">
        <div class="cp-handleBtn" @click="handleClick">
          <span class="cp-icons">
            <i class="iconfont icon-tianjiaxinxi" />
          </span>
          <span class="cp-texts">
            <img
              style="width: 16px; height: 16px"
              :src="require('@/assets/main/mantAgree.png')"
              alt=""
            />
            <span style="margin-left: 5px"> 导出 </span>
          </span>
        </div>
      </div>
      <div class="cp-btns">
        <div class="cp-handleBtn" @click="handleClick">
          <span class="cp-icons">
            <i class="iconfont icon-tianjiaxinxi" />
          </span>
          <span class="cp-texts">
            <img
              style="width: 16px; height: 16px"
              :src="require('@/assets/main/属性.png')"
              alt=""
            />
            <span style="margin-left: 5px"> 批量设计属性 </span>
          </span>
        </div>
      </div>
      <div class="cp-btns">
        <div class="cp-handleBtn" @click="handleClick">
          <span class="cp-icons">
            <i class="iconfont icon-tianjiaxinxi" />
          </span>
          <span class="cp-texts">
            <img
              style="width: 16px; height: 16px"
              :src="require('@/assets/main/属性.png')"
              alt=""
            />
            <span style="margin-left: 5px"> 批量技经属性 </span>
          </span>
        </div>
      </div>
      <div class="cp-btns">
        <div class="cp-handleBtn" @click="handleClick">
          <span class="cp-icons">
            <i class="iconfont icon-tianjiaxinxi" />
          </span>
          <span class="cp-texts">
            <img
              style="width: 16px; height: 16px"
              :src="require('@/assets/main/曲线.png')"
              alt=""
            />
            <span style="margin-left: 5px"> 查看价格曲线 </span>
          </span>
        </div>
      </div>
      <div class="cp-btns">
        <div class="cp-handleBtn" @click="handleClick">
          <span class="cp-icons">
            <i class="iconfont icon-tianjiaxinxi" />
          </span>
          <span class="cp-texts">
            <img
              style="width: 16px; height: 16px"
              :src="require('@/assets/main/刷新.png')"
              alt=""
            />
            <span style="margin-left: 5px"> 刷新价格 </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: '',
  data() {
    return {
      form: {
        selectVersion: 1,
      },
      version: [
        { Id: 1, Name: '20221024' },
        { Id: 2, Name: '20220915' },
      ],
    }
  },
  methods: {
    handleClick() {},
  },
}
</script>
<style lang="scss" scoped>
.cp-tableInfo {
  display: flex;
  background: #ffffff;
  .cp-checkNav {
    width: 400px;
    background: #f5f6fa;
    padding-left: 32px;
    height: 36px;
  }
  .cp-btns {
    background: #ffffff;
    margin: 0px 0 16px 16px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-left: 12px;
    color: #7286e8;
    .cp-handleBtn {
      height: 26px;
      font-size: 12px;
      display: flex;
      color: #333333;
      background: #ffffff;
      border-radius: 5px;
      padding: 0 12px;
      align-items: center;
      cursor: pointer;
      border: 1px solid #abbdf7;
    }
    .cp-handleBtn:nth-child(2) {
      margin-left: 10px;
    }
    .cp-drawer {
      margin: 0 10px;
      color: #ccc;
    }

    .cp-handleUnBtn:hover {
      color: #7487e4;
      border-color: #7487e4;
    }

    .cp-handleUnBtn:active {
      color: #0c6b66;
      border-color: #0c6b66;
    }

    .cp-handleUnBtn:focus {
      color: #0c6b66;
      border-color: #0c6b66;
    }

    .cp-handleBtn {
      height: 26px;
      font-size: 12px;
      display: flex;
      color: #333333;
      background: #ffffff;
      border-radius: 5px;
      padding: 0 12px;
      align-items: center;
      cursor: pointer;
      border: 1px solid #abbdf7;
      .cp-icons {
        margin-right: 4px;

        .iconfont {
          font-size: 12px !important;
        }
      }

      .cp-texts {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        /*padding: 0 5px;*/
      }
    }

    .cp-handleUnBtn {
      height: 26px;
      font-size: 12px;
      display: flex;
      border-radius: 20px;
      border: 1px solid #7487e4;
      align-items: center;
      background-color: #fff;
      padding: 0 12px;
      cursor: pointer;
      color: #7487e4;

      .cp-icons {
        margin-right: 2px;

        .iconfont {
          font-size: 12px !important;
        }
      }
    }
  }
}
</style>
