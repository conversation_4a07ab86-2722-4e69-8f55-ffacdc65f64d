<template>
  <div>
    <!--配电站房-->
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div v-if="showBackAdds" class="maps-zhedNav" @click="backCurrentDom">
            <img
              class="mapites-backImg"
              :src="require('@/assets/' + 'map/settingImg/backAdds.png')"
              alt=""
            />
          </div>
          <span />
          配电站房
          <div
            v-if="!showBackAdds"
            class="maps-zhedNav"
            @click="showEveryItemSet"
          >
            <img
              v-show="!isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zhedie.png')"
              alt=""
            />
            <img
              v-show="isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zkzhedie.png')"
              alt=""
            />
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img
            class="settingImg"
            :src="require('@/assets/' + 'map/settingImg/useSetting.png')"
            alt=""
          />
          <p>启用</p>
        </div>
      </div>
      <div v-show="isFoldArea" class="pro-addForm">
        <div class="map-showNav">基本信息</div>
        <van-row>
          <van-field
            v-model="electrical.mark"
            label="编号"
            placeholder="请输入配电站房编号"
          />
        </van-row>
        <div class="map-showNav">选型信息</div>
        <van-row>
          <!--配电站房类别-->
          <van-field
            readonly
            clickable
            :value="electrical.planType"
            label="站房类别"
            placeholder="请选择站房类别"
            @click="settingObj.electrical.planType = true"
          />
          <van-popup
            v-model="settingObj.electrical.planType"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="站房类别"
              value-key="moduletypename"
              :columns="electricalPlanType"
              @confirm="onConfirmPdzfSel(0, '', $event)"
              @cancel="settingObj.electrical.planType = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--配电站房方案类别-->
          <van-field
            readonly
            clickable
            :value="electrical.scheme"
            label="方案类别"
            placeholder="方案类别"
            @click="settingObj.electrical.scheme = true"
          />
          <van-popup
            v-model="settingObj.electrical.scheme"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="方案类别"
              value-key="moduleName"
              :columns="electricalScheme"
              @confirm="onConfirmPdzfSel(1, '', $event)"
              @cancel="settingObj.electrical.scheme = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--配电站房状态-->
          <van-field
            readonly
            clickable
            :value="electrical.state"
            label="站房状态"
            placeholder="请选择站房状态"
            @click="settingObj.electrical.state = true"
          />
          <van-popup
            v-model="settingObj.electrical.state"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="站房状态"
              value-key="key"
              :columns="cableLineState"
              @confirm="onConfirmPdzfSel(2, '', $event)"
              @cancel="settingObj.electrical.state = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--站房编号-->
          <van-field
            v-model="electrical.electNum"
            label="站房编号"
            placeholder="请输入站房编号"
          />
        </van-row>
        <!--站房型号-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="electrical.electModel"
            label="站房型号"
            placeholder="请选择站房型号"
            @click="settingObj.electrical.electModel = true"
          />
          <van-popup
            v-model="settingObj.electrical.electModel"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="站房型号"
              value-key="moduleName"
              :columns="pdzfzfxh"
              @confirm="onConfirmPdzfSel(3, '', $event)"
              @cancel="settingObj.electrical.electModel = false"
            />
          </van-popup>
        </van-row>
        <!--通道类别-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="electrical.aisleType"
            label="通道类别"
            placeholder="请选择通道类别"
            @click="settingObj.electrical.aisleType = true"
          />
          <van-popup
            v-model="settingObj.electrical.aisleType"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="通道类别"
              value-key="value"
              :columns="tdlaying"
              @confirm="onConfirmPdzfSel(4, '', $event)"
              @cancel="settingObj.electrical.aisleType = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--电压等级-->
          <van-field
            readonly
            clickable
            :value="electrical.voltage"
            label="电压等级"
            placeholder="请选择电压等级"
            @click="settingObj.electrical.voltage = true"
          />
          <van-popup
            v-model="settingObj.electrical.voltage"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="电压等级"
              value-key="key"
              :columns="voltage"
              @confirm="onConfirmPdzfSel(6, '', $event)"
              @cancel="settingObj.electrical.voltage = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--破路面类型-->
          <van-field
            readonly
            clickable
            :value="electrical.plmType"
            label="破路面类型"
            placeholder="请选择破路面类型"
            @click="settingObj.electrical.plmType = true"
          />
          <van-popup
            v-model="settingObj.electrical.plmType"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="破路面类型"
              value-key="key"
              :columns="plmType"
              @confirm="onConfirmPdzfSel(7, '', $event)"
              @cancel="settingObj.electrical.plmType = false"
            />
          </van-popup>
        </van-row>
        <!--电缆线路电缆设置-->
        <van-row>
          <div class="pro-addTitle">
            电缆设置
            <van-icon
              name="add-o"
              size="14"
              :style="{ marginLeft: '0.8rem', top: '0.7rem' }"
              @click="addCableItem"
            >
              添加
            </van-icon>
          </div>
          <div :style="{ width: '100%', overflowX: 'scroll' }">
            <table
              border="1"
              cellspacing="0"
              class="map-table"
              cellpadding="0"
              align="center"
            >
              <tr>
                <th :style="{ width: '5rem' }">线路名称</th>
                <th :style="{ width: '12rem' }">电缆型号</th>
                <th :style="{ width: '14rem' }">通道型号</th>
                <th :style="{ width: '12rem' }">终端头型号</th>
                <th :style="{ width: '12rem' }">中间头型号</th>
                <th :style="{ width: '5rem' }">状态</th>
                <th :style="{ width: '5rem' }">终端头</th>
                <th :style="{ width: '5rem' }">中间头</th>
                <th :style="{ width: '5rem' }">编辑</th>
              </tr>
              <tr v-for="(item, index) in electrical.listArr">
                <!--电缆设置-->
                <td>
                  <van-field
                    v-model="item.lineName"
                    placeholder="请输入电路名称"
                  />
                </td>
                <td>
                  <!--电缆型号-->
                  <van-field
                    readonly
                    clickable
                    :value="item.lineModel"
                    class="map-dlxh"
                    placeholder="电缆型号"
                    @click="
                      settingObj.electrical.listArr[index].lineModel = true
                    "
                  />
                  <van-popup
                    v-model="settingObj.electrical.listArr[index].lineModel"
                    round
                    position="bottom"
                  >
                    <van-picker
                      show-toolbar
                      title="电缆型号"
                      value-key="name"
                      :columns="pdzfModel"
                      @confirm="onConfirmPdzfSel(10, index, $event)"
                      @cancel="
                        settingObj.electrical.listArr[index].lineModel = false
                      "
                    />
                  </van-popup>
                </td>
                <td>
                  <!--通道型号-->
                  <van-field
                    readonly
                    clickable
                    class="map-dlxh"
                    :value="item.tdTypeSpec"
                    placeholder="请选择通道型号"
                    @click="settingObj.electrical.listArr[index].tdType = true"
                  />
                  <van-popup
                    v-model="settingObj.electrical.listArr[index].tdType"
                    round
                    position="bottom"
                  >
                    <van-picker
                      show-toolbar
                      title="通道型号"
                      value-key="moduleName"
                      :columns="pdzftdxh"
                      @confirm="onConfirmPdzfSel(5, index, $event)"
                      @cancel="
                        settingObj.electrical.listArr[index].tdType = false
                      "
                    />
                  </van-popup>
                </td>
                <!--终端头型号-->
                <td>
                  <van-field
                    readonly
                    clickable
                    class="map-dlxh"
                    :value="item.zdtModel"
                    placeholder="终端头型号"
                    @click="
                      settingObj.electrical.listArr[index].zdtModel = true
                    "
                  />
                  <van-popup
                    v-model="settingObj.electrical.listArr[index].zdtModel"
                    round
                    position="bottom"
                  >
                    <van-picker
                      show-toolbar
                      title="终端头型号"
                      value-key="moduleName"
                      :columns="pdzfzdtList"
                      @confirm="onConfirmPdzfSel(9, index, $event)"
                      @cancel="
                        settingObj.electrical.listArr[index].zdtModel = false
                      "
                    />
                  </van-popup>
                </td>
                <!--中间头型号-->
                <td>
                  <van-field
                    readonly
                    clickable
                    class="map-dlxh"
                    :value="item.zjtModel"
                    placeholder="中间头型号"
                    @click="
                      settingObj.electrical.listArr[index].zjtModel = true
                    "
                  />
                  <van-popup
                    v-model="settingObj.electrical.listArr[index].zjtModel"
                    round
                    position="bottom"
                  >
                    <van-picker
                      show-toolbar
                      title="中间头型号"
                      value-key="moduleName"
                      :columns="pdzfzjtList"
                      @confirm="onConfirmPdzfSel(8, index, $event)"
                      @cancel="
                        settingObj.electrical.listArr[index].zjtModel = false
                      "
                    />
                  </van-popup>
                </td>
                <!--电缆状态-->
                <td>
                  <van-field
                    readonly
                    clickable
                    class="map-dlxh"
                    :value="item.lineState"
                    placeholder="状态"
                    @click="
                      settingObj.electrical.listArr[index].lineState = true
                    "
                  />
                  <van-popup
                    v-model="settingObj.electrical.listArr[index].lineState"
                    round
                    position="bottom"
                  >
                    <van-picker
                      show-toolbar
                      title="状态"
                      value-key="key"
                      :columns="cableLineState"
                      @confirm="onConfirmPdzfSel(11, index, $event)"
                      @cancel="
                        settingObj.electrical.listArr[index].lineState = false
                      "
                    />
                  </van-popup>
                </td>
                <!--终端头-->
                <td :style="{ paddingLeft: '1.8rem' }">
                  <van-checkbox v-model="item.terminal" />
                </td>
                <!--中间头-->
                <td :style="{ textAlign: 'center' }">
                  <van-field v-model="item.middle" placeholder="中间头" />
                </td>
                <td :style="{ textAlign: 'center', color: 'red' }">
                  <span @click="removeMainLine(index)">删除</span>
                </td>
              </tr>
            </table>
          </div>
        </van-row>
        <div v-if="isShowNav" class="map-showNav">其他信息</div>
        <van-row v-if="isShowNav">
          <!--经度-->
          <van-field v-model="lngtitude" label="经度" disabled />
        </van-row>
        <van-row v-if="isShowNav">
          <!--纬度-->
          <van-field v-model="lattitude" label="纬度" disabled />
        </van-row>
        <van-row v-if="isShowNav">
          <!--高程-->
          <van-field v-model="highNum" label="高程" disabled />
        </van-row>
        <van-row>
          <van-field
            v-model="electrical.loactionMsg"
            rows="1"
            autosize
            label="安装位置"
            type="textarea"
            placeholder="请输入安装位置"
          />
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { apiget,apipost } from "@/utils/mapRequest";
import { Toast } from "vant";
export default {
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false,
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的id
    editData: {
      type: Object,
      defaults: () => {},
    },
    // 通道型号
    tdxhList: {
      type: Array,
      defaults: () => [],
    },
    // 电缆线路
    dlxlList: {
      type: Array,
      defaults: () => [],
    },
    // 通道类别
    tdlbList: {
      type: Array,
      defaults: () => [],
    },
    // 中间头型号
    zjtTypeList: {
      type: Array,
      defaults: () => [],
    },
    // 终端头型号
    zdtTypeList: {
      type: Array,
      defaults: () => [],
    },
    remodeState: {
      type: Boolean,
      defaults: false,
    },
  },
  data() {
    return {
      isFoldArea: false,
      pdzfzfxh: [], // 配电站房站房型号
      pdzftdxh: [], // 配电站房通道型号
      pdzfzjtList: [], // 配电站房中间头
      pdzfzdtList: [], // 配电站房终端头
      pdzfModel: [], // 配电站房电缆型号
      electricalPlanType: [], // 站房类别
      electricalScheme: [], // 方案类别
      tdlaying: [], // 电缆线路的通道类别
      lngtitude: "",
      lattitude: "",
      highNum: "",
      plmType: [
        {
          key: "混凝土路面(150mm以下)",
          value: "混凝土路面(150mm以下)",
        },
        {
          key: "混凝土路面(250mm以下)",
          value: "混凝土路面(250mm以下)",
        },
        {
          key: "沥青路面",
          value: "沥青路面",
        },
        {
          key: "花岗石路面",
          value: "花岗石路面",
        },
        {
          key: "彩砖路面",
          value: "彩砖路面",
        },
        {
          key: "瓷砖路面",
          value: "瓷砖路面",
        },
        {
          key: "彩砖路面",
          value: "彩砖路面",
        },
        {
          key: "绿化带",
          value: "绿化带",
        },
      ], // 破路面类型
      voltage: [
        {
          key: "10kV",
          value: "10kV",
        },
        {
          key: "0.4kV",
          value: "0.4kV",
        },
      ], // 电压等级
      cableLineState: [], // 电缆线路状态
      lineState: [
        {
          key: "新建",
          value: "新建",
        },
        {
          key: "原有",
          value: "原有",
        },
        {
          key: "拆除",
          value: "拆除",
        },
      ],
      settingObj: {
        // 配电站房
        electrical: {
          planType: false, // 站房类别
          scheme: false, // 方案类别
          state: false, // 站房状态
          electModel: false, // 站房型号
          aisleType: false, // 通道类别
          voltage: false, // 电压等级
          plmType: false, // 破路面类型
          zjtModel: false, // 中间头类型
          zdtModel: false, // 终端头类型
          listArr: [
            {
              tdType: false, // 电缆型号
              lineType: false, // 通道型号
              lineState: false, // 线路状态
              lineModel: false, // 线路型号
              zjtModel: false, // 线路型号
              zdtModel: false, // 线路型号
            },
          ],
        },
      },
      // 配电站房
      electrical: {
        planType: "", // 站房类别
        planTypeId: "", // 站房类别
        scheme: "", // 方案类别
        schemeId: "", // 方案类别
        state: "新建", // 站房状态
        electNum: "pdzf001", // 站房编号
        electModel: "", // 站房型号
        electModelId: "", // 站房型号id
        aisleType: "直埋", // 通道类别
        voltage: "10kV", // 电压等级
        plmType: "混凝土路面(150mm以下)", // 破路面类型
        imgList: [], // 文件列表
        message: "", // 备注信息
        mark: "", // 编号
        audioList: [], // 语音列表
        loactionMsg: "", // 位置信息
        listArr: [
          {
            tdType: "", // 通道型号
            tdTypeSpec: "", // 通道型号
            lineName: "线路1", // 线路名称
            lineState: "新建", // 线路状态
            lineModel: "", // 线路型号
            lineModelId: "", // 线路型号id
            zjtModel: "", // 中间头类型
            zjtModelId: "", // 中间头类型id
            zdtModel: "", // 终端头类型
            zdtModelId: "", // 终端头类型id
            terminal: true, // 终端头
            lineLength: 0,
            middle: "250", // 中间头
          },
        ],
      },
    };
  },
  watch: {
    // 监听改前改后状态
    remodeState: {
      handler(newVal) {
        if (newVal) {
          // 改后不显示拆除的数据
          this.cableLineState = this.lineState.slice(0, 2);
          this.electrical.state = "新建";
          for (const j in this.electrical.listArr) {
            this.electrical.listArr[j].lineState = "新建";
          }
        } else {
          // 改前不显示新建的数据
          this.cableLineState = this.lineState.slice(1);
          this.electrical.state = "原有";
          for (const j in this.electrical.listArr) {
            this.electrical.listArr[j].lineState = "原有";
          }
        }
      },
      deep: true,
      immediate: true,
    },
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal;
      },
      deep: true,
      immediate: true,
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal;
        if (data.moudleType === "PDZFHZ") {
          this.lngtitude = data.longitude;
          this.lattitude = data.latitude;
          this.highNum = data.high;
          // 查询配电站房通道类别
          this.getAwaitTowerOrLineType(
            4,
            "",
            3,
            "",
            "",
            "DLTD",
            "",
            "",
            "",
            data.category
          );
          // 查询配电站房中间头
          this.getAwaitTowerOrLineType(
            1,
            "",
            4,
            "",
            "DLZJT",
            "",
            "",
            data.voltage
          );
          // 查询配电站房终端头
          this.getAwaitTowerOrLineType(
            2,
            "",
            4,
            "",
            "DLZDT",
            "",
            "",
            data.voltage
          );
          // 查询配电站房电缆型号
          this.getAwaitTowerOrLineType(3, 4, 1, "", "", "", "", data.voltage);
          this.settingObj.electrical.listArr = [];
          this.electrical.listArr = [];
          this.electrical.imgList = [];
          this.electrical.audioList = [];
          this.$nextTick(() => {
            for (const j in data.listArr) {
              const obj = {
                lineName: data.listArr[j].lineName, // 线路名称
                lineState: data.listArr[j].state, // 线路状态
                tdTypeSpec: data.listArr[j].tdTypeSpec, // 通道型号
                tdType: data.listArr[j].tdType, // 通道型号
                lineModel: data.listArr[j].lineModel, // 导线型号
                zjtModel: data.listArr[j].zjtModelSpec, // 导线id
                zjtModelId: data.listArr[j].zjtModelId, // 导线id
                zdtModel: data.listArr[j].zdtModelSpec, // 导线id
                zdtModelId: data.listArr[j].zdtModelId, // 导线id
                terminal: data.listArr[j].terminal === "1", // 终端头
                lineLength: data.listArr[j].moduleId, // 导线id
                middle: data.listArr[j].middle, // 导线id
              };
              const isShow_sevent = {
                lineState: false, // 线路状态
                tdType: false,
                lineModel: false, // 线路型号
                zjtModel: false, // 线路型号
                zdtModel: false, // 线路型号
                lineType: false,
              };
              this.settingObj.electrical.listArr.push(isShow_sevent);
              this.electrical.listArr.push(obj);
            }
            this.electrical.planType = data.zfType;
            this.electrical.scheme = data.faTypeSpec;
            this.electrical.schemeId = data.faType;
            this.electrical.state = data.hbState;
            this.electrical.electNum = data.zfNum;
            this.electrical.electModel = data.zfModuleSpec;
            this.electrical.electModelId = data.zfModel;
            this.electrical.aisleType = data.category;
            this.electrical.plmType = data.plmType;
            this.electrical.voltage = data.voltage;
            this.electrical.mark = data.mark; // 编号
            this.electrical.message = data.note;
            this.electrical.loactionMsg = data.dljPosition;
            for (const k in data.imgList) {
              const objs = {
                url: data.imgList[k].path,
                isImage: true,
                isSaveReport: data.imgList[k].isSaveReport,
              };
              this.electrical.imgList.push(objs);
            }
            for (const s in data.voiceList) {
              const objs = {
                content: data.voiceList[s].path,
              };
              this.electrical.audioList.push(objs);
            }
            this.getAwaitTowerOrLineType(
              0,
              "",
              3,
              "",
              "",
              "PDZF",
              "",
              "",
              "",
              data.zfType,
              data.faType
            );
          });
        }
      },
      deep: true,
    },
    // 通道型号
    tdxhList: {
      handler(newVal) {
        this.pdzftdxh = newVal;
        this.electrical.listArr[0].tdTypeSpec = newVal[0].moduleName;
        this.electrical.listArr[0].tdType = newVal[0].moduleID;
      },
      deep: true,
    },
    // 电缆线路
    dlxlList: {
      handler(newVal) {
        this.pdzfModel = newVal;
        this.electrical.listArr[0].lineModel = newVal[0].name;
        this.electrical.listArr[0].lineModelId = newVal[0].id;
      },
      deep: true,
    },
    // 通道类别
    tdlbList: {
      handler(newVal) {
        this.electrical.aisleType = newVal[0].moduletypename;
        this.tdlaying = newVal;
      },
      deep: true,
    },
    // 中间头型号
    zjtTypeList: {
      handler(newVal) {
        this.electrical.listArr[0].zjtModel = newVal[0].moduleName;
        this.electrical.listArr[0].zjtModelId = newVal[0].moduleID;
        this.pdzfzjtList = newVal;
      },
      deep: true,
    },
    // 终端头型号
    zdtTypeList: {
      handler(newVal) {
        this.electrical.listArr[0].zdtModel = newVal[0].moduleName;
        this.electrical.listArr[0].zdtModelId = newVal[0].moduleID;
        this.pdzfzdtList = newVal;
      },
      deep: true,
    },
  },
  mounted() {
    this.getFirstTowerOrLineType(0, "", 3, "", "", "PDZF", "", "");
  },
  methods: {
    removeMainLine(index) {
      if (this.cableLine.listArr.length !== 1) {
        this.settingObj.cableLine.listArr.splice(index, 1);
        this.cableLine.listArr.splice(index, 1);
      } else {
        Toast.fail("最少有一条线路!");
      }
    },
    /**
     * 提交数据
     */
    submitData() {
      const parma = {
        type: 18,
        param: this.electrical,
        visParam: this.settingObj.electrical,
      };
      this.$emit("submitChildData", parma);
    },
    backCurrentDom() {
      this.$emit("backCurrentDom");
      this.getFirstTowerOrLineType(0, "", 3, "", "", "PDZF", "", "");
      // 配电站房通道型号
      this.pdzftdxh = this.tdxhList;
      this.electrical.listArr[0].tdTypeSpec = this.tdxhList[0].moduleName;
      this.electrical.listArr[0].tdType = this.tdxhList[0].moduleID;
      // 配电站房电缆线路
      this.pdzfModel = this.dlxlList;
      this.electrical.listArr[0].lineModel = this.pdzfModel[0].name;
      this.electrical.listArr[0].lineModelId = this.pdzfModel[0].id;
      // 配电站房通道类别
      this.electrical.aisleType = this.tdlbList[0].moduletypename;
      this.tdlaying = this.tdlbList;
      // 配电站房中间头型号
      this.electrical.listArr[0].zjtModel = this.pdzfzjtList[0].moduleName;
      this.electrical.listArr[0].zjtModelId = this.pdzfzjtList[0].moduleID;
      this.pdzfzjtList = this.zjtTypeList;
      // 配电站房终端头型号
      this.electrical.listArr[0].zdtModel = this.pdzfzdtList[0].moduleName;
      this.electrical.listArr[0].zdtModelId = this.pdzfzdtList[0].moduleID;
      this.pdzfzdtList = this.zdtTypeList;
      this.electrical.planType = "";
      this.electrical.scheme = "";
      const stateText = this.remodeState ? "新建" : "原有";
      this.electrical.state = stateText;
      this.electrical.electNum = "pdzf001";
      this.electrical.aisleType = "直埋";
      this.electrical.voltage = "10kV";
      this.electrical.plmType = "混凝土路面(150mm以下)";
      this.electrical.imgList = [];
      this.electrical.message = "";
      this.electrical.audioList = [];
      this.electrical.listArr = [
        {
          lineName: "线路1", // 线路名称
          lineModel: "",
          lineModelId: "",
          lineState: stateText, // 状态
          terminal: true, // 终端头
          lineLength: 0,
          middle: "250", // 中间头
        },
      ];
      this.settingObj.electrical.planType = false;
      this.settingObj.electrical.scheme = false;
      this.settingObj.electrical.state = false;
      this.settingObj.electrical.electModel = false;
      this.settingObj.electrical.voltage = false;
      this.settingObj.electrical.plmType = false;
      this.settingObj.electrical.zjtModel = false;
      this.settingObj.electrical.listArr = [
        {
          lineState: false, // 线路状态
          tdType: false, // 通道型号
          lineType: false, // 线路状态
          lineModel: false, // 线路型号
          zjtModel: false, // 线路型号
          zdtModel: false, // 线路型号
        },
      ];
    },
    showEveryItemSet() {
      this.isFoldArea = !this.isFoldArea;
    },
    /**
     * 获取语音数据
     */
    getAudioData(data) {
      this.transformer.audioList = data.aduioList;
    },
    /**
     * 获取上传图片数据
     */
    getImgData(data) {
      this.transformer.imgList = data.imgList;
    },
    /**
     * 获取备注信息
     * @param data
     */
    getMsgData(data) {
      this.transformer.message = data.message;
    },
    /**
     * 配电站房
     */
    onConfirmPdzfSel(type, index, item) {
      const val = item.value;
      switch (type) {
        case 0:
          // 站房类别
          this.electrical.planType = item.moduletypename;
          this.electrical.planTypeId = item.moduletypeid;
          // 查询电缆线路类型
          this.settingObj.electrical.planType = false;
          this.getTowerOrLineType(0, "", 4, "", item.moduletypekey, "", "", "");
          break;
        case 1:
          // 方案类别
          this.electrical.scheme = item.moduleName;
          this.electrical.schemeId = item.moduleID;
          this.settingObj.electrical.scheme = false;
          // 查询站房型号
          this.getTowerOrLineType(5, "", 4, "", "", "", item.moduleCode, "");
          break;
        case 2:
          // 站房状态
          this.electrical.state = val;
          this.settingObj.electrical.state = false;
          break;
        case 3:
          // 站房型号
          this.electrical.electModel = item.moduleName;
          this.electrical.electModelId = item.moduleID;
          this.settingObj.electrical.electModel = false;
          break;
        case 4:
          // 通道类别
          this.electrical.aisleType = item.value;
          // 查询通道型号
          this.getTowerOrLineType(1, "", 4, "", item.moduletypekey, "", "", "");
          // 查询电缆线路类型
          this.settingObj.electrical.aisleType = false;
          break;
        case 5:
          // 通道型号
          this.electrical.listArr[index].tdTypeSpec = item.moduleName;
          this.electrical.listArr[index].tdType = item.moduleID;
          this.settingObj.electrical.listArr[index].tdType = false;
          break;
        case 6:
          // 电压等级
          this.electrical.voltage = val;
          // 查询配电站房的电缆线路 中间头 终端头
          this.getTowerOrLineType(2, "", 4, "", "DLZDT", "", "", val);
          this.getTowerOrLineType(3, "", 4, "", "DLZJT", "", "", val);
          this.getTowerOrLineType(4, 4, 1, "", "", "", "", val);
          this.settingObj.electrical.voltage = false;
          break;
        case 7:
          // 破路面类型
          this.electrical.plmType = val;
          this.settingObj.electrical.plmType = false;
          break;
        case 8:
          // 中间头型号
          this.electrical.listArr[index].zjtModel = item.moduleName;
          this.electrical.listArr[index].zjtModelId = item.moduleID;
          this.settingObj.electrical.listArr[index].zjtModel = false;
          break;
        case 9:
          // 终端头型号
          this.electrical.listArr[index].zdtModel = item.moduleName;
          this.electrical.listArr[index].zdtModelId = item.moduleID;
          this.settingObj.electrical.listArr[index].zdtModel = false;
          break;
        case 10:
          // 电缆型号
          this.electrical.listArr[index].lineModel = item.name;
          this.electrical.listArr[index].lineModelId = item.id;
          this.settingObj.electrical.listArr[index].lineModel = false;
          break;
        case 11:
          this.electrical.listArr[index].lineState = val;
          this.settingObj.electrical.listArr[index].lineState = false;
          break;
      }
    },
    getFirstTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          switch (settype) {
            case 0:
              // 柱上设备类型
              // 配电站房站房类别
              that.electrical.planType = res.data[0].moduletypename;
              that.electrical.planTypeId = res.data[0].moduletypeid;
              that.electricalPlanType = res.data;
              that.getFirstTowerOrLineType(
                1,
                "",
                4,
                "",
                res.data[0].moduletypekey,
                "",
                "",
                ""
              );
              break;
            case 1:
              // 配电站房方案类别
              that.electrical.scheme = res.data[0].moduleName;
              that.electrical.schemeId = res.data[0].moduleID;
              that.electricalScheme = res.data;
              that.getFirstTowerOrLineType(
                2,
                "",
                4,
                "",
                "",
                "",
                res.data[0].moduleCode,
                ""
              );
              break;
            case 2:
              // 配电站房站房型号FelectricalPlanType
              that.electrical.electModel = res.data[0].moduleName;
              that.electrical.electModelId = res.data[0].moduleID;
              that.pdzfzfxh = res.data;
              break;
          }
        }
      });
    },
    getAwaitTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName,
      selectVal,
      selectLevelTwoVal
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          switch (settype) {
            case 0:
              // 查询配电站房站房类别
              that.settingObj.electricalPlanType = res.data;
              // 站房类别 查询方案类别的参数
              let zflbType;
              for (const j in res.data) {
                if (selectVal === res.data[j].moduletypename) {
                  zflbType = res.data[j].moduletypekey;
                }
              }
              // 查询配电站房方案类别
              that.getAwaitTowerOrLineType(
                30,
                "",
                4,
                "",
                zflbType,
                "",
                "",
                "",
                "",
                "",
                selectLevelTwoVal
              );
              break;
            case 1:
              // 配电站房电缆中间头
              that.pdzfzjtList = res.data;
              break;
            case 2:
              // 配电站房电缆终端头
              that.pdzfzdtList = res.data;
              break;
            case 3:
              // 配电站房电缆型号
              if (voltage === "10kV") {
                that.pdzfModel = res.data.tenDl;
              } else {
                that.pdzfModel = res.data.lowDl;
              }
              break;
            case 4:
              // 土建路径
              that.tdlaying = res.data;
              let tdlbType;
              for (const j in res.data) {
                if (selectVal === res.data[j].moduletypename) {
                  tdlbType = res.data[j].moduletypekey;
                }
              }
              that.getAwaitTowerOrLineType(17, "", 4, "", tdlbType, "", "", "");
              break;
            case 16:
              // 配电站房站房型号
              that.pdzfzfxh = res.data;
              break;
            case 17:
              // 配电站房通道型号
              that.pdzftdxh = res.data;
              break;
            case 30:
              // 配电站房方案类别
              that.electricalScheme = res.data;
              // 方案类别 查询站房型号的参数
              let zfxhTtype;
              for (const j in res.data) {
                if (selectLevelTwoVal === res.data[j].moduleID) {
                  zfxhTtype = res.data[j].moduleCode;
                }
              }
              // 查询配电站房站房型号
              that.getAwaitTowerOrLineType(
                16,
                "",
                4,
                "",
                "",
                "",
                zfxhTtype,
                ""
              );
              break;
          }
        }
      });
    },
    getTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          switch (settype) {
            case 0:
              // 配电站房方案类别
              if (res.data.length === 0) {
                that.electricalScheme = [];
                that.electrical.scheme = "";
                that.electrical.schemeId = "";
                that.pdzfzfxh = [];
                that.electrical.electModel = "";
                that.electrical.electModelId = "";
              } else {
                that.electricalScheme = res.data;
                that.electrical.scheme = res.data[0].moduleName;
                that.electrical.schemeId = res.data[0].moduleID;
                that.getTowerOrLineType(
                  5,
                  "",
                  4,
                  "",
                  "",
                  "",
                  res.data[0].moduleCode,
                  ""
                );
              }
              break;
            case 1:
              // 配电站房通道型号
              that.pdzftdxh = res.data;
              that.electrical.listArr[0].tdTypeSpec = res.data[0].moduleName;
              that.electrical.listArr[0].tdType = res.data[0].moduleID;
              break;
            case 2:
              // 配电站房电缆中间头
              that.pdzfzjtList = res.data;
              that.electrical.listArr[0].zjtModel = res.data[0].moduleName;
              that.electrical.listArr[0].zjtModelId = res.data[0].moduleID;
              break;
            case 3:
              // 配电站房电缆终端头
              that.pdzfzdtList = res.data;
              if (res.data.length === 0) {
                that.electrical.listArr[0].zdtModel = "";
                that.electrical.listArr[0].zdtModelId = "";
              } else {
                that.electrical.listArr[0].zdtModel = res.data[0].moduleName;
                that.electrical.listArr[0].zdtModelId = res.data[0].moduleID;
              }
              break;
            case 4:
              if (voltage === "10kV") {
                // 配电站房电缆型号
                that.pdzfModel = res.data.tenDl;
                that.electrical.listArr[0].lineModel = res.data.tenDl[0].name;
                that.electrical.listArr[0].lineModelId = res.data.tenDl[0].id;
              } else {
                // 配电站房电缆型号
                that.pdzfModel = res.data.lowDl;
                that.electrical.listArr[0].lineModel = res.data.lowDl[0].name;
                that.electrical.listArr[0].lineModelId = res.data.lowDl[0].id;
              }
              break;
            case 5:
              // 站房型号
              that.pdzfzfxh = res.data;
              that.electrical.electModel = res.data[0].moduleName;
              that.electrical.electModelId = res.data[0].moduleID;
              break;
          }
        }
      });
    },
    addCableItem() {
      const stateText = this.remodeState ? "新建" : "原有";
      const item_one = {
        lineName: "线路1", // 线路名称
        tdType: this.pdzftdxh[0].moduleID, // 通道型号
        tdTypeSpec: this.pdzftdxh[0].moduleName, // 通道型号
        lineModel: this.pdzfModel[0].name,
        lineModelId: this.pdzfModel[0].id,
        zjtModel: this.pdzfzjtList[0].moduleName, // 型号
        zjtModelId: this.pdzfzjtList[0].moduleID, // 型号
        zdtModel: this.pdzfzdtList[0].moduleName, // 型号
        zdtModelId: this.pdzfzdtList[0].moduleID, // 型号
        lineState: stateText, // 状态
        terminal: true, // 终端头
        lineLength: 0,
        middle: "250", // 中间头
      };
      const isshow_one = {
        tdType: false, // 电缆型号
        lineType: false, // 通道型号
        lineState: false, // 线路状态
        lineModel: false, // 线路型号
        zjtModel: false, // 线路型号
        zdtModel: false, // 线路型号
      };
      this.electrical.listArr.push(item_one);
      this.settingObj.electrical.listArr.push(isshow_one);
    },
  },
};
</script>

<style lang="sass" scoped>
</style>

