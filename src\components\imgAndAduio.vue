<template>
  <div>
    <div class="map-showNav">附加图片</div>
    <van-row class="map-uploadImg">
      <van-uploader v-model="cimgList" :after-read="onRead" multiple :max-count="9" />
    </van-row>
    <div class="map-showNav">附加录音</div>
    <van-row>
      <Audio :audio-file="caudioList" @submitAudio="getAduioList" />
    </van-row>
    <div class="map-showNav">备注信息</div>
    <van-row>
      <van-field
        v-model="cmessage"
        rows="1"
        autosize
        label="留言"
        type="textarea"
        placeholder="请输入备注信息"
        @change="submitMsg"
      />
    </van-row>
  </div>
</template>

<script>
import Audio from '@/components/audio'
import { Dialog } from 'vant'
export default {
  components: {
    Audio
  },
  props: {
    type: {
      type: Number,
      default: 0
    },
    // 接受自父组件的备注信息
    message: {
      type: String,
      default: ''
    },
    // 接受自父组件的上传图片数组
    imgList: {
      type: Array,
      default: () => []
    },
    //  接受自父组件的录音数组
    aduioList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      cmessage: '',
      cimgList: [],
      caudioList: [],
      types: this.type
    }
  },
  watch: {
    aduioList: {
      handler(newVal, oldVal) {
        this.caudioList = newVal
      },
      immediate: true,
      deep: true
    },
    imgList: {
      handler(newVal, oldVal) {
        this.cimgList = newVal
      },
      immediate: true,
      deep: true
    },
    message: {
      handler(newVal, oldVal) {
        this.cmessage = newVal
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
  },
  methods: {
    getAduioList(val) {
      const data = {
        type: this.types,
        aduioList: val
      }
      this.$emit('submitAduioData', data)
    },
    submitMsg() {
      const data = {
        type: this.types,
        message: this.cmessage
      }
      this.$emit('submitMsgData', data)
    },
    onRead(file) {
      // 这里上传图片是一个的时候 file是个对象
      // 两个以上 file是个数组
      if (file.length > 1) {
        for (const j in file) {
          const canvas = document.createElement('canvas') // 创建Canvas对象(画布)
          const context = canvas.getContext('2d')
          const img = new Image()
          img.src = file[j].content // 指定图片的DataURL(图片的base64编码数据)
          img.onload = function() {
            // 画布大小 这里的this指向img
            canvas.width = this.width
            canvas.height = this.height
            context.drawImage(img, 0, 0, this.width, this.height) // 图片大小
            file[j].content = canvas.toDataURL(file[j].file.type, 0.3) // 0.92为默认压缩质量
          }
        }
      } else {
        const canvas = document.createElement('canvas') // 创建Canvas对象(画布)
        const context = canvas.getContext('2d')
        const img = new Image()
        img.src = file.content // 指定图片的DataURL(图片的base64编码数据)
        img.onload = function() {
          // 画布大小 这里的this指向img
          canvas.width = this.width
          canvas.height = this.height
          context.drawImage(img, 0, 0, this.width, this.height) // 图片大小
          file.content = canvas.toDataURL(file.file.type, 0.3) // 0.92为默认压缩质量
        }
      }
      Dialog.confirm({
        message: '是否放入可研报告?'
      })
        .then(() => {
          file.isSaveReport = 1
        })
        .catch(() => {
          file.isSaveReport = 0
          // on cancel
        })
      const data = {
        type: this.types,
        imgList: this.cimgList
      }
      this.$emit('submitImgList', data)
    }
  }
}

</script>

<style lang="scss" scoped>
</style>

