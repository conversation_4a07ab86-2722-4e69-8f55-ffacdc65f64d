define(["./PrimitivePipeline-3b9da2a6","./createTaskProcessorWorker","./buildModuleUrl-4e1b81e7","./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./Math-119be1a3","./Cartesian2-47311507","./PrimitiveType-a54dc62f","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-3a88ba31","./FeatureDetection-c3b71206","./GeometryAttributes-252e9929","./GeometryPipeline-9d1ef0b6","./AttributeCompression-90851096","./EncodedCartesian3-f1396b05","./IndexDatatype-8a5eead4","./IntersectionTests-7386ffbf","./Plane-7ae8294c","./WebMercatorProjection-01b1b5e7"],(function(e,t,i,r,a,n,o,b,c,s,m,d,u,P,p,f,l,C,y,v,G,k,h,A){"use strict";return t((function(t,i){var r=e.PrimitivePipeline.unpackCombineGeometryParameters(t),a=e.PrimitivePipeline.combineGeometry(r);return e.PrimitivePipeline.packCombineGeometryResults(a,i)}))}));
