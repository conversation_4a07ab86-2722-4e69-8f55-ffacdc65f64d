define(["./when-b60132fc","./PrimitivePipeline-3b9da2a6","./createTaskProcessorWorker","./buildModuleUrl-4e1b81e7","./Cartographic-3309dd0d","./Check-7b2a090c","./Math-119be1a3","./Cartesian2-47311507","./PrimitiveType-a54dc62f","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-3a88ba31","./FeatureDetection-c3b71206","./GeometryAttributes-252e9929","./GeometryPipeline-9d1ef0b6","./AttributeCompression-90851096","./EncodedCartesian3-f1396b05","./IndexDatatype-8a5eead4","./IntersectionTests-7386ffbf","./Plane-7ae8294c","./WebMercatorProjection-01b1b5e7"],(function(e,r,t,a,n,i,o,s,c,b,f,u,d,l,m,p,y,v,C,P,k,h,G,W){"use strict";var A={};function T(r){var t=A[r];return e.defined(t)||("object"==typeof exports?A[t]=t=require("Workers/"+r):require(["Workers/"+r],(function(e){A[t=e]=e}))),t}return t((function(t,a){for(var n=t.subTasks,i=n.length,o=new Array(i),s=0;s<i;s++){var c=n[s],b=c.geometry,f=c.moduleName;if(e.defined(f)){var u=T(f);o[s]=u(b,c.offset)}else o[s]=b}return e.when.all(o,(function(e){return r.PrimitivePipeline.packCreateGeometryResults(e,a)}))}))}));
