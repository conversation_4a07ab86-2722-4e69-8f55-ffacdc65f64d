<template>
  <div class="setArea">
    <div class="setFormArea">
      <el-card class="box-card">
        <el-descriptions title="基础信息:">
          <el-descriptions-item label="工程编码">{{
            this.foundationXX.code
          }}</el-descriptions-item>
          <el-descriptions-item label="工程名称">{{
            this.foundationXX.projectName
          }}</el-descriptions-item>
          <el-descriptions-item label="地市名称">{{
            this.foundationXX.cityName
          }}</el-descriptions-item>
          <el-descriptions-item label="项目包编码">{{
            this.foundationXX.codeName
          }}</el-descriptions-item>
          <el-descriptions-item label="项目包名称">{{
            this.foundationXX.xmmc
          }}</el-descriptions-item>
        </el-descriptions>
        <!-- <el-descriptions title="结算信息:"> </el-descriptions> -->
        <el-descriptions title="结算信息(单位:元)">
          <span slot="title" v-if="ProCode.tgzt == '1'">
            <div style="display: flex;align-items: center;"><span>结算信息(单位:元)</span><el-upload class="upload-demo"
                accept=".word,.xlsx,.xls,txt,bmp,.docx,.doc" :before-upload="beforeDesignUpload" :show-file-list="false"
                :on-success="DesignSuccess" :on-error="DesignError" :file-list="JSbookList.file"
                :http-request="JSbookListUpload">
                <el-button class="blue-btn"><i class="el-icon-upload2"></i> 导入结算书
                </el-button>
              </el-upload></div>
          </span>
        </el-descriptions>
        <el-form ref="formJS" :model="formJS" :inline="true" :rules="rules">
          <el-row>
            <el-col :span="8">
              <el-form-item label="结算报审结算费总额:" prop="sgjsf" label-width="175px">
                <el-input v-model="formJS.sgjsf" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('sgjsf')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算报审建筑工程费:" prop="bstjf" label-width="175px">
                <el-input v-model="formJS.bstjf" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('bstjf')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算报审安装工程费:" prop="bsazf" label-width="175px">
                <el-input v-model="formJS.bsazf" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('bsazf')"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="结算报审其他费用:" prop="bstf" label-width="175px">
                <el-input v-model="formJS.bstf" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('bstf')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算报审基本预备费:" prop="jbybf" label-width="175px">
                <el-input v-model="formJS.jbybf" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('jbybf')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算报审甲供设备费:" prop="bsjgsbf" label-width="175px">
                <el-input v-model="formJS.bsjgsbf" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('bsjgsbf')"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="结算报审甲供材料费:" prop="bsjgclf" label-width="175px">
                <el-input v-model="formJS.bsjgclf" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('bsjgclf')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="计划审核日期:" prop="jhshrq" label-width="175px">
                <el-date-picker v-model="formJS.jhshrq" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                  placeholder="选择日期" :disabled="sbOrSh == 'cksh'">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算报审日期:" prop="sendDate" label-width="175px">
                <el-date-picker v-model="formJS.sendDate" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                  placeholder="选择日期" :disabled="sbOrSh == 'cksh'">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="完工报告固定资产信息:" label-width="175px">
                <el-input v-model="formJS.wgbggdzcxx" clearable :disabled="sbOrSh == 'cksh'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算报审静态投资:" prop="ssjttz" label-width="175px">
                <el-input v-model="formJS.ssjttz" :disabled="sbOrSh == 'cksh'" clearable
                  @input="handleNumericInput('ssjttz')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算报审动态投资:" prop="ssdttz" label-width="175px">
                <el-input v-model="formJS.ssdttz" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('ssdttz')"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="结算报审设备购置费:" prop="sbgzf" label-width="175px">
                <el-input v-model="formJS.sbgzf" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('sbgzf')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算审核动态投资:" prop="yjdttz" label-width="175px">
                <el-input v-model="formJS.yjdttz" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('yjdttz')"></el-input>
              </el-form-item>
            </el-col>
             <el-col :span="8">
              <el-form-item label="结算审核时间:" prop="jsshsj" label-width="175px">
                 <el-date-picker v-model="formJS.jsshsj" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            
          </el-row>
          <!-- 运检字段start -->
          <el-row>
            <el-col :span="8">
              <el-form-item label="结算审核设备购置费:" prop="yjbssbgzf" label-width="175px">
                <el-input v-model="formJS.yjbssbgzf" clearable @input="handleNumericInput('yjbssbgzf')"
                  :disabled="sbOrSh == 'cksh'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算审核结算费总额:" prop="yjsgjsf" label-width="175px">
                <el-input v-model="formJS.yjsgjsf" clearable @input="handleNumericInput('yjsgjsf')"
                  :disabled="sbOrSh == 'cksh'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算审核其他费用:" prop="yjbstf" label-width="175px">
                <el-input v-model="formJS.yjbstf" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('yjbstf')"></el-input>
              </el-form-item>
            </el-col>
            
          </el-row>
          <el-row>
<el-col :span="8">
              <el-form-item label="结算审核基本预备费:" prop="yjjbybf" label-width="175px">
                <el-input v-model="formJS.yjjbybf" clearable @input="handleNumericInput('yjjbybf')"
                  :disabled="sbOrSh == 'cksh'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算审核甲供设备费:" prop="yjbsjgsbf" label-width="175px">
                <el-input v-model="formJS.yjbsjgsbf" clearable @input="handleNumericInput('yjbsjgsbf')"
                  :disabled="sbOrSh == 'cksh'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算审核甲供材料费:" prop="yjbsjgclf" label-width="175px">
                <el-input v-model="formJS.yjbsjgclf" clearable @input="handleNumericInput('yjbsjgclf')"
                  :disabled="sbOrSh == 'cksh'"></el-input>
              </el-form-item>
            </el-col>
            
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="结算审核静态投资:" prop="yjjttz" label-width="175px">
                <el-input v-model="formJS.yjjttz" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('yjjttz')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算审核建筑工程费:" prop="yjbstjf" label-width="175px">
                <el-input v-model="formJS.yjbstjf" clearable @input="handleNumericInput('yjbstjf')"
                  :disabled="sbOrSh == 'cksh'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算审核安装工程费:" prop="yjbsazf" label-width="175px">
                <el-input v-model="formJS.yjbsazf" clearable @input="handleNumericInput('yjbsazf')"
                  :disabled="sbOrSh == 'cksh'"></el-input>
              </el-form-item>
            </el-col>
            <!-- 运检字段end -->
            
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="结算审计结算费总额:" prop="sdgczj" label-width="175px">
                <el-input v-model="formJS.sdgczj" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('sdgczj')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算审计建筑工程费:" prop="jzgcf" label-width="175px">
                <el-input v-model="formJS.jzgcf" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('jzgcf')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算审计安装工程费:" prop="sdazf" label-width="175px">
                <el-input v-model="formJS.sdazf" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('sdazf')"></el-input>
              </el-form-item>
            </el-col>
           
          </el-row>
          <el-row>
             <el-col :span="8">
              <el-form-item label="结算审计其他费用:" prop="sdqtf" label-width="175px">
                <el-input v-model="formJS.sdqtf" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('sdqtf')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算审计基本预备费:" prop="sdjbybf" label-width="175px">
                <el-input v-model="formJS.sdjbybf" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('sdjbybf')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算审计设备购置费:" prop="sdsbgzf" label-width="175px">
                <el-input v-model="formJS.sdsbgzf" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('sdsbgzf')"></el-input>
              </el-form-item>
            </el-col>
            
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="结算审计甲供设备费:" prop="sdjgsbf" label-width="175px">
                <el-input v-model="formJS.sdjgsbf" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('sdjgsbf')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算审计甲供材料费:" prop="sdjgclf" label-width="175px">
                <el-input v-model="formJS.sdjgclf" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('sdjgclf')"> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="审增值:" prop="szz" label-width="175px">
                <el-input v-model="formJS.szz" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('szz')"></el-input>
              </el-form-item>
            </el-col>
            
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="审减值:" prop="sjz" label-width="175px">
                <el-input v-model="formJS.sjz" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('sjz')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="审定时间:" prop="approvalTime" label-width="175px">
                <el-date-picker v-model="formJS.approvalTime" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date"
                  placeholder="选择日期" :disabled="sbOrSh == 'cksh'">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="审价单位名称:" prop="auditCompanyName" label-width="175px">
                <el-input v-model="formJS.auditCompanyName" clearable :disabled="sbOrSh == 'cksh'"></el-input>
              </el-form-item>
            </el-col>
           
          </el-row>
          <el-row>
 <el-col :span="8">
              <el-form-item label="结算审计线路长度:" prop="sdxlcd" label-width="175px">
                <el-input v-model="formJS.sdxlcd" clearable :disabled="sbOrSh == 'cksh'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="出具评审意见日期:" prop="cjpsyjrq" label-width="175px">
                <el-date-picker v-model="formJS.cjpsyjrq" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                  placeholder="选择日期" :disabled="sbOrSh == 'cksh'">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="评审意见文号:" prop="psyjwh" label-width="175px">
                <el-input v-model="formJS.psyjwh" clearable :disabled="sbOrSh == 'cksh'"></el-input>
              </el-form-item>
            </el-col>
            
          </el-row>
          <el-row>
<el-col :span="8">
              <el-form-item label="结算审计动态投资:" prop="jsdttz" label-width="175px">
                <el-input v-model="formJS.jsdttz" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('jsdttz')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算审计静态投资:" prop="jsjttz" label-width="175px">
                <el-input v-model="formJS.jsjttz" clearable :disabled="sbOrSh == 'cksh'"
                  @input="handleNumericInput('jsjttz')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="工程审核负责人:" prop="gcshfzr" label-width="175px">
                <el-input v-model="formJS.gcshfzr" clearable :disabled="sbOrSh == 'cksh'"></el-input>
              </el-form-item>
            </el-col>
            
          </el-row>
          <el-row type="flex" align="middle">
<el-col :span="8">
              <el-form-item label="专业审核人:" prop="zyshr" label-width="175px">
                <el-input v-model="formJS.zyshr" clearable :disabled="sbOrSh == 'cksh'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="咨询单位:" prop="zxdw" label-width="175px">
                <el-input v-model="formJS.zxdw" clearable :disabled="sbOrSh == 'cksh'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="审定结算较批复概算偏差比:" prop="jsjgspcb" label-width="175px" style="height:100%;display:flex;align-items: center">
                <el-input v-model="jsjgspcbDisplay" clearable :disabled="true"></el-input>
              </el-form-item>
            </el-col>
            
          </el-row>

          <el-row type="flex" align="middle">
            <el-col :span="8">
              <el-form-item label="审定结算较批复概算偏差超5%原因:" :prop="!isDisabledKygs?'jsjgspcyy':''" label-width="175px" style="height:100%;display:flex;align-items: center">
                <el-input v-model="formJS.jsjgspcyy" clearable :disabled="isDisabled || sbOrSh == 'cksh'"></el-input>
              </el-form-item>
              
            </el-col>
            <el-col :span="8">
              <el-form-item label="批复概算较可研估算偏差比:" prop="gsjgspcb" label-width="175px" style="height:100%;display:flex;align-items: center">
                <el-input v-model="gsjgspcbDisplay" clearable :disabled="true"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="批复概算较可研估算偏差超10%原因:" :prop="!isDisabledKygs?'gsjgspcyy':''" label-width="175px" style="height:100%;display:flex;align-items: center">
                <el-input v-model="formJS.gsjgspcyy" clearable
                  :disabled="isDisabledKygs || sbOrSh == 'cksh'"></el-input>
              </el-form-item>
            </el-col>
           
          </el-row>
           <el-row><el-col :span="24" class="midSearchArea">
              <el-button class="blue-btn" @click="computedData">计算偏差比</el-button>
            </el-col></el-row>
 
          <!-- ----------------------------------------------新增字段 -->
          <!-- <el-row>
            <el-col :span="8">
              <el-form-item
                label="结算实际配电变压器台数（台）:"
                prop="jssjpdbyqts"
                label-width="175px"
              >
                <el-input v-model="formJS.jssjpdbyqts" clearable  oninput="value=value.replace(/^0+|[^0-9.]/g,'')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="结算实际配电变压器容量（千伏安）:"
                prop="jssjpdbyqrl"
                label-width="175px"
              >
                <el-input v-model="formJS.jssjpdbyqrl" clearable  oninput="value=value.replace(/^0+|[^0-9.]/g,'')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算实际低压电缆线路长度（千米）:" prop="jssjdydlxlcd" label-width="175px">
                <el-input v-model="formJS.jssjdydlxlcd" clearable  oninput="value=value.replace(/^0+|[^0-9.]/g,'')"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="结算实际架空线路长度（千米）:"
                prop="jssjjkxlcd"
                label-width="175px"
              >
                <el-input v-model="formJS.jssjjkxlcd" clearable  oninput="value=value.replace(/^0+|[^0-9.]/g,'')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="结算实际电缆线路长度（千米）:"
                prop="jssjdlxlcd"
                label-width="175px"
              >
                <el-input v-model="formJS.jssjdlxlcd" clearable  oninput="value=value.replace(/^0+|[^0-9.]/g,'')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结算实际低压架空线路长度（千米）:" prop="jssjdyjkxlcd" label-width="175px">
                <el-input v-model="formJS.jssjdyjkxlcd" clearable  oninput="value=value.replace(/^0+|[^0-9.]/g,'')"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="施工单位名称:"
                prop="sgdw"
                label-width="175px"
              >
                <el-input v-model="formJS.sgdw" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row> -->
        </el-form>
        <!--				<el-form-item label="送审设备购置费" prop="sbgzf" label-width="175px">-->
        <!--					<el-input v-model="formJS.sbgzf" clearable></el-input>-->
        <!--				</el-form-item>-->
        <el-descriptions title="概算信息(单位:元):">
          <el-descriptions-item label="总概算费用">{{
            this.formGS.hj
          }}</el-descriptions-item>
          <el-descriptions-item label="估算费用">{{
            this.formJS.source
          }}</el-descriptions-item>
          <el-descriptions-item label="建筑工程费">{{
            this.formGS.jzgcf
          }}</el-descriptions-item>
          <el-descriptions-item label="安装工程费">{{
            this.formGS.azgcf
          }}</el-descriptions-item>
          <el-descriptions-item label="设备购置费">{{
            this.formGS.sbgzf
          }}</el-descriptions-item>
          <el-descriptions-item label="其他费用">{{
            this.formGS.qtfy
          }}</el-descriptions-item>
          <el-descriptions-item label="基本预备费">{{
            this.formGS.jbfy
          }}</el-descriptions-item>
          <el-descriptions-item label="设计费">{{
            this.formGS.sjf
          }}</el-descriptions-item>
          <el-descriptions-item label="监理费">{{
            this.formGS.jlf
          }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      <el-card class="box-card">
        <el-descriptions title="设计物料清单:"> </el-descriptions>
        <div style="margin-bottom: 10px;display: none;" class="buttonUpDon">
          <el-upload class="upload-demo" :data="DesignData()" accept=".word,.xlsx,.xls,txt,bmp,.docx,.doc"
            :before-upload="beforeDesignUpload" :show-file-list="false" :on-success="DesignSuccess"
            :on-error="DesignError" :file-list="DesignList.file" :http-request="uploadImportJs">
            <el-button class="blue-btn"><i class="el-icon-upload2"></i> 物料导入
            </el-button>
          </el-upload>
          <el-button class="directiveBtn" @click="DesignDown"><i class="el-icon-download"></i> 模板下载</el-button>
        </div>
        <template>
          <el-table :data="tableDataDesign" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
            height="200" border style="width: 100%">
            <el-table-column type="index" label="序号" align="center" :index="DesignindexMethod" :resizable="false"
              width="60">
            </el-table-column>
            <el-table-column prop="bm" label="物料编码" align="center">
            </el-table-column>
            <el-table-column prop="wlms" label="物料描述" align="center">
            </el-table-column>
            <el-table-column prop="sl" label="数量" align="center">
            </el-table-column>
            <el-table-column prop="dw" label="单位" align="center">
            </el-table-column>
            <!-- <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button @click="DesignDet(scope.row)" class="el-button" type="text" size="small">删除
                </el-button>
              </template>
</el-table-column> -->
          </el-table>
          <el-pagination style="margin: 10px 0px" background :current-page="this.formDesign.pageIndex"
            :page-size="this.formDesign.pageSize" :page-sizes="pageSizeDesign" layout="total, sizes, prev, pager, next"
            @current-change="DesignCurrentChange" @size-change="DesignSizeChange" :total="DesignTotal">
          </el-pagination>
        </template>
      </el-card>
      <el-card class="box-card">
        <el-descriptions title="申报料单:"> </el-descriptions>
        <div style="margin-bottom: 10px;display: none;" class="buttonUpDon">
          <el-upload class="upload-demo" :data="ReportData()" :action="url + '/tDtfProject/importJs'"
            accept=".word,.xlsx,.xls,txt,bmp,.docx,.doc" :before-upload="beforeDesignUpload" :show-file-list="false"
            :on-success="ReportSuccess" :on-error="ReportError" :file-list="ReportList.file">
            <el-button class="blue-btn"><i class="el-icon-upload2"></i> 物料导入
            </el-button>
          </el-upload>
          <el-button class="directiveBtn" @click="ReportDown"><i class="el-icon-download"></i> 模板下载</el-button>
        </div>
        <template>
          <el-table :data="tableDataReport" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
            height="200" border style="width: 100%">
            <el-table-column type="index" label="序号" align="center" :index="ReportindexMethod" :resizable="false"
              width="60">
            </el-table-column>
            <el-table-column prop="bm" label="物料编码" align="center">
            </el-table-column>
            <el-table-column prop="wlms" label="物料描述" align="center">
            </el-table-column>
            <el-table-column prop="sl" label="数量" align="center">
            </el-table-column>
            <el-table-column prop="dw" label="单位" align="center">
            </el-table-column>
            <el-table-column prop="dj" label="单价" align="center">
            </el-table-column>
            <el-table-column prop="zj" label="总价" align="center">
            </el-table-column>
            <!-- <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button @click="ReportDet(scope.row)" class="el-button" type="text" size="small">删除
                </el-button>
              </template>
  </el-table-column> -->
          </el-table>
          <el-pagination style="margin: 10px 0px" background :current-page="this.formReport.pageIndex"
            :page-sizes="pageSizeReport" :page-size="this.formReport.pageSize" layout="total, sizes, prev, pager, next"
            @current-change="ReportCurrentChange" @size-change="ReportSizeChange" :total="ReportTotal">
          </el-pagination>
        </template>
      </el-card>
      <el-card class="box-card">
        <el-descriptions title="移入料单:"> </el-descriptions>
        <div style="margin-bottom: 10px;display: none;" class="buttonUpDon">
          <el-upload class="upload-demo" :data="MoveInData()" :action="url + '/tDtfProject/importJs'"
            accept=".word,.xlsx,.xls,txt,bmp,.docx,.doc" :before-upload="beforeDesignUpload" :show-file-list="false"
            :on-success="MoveInSuccess" :on-error="MoveInError" :file-list="MoveInList.file">
            <el-button class="blue-btn"><i class="el-icon-upload2"></i> 物料导入
            </el-button>
          </el-upload>
          <el-button class="directiveBtn" @click="MoveInDown"><i class="el-icon-download"></i> 模板下载</el-button>
        </div>
        <template>
          <el-table :data="tableDataMoveIn" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
            height="200" border style="width: 100%">
            <el-table-column type="index" label="序号" align="center" :index="MoveInindexMethod" :resizable="false"
              width="60">
            </el-table-column>
            <el-table-column prop="bm" label="物料编码" align="center">
            </el-table-column>
            <el-table-column prop="wlms" label="物料描述" align="center">
            </el-table-column>
            <el-table-column prop="sl" label="数量" align="center">
            </el-table-column>
            <el-table-column prop="dw" label="单位" align="center">
            </el-table-column>
            <el-table-column prop="dj" label="单价" align="center">
            </el-table-column>
            <el-table-column prop="zj" label="总价" align="center">
            </el-table-column>
            <!-- <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button @click="MoveInDet(scope.row)" class="el-button" type="text" size="small">删除
                </el-button>
              </template>
  </el-table-column> -->
          </el-table>
          <el-pagination style="margin: 10px 0px" background :current-page="this.formMoveIn.pageIndex"
            :page-sizes="pageSizeMoveIn" :page-size="this.formMoveIn.pageSize" layout="total, sizes, prev, pager, next"
            @current-change="MoveInCurrentChange" @size-change="MoveInSizeChange" :total="MoveInTotal">
          </el-pagination>
        </template>
      </el-card>
      <el-card class="box-card">
        <el-descriptions title="移出料单:"> </el-descriptions>
        <div style="margin-bottom: 10px;display: none;" class="buttonUpDon">
          <el-upload class="upload-demo" :data="MoveOutData()" :action="url + '/tDtfProject/importJs'"
            accept=".word,.xlsx,.xls,txt,bmp,.docx,.doc" :before-upload="beforeDesignUpload" :show-file-list="false"
            :on-success="MoveOutSuccess" :on-error="MoveOutError" :file-list="MoveOutList.file">
            <el-button class="blue-btn"><i class="el-icon-upload2"></i> 物料导入
            </el-button>
          </el-upload>
          <el-button class="directiveBtn" @click="MoveOutDown"><i class="el-icon-download"></i> 模板下载</el-button>
        </div>
        <template>
          <el-table :data="tableDataMoveOut" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
            height="200" border style="width: 100%">
            <el-table-column type="index" label="序号" align="center" :index="MoveOutindexMethod" :resizable="false"
              width="60">
            </el-table-column>
            <el-table-column prop="bm" label="物料编码" align="center">
            </el-table-column>
            <el-table-column prop="wlms" label="物料描述" align="center">
            </el-table-column>
            <el-table-column prop="sl" label="数量" align="center">
            </el-table-column>
            <el-table-column prop="dw" label="单位" align="center">
            </el-table-column>
            <el-table-column prop="dj" label="单价" align="center">
            </el-table-column>
            <el-table-column prop="zj" label="总价" align="center">
            </el-table-column>
            <!-- <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button @click="MoveOutDet(scope.row)" class="el-button" type="text" size="small">删除
                </el-button>
              </template>
  </el-table-column> -->
          </el-table>
          <el-pagination style="margin: 10px 0px" background :current-page="this.formMoveOut.pageIndex"
            :page-sizes="pageSizeMoveOut" :page-size="this.formMoveOut.pageSize"
            layout="total, sizes, prev, pager, next" @current-change="MoveOutCurrentChange"
            @size-change="MoveOutSizeChange" :total="MoveOutTotal">
          </el-pagination>
        </template>
      </el-card>
      <el-card class="box-card">
        <el-descriptions title="出库料单:"> </el-descriptions>
        <div style="margin-bottom: 10px;display: none;" class="buttonUpDon">
          <el-upload class="upload-demo" :data="boundData()" :action="url + '/tDtfProject/importJs'"
            accept=".word,.xlsx,.xls,txt,bmp,.docx,.doc" :before-upload="beforeDesignUpload" :show-file-list="false"
            :on-success="boundSuccess" :on-error="boundError" :file-list="boundList.file">
            <el-button class="blue-btn"><i class="el-icon-upload2"></i> 物料导入
            </el-button>
          </el-upload>
          <el-button class="directiveBtn" @click="boundDown"><i class="el-icon-download"></i> 模板下载</el-button>
        </div>
        <template>
          <el-table :data="tableDatabound" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }" height="200"
            border style="width: 100%">
            <el-table-column type="index" label="序号" align="center" :index="boundindexMethod" :resizable="false"
              width="60">
            </el-table-column>
            <el-table-column prop="bm" label="物料编码" align="center">
            </el-table-column>
            <el-table-column prop="wlms" label="物料描述" align="center">
            </el-table-column>
            <el-table-column prop="sl" label="数量" align="center">
            </el-table-column>
            <el-table-column prop="dw" label="单位" align="center">
            </el-table-column>
            <!-- <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button @click="boundDet(scope.row)" class="el-button" type="text" size="small">删除
                </el-button>
              </template>
  </el-table-column> -->
          </el-table>
          <el-pagination style="margin: 10px 0px" background :current-page="this.formbound.pageIndex"
            :page-sizes="pageSizebound" :page-size="this.formbound.pageSize" layout="total, sizes, prev, pager, next"
            @current-change="boundCurrentChange" @size-change="boundSizeChange" :total="boundTotal">
          </el-pagination>
        </template>
      </el-card>
      <el-card class="box-card">
        <el-descriptions title="退料单:"> </el-descriptions>
        <div style="margin-bottom: 10px;display: none;" class="buttonUpDon">
          <el-upload class="upload-demo" :data="RefundData()" :action="url + '/tDtfProject/importJs'"
            accept=".word,.xlsx,.xls,txt,bmp,.docx,.doc" :before-upload="beforeDesignUpload" :show-file-list="false"
            :on-success="RefundSuccess" :on-error="RefundError" :file-list="RefundList.file">
            <el-button class="blue-btn"><i class="el-icon-upload2"></i> 物料导入
            </el-button>
          </el-upload>
          <el-button class="directiveBtn" @click="RefundDown"><i class="el-icon-download"></i> 模板下载</el-button>
        </div>
        <template>
          <el-table :data="tableDataRefund" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
            height="200" border style="width: 100%">
            <el-table-column type="index" label="序号" align="center" :index="RefundindexMethod" :resizable="false"
              width="60">
            </el-table-column>
            <el-table-column prop="bm" label="物料编码" align="center">
            </el-table-column>
            <el-table-column prop="wlms" label="物料描述" align="center">
            </el-table-column>
            <el-table-column prop="sl" label="数量" align="center">
            </el-table-column>
            <el-table-column prop="dw" label="单位" align="center">
            </el-table-column>
            <!-- <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button @click="RefundDet(scope.row)" class="el-button" type="text" size="small">删除
                </el-button>
              </template>
  </el-table-column> -->
          </el-table>
          <el-pagination style="margin: 10px 0px" background :current-page="this.formRefund.pageIndex"
            :page-sizes="pageSizeRefund" :page-size="this.formRefund.pageSize" layout="total, sizes, prev, pager, next"
            @current-change="RefundCurrentChange" @size-change="RefundSizeChange" :total="RefundTotal">
          </el-pagination>
        </template>
      </el-card>
      <!-- 签证信息 -->
      <el-card class="box-card">
        <el-descriptions title="签证信息:"> </el-descriptions>
        <div style="margin-bottom: 10px;display: none;" class="buttonUpDon">
          <el-upload class="upload-demo" :data="VisaData()" :action="url + '/tDtfProject/importJs'"
            accept=".word,.xlsx,.xls,txt,bmp,.docx,.doc" :before-upload="beforeDesignUpload" :show-file-list="false"
            :on-success="VisaSuccess" :on-error="VisaError" :file-list="VisaList.file">
            <el-button class="blue-btn"><i class="el-icon-upload2"></i> 物料导入
            </el-button>
          </el-upload>
          <el-button class="directiveBtn" @click="VisaDown"><i class="el-icon-download"></i> 模板下载</el-button>
        </div>
        <template>
          <el-table :data="tableDataVisa" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }" height="200"
            border style="width: 100%">
            <el-table-column type="index" label="序号" align="center" :index="VisaindexMethod" :resizable="false"
              width="60">
            </el-table-column>
            <el-table-column prop="qzbt" label="签证标题" align="center">
            </el-table-column>
            <el-table-column prop="qzyy" label="签证原因" align="center">
            </el-table-column>
            <el-table-column prop="qzygje" label="签证预估金额" align="center">
            </el-table-column>
            <el-table-column prop="sgxmjld" label="施工项目经理名称" align="center">
            </el-table-column>
            <el-table-column prop="sgdwmc" label="施工单位名称" align="center">
            </el-table-column>
            <el-table-column prop="jldwmc" label="监理单位名称" align="center">
            </el-table-column>
            <el-table-column prop="qzsy" label="签证事由" align="center">
            </el-table-column>
            <el-table-column prop="sdje" label="审定金额" align="center">
            </el-table-column>
            <!-- <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button @click="VisaDet(scope.row)" class="el-button" type="text" size="small">删除
                </el-button>
              </template>
  </el-table-column> -->
          </el-table>
          <el-pagination style="margin: 10px 0px" background :current-page="this.formVisa.pageIndex"
            :page-sizes="pageSizeVisa" :page-size="this.formVisa.pageSize" layout="total, sizes, prev, pager, next"
            @current-change="VisaCurrentChange" @size-change="VisaSizeChange" :total="VisaTotal">
          </el-pagination>
        </template>
      </el-card>
      <!-- 设计变更 -->
      <el-card class="box-card">
        <el-descriptions title="设计变更:"> </el-descriptions>
        <div style="margin-bottom: 10px;display: none;" class="buttonUpDon">
          <el-upload class="upload-demo" :data="ChangeData()" :action="url + '/tDtfProject/importJs'"
            accept=".word,.xlsx,.xls,txt,bmp,.docx,.doc" :before-upload="beforeDesignUpload" :show-file-list="false"
            :on-success="ChangeSuccess" :on-error="ChangeError" :file-list="ChangeList.file">
            <el-button class="blue-btn"><i class="el-icon-upload2"></i> 物料导入
            </el-button>
          </el-upload>
          <el-button class="directiveBtn" @click="ChangeDown"><i class="el-icon-download"></i> 模板下载</el-button>
        </div>
        <template>
          <el-table :data="tableDataChange" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
            height="200" border style="width: 100%">
            <el-table-column type="index" label="序号" align="center" :index="ChangeindexMethod" :resizable="false"
              width="60">
            </el-table-column>
            <el-table-column prop="bgje" label="变更金额" align="center">
            </el-table-column>
            <el-table-column prop="bgdw" label="变更单位" align="center">
            </el-table-column>
            <el-table-column prop="xmjl" label="项目经理" align="center">
            </el-table-column>
            <el-table-column prop="bgsj" label="变更时间" align="center">
            </el-table-column>
            <el-table-column prop="bglx" label="变更类型" align="center">
            </el-table-column>
            <el-table-column prop="bhsje" label="不含税金额" align="center">
            </el-table-column>
            <el-table-column prop="jldwmc" label="监理单位名称" align="center">
            </el-table-column>
            <el-table-column prop="sdje" label="审定金额" align="center">
            </el-table-column>
            <el-table-column prop="gclms" label="工程量描述" align="center">
            </el-table-column>
            <el-table-column prop="bgyy" label="变更原因" align="center">
            </el-table-column>
            <!-- <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button @click="ChangeDet(scope.row)" class="el-button" type="text" size="small">删除
                </el-button>
              </template>
  </el-table-column> -->
          </el-table>
          <el-pagination style="margin: 10px 0px" background :current-page="this.formChange.pageIndex"
            :page-sizes="pageSizeChange" :page-size="this.formChange.pageSize" layout="total, sizes, prev, pager, next"
            @current-change="ChangeCurrentChange" @size-change="ChangeSizeChange" :total="ChangeTotal">
          </el-pagination>
        </template>
      </el-card>
      <el-card class="box-card">
        <el-descriptions title="分包费用:">
          <el-descriptions-item label="专业分包工程量">{{
            this.formFBCost.zyfbgcl
          }}</el-descriptions-item>
          <el-descriptions-item label="送审专业分包费用">{{
            this.formFBCost.sszyfbfy
          }}</el-descriptions-item>
          <el-descriptions-item label="审定专业分包费用">{{
            this.formFBCost.sdzybfy
          }}</el-descriptions-item>
          <el-descriptions-item label="审定专业分包费用（折扣后）">{{
            this.formFBCost.sdzyfbfyzk
          }}</el-descriptions-item>
          <el-descriptions-item label="劳务分包总工期">{{
            this.formFBCost.lwfbzgq
          }}</el-descriptions-item>
          <el-descriptions-item label="送审劳务分包费用">{{
            this.formFBCost.sslwbfy
          }}</el-descriptions-item>
          <el-descriptions-item label="分包占比">{{
            this.formFBCost.sdsgf
          }}</el-descriptions-item>
          <el-descriptions-item label="审定施工费">{{
            this.formFBCost.fbzb
          }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      <!-- 附件上传 -->
      <el-card class="box-card">
        <el-descriptions title="附件上传:"> </el-descriptions>
        <template>
          <el-table :data="tableDataAnnex" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }" height="200"
            border style="width: 100%">
            <el-table-column type="index" label="序号" align="center" :index="AnnexindexMethod" :resizable="false"
              width="60">
            </el-table-column>
            <el-table-column prop="type" label="上传内容" align="center">
            </el-table-column>
            <el-table-column prop="name" label="文件名称" align="center">
            </el-table-column>
            <el-table-column label="操作" width="" align="center">
              <template slot-scope="scope">
                <div style="display: flex; justify-content: center; align-items: center;">
                  <el-upload class="upload-demo" :data="AnnexData()" action="test"
                    accept=".word,.xlsx,.xls,txt,bmp,.docx,.doc" :before-upload="beforeDesignUpload"
                    :show-file-list="false" :on-success="AnnexSuccess" :on-error="AnnexError"
                    :file-list="AnnexList.file" :http-request="fjUpload">
                    <div :class="ProCode.tgzt != '1' ? 'el-buttonDisabled' : 'el-buttonStyle'"><span
                        @click="AnnexUp(scope.row)" style="color: #526ade; cursor: pointer"
                        :class="ProCode.tgzt != '1' ? 'el-buttonDisabled' : 'el-buttonStyle'">上传
                      </span></div>
                  </el-upload>
                  <div :class="ProCode.tgzt != '1' ? 'el-buttonDisabled' : 'el-buttonStyle'"><span
                      @click="AnnexDon(scope.row)" style="color: #526ade; cursor: pointer"
                      :class="ProCode.tgzt != '1' ? 'el-buttonDisabled' : 'el-buttonStyle'">下载</span></div>

                  <!-- <el-button @click="AnnexDet(scope.row)" class="el-button" type="text" size="small">删除
                  </el-button> -->
                </div>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination style="margin: 10px 0px" background :current-page="this.formAnnex.pageIndex"
            :page-sizes="pageSize" :page-size="this.formAnnex.pageSize" layout="total, sizes, prev, pager, next"
            @current-change="handleCurrentChange" @size-change="handleSizeChange" :total="AnnexTotal">
          </el-pagination>
        </template>
      </el-card>
    </div>
    <!-- 基础信息 结算信息 概算信息 -->
    <div class="setFooter" v-show="ProCode.tgzt == '1'">
      <span slot="footer" class="dialog-footer" style="float: right; margin: 10px 10px 10px 0px">
        <el-button @click="feederDialogQuery">保 存</el-button>
        <el-button class="blue-btn" @click="handleSubt">提 交</el-button>
      </span>
    </div>
  </div>
</template>

<script>
import {
  getSB,
  resquest,
  getDet,
  getVisa,
  getChange,
  ChangeDet,
  VisaDet,
  getFBCost,
  FBCostSave,
  AnnexDet,
  getAnnex,
  getGSInformation,
  getJSInformation,
  saveShForm,
  importFileJs,
  fjImportFileJs,
  importSettlement,
  fjDownFileJs
} from '@/api/api'
import axios from 'axios'
export default {
  components: {},
  props: {
    ProCode: {
      default: '',
    },
    sbOrSh: {
      default: ''
    }
  },
  data () {
    return {
   
      rules: {
        bstjf: [{
          required: true,
          message: '请输入送审建筑工程费',
          trigger: 'blur'
        }],
        sgjsf: [{
          required: true,
          message: '请输入送审结算费总额',
          trigger: 'blur'
        }],
        bsazf: [{
          required: true,
          message: '请输入送审安装工程费',
          trigger: 'blur'
        }],
        bstf: [{
          required: true,
          message: '请输入送审其他费用',
          trigger: 'blur'
        }],
        jbybf: [{
          required: true,
          message: '请输入审定基本预备费',
          trigger: 'blur'
        }],
        sbgzf: [{
          required: true,
          message: '请输入送审设备购置费',
          trigger: 'blur'
        }],
        bsjgsbf: [{
          required: true,
          message: '请输入送审甲供设备费',
          trigger: 'blur'
        }],
        bsjgclf: [{
          required: true,
          message: '请输入送审甲供材料费',
          trigger: 'blur'
        }],
        jhshrq: [{
          required: true,
          message: '请选择计划审核日期',
          trigger: 'change'
        }],
        sendDate: [{
          required: true,
          message: '请选择送审日期',
          trigger: 'change'
        }],
        zyfbgcl: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        // wgbggdzcxx: [{
        //   required: true,
        //   message: '请输入完工报告固定资产信息',
        //   trigger: 'blur'
        // }],
        sszyfbfy: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        sdzybfy: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        sdzyfbfyzk: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        lwfbzgq: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        sslwbfy: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        sslwbfy: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        sdlwfbfy: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        sdsgf: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        cjpsyjrq: [{
          required: true,
          message: '请选择出具评审意见日期',
          trigger: 'change'
        }],
        psyjwh: [{
          required: true,
          message: '请输入评审意见文号',
          trigger: 'blur'
        }],
        gcshfzr: [{
          required: true,
          message: '请输入工程审核负责人',
          trigger: 'blur'
        }],
        zyshr: [{
          required: true,
          message: '请输入专业审核人',
          trigger: 'blur'
        }],
        zxdw: [{
          required: true,
          message: '请输入咨询单位',
          trigger: 'blur'
        }],
        jsjttz: [{
          required: true,
          message: '请输入结算静态投资',
          trigger: 'blur'
        }],
        sdjgsbf: [{
          required: true,
          message: '请输入审定甲供设备费',
          trigger: 'blur'
        }],
        sdsbgzf: [{
          required: true,
          message: '请输入结算审计设备购置费',
          trigger: 'blur'
        }],
        sdjgclf: [{
          required: true,
          message: '请输入审定甲供材料费',
          trigger: 'blur'
        }],
        sdjbybf: [{
          required: true,
          message: '请输入结算审计基本预备费',
          trigger: 'blur'
        }],
        sdqtf: [{
          required: true,
          message: '请输入审定其他费用',
          trigger: 'blur'
        }],
        sdxlcd: [{
          required: true,
          message: '请输入审定线路长度',
          trigger: 'blur'
        }],
        auditCompanyName: [{
          required: true,
          message: '请输入审价单位名称',
          trigger: 'blur'
        }],
        approvalTime: [{
          required: true,
          message: '请选择审定时间',
          trigger: 'change'
        }],
        sjz: [{
          required: true,
          message: '请输入审减值',
          trigger: 'blur'
        }],
        szz: [{
          required: true,
          message: '请输入审增值',
          trigger: 'blur'
        }],
        sdgczj: [{
          required: true,
          message: '请输入审定结算费总额',
          trigger: 'blur'
        }],
        jsdttz: [{
          required: true,
          message: '请输入结算动态投资',
          trigger: 'blur'
        }],
        jzgcf: [{
          required: true,
          message: '请输入审定建筑工程费',
          trigger: 'blur'
        }],
        sdazf: [{
          required: true,
          message: '请输入审定安装工程费',
          trigger: 'blur'
        }],
        fbzb: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        ssjttz: [{
          required: true,
          message: '请输入送审静态投资',
          trigger: 'blur'
        }],
        ssdttz: [{
          required: true,
          message: '请输入送审动态投资',
          trigger: 'blur'
        }],
        yjdttz: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        jsshsj: [{
          required: true,
          message: '请选择结算审核时间',
          trigger: 'blur'
        }],
        yjbssbgzf: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        yjsgjsf: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        yjbstf: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        yjjbybf: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        yjbsjgsbf: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        yjbsjgclf: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        yjjttz: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        yjbstjf: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        yjbsazf: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        // jsjgspcb: [{
        //   required: true,
        //   message: '请输入内容',
        //   trigger: 'blur'
        // }],
        jsjgspcyy: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
        // gsjgspcb: [{
        //   required: true,
        //   message: '请输入内容',
        //   trigger: 'blur'
        // }],
        gsjgspcyy: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }],
      },
      url: resquest,
      formJS: { //表单参数
        gcjsid: '',
        sgjsf: '',
        bstjf: '',
        bsazf: '',
        bstf: '',
        jbybf: '',
        sbgzf: '',
        bsjgsbf: '',
        bsjgclf: '',
        jhshrq: '',
        sendDate: '',
        wgbggdzcxx: '',
        xmbm: '',
        sdgczj: '',
        szz: '',
        sjz: '',
        approvalTime: '',
        auditCompanyName: '',
        sdxlcd: '',
        cjpsyjrq: '',
        psyjwh: '',
        gcshfzr: '',
        zyshr: '',
        zxdw: '',
        jzgcf: '',
        sdazf: '',
        jsdttz: '',
        jsjttz: '',
        sdjgsbf: '',
        sdjgclf: '',
        sdqtf: '',
        jssjpdbyqts: '',
        jssjpdbyqrl: '',
        jssjdydlxlcd: '',
        jssjjkxlcd: '',
        jssjdlxlcd: '',
        jssjdyjkxlcd: '',
        sgdw: '',
        // 新增字段
        sdsbgzf: '',
        ssjttz: '',
        ssdttz: '',
        yjdttz: '',
        yjbssbgzf: '',
        yjsgjsf: '',
        yjbstf: '',
        yjjbybf: '',
        yjbsjgsbf: '',
        yjbsjgclf: '',
        yjjttz: '',
        yjbstjf: '',
        yjbsazf: '',
        jsjgspcb: '',
        jsjgspcyy: '',
        gsjgspcb: '',
        gsjgspcyy: '',
        source: '',
        sdjbybf: '',
        jsshsj:''
      },
      formFBCost: { ///分包费用
        procode: '',
        zyfbgcl: '',
        sszyfbfy: '',
        sdzybfy: '',
        sdzyfbfyzk: '',
        lwfbzgq: '',
        sslwbfy: '',
        sdlwfbfy: '',
        sdsgf: '',
        fbzb: '',
        jzgcf: '',
        sdazf: ''
      },
      formGS: { //概算信息
        id: '',
        hj: '',
        jzgcf: '',
        azgcf: '',
        sbgzf: '',
        qtfy: '',
        jbfy: '',
        sjf: '',
        jlf: '',
        source: ''
      },
      foundationXX: {
        codeName: ''
      },
      JSbookList: {
        file: []
      },
      cuttentProCode: '',
      jsdwOptions: [], //批次名次下拉数据
      tableData: [],
      tableHeight: 0,
      dialogProblem: false, //编辑
      dialogSB: false, //申报
      tableDatas: [],
      //设计物料清单
      formDesign: {
        type: '设计',
        taskid: '',
        pageSize: 5,
        pageIndex: 1,
        code: ''
      },
      tableDataDesign: [],
      DesignTotal: 0,
      pageSizeDesign: [5, 10, 20, 50, 100],
      downloadAllUrlDesign: resquest + '/tDtfProject/downFilewl',
      DesignList: {
        file: [], // 文件上传
      },
      //申报料单
      formReport: {
        type: '领料',
        taskid: '',
        pageSize: 5,
        pageIndex: 1,
        code: ''
      },
      tableDataReport: [],
      ReportTotal: 0,
      pageSizeReport: [5, 10, 20, 50, 100],
      ReportList: {
        file: [], // 文件上传
      },
      //移入料单
      formMoveIn: {
        type: '移入',
        taskid: '',
        pageSize: 5,
        pageIndex: 1,
        code: ''
      },
      tableDataMoveIn: [],
      MoveInTotal: 0,
      pageSizeMoveIn: [5, 10, 20, 50, 100],
      MoveInList: {
        file: [], // 文件上传
      },
      //移出料单
      formMoveOut: {
        type: '移出',
        taskid: '',
        pageSize: 5,
        pageIndex: 1,
        code: ''
      },
      tableDataMoveOut: [],
      MoveOutTotal: 0,
      pageSizeMoveOut: [5, 10, 20, 50, 100],
      MoveOutList: {
        file: [], // 文件上传
      },
      //出库料单
      formbound: {
        type: '出库',
        taskid: '',
        pageSize: 5,
        pageIndex: 1,
        code: ''
      },
      tableDatabound: [],
      boundTotal: 0,
      pageSizebound: [5, 10, 20, 50, 100],
      boundList: {
        file: [], // 文件上传
      },
      //退料单
      formRefund: {
        type: '退料',
        taskid: '',
        pageSize: 5,
        pageIndex: 1,
        code: ''
      },
      tableDataRefund: [],
      RefundTotal: 0,
      pageSizeRefund: [5, 10, 20, 50, 100],
      RefundList: {
        file: [], // 文件上传
      },
      //签证
      formVisa: {
        code: '',
        pageSize: 5,
        pageIndex: 1
      },
      tableDataVisa: [],
      VisaTotal: 0,
      pageSizeVisa: [5, 10, 20, 50, 100],
      VisaList: {
        file: [], // 文件上传
      },
      //设计变更
      formChange: {
        code: '',
        pageSize: 5,
        pageIndex: 1
      },
      tableDataChange: [],
      ChangeTotal: 0,
      pageSizeChange: [5, 10, 20, 50, 100],
      ChangeList: {
        file: [], // 文件上传
      },
      // 附件
      formAnnex: {
        code: '',
        pageSize: 5,
        pageIndex: 1
      },
      tableDataAnnex: [],
      AnnexTotal: 0,
      pageSize: [5, 10, 20, 50, 100], //分页页数
      downloadAllUrlAnnex: resquest + '/tDtfProject/downFile3',
      AnnexList: {
        file: [], // 文件上传
      },
      isDisabled: '',
      isDisabledKygs: '',
    }
  },
  created () {
    this.getHeight()
    this.getList()
    this.FBCost()
    window.addEventListener('resize', this.getHeight)
  },
  computed: {
    jsjgspcbDisplay: {
      get () {
        const value = this.formJS.jsjgspcb

        if ( value == Infinity || value == -Infinity) {
          this.$message.warning("数据有误，请检查数据或重新填写!")
          return ''
        }
        return (value * 100).toFixed(2) + '%'
      }
    },
    gsjgspcbDisplay: {
      get () {
        const value = this.formJS.gsjgspcb
        if ( value == Infinity || value == -Infinity) {
          this.$message.warning("数据有误，请检查数据或重新填写!")
          return ''
        }
        return (value * 100).toFixed(2) + '%'
      },

    }
  },

  methods: {
    // 校验方法是否合规
    handleNumericInput (field) {
      let value = this.formJS[field]
      // 过滤非数字和多余的点
      let cleanedValue = value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')

      // 去除前导零，但保留小数点前的一个零
      if (cleanedValue.startsWith('0.') || cleanedValue === '0') {
        this.formJS[field] = cleanedValue
      } else {
        this.formJS[field] = cleanedValue.replace(/^0+/, '')
      }
    },
    uploadList (type, files, taskid) {
      console.log(files, 'filessss')
      const formData = new FormData()
      formData.append('code', taskid)
      formData.append('type', type)
      formData.append('file', files.file)
      console.log(formData.get('code'), formData.get('file'), '上传文件')
      importFileJs(formData).then(res => {
        console.log(res)
        this.getList()
      })
    },
    // 导入结算书
    JSbookListUpload (files) {
      console.log('导入结算书', files)
      const formData = new FormData()
      formData.append('code', this.ProCode.code)
      formData.append('type', '2')
      formData.append('file', files.file)
      importSettlement(formData).then(res => {
        console.log(res, '上传成功')
        getJSInformation(this.ProCode.code)
          .then((res) => {
            if (res.data.result === '') { } else {
              this.$message.success("导入成功")
              // this.formJS = res.data.result
              this.formJS.sdgczj= res.data.result.sdgczj
              this.formJS.jzgcf= res.data.result.jzgcf
              this.formJS.sdazf= res.data.result.sdazf
              this.formJS.sdqtf= res.data.result.sdqtf
              this.formJS.sdjbybf= res.data.result.sdjbybf
              this.formJS.jsdttz= res.data.result.jsdttz
              this.formJS.jsjttz= res.data.result.jsjttz
            }
          })
          .catch(() => { this.$message.error('上传失败，请稍后再试') })
      }).catch(() => { this.$message.error("上传失败，请稍后再试") })
      console.log('导入结算书', files)
    },
    uploadImportJs (files) {
      this.uploadList('设计', files, this.formDesign.taskid)
    },

    FBCost () {
      // 分包单位
      this.formFBCost.procode = this.ProCode.code
      getFBCost(this.ProCode.code)
        .then((res) => {
          if (res.data.result === '') { } else {
            this.formFBCost = res.data.result
          }
        })
        .catch(() => { })
      // 概算信息
      getGSInformation(this.ProCode.code)
        .then((res) => {
          if (res.data.result === '') { } else {
            this.formGS = res.data.result
          }
        })
        .catch(() => { })
      // 结算信息
      getJSInformation(this.ProCode.code)
        .then((res) => {
          if (res.data.result === '') { } else {
            console.log(res.data, 'GSSSSSSSSSSS')

            this.formJS = res.data.result

          }
        })
        .catch(() => { })

    },
    // 查询
    getList () {
      this.foundationXX = this.ProCode
      // 每个表格传taskid
      this.formDesign.taskid = this.foundationXX.code
      this.formReport.taskid = this.foundationXX.code
      this.formMoveIn.taskid = this.foundationXX.code
      this.formMoveOut.taskid = this.foundationXX.code
      this.formbound.taskid = this.foundationXX.code
      this.formRefund.taskid = this.foundationXX.code
      this.formVisa.code = this.foundationXX.code
      this.formChange.code = this.foundationXX.code
      this.formAnnex.code = this.foundationXX.code
      // 截取字符
      const data = this.foundationXX.code
      var nstr = data.split('-')
      this.foundationXX.codeName = nstr[nstr.length - 2]
      //设计物料清单
      const promises = [
        getSB(this.formDesign),
        getSB(this.formReport),
        getSB(this.formMoveIn),
        getSB(this.formMoveOut),
        getSB(this.formbound),
        getSB(this.formRefund),
        getVisa(this.formVisa),
        getChange(this.formChange),
        getAnnex(this.formAnnex)
      ]
      Promise.all(promises)
        .then((results) => {
          results.forEach((res, index) => {
            switch (index) {
              case 0:
                this.tableDataDesign = res.data.result
                this.DesignTotal = res.data.count
                break
              case 1:
                this.tableDataReport = res.data.result
                this.ReportTotal = res.data.count
                break
              case 2:
                this.tableDataMoveIn = res.data.result
                this.MoveInTotal = res.data.count
                break
              case 3:
                this.tableDataMoveOut = res.data.result
                this.MoveOutTotal = res.data.count
                break
              case 4:
                this.tableDatabound = res.data.result
                this.boundTotal = res.data.count
                break
              case 5:
                this.tableDataRefund = res.data.result
                this.RefundTotal = res.data.count
                break
              case 6:
                this.tableDataVisa = res.data.result
                this.VisaTotal = res.data.count
                break
              case 7:
                this.tableDataChange = res.data.result
                this.ChangeTotal = res.data.count
                break
              case 8:
                this.tableDataAnnex = res.data.result
                this.AnnexTotal = res.data.count
                break
            }
          })

          // 所有接口请求完成后触发的事件
          console.log("所有接口完成")
          setTimeout(() => {
            this.computedData()
          }, 500)
        })
        .catch((error) => {
          console.error('请求失败', error)
        }).finally(() => {
          console.log("所有接口完成")
          setTimeout(() => {
            this.computedData()
          }, 500)
        })
    },
    // 计算审定结算较批复概算偏差比和批复概算较可研估算偏差比
    computedData () {
      console.log(this.formGS.hj, '总概算费用')
      console.log(this.formJS.yjsgjsf, '运检审核结算费总额')
      const result = (this.formGS.hj - this.formJS.yjsgjsf) / this.formGS.hj
      this.formJS.jsjgspcb = Math.floor(result * 10000) / 10000
      console.log(this.formJS.jsjgspcb, '审定结算较批复概算偏差比')
      this.isDisabled = this.formJS.jsjgspcb >= 0 && this.formJS.jsjgspcb <= 0.05

      console.log(`${this.formGS.hj} - ${this.formJS.yjsgjsf}/${this.formGS.hj}=${result}`, 'cb')
    // 估算费用uap从可研取，vue待定，先写死
      const resultKY = (this.formJS.source - this.formGS.hj) / this.formJS.source
      this.formJS.gsjgspcb = Math.floor(resultKY * 10000) / 10000
      console.log(`${this.formJS.source}-${this.formGS.hj}/ ${this.formJS.source}=${resultKY}`, 'KY')
       console.log( this.formJS.gsjgspcb, '批复概算较可研估算偏差比')
      this.isDisabledKygs = this.formJS.gsjgspcb >= 0 && this.formJS.gsjgspcb <= 0.1
    },
    // ↓在methods里面(窗体大小改变计算表格高度)
    getHeight () {
      this.tableHeight = (window.innerHeight - 320)
    },

    // 退料单
    RefundCurrentChange (val) {
      this.formRefund.pageIndex = val
      this.getList()
      // table列表序号索引
    },
    RefundSizeChange (val) {
      this.formRefund.pageSize = val
      this.getList()
    },
    RefundindexMethod (index) {
      return (this.formRefund.pageIndex - 1) * 5 + index + 1
    },
    RefundDown () {
      var link = document.createElement('a')
      link.style.display = "none"
      link.href = `${this.downloadAllUrlDesign}?id=wl`
      link.setAttribute("download", name)
      document.body.appendChild(link)
      link.click()
    },
    // 上传传参
    RefundData () {
      return {
        code: this.formRefund.taskid,
        type: '退料',
      }
    },
    //上传成功
    RefundSuccess () {
      this.$message.success('上传成功!')
      this.getList()
    },
    // 上传失败
    RefundError () {
      this.$message.error('上传失败!')
    },
    // 删除表格
    RefundDet (row) {
      getDet(row.qdid)
        .then((res) => {
          if (res.data.message == 'success') {
            this.$message.success('删除成功!')
            this.getList()
          } else {
            this.$message.error('删除失败!')
          }
        })
        .catch(() => { })
    },
    // 出库料单
    boundCurrentChange (val) {
      this.formbound.pageIndex = val
      this.getList()
    },
    boundSizeChange (val) {
      this.formbound.pageSize = val
      this.getList()
    },
    boundindexMethod (index) {
      return (this.formbound.pageIndex - 1) * 5 + index + 1
    },
    boundDown () {
      var link = document.createElement('a')
      link.style.display = "none"
      link.href = `${this.downloadAllUrlDesign}?id=wl`
      link.setAttribute("download", name)
      document.body.appendChild(link)
      link.click()
    },
    // 上传传参
    boundData () {
      return {
        code: this.formbound.taskid,
        type: '出库',
      }
    },
    //上传成功
    boundSuccess () {
      this.$message.success('上传成功!')
      this.getList()
    },
    // 上传失败
    boundError () {
      this.$message.error('上传失败!')
    },
    // 删除表格
    boundDet (row) {
      getDet(row.qdid)
        .then((res) => {
          if (res.data.message == 'success') {
            this.$message.success('删除成功!')
            this.getList()
          } else {
            this.$message.error('删除失败!')
          }
        })
        .catch(() => { })
    },
    // 移除料单
    MoveOutCurrentChange (val) {
      this.formMoveOu.pageIndex = val
      this.getList()
    },
    MoveOutSizeChange (val) {
      this.formMoveOu.pageSize = val
      this.getList()
    },
    MoveOutindexMethod (index) {
      return (this.formMoveOut.pageIndex - 1) * 5 + index + 1
    },
    MoveOutDown () {
      var link = document.createElement('a')
      link.style.display = "none"
      link.href = `${this.downloadAllUrlDesign}?id=qtwl`
      link.setAttribute("download", name)
      document.body.appendChild(link)
      link.click()
    },
    // 上传传参
    MoveOutData () {
      return {
        code: this.formMoveOut.taskid,
        type: '移出',
      }
    },
    //上传成功
    MoveOutSuccess () {
      this.$message.success('上传成功!')
      this.getList()
    },
    // 上传失败
    MoveOutError () {
      this.$message.error('上传失败!')
    },
    // 删除表格
    MoveOutDet (row) {
      getDet(row.qdid)
        .then((res) => {
          if (res.data.message == 'success') {
            this.$message.success('删除成功!')
            this.getList()
          } else {
            this.$message.error('删除失败!')
          }
        })
        .catch(() => { })
    },
    // 申报料单
    ReportCurrentChange (val) {
      this.formReport.pageIndex = val
      this.getList()
    },
    ReportSizeChange (val) {
      this.formReport.pageSize = val
      this.getList()
    },
    ReportindexMethod (index) {
      return (this.formReport.pageIndex - 1) * 5 + index + 1
    },
    ReportDown () {
      var link = document.createElement('a')
      link.style.display = "none"
      link.href = `${this.downloadAllUrlDesign}?id=wl`
      link.setAttribute("download", name)
      document.body.appendChild(link)
      link.click()
    },
    // 上传传参
    ReportData () {
      return {
        code: this.formReport.taskid,
        type: '领料',
      }
    },
    //上传成功
    ReportSuccess () {
      this.$message.success('上传成功!')
      this.getList()
    },
    // 上传失败
    ReportError () {
      this.$message.error('上传失败!')
    },
    // 删除表格
    ReportDet (row) {
      getDet(row.qdid)
        .then((res) => {
          if (res.data.message == 'success') {
            this.$message.success('删除成功!')
            this.getList()
          } else {
            this.$message.error('删除失败!')
          }
        })
        .catch(() => { })
    },
    // 设计物料清单
    DesignCurrentChange (val) {
      this.formDesign.pageIndex = val
      this.getList()
    },
    DesignSizeChange (val) {
      this.formDesign.pageSize = val
      this.getList()
    },
    DesignindexMethod (index) {
      return (this.formDesign.pageIndex - 1) * 5 + index + 1
    },
    // 下载
    DesignDown () {
      var link = document.createElement('a')
      link.style.display = "none"
      link.href = `${this.downloadAllUrlDesign}?id=qtwl`
      link.setAttribute("download", name)
      document.body.appendChild(link)
      link.click()
    },
    // 上传传参
    DesignData () {
      return {
        code: this.formDesign.taskid,
        type: '设计',
      }
    },
    //上传成功
    DesignSuccess () {
      this.$message.success('上传成功!')
      this.getList()
    },
    // 上传失败
    DesignError () {
      this.$message.error('上传失败!')
    },
    // 上传前判断
    beforeDesignUpload (file) {
      let fileName = file.name
      let pos = fileName.lastIndexOf('.')
      let lastName = fileName.substring(pos, fileName.length)
      const ext = lastName.toLowerCase()
      if (ext !== '.doc' && ext !== '.docx' && ext != '.xls' && ext != '.xlsx' && ext != '.txt' && ext !=
        '.bmp' && ext != '.word') {
        this.$message.error('文件必须为.doc')
        return
      }
    },
    // 删除表格
    DesignDet (row) {
      getDet(row.qdid)
        .then((res) => {
          if (res.data.message == 'success') {
            this.$message.success('删除成功!')
            this.getList()
          } else {
            this.$message.error('删除失败!')
          }
        })
        .catch(() => { })
    },
    // 移入料单
    MoveInCurrentChange (val) {
      this.formMoveIn.pageIndex = val
      this.getList()
    },
    MoveInSizeChange (val) {
      this.formMoveIn.pageSize = val
      this.getList()
    },
    MoveInindexMethod (index) {
      return (this.formMoveIn.pageIndex - 1) * 5 + index + 1
    },
    MoveInDown () {
      var link = document.createElement('a')
      link.style.display = "none"
      link.href = `${this.downloadAllUrlDesign}?id=qtwl`
      link.setAttribute("download", name)
      document.body.appendChild(link)
      link.click()
    },
    // 上传传参
    MoveInData () {
      return {
        code: this.formMoveIn.taskid,
        type: '移入',
      }
    },
    //上传成功
    MoveInSuccess () {
      this.$message.success('上传成功!')
      this.getList()
    },
    // 上传失败
    MoveInError () {
      this.$message.error('上传失败!')
    },
    // 删除表格
    MoveInDet (row) {
      getDet(row.qdid)
        .then((res) => {
          if (res.data.message == 'success') {
            this.$message.success('删除成功!')
            this.getList()
          } else {
            this.$message.error('删除失败!')
          }
        })
        .catch(() => { })
    },
    // 签证
    VisaSizeChange (val) {
      this.formVisa.pageSize = val
      this.getList()
    },
    VisaCurrentChange (val) {
      this.formVisa.pageIndex = val
      this.getList()
    },
    VisaindexMethod (index) {
      return (this.formVisa.pageIndex - 1) * 5 + index + 1
    },
    VisaDown () {
      var link = document.createElement('a')
      link.style.display = "none"
      link.href = `${this.downloadAllUrlDesign}?id=qz`
      link.setAttribute("download", name)
      document.body.appendChild(link)
      link.click()
    },
    // 上传传参
    VisaData () {
      return {
        code: this.formVisa.taskid,
        type: '签证',
      }
    },
    //上传成功
    VisaSuccess () {
      this.$message.success('上传成功!')
      this.getList()
    },
    // 上传失败
    VisaError () {
      this.$message.error('上传失败!')
    },
    // 表格删除
    VisaDet (row) {
      console.log(row)
      VisaDet(row.qzid)
        .then((res) => {
          if (res.data.message == 'success') {
            this.$message.success('删除成功!')
            this.getList()
          } else {
            this.$message.error('删除失败!')
          }
        })
        .catch(() => { })
    },
    // 设计变更
    ChangeCurrentChange (val) {
      this.formChange.pageIndex = val
      this.getList()
    },
    ChangeSizeChange (val) {
      this.formChange.pageSize = val
      this.getList()
    },
    ChangeindexMethod (index) {
      return (this.formChange.pageIndex - 1) * 5 + index + 1
    },
    ChangeDown () {
      var link = document.createElement('a')
      link.style.display = "none"
      link.href = `${this.downloadAllUrlDesign}?id=bg`
      link.setAttribute("download", name)
      document.body.appendChild(link)
      link.click()
    },
    // 上传传参
    ChangeData () {
      return {
        code: this.formChange.taskid,
        type: '签证',
      }
    },
    //上传成功
    ChangeSuccess () {
      this.$message.success('上传成功!')
      this.getList()
    },
    // 上传失败
    ChangeError () {
      this.$message.error('上传失败!')
    },
    // 表格删除
    ChangeDet (row) {
      ChangeDet(row.sjbgid)
        .then((res) => {
          if (res.data.message == 'success') {
            this.$message.success('删除成功!')
            this.getList()
          } else {
            this.$message.error('删除失败!')
          }
        })
        .catch(() => { })
    },

    handleSizeChange (val) {
      this.formAnnex.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.formAnnex.pageIndex = val
      this.getList()
    },
    AnnexindexMethod (index) {
      return (this.formAnnex.pageIndex - 1) * 5 + index + 1
    },
    // 附件下载
    async AnnexDon (row) {
      let params = { id: row.id }
      const res = await fjDownFileJs(params)
      console.log(res, "下载模板----------------------------------------")
      const blob = new Blob([res.data])

      let link = document.createElement('a')
      link.download = row.name
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)
    },
    // 附件上传
    AnnexUp (row) {
      this.formAnnex.code = row.id
    },
    // 附件删除
    AnnexDet (row) {
      AnnexDet(row.id)
        .then((res) => {
          console.log(res)
          if (res.data.message == 'success') {
            this.$message.success('删除成功!')
            this.getList()
          } else {
            this.$message.error('删除失败!')
          }
        })
        .catch(() => { })
    },
    // 上传传参
    AnnexData () {
      return {
        code: this.formAnnex.code,
      }
    },
    //上传成功
    AnnexSuccess () {
      this.$message.success('上传成功!')
      this.getList()
    },
    fjUpload (files) {

      const formData = new FormData()
      formData.append('code', this.formAnnex.code)
      formData.append('file', files.file)
      fjImportFileJs(formData).then(res => {
        this.$message.success("上传成功")
        console.log(res, '上传结果')
        this.getList()

      })
    },
    // 上传失败
    AnnexError () {
      this.$message.error('上传失败!')
    },
    // 编辑按钮
    handleEdit () {
      this.dialogProblem = true
    },
    // 提交
    handleSubt () {
       this.formJS.xmbm = this.ProCode.code
      this.formJS.gcjsid = this.ProCode.code
      this.formJS.jhshrq = this.formJS.jhshrq + ' 00:00:00'
      this.formJS.approvalTime = this.formJS.approvalTime + ' 00:00:00'
   
      this.$refs['formJS'].validate((valid) => {
        if (valid) {
          saveShForm(this.formJS).then(res => {
            if (res.data.message == 'success') {
               this.$emit("sumbitDataSH", this.ProCode)
                this.$emit('cloaseDialogVis', false)
              this.saveType = ''
            } else {
              this.$message.warning('提交失败请检查填写的数据是否正确!')
            }
          })
        
        } else {
          return this.$message.warning('请填写必填信息')
        }
      })
     
    },
    // 编辑保存
    dialogQuery () {
      this.dialogProblem = false
    },
    // 申报
    handleaddress () {
      this.dialogSB = true
    },

    operation () {
      this.dialogSB = true
    },
    // 保存
    feederDialogQuery () {
      // this.formFBCost.procode = this.ProCode.code
      this.formJS.xmbm = this.ProCode.code
      this.formJS.gcjsid = this.ProCode.code
      this.formJS.jhshrq = this.formJS.jhshrq + ' 00:00:00'
      this.formJS.approvalTime = this.formJS.approvalTime + ' 00:00:00'
   
      this.$refs['formJS'].validate((valid) => {
        if (valid) {
          saveShForm(this.formJS).then(res => {
            if (res.data.message == 'success') {
             
              this.$message.success('保存成功')
              this.saveType = ''
            } else {
              this.$message.error('保存失败!')
            }
          })
        
        } else {
          return this.$message.warning('请填写必填信息')
        }
      })

    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-descriptions-item__label:not(.is-bordered-label) {
  margin-left: 50px;
}

.box-card {
  margin-bottom: 10px;
}

.buttonUpDon {
  display: flex;
}

.upload-demo {
  margin-right: 10px;
}

.directiveBtn {
  height: 34px;
  margin-top: 10px;
}

.setFormArea {
  height: 75vh;
  overflow-y: scroll;
  margin-bottom: 20px;
}

.setFooter {
  position: absolute;
  bottom: 0;
  right: 30px;
}
</style>
