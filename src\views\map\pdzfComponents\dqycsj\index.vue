<template>
  <div>
    <div class="dqycsj">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="高压" name="高压" >
          <tables v-if="activeName === '高压'" :isSaveTable="saveTable" @isSubmit="submitData" :clickPointId="pointId" :tabsType="activeName"></tables>
        </el-tab-pane>
        <el-tab-pane label="低压" name="低压">
          <tables v-if="activeName === '低压'" :isSaveTable="saveTable" @isSubmit="submitData" :clickPointId="pointId" :tabsType="activeName"></tables>
        </el-tab-pane>
      </el-tabs>
    </div>

  </div>
</template>
<script>
  import tables from '@/views/map/pdzfComponents/dqycsj/components/table' // 电缆井绘制模块
  import { apipost } from '@/utils/mapRequest'
  export default {
    components: {
      tables
    },
    props: {
      pointId: {
        type: String,
        defaults: ''
      },

    },
    data() {
      return {
        activeName: '高压',
        saveTable: '',
        childtableData: [], // 子组件列表数据
      };
    },
    methods: {
      handleClick() {
        let newArr = []
        for(let j in this.childtableData) {
          const obj = {
            "id": this.childtableData[j].customschemeid,
            "code": this.childtableData[j].intervalnumber
          }
          newArr.push(obj)
        }
        const param = {
          data: newArr
        }
        // tabs click点击得时候 销毁组件导致没法监听传过去的值 因此更新间隔的方法写在父组件当中
        apipost(`/mapModel/saveIntervalSchema`, param).then((res)=>{

        })
      },
      submitData(val) {
        this.childtableData = val
      }
    },
    mounted() {
      console.log('点击的点id',this.pointId)
    },
  }

</script>

<style lang="scss" scoped>

  .dqycsj-footer{
    position: relative;
    display: flex;
    justify-content: end;
    margin: 12px 0 12px 0;
    .submitBtn{
      width: 80px;
      background: #526ADE;
      border-radius: 20px;
      display: block;
      color: white;
      text-align: center;
      height: 30px;
      line-height: 30px;
    }
  }
</style>

