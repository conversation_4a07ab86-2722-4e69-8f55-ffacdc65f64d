<template>
  <div id="app">
    <!-- <router-view /> -->

    <keep-alive v-if="isShow"> 
     <router-view v-if="$route.meta.keepAlive" />
    </keep-alive>
   <router-view v-if="!$route.meta.keepAlive" />
    <div class="wcqBox" v-if="!isShow">
      <img :src="require('@/assets/img/<EMAIL>')" alt="" />
      <p class="tipTitle">403</p>
      <p class="tipText">用户无权限，请联系运维人员分配权限</p>
    </div>

  </div>
</template>
<script>
import {
  getuserRole_Http,
  getTicket_Http,
  getUserInfoTygzt,
  
} from "@/api/api.js"
import { validRoleFun } from "@/utils/validRoleFun"
export default {
  data () {
    return {
      isShow: true,
      timer: null,
      validInfo: [
        "配网工程数字化移交样板间",
        "国网山东电力公司本部_省公司设备部",
        "国网山东电力公司本部_全景-省公司领导",
        "省公司配电专业领导角色",
        "全景-省公司领导",
        "省公司设备部",
        "配网工程数字化管控",
      ],
    }
  },
  mounted () {
    console.warn(process.env,'dkldlkld')
    if (!sessionStorage.getItem("PWYJYBJ-BHXN")) {
      this.getTicket()
    }
    // setTimeout(() => {
    //   if (process.env.VUE_APP_API_isHasprofessionalType != "1") {
    //     this.getUserRole()
    //   }
    // }, 400)
    this.getUserinfo()
  
  },
  beforeDestroy () {
    clearInterval(this.timer)
    this.timer = null
  },
  methods: {
    getUserRole () {
      let cacheInfo = JSON.parse(localStorage.getItem("userRolesInfo"))
      console.log(cacheInfo, "获取用户信息");
      
      let params={userId:cacheInfo.userID}
      getUserInfoTygzt(params).then((res) => {
        console.log(res.data, "获取用户信息")
      
      })
    },
    getUserinfo (userid) {
  
      // E885DB8A033C675CE0408D0A5B0463A0
      let cacheInfo = JSON.parse(localStorage.getItem("userRolesInfo"))
      if (validRoleFun(cacheInfo.roles, this.validInfo)) {
        console.log("验证用户通过")
        this.isShow = true
        return
      }
      console.warn("无权限访问")
      this.isShow = false
    },
    getTicket () {
      getTicket_Http({
        authKey: "04e9256834f288c9d4f72f55ee7a417ed4897ad5a0d5e61eae703bc698ed4270a4396ec9a24a0cc9936becf4a042cda5fac15ea7d3caef596f11c3d223718989a518291eb2b019aae30f73e38b9af4f0e01d4435617ab23801738c1084cbe4db4f0dc3f19f33839fdbb7086b4aef61f87b8e2d937d89f187f7cf10e49a6f94d916e942bf57b97d586dfab1a5ff&&046da4cf978b4da15cee314efae149fa902673983eab0eea71c499a1ed3c49736778d5730fc1e5c9e742869cb4911c0831461a3a0e0ad3e2427b9b49413cb7b46baf9a3b33faaefe19651b079ef6d759f276f496473e244e6accf04f74ad6a3bfaa8dd5d4db0c742d14363bc5def31bdae77263e4f08682d66d5cf295dd56842e3ae7b54450caefb33b61b9477",
      }).then((res) => {
        if (res?.code == 0 && JSON.stringify(res?.data) != "{}") {

        }
        sessionStorage.setItem("PWYJYBJ-BHXN", res.data.data.accessTicket)
        this.getUserinfo()
      })
    },
  },
}
</script>
<style lang="scss">
@import "styles/scss/global.scss";
@import "styles/scss/common.scss";
@import "styles/scss/elentuiDirective.scss";

#app {
  margin: 16px;
  background: #ffffff;
}

body {
  padding-right: 0 !important;
}

.wcqBox {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.tipText {
  color: #646566;
  font-family: Microsoft YaHei;
  font-weight: regular;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0px;
  text-align: left;
}

.tipTitle {
  color: #323233;
  font-family: Microsoft YaHei;
  font-weight: regular;
  font-size: 24px;
  line-height: 22px;
  letter-spacing: 0px;
  text-align: left;
}

.el-button--primary {
  border-radius: 2px;
  background: #526ade !important;
}

::deep.el-message {
  z-index: 9999 !important
}
</style>
