<template>
  <svg :class="svgClass" aria-hidden="true" v-on="$listeners">
    <use :xlink:href="iconName" />
  </svg>
</template>

<script>
export default {
  name: 'SvgIcon',
  props: {
    iconClass: {
      type: String,
      required: true
    },
    className: {
      type: String,
      default: ''
    }
  },
  computed: {
    iconName() {
      return `#icon-${this.iconClass}`
    },
    svgClass() {
      if (this.className) {
        return 'svg-icon ' + this.className
      } else {
        return 'svg-icon'
      }
    }
  }
}
</script>

<style scoped>
  @media screen and (min-width: 400px) {
    .svg-icon {
      width: 4em;
      height: 4em;
      /*vertical-align: -0.15em;*/
      /*fill: currentColor;*/
      /*overflow: hidden;*/
    }
  }
  @media screen and (max-width: 399px) and (min-width: 350px){
    .svg-icon {
      width: 3.5em;
      height: 3.5em;
      vertical-align: -0.15em;
      fill: currentColor;
      overflow: hidden;
    }
  }
</style>
