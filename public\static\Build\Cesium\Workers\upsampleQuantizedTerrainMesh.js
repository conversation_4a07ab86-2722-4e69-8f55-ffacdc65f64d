define(["./AttributeCompression-90851096","./buildModuleUrl-4e1b81e7","./Cartesian2-47311507","./Cartographic-3309dd0d","./when-b60132fc","./TerrainEncoding-c4158481","./IndexDatatype-8a5eead4","./Check-7b2a090c","./Math-119be1a3","./OrientedBoundingBox-87d59c2a","./createTaskProcessorWorker","./PrimitiveType-a54dc62f","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./EllipsoidTangentPlane-edb321d3","./IntersectionTests-7386ffbf","./Plane-7ae8294c","./GeometryAttribute-3a88ba31","./FeatureDetection-c3b71206","./PolygonPipeline-660e1625","./earcut-2.2.1-20c8012f","./EllipsoidRhumbLine-ed1a6bf4"],(function(e,i,t,n,r,s,h,u,o,a,d,p,l,f,g,c,m,x,v,C,w,y,B,b,A){"use strict";var I={clipTriangleAtAxisAlignedThreshold:function(e,i,t,n,s,h){var u,o,a;r.defined(h)?h.length=0:h=[],i?(u=t<e,o=n<e,a=s<e):(u=t>e,o=n>e,a=s>e);var d,p,l,f,g,c,m=u+o+a;return 1===m?u?(d=(e-t)/(n-t),p=(e-t)/(s-t),h.push(1),h.push(2),1!==p&&(h.push(-1),h.push(0),h.push(2),h.push(p)),1!==d&&(h.push(-1),h.push(0),h.push(1),h.push(d))):o?(l=(e-n)/(s-n),f=(e-n)/(t-n),h.push(2),h.push(0),1!==f&&(h.push(-1),h.push(1),h.push(0),h.push(f)),1!==l&&(h.push(-1),h.push(1),h.push(2),h.push(l))):a&&(g=(e-s)/(t-s),c=(e-s)/(n-s),h.push(0),h.push(1),1!==c&&(h.push(-1),h.push(2),h.push(1),h.push(c)),1!==g&&(h.push(-1),h.push(2),h.push(0),h.push(g))):2===m?u||t===e?o||n===e?a||s===e||(p=(e-t)/(s-t),l=(e-n)/(s-n),h.push(2),h.push(-1),h.push(0),h.push(2),h.push(p),h.push(-1),h.push(1),h.push(2),h.push(l)):(c=(e-s)/(n-s),d=(e-t)/(n-t),h.push(1),h.push(-1),h.push(2),h.push(1),h.push(c),h.push(-1),h.push(0),h.push(1),h.push(d)):(f=(e-n)/(t-n),g=(e-s)/(t-s),h.push(0),h.push(-1),h.push(1),h.push(0),h.push(f),h.push(-1),h.push(2),h.push(0),h.push(g)):3!==m&&(h.push(0),h.push(1),h.push(2)),h},computeBarycentricCoordinates:function(e,i,t,s,h,u,o,a,d){var p=t-o,l=o-h,f=u-a,g=s-a,c=1/(f*p+l*g),m=i-a,x=e-o,v=(f*x+l*m)*c,C=(-g*x+p*m)*c,w=1-v-C;return r.defined(d)?(d.x=v,d.y=C,d.z=w,d):new n.Cartesian3(v,C,w)},computeLineSegmentLineSegmentIntersection:function(e,i,n,s,h,u,o,a,d){var p=(a-u)*(n-e)-(o-h)*(s-i);if(0!==p){var l=((o-h)*(i-u)-(a-u)*(e-h))/p,f=((n-e)*(i-u)-(s-i)*(e-h))/p;return l>=0&&l<=1&&f>=0&&f<=1?(r.defined(d)||(d=new t.Cartesian2),d.x=e+l*(n-e),d.y=i+l*(s-i),d):void 0}}},M=32767,H=16383,T=[],z=[],E=[],N=new n.Cartographic,V=new n.Cartesian3,R=[],O=[],P=[],U=[],F=[],S=new n.Cartesian3,D=new i.BoundingSphere,L=new a.OrientedBoundingBox,X=new t.Cartesian2,k=new n.Cartesian3;function W(){this.vertexBuffer=void 0,this.index=void 0,this.first=void 0,this.second=void 0,this.ratio=void 0}W.prototype.clone=function(e){return r.defined(e)||(e=new W),e.uBuffer=this.uBuffer,e.vBuffer=this.vBuffer,e.heightBuffer=this.heightBuffer,e.normalBuffer=this.normalBuffer,e.index=this.index,e.first=this.first,e.second=this.second,e.ratio=this.ratio,e},W.prototype.initializeIndexed=function(e,i,t,n,r){this.uBuffer=e,this.vBuffer=i,this.heightBuffer=t,this.normalBuffer=n,this.index=r,this.first=void 0,this.second=void 0,this.ratio=void 0},W.prototype.initializeFromClipResult=function(e,i,t){var n=i+1;return-1!==e[i]?t[e[i]].clone(this):(this.vertexBuffer=void 0,this.index=void 0,this.first=t[e[n]],++n,this.second=t[e[n]],++n,this.ratio=e[n],++n),n},W.prototype.getKey=function(){return this.isIndexed()?this.index:JSON.stringify({first:this.first.getKey(),second:this.second.getKey(),ratio:this.ratio})},W.prototype.isIndexed=function(){return r.defined(this.index)},W.prototype.getH=function(e,i){if(r.defined(this.index))return this.heightBuffer[this.index];var t=this.first.getH(e,i),n=this.second.getH(e,i);return 0===i+t/M*e||0===i+n/M*e?0:o.CesiumMath.lerp(this.first.getH(),this.second.getH(),this.ratio)},W.prototype.getU=function(){return r.defined(this.index)?this.uBuffer[this.index]:o.CesiumMath.lerp(this.first.getU(),this.second.getU(),this.ratio)},W.prototype.getV=function(){return r.defined(this.index)?this.vBuffer[this.index]:o.CesiumMath.lerp(this.first.getV(),this.second.getV(),this.ratio)};var K=new t.Cartesian2,Y=-1,_=[new n.Cartesian3,new n.Cartesian3],G=[new n.Cartesian3,new n.Cartesian3];function J(i,t){++Y;var r=_[Y],s=G[Y];return r=e.AttributeCompression.octDecode(i.first.getNormalX(),i.first.getNormalY(),r),s=e.AttributeCompression.octDecode(i.second.getNormalX(),i.second.getNormalY(),s),V=n.Cartesian3.lerp(r,s,i.ratio,V),n.Cartesian3.normalize(V,V),e.AttributeCompression.octEncode(V,t),--Y,t}W.prototype.getNormalX=function(){return r.defined(this.index)?this.normalBuffer[2*this.index]:(K=J(this,K)).x},W.prototype.getNormalY=function(){return r.defined(this.index)?this.normalBuffer[2*this.index+1]:(K=J(this,K)).y};var Z=[];function j(e,i,t,n,s,h,u,o,a,d,p){if(0!==u.length){for(var l=0,f=0;f<u.length;)f=Z[l++].initializeFromClipResult(u,f,o);for(var g=0;g<l;++g){var c=Z[g];if(c.isIndexed())c.newIndex=h[c.index],c.uBuffer=e,c.vBuffer=i,c.heightBuffer=t,a&&(c.normalBuffer=n);else{var m=c.getKey();if(r.defined(h[m]))c.newIndex=h[m];else{var x=e.length;e.push(c.getU()),i.push(c.getV()),t.push(c.getH(d,p)),a&&(n.push(c.getNormalX()),n.push(c.getNormalY())),c.newIndex=x,h[m]=x}}}3===l?(s.push(Z[0].newIndex),s.push(Z[1].newIndex),s.push(Z[2].newIndex)):4===l&&(s.push(Z[0].newIndex),s.push(Z[1].newIndex),s.push(Z[2].newIndex),s.push(Z[0].newIndex),s.push(Z[2].newIndex),s.push(Z[3].newIndex))}}return Z.push(new W),Z.push(new W),Z.push(new W),Z.push(new W),d((function(e,u){var d=e.isEastChild,p=e.isNorthChild,l=d?H:0,f=d?M:H,g=p?H:0,c=p?M:H,m=R,x=O,v=P,C=F;m.length=0,x.length=0,v.length=0,C.length=0;var w=U;w.length=0;var y={},B=e.vertices,b=e.indices;b=b.subarray(0,e.indexCountWithoutSkirts);var A,K,Y,_,G,J=s.TerrainEncoding.clone(e.encoding),Z=J.hasVertexNormals,q=e.exaggeration,Q=0,$=e.vertexCountWithoutSkirts,ee=e.minimumHeight,ie=e.maximumHeight,te=r.defined(e.validMinimumHeight)?e.validMinimumHeight:e.minimumHeight,ne=r.defined(e.validMaximumHeight)?e.validMaximumHeight:e.maximumHeight,re=new Array($),se=new Array($),he=new Array($),ue=Z?new Array(2*$):void 0;for(K=0,Y=0;K<$;++K,Y+=2){var oe=J.decodeTextureCoordinates(B,K,X);if(A=J.decodeHeight(B,K)/q,_=o.CesiumMath.clamp(oe.x*M|0,0,M),G=o.CesiumMath.clamp(oe.y*M|0,0,M),he[K]=o.CesiumMath.clamp((A-ee)/(ie-ee)*M|0,0,M),_<20&&(_=0),G<20&&(G=0),M-_<20&&(_=M),M-G<20&&(G=M),re[K]=_,se[K]=G,Z){var ae=J.getOctEncodedNormal(B,K,k);ue[Y]=ae.x,ue[Y+1]=ae.y}(d&&_>=H||!d&&_<=H)&&(p&&G>=H||!p&&G<=H)&&(y[K]=Q,m.push(_),x.push(G),v.push(he[K]),Z&&(C.push(ue[Y]),C.push(ue[Y+1])),++Q)}var de=[];de.push(new W),de.push(new W),de.push(new W);var pe,le=[];for(le.push(new W),le.push(new W),le.push(new W),K=0;K<b.length;K+=3){var fe=b[K],ge=b[K+1],ce=b[K+2],me=re[fe],xe=re[ge],ve=re[ce];de[0].initializeIndexed(re,se,he,ue,fe),de[1].initializeIndexed(re,se,he,ue,ge),de[2].initializeIndexed(re,se,he,ue,ce);var Ce=I.clipTriangleAtAxisAlignedThreshold(H,d,me,xe,ve,T);(pe=0)>=Ce.length||((pe=le[0].initializeFromClipResult(Ce,pe,de))>=Ce.length||(pe=le[1].initializeFromClipResult(Ce,pe,de))>=Ce.length||(pe=le[2].initializeFromClipResult(Ce,pe,de),j(m,x,v,C,w,y,I.clipTriangleAtAxisAlignedThreshold(H,p,le[0].getV(),le[1].getV(),le[2].getV(),z),le,Z,ie,ee),pe<Ce.length&&(le[2].clone(le[1]),le[2].initializeFromClipResult(Ce,pe,de),j(m,x,v,C,w,y,I.clipTriangleAtAxisAlignedThreshold(H,p,le[0].getV(),le[1].getV(),le[2].getV(),z),le,Z,ie,ee))))}var we=d?-32767:0,ye=p?-32767:0,Be=[],be=[],Ae=[],Ie=[],Me=Number.MAX_VALUE,He=-Me,Te=Number.MAX_VALUE,ze=-Me,Ee=E;Ee.length=0;var Ne=t.Ellipsoid.clone(e.ellipsoid),Ve=t.Rectangle.clone(e.childRectangle),Re=Ve.north,Oe=Ve.south,Pe=Ve.east,Ue=Ve.west;for(Pe<Ue&&(Pe+=o.CesiumMath.TWO_PI),K=0;K<m.length;++K){(_=Math.round(m[K]))<=l?(Be.push(K),_=0):_>=f?(Ae.push(K),_=M):_=2*_+we,m[K]=_,(G=Math.round(x[K]))<=g?(be.push(K),G=0):G>=c?(Ie.push(K),G=M):G=2*G+ye,x[K]=G,(A=o.CesiumMath.lerp(ee,ie,v[K]/M))<Me&&(Me=A),A>He&&(He=A);var Fe=A;(Fe=o.CesiumMath.clamp(Fe,te,ne))<Te&&(Te=Fe),Fe>ze&&(ze=Fe),v[K]=A,N.longitude=o.CesiumMath.lerp(Ue,Pe,_/M),N.latitude=o.CesiumMath.lerp(Oe,Re,G/M),N.height=A,Ne.cartographicToCartesian(N,V),Ee.push(V.x),Ee.push(V.y),Ee.push(V.z)}var Se=i.BoundingSphere.fromVertices(Ee,n.Cartesian3.ZERO,3,D),De=a.OrientedBoundingBox.fromRectangle(Ve,Me,He,Ne,L),Le=a.OrientedBoundingBox.fromRectangle(Ve,Te,ze,Ne,L),Xe=new s.EllipsoidalOccluder(Ne).computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid(Se.center,Ee,3,Se.center,Me,S),ke=He-Me,We=new Uint16Array(m.length+x.length+v.length);for(K=0;K<m.length;++K)We[K]=m[K];var Ke=m.length;for(K=0;K<x.length;++K)We[Ke+K]=x[K];for(Ke+=x.length,K=0;K<v.length;++K)We[Ke+K]=M*(v[K]-Me)/ke;var Ye,_e=h.IndexDatatype.createTypedArray(m.length,w);if(Z){var Ge=new Uint8Array(C);u.push(We.buffer,_e.buffer,Ge.buffer),Ye=Ge.buffer}else u.push(We.buffer,_e.buffer);return{vertices:We.buffer,encodedNormals:Ye,indices:_e.buffer,minimumHeight:Me,maximumHeight:He,westIndices:Be,southIndices:be,eastIndices:Ae,northIndices:Ie,boundingSphere:Se,orientedBoundingBox:De,horizonOcclusionPoint:Xe,validMinimumHeight:Te,validMaximumHeight:ze,validOrientedBoundingBox:Le}}))}));
