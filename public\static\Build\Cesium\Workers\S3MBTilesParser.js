define(["require","./createTaskProcessorWorker","./Check-7b2a090c","./FeatureDetection-c3b71206","./when-b60132fc","./PrimitiveType-a54dc62f","./Cartesian2-47311507","./Cartographic-3309dd0d","./Cartesian4-3ca25aab","./Color-5008547b","./ComponentDatatype-c140a87d","./getStringFromTypedArray-c37342c0","./buildModuleUrl-4e1b81e7","./S3MCompressType-d814d7f5","./IndexDatatype-8a5eead4","./RuntimeError-4a5c8994","./BoundingRectangle-3ed8ca6d","./S3MPixelFormat-4f2b7689","./pako_inflate-f73548c4","./arrayFill-4513d7ad","./CompressedTextureBuffer-290a1ff4","./PixelFormat-34cafcee","./Math-119be1a3","./WebGLConstants-4ae0db90","./Event-16a2dfbf"],(function(t,e,r,a,n,A,o,i,B,E,C,s,y,d,l,f,u,P,c,p,T,L,D,g,F){"use strict";function M(t,e,r){if("function"==typeof t.slice)return t.slice(e,r);for(var n=Array.prototype.slice.call(t,e,r),A=a.FeatureDetection.typedArrayTypes,o=A.length,i=0;i<o;++i)if(t instanceof A[i]){n=new A[i](n);break}return n}function m(){}var I;function v(t,e,r){var a,A=t.num_points(),o=r.num_components(),i=new I.AttributeQuantizationTransform;if(i.InitFromAttribute(r)){for(var B=new Array(o),E=0;E<o;++E)B[E]=i.min_value(E);a={quantizationBits:i.quantization_bits(),minValues:B,range:i.range(),octEncoded:!1}}I.destroy(i),(i=new I.AttributeOctahedronTransform).InitFromAttribute(r)&&(a={quantizationBits:i.quantization_bits(),octEncoded:!0}),I.destroy(i);var s,y=A*o;s=n.defined(a)?function(t,e,r,a,n){var A,o;a.quantizationBits<=8?(o=new I.DracoUInt8Array,A=new Uint8Array(n),e.GetAttributeUInt8ForAllPoints(t,r,o)):(o=new I.DracoUInt16Array,A=new Uint16Array(n),e.GetAttributeUInt16ForAllPoints(t,r,o));for(var i=0;i<n;++i)A[i]=o.GetValue(i);return I.destroy(o),A}(t,e,r,a,y):function(t,e,r,a){var n,A;switch(r.data_type()){case 1:case 11:A=new I.DracoInt8Array,n=new Int8Array(a),e.GetAttributeInt8ForAllPoints(t,r,A);break;case 2:A=new I.DracoUInt8Array,n=new Uint8Array(a),e.GetAttributeUInt8ForAllPoints(t,r,A);break;case 3:A=new I.DracoInt16Array,n=new Int16Array(a),e.GetAttributeInt16ForAllPoints(t,r,A);break;case 4:A=new I.DracoUInt16Array,n=new Uint16Array(a),e.GetAttributeUInt16ForAllPoints(t,r,A);break;case 5:case 7:A=new I.DracoInt32Array,n=new Int32Array(a),e.GetAttributeInt32ForAllPoints(t,r,A);break;case 6:case 8:A=new I.DracoUInt32Array,n=new Uint32Array(a),e.GetAttributeUInt32ForAllPoints(t,r,A);break;case 9:case 10:A=new I.DracoFloat32Array,n=new Float32Array(a),e.GetAttributeFloatForAllPoints(t,r,A)}for(var o=0;o<a;++o)n[o]=A.GetValue(o);return I.destroy(A),n}(t,e,r,y);var d=C.ComponentDatatype.fromTypedArray(s);return{array:s,data:{componentsPerAttribute:o,componentDatatype:d,byteOffset:r.byte_offset(),byteStride:C.ComponentDatatype.getSizeInBytes(d)*o,normalized:r.normalized(),quantization:a}}}var _=new i.Cartesian3(40680631590769,40680631590769,40408299984661.445),S=new i.Cartesian3,O=new i.Cartesian3;function N(t,e,r,a){var A=Math.cos(e);S.x=A*Math.cos(t),S.y=A*Math.sin(t),S.z=Math.sin(e),S=i.Cartesian3.normalize(S,S),i.Cartesian3.multiplyComponents(_,S,O);var o=Math.sqrt(i.Cartesian3.dot(S,O));return O=i.Cartesian3.divideByScalar(O,o,O),S=i.Cartesian3.multiplyByScalar(S,r,S),n.defined(a)||(a=new i.Cartesian3),i.Cartesian3.add(O,S,a)}var h=new A.Matrix4,R=new A.Matrix4,G=new i.Cartesian3,x=new i.Cartographic;function b(t,e,r,a,E,s,y,l){var f=void 0,u=void 0,P=void 0,c=void 0,p=r.vertexAttributes,T=r.attrLocation;if(r.nCompressOptions=0,n.defined(a.posUniqueID)&&a.posUniqueID>=0){n.defined(l)||(r.nCompressOptions|=d.VertexCompressOption.SVC_Vertex);var L=e.GetAttribute(t,a.posUniqueID),D=v(t,e,L),g=D.data.componentsPerAttribute;r.verticesCount=D.array.length/g,r.vertCompressConstant=D.data.quantization.range/(1<<D.data.quantization.quantizationBits);var F=D.data.quantization.minValues;r.minVerticesValue=new B.Cartesian4(F[0],F[1],F[2],1),g>3&&(r.minVerticesValue.w=F[3]);var M=r.verticesCount;if(s&&(f=new i.Cartographic,u=new i.Cartographic,P=new Float32Array(2*M),c=new Float64Array(2*M)),n.defined(l)){var m=D.array,I=3===g?i.Cartesian3.unpackArray(m):B.Cartesian4.unpackArray(m);for(let t=0,e=I.length;t<e;t++){let e=I[t];i.Cartesian3.multiplyByScalar(e,r.vertCompressConstant,e),i.Cartesian3.add(e,r.minVerticesValue,e)}var _=A.Matrix4.multiply(l.sphereMatrix,l.geoMatrix,h),S=A.Matrix4.multiply(l.ellipsoidMatrix,l.geoMatrix,R);A.Matrix4.inverse(S,S);var O=new o.Ellipsoid(6378137,6378137,6378137);for(let t=0,e=I.length;t<e;t++){let e=I[t];A.Matrix4.multiplyByPoint(_,e,G);let r=O.cartesianToCartographic(G,x);s&&(c[2*t]=r.longitude,c[2*t+1]=r.latitude,0===t?(f.longitude=r.longitude,f.latitude=r.latitude,u.longitude=r.longitude,u.latitude=r.latitude):(f.longitude=Math.max(r.longitude,f.longitude),f.latitude=Math.max(r.latitude,f.latitude),u.longitude=Math.min(r.longitude,u.longitude),u.latitude=Math.min(r.latitude,u.latitude)));let a=N(r.longitude,r.latitude,r.height,G);A.Matrix4.multiplyByPoint(S,a,e)}var b=new Array(3*I.length);3===g?i.Cartesian3.packArray(I,b):B.Cartesian4.packArray(I,b),D.array=new Float32Array(b),D.data.componentDatatype=C.ComponentDatatype.FLOAT,D.data.byteStride=4*g}if(T.aPosition=p.length,p.push({index:T.aPosition,typedArray:D.array,componentsPerAttribute:g,componentDatatype:D.data.componentDatatype,offsetInBytes:D.data.byteOffset,strideInBytes:D.data.byteStride,normalize:D.data.normalized}),!n.defined(l)&&s)for(var U=new i.Cartesian3,K=new i.Cartesian3,H=new i.Cartographic,V=0;V<M;V++)A.Matrix4.multiplyByPoint(E,i.Cartesian3.fromElements(D.array[3*V]*r.vertCompressConstant+F[0],D.array[3*V+1]*r.vertCompressConstant+F[1],D.array[3*V+2]*r.vertCompressConstant+F[2],U),K),H=i.Cartographic.fromCartesian(K),c[2*V]=H.longitude,c[2*V+1]=H.latitude,0===V?(f.longitude=H.longitude,f.latitude=H.latitude,u.longitude=H.longitude,u.latitude=H.latitude):(f.longitude=Math.max(H.longitude,f.longitude),f.latitude=Math.max(H.latitude,f.latitude),u.longitude=Math.min(H.longitude,u.longitude),u.latitude=Math.min(H.latitude,u.latitude));if(s){for(V=0;V<M;V++)P[2*V]=c[2*V]-u.longitude,P[2*V+1]=c[2*V+1]-u.latitude;T.img=p.length,p.push({index:T.img,typedArray:P,componentsPerAttribute:2,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:2*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),y.max=f,y.min=u}}if(n.defined(a.normalUniqueID)&&a.normalUniqueID>=0){r.nCompressOptions|=d.VertexCompressOption.SVC_Normal;var w=e.GetAttribute(t,a.normalUniqueID),Y=v(t,e,w),J=Y.data.quantization;r.normalRangeConstant=(1<<J.quantizationBits)-1,T.aNormal=p.length,p.push({index:T.aNormal,typedArray:Y.array,componentsPerAttribute:Y.data.componentsPerAttribute,componentDatatype:Y.data.componentDatatype,offsetInBytes:Y.data.byteOffset,strideInBytes:Y.data.byteStride,normalize:Y.data.normalized})}if(n.defined(a.colorUniqueID)&&a.colorUniqueID>=0){r.nCompressOptions|=d.VertexCompressOption.SVC_VertexColor;var k=e.GetAttribute(t,a.colorUniqueID),W=v(t,e,k);T.aColor=p.length,p.push({index:T.aColor,typedArray:W.array,componentsPerAttribute:W.data.componentsPerAttribute,componentDatatype:W.data.componentDatatype,offsetInBytes:W.data.byteOffset,strideInBytes:W.data.byteStride,normalize:W.data.normalized})}for(V=0;V<a.texCoordUniqueIDs.length;V++){r.texCoordCompressConstant=[],r.minTexCoordValue=[];var Z=a.texCoordUniqueIDs[V];if(!(Z<0)){var X=e.GetAttribute(t,Z),Q=v(t,e,X);if(n.defined(Q.data.quantization)){r.nCompressOptions|=d.VertexCompressOption.SVC_TexutreCoord,r.texCoordCompressConstant.push(Q.data.quantization.range/(1<<Q.data.quantization.quantizationBits));F=Q.data.quantization.minValues;r.minTexCoordValue.push(new o.Cartesian2(F[0],F[1]))}var z="aTexCoord"+V;T[z]=p.length,p.push({index:T[z],typedArray:Q.array,componentsPerAttribute:Q.data.componentsPerAttribute,componentDatatype:Q.data.componentDatatype,offsetInBytes:Q.data.byteOffset,strideInBytes:Q.data.byteStride,normalize:Q.data.normalized}),r.textureCoordIsW=!0}}for(V=0;V<a.vertexAttrUniqueIDs.length;V++){var j=a.vertexAttrUniqueIDs[V];if(!(j<0)){var q=e.GetAttribute(t,j),$=v(t,e,q);T.aVertexWeight=p.length,p.push({index:T.aVertexWeight,typedArray:$.array,componentsPerAttribute:$.data.componentsPerAttribute,componentDatatype:$.data.componentDatatype,offsetInBytes:$.data.byteOffset,strideInBytes:$.data.byteStride,normalize:$.data.normalized}),r.customVertexAttribute={VertexWeight:0}}}}m.dracoDecodePointCloud=function(t,e,r,a,n){for(var A=new(I=t).Decoder,o=["POSITION","NORMAL","COLOR"],i=0;i<o.length;++i)A.SkipAttributeTransform(I[o[i]]);var B=new I.DecoderBuffer;if(B.Init(e,r),A.GetEncodedGeometryType(B)!==I.POINT_CLOUD)throw new f.RuntimeError("Draco geometry type must be POINT_CLOUD.");var E=new I.PointCloud,C=A.DecodeBufferToPointCloud(B,E);if(!C.ok()||0===E.ptr)throw new f.RuntimeError("Error decoding draco point cloud: "+C.error_msg());I.destroy(B),b(E,A,a,n),I.destroy(E),I.destroy(A)},m.dracoDecodeMesh=function(t,e,r,a,n,o,i,B,E,C){for(var s=new(I=t).Decoder,y=["POSITION","NORMAL","COLOR","TEX_COORD"],d=0;d<y.length;++d)s.SkipAttributeTransform(I[y[d]]);var u=new I.DecoderBuffer;if(u.Init(e,r),s.GetEncodedGeometryType(u)!==I.TRIANGULAR_MESH)throw new f.RuntimeError("Unsupported draco mesh geometry type.");var P=new I.Mesh;if(!s.DecodeBufferToMesh(u,P).ok()||0===P.ptr)return!1;I.destroy(u),b(P,s,a,o,i,B,E,C);var c=function(t,e){for(var r=t.num_points(),a=t.num_faces(),n=new I.DracoInt32Array,A=3*a,o=l.IndexDatatype.createTypedArray(r,A),i=0,B=0;B<a;++B)e.GetFaceFromMesh(t,B,n),o[i+0]=n.GetValue(0),o[i+1]=n.GetValue(1),o[i+2]=n.GetValue(2),i+=3;var E=l.IndexDatatype.UNSIGNED_SHORT;return o instanceof Uint32Array&&(E=l.IndexDatatype.UNSIGNED_INT),I.destroy(n),{typedArray:o,numberOfIndices:A,indexDataType:E}}(P,s);n.indicesTypedArray=c.typedArray,n.indicesCount=c.numberOfIndices,n.indexType=c.indexDataType,n.primitiveType=A.PrimitiveType.TRIANGLES,I.destroy(P),I.destroy(s)};var U=Object.freeze({OSGBFile:0,OSGBCacheFile:1,ClampGroundPolygon:2,ClampObjectPolygon:3,ClampGroundLine:4,ClampObjectLine:5,IconPoint:6,Text:7,PointCloudFile:8,ExtendRegion3D:9,ExtendClampPolygonCache:10,PolylineEffect:11,RegionEffect:12,ClampGroundAndObjectLineCache:13,ClampGroundRealtimeRasterCache:14});function K(){}function H(t){var e=new y.BoundingSphere,r=t.instanceBounds;if(!n.defined(r))return e;var a=new i.Cartesian3(r[0],r[1],r[2]),A=new i.Cartesian3(r[3],r[4],r[5]),o=i.Cartesian3.lerp(a,A,.5,new i.Cartesian3),B=i.Cartesian3.distance(o,a);return e.center=o,e.radius=B,e}function V(t){var e,r,a=new y.BoundingSphere,A=new i.Cartesian3,o=t.vertexAttributes[0],B=o.componentsPerAttribute,E=n.defined(t.nCompressOptions)&&(t.nCompressOptions&d.VertexCompressOption.SVC_Vertex)===d.VertexCompressOption.SVC_Vertex,C=1;E?(C=t.vertCompressConstant,e=new i.Cartesian3(t.minVerticesValue.x,t.minVerticesValue.y,t.minVerticesValue.z),r=new Uint16Array(o.typedArray.buffer,o.typedArray.byteOffset,o.typedArray.byteLength/2)):r=new Float32Array(o.typedArray.buffer,o.typedArray.byteOffset,o.typedArray.byteLength/4);for(var s=[],l=0;l<t.verticesCount;l++)i.Cartesian3.fromArray(r,B*l,A),E&&(A=i.Cartesian3.multiplyByScalar(A,C,A),A=i.Cartesian3.add(A,e,A)),s.push(i.Cartesian3.clone(A));return y.BoundingSphere.fromPoints(s,a),s.length=0,a}function w(t){var e,r,a=new y.BoundingSphere,A=new i.Cartesian3,o=n.defined(t.nCompressOptions)&&(t.nCompressOptions&d.VertexCompressOption.SVC_Vertex)===d.VertexCompressOption.SVC_Vertex,B=t.vertexAttributes[0],E=B.componentsPerAttribute,C=1;o?(C=t.vertCompressConstant,r=new i.Cartesian3(t.minVerticesValue.x,t.minVerticesValue.y,t.minVerticesValue.z),e=new Uint16Array(B.typedArray.buffer,B.typedArray.byteOffset,B.typedArray.byteLength/2)):e=new Float32Array(B.typedArray.buffer,B.typedArray.byteOffset,B.typedArray.byteLength/4);for(var s=[],l=0;l<t.verticesCount;l++)i.Cartesian3.fromArray(e,E*l,A),o&&(A=i.Cartesian3.multiplyByScalar(A,C,A),A=i.Cartesian3.add(A,r,A)),s.push(i.Cartesian3.clone(A));return y.BoundingSphere.fromPoints(s,a),s.length=0,a}function Y(t){var e,r,a=n.defined(t.nCompressOptions)&&(t.nCompressOptions&d.VertexCompressOption.SVC_Vertex)===d.VertexCompressOption.SVC_Vertex,A=new y.BoundingSphere,o=new i.Cartesian3,E=new i.Cartesian3,C=t.vertexAttributes[0],s=C.componentsPerAttribute,l=t.attrLocation.aPosition,f=t.vertexAttributes[l],u=t.attrLocation.aTexCoord5,P=t.vertexAttributes[u],c=P.componentsPerAttribute;a?(s=3,c=3,e=k(t,f),r=function(t,e,r){for(var a,n,A,o=e.componentsPerAttribute,i=t.texCoordCompressConstant[r],E=new B.Cartesian4(t.minTexCoordValue[r].x,t.minTexCoordValue[r].y,t.minTexCoordValue[r].z,t.minTexCoordValue[r].w),C=new Uint16Array(e.typedArray.buffer,e.typedArray.byteOffset,e.typedArray.byteLength/2),s=new Float32Array(3*t.verticesCount),y=0;y<t.verticesCount;y++)a=C[o*y]*i+E.x,n=C[o*y+1]*i+E.y,A=C[o*y+2]*i+E.z,s[3*y]=a,s[3*y+1]=n,s[3*y+2]=A;return s}(t,P,5)):(e=new Float32Array(C.typedArray.buffer,C.typedArray.byteOffset,C.typedArray.byteLength/4),r=new Float32Array(P.typedArray.buffer,P.typedArray.byteOffset,P.typedArray.byteLength/4));for(var p=[],T=0;T<t.verticesCount;T++)i.Cartesian3.fromArray(e,s*T,o),i.Cartesian3.fromArray(r,c*T,E),i.Cartesian3.add(o,E,o),p.push(i.Cartesian3.clone(o));return y.BoundingSphere.fromPoints(p,A),p.length=0,A}function J(t){var e=A.PrimitiveType.TRIANGLES;switch(t){case 1:e=A.PrimitiveType.POINTS;break;case 2:e=A.PrimitiveType.LINES;break;case 3:e=A.PrimitiveType.LINE_STRIP;break;case 4:e=A.PrimitiveType.TRIANGLES}return e}function k(t,e){for(var r,a,n,A=e.componentsPerAttribute,o=t.vertCompressConstant,B=new i.Cartesian3(t.minVerticesValue.x,t.minVerticesValue.y,t.minVerticesValue.z),E=new Uint16Array(e.typedArray.buffer,e.typedArray.byteOffset,e.typedArray.byteLength/2),C=new Float32Array(3*t.verticesCount),s=0;s<t.verticesCount;s++)r=E[A*s]*o+B.x,a=E[A*s+1]*o+B.y,n=E[A*s+2]*o+B.z,C[3*s]=r,C[3*s+1]=a,C[3*s+2]=n;return C}K.calcBoundingSphereInWorker=function(t,e){return e.instanceIndex>-1?H(e):n.defined(e.clampRegionEdge)?Y(e):t>=U.ClampGroundPolygon&&t<=U.ClampObjectLine?w(e):t==U.ClampGroundAndObjectLineCache?Y(e):V(e)},K.calcBoundingSphere=function(t,e,r){var a,A=t._fileType;return a=e.instanceIndex>-1?H(e):n.defined(e.clampRegionEdge)?Y(e):A>=U.ClampGroundPolygon&&A<=U.ClampObjectLine?w(e):A==U.ClampGroundAndObjectLineCache?Y(e):V(e),y.BoundingSphere.transform(a,r,a),a},K.calcBoundingRectangle=function(t,e){var r;return t._fileType===U.ClampGroundPolygon&&(r=function(t){var e,r,a=n.defined(t.nCompressOptions)&&(t.nCompressOptions&d.VertexCompressOption.SVC_Vertex)===d.VertexCompressOption.SVC_Vertex,A=new u.BoundingRectangle,B=t.vertexAttributes[0],E=B.componentsPerAttribute,C=1;a?(C=t.vertCompressConstant,r=new i.Cartesian3(t.minVerticesValue.x,t.minVerticesValue.y,t.minVerticesValue.z),e=new Uint16Array(B.typedArray.buffer,B.typedArray.byteOffset,B.typedArray.byteLength/2)):e=new Float32Array(B.typedArray.buffer,B.typedArray.byteOffset,B.typedArray.byteLength/4);for(var s=[],y=0;y<t.verticesCount;y++){var l=e[E*y],f=e[E*y+1];a&&(l=C*l+r.x,f=C*f+r.y),s.push(new o.Cartesian2(l,f))}return u.BoundingRectangle.fromPoints(s,A),s.length=0,A}(e)),r},K.createEdge=function(t,e){if(!(e.length<1)){var r=function(t){for(var e=[],r=t.length,a=0;a<r;a++){var n=J(t[a].primitiveType);n!==A.PrimitiveType.LINES&&n!==A.PrimitiveType.LINE_STRIP||e.push(t[a])}return e}(e);if(0!=r.length){var a,o=function(t){for(var e=0,r=t.length,a=0;a<r;a++){var n=t[a],o=J(n.primitiveType);o==A.PrimitiveType.LINES?e+=n.indicesCount/2:o==A.PrimitiveType.LINE_STRIP&&e++}return e}(r),B=t.attrLocation.aPosition,E=t.vertexAttributes[B],s=n.defined(t.nCompressOptions)&&(t.nCompressOptions&d.VertexCompressOption.SVC_Vertex)===d.VertexCompressOption.SVC_Vertex,y=E.componentsPerAttribute;s?(y=3,a=k(t,E)):a=new Float32Array(E.typedArray.buffer,E.typedArray.byteOffset,E.typedArray.byteLength/4);for(var l=function(t){for(var e=0,r=t.length,a=0;a<r;a++)e+=t[a].indicesCount;return e}(r),f=function(t,e,r){for(var a,n=[],o=r.length,B=0;B<o;B++){var E,C=r[B];E=0===C.indexType?new Uint16Array(C.indicesTypedArray.buffer,C.indicesTypedArray.byteOffset,C.indicesTypedArray.byteLength/2):new Uint32Array(C.indicesTypedArray.buffer,C.indicesTypedArray.byteOffset,C.indicesTypedArray.byteLength/4);var s=J(C.primitiveType);if(s==A.PrimitiveType.LINES)for(a=0;a<C.indicesCount;a+=2){var y=[],d=new i.Cartesian3;d.x=t[E[a]*e],d.y=t[E[a]*e+1],d.z=t[E[a]*e+2],y.push(d);var l=new i.Cartesian3;l.x=t[E[a+1]*e],l.y=t[E[a+1]*e+1],l.z=t[E[a+1]*e+2],y.push(l),n.push(y)}else if(s==A.PrimitiveType.LINE_STRIP){for(y=[],a=0;a<C.indicesCount;a++){var f=new i.Cartesian3;f.x=t[E[a]*e],f.y=t[E[a]*e+1],f.z=t[E[a]*e+2],y.push(f)}n.push(y)}}return n}(a,y,r),u=4*l-4*o,P=new Float32Array(3*u),c=new Float32Array(3*u),p=new Float32Array(3*u),T=new Int8Array(2*u),L=0,D=0;D<o;D++){for(var g=f[D].length,F=0;F<g;F++){var M=4*L-4*D,m=3*M+12*F,I=f[D][F];0!=F&&(P[m-6]=I.x,P[m-5]=I.y,P[m-4]=I.z,P[m-3]=I.x,P[m-2]=I.y,P[m-1]=I.z),F!=g-1&&(P[m]=I.x,P[m+1]=I.y,P[m+2]=I.z,P[m+3]=I.x,P[m+4]=I.y,P[m+5]=I.z);var v=I;F+1<g&&(v=f[D][F+1]),0!=F&&(p[m-6]=v.x,p[m-5]=v.y,p[m-4]=v.z,p[m-3]=v.x,p[m-2]=v.y,p[m-1]=v.z),F!=g-1&&(p[m]=v.x,p[m+1]=v.y,p[m+2]=v.z,p[m+3]=v.x,p[m+4]=v.y,p[m+5]=v.z);var _=I;F>=1&&(_=f[D][F-1]),0!=F&&(c[m-6]=_.x,c[m-5]=_.y,c[m-4]=_.z,c[m-3]=_.x,c[m-2]=_.y,c[m-1]=_.z),F!=g-1&&(c[m]=_.x,c[m+1]=_.y,c[m+2]=_.z,c[m+3]=_.x,c[m+4]=_.y,c[m+5]=_.z),m=2*M+8*F,0!=F&&(T[m-4]=-1,T[m-3]=-1,T[m-2]=1,T[m-1]=-1),F!=g-1&&(T[m]=-1,T[m+1]=1,T[m+2]=1,T[m+3]=1)}L+=f[D].length}var S={vertexAttributes:[],attrLocation:{}},O=S.vertexAttributes,N=S.attrLocation;S.instanceCount=0,S.instanceMode=0,N.aPosition=0,O.push({index:N.aPosition,typedArray:P,componentsPerAttribute:3,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:3*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),N.aNormal=1,O.push({index:N.aNormal,typedArray:c,componentsPerAttribute:3,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:3*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),N.aTexCoord0=2,O.push({index:N.aTexCoord0,typedArray:p,componentsPerAttribute:3,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:3*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),N.aTexCoord1=3,O.push({index:N.aTexCoord1,typedArray:T,componentsPerAttribute:2,componentDatatype:C.ComponentDatatype.BYTE,offsetInBytes:0,strideInBytes:2*Int8Array.BYTES_PER_ELEMENT,normalize:!1});for(var h=[],R=0;R<f.length;R++)h.push(f[R].length);var G=function(t,e,r,a){var n,o={};o.indicesCount=6*(t-e),o.indexType=a>65535?1:0,o.primitiveType=A.PrimitiveType.TRIANGLES,n=0===o.indexType?new Uint16Array(o.indicesCount):new Uint32Array(o.indicesCount);for(var i=0,B=0;B<e;B++){for(var E=0;E<r[B]-1;E++)n[6*(i-B+E)]=4*(i-B+E),n[6*(i-B+E)+1]=4*(i-B+E)+2,n[6*(i-B+E)+2]=4*(i-B+E)+1,n[6*(i-B+E)+3]=4*(i-B+E)+1,n[6*(i-B+E)+4]=4*(i-B+E)+2,n[6*(i-B+E)+5]=4*(i-B+E)+3;i+=r[B]}return o.indicesTypedArray=n,o}(l,o,h,u);return{vertexPackage:S,indexPackage:G}}}};var W,Z,X=Object.freeze({S3M:49,S3M4:1}),Q=function(){var t=new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,3,2,0,0,5,3,1,0,1,12,1,0,10,22,2,12,0,65,0,65,0,65,0,252,10,0,0,11,7,0,65,0,253,15,26,11]),e=new Uint8Array([32,0,65,2,1,106,34,33,3,128,11,4,13,64,6,253,10,7,15,116,127,5,8,12,40,16,19,54,20,9,27,255,113,17,42,67,24,23,146,148,18,14,22,45,70,69,56,114,101,21,25,63,75,136,108,28,118,29,73,115]);if("object"!=typeof WebAssembly)return{supported:!1};var r,a="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";WebAssembly.validate(t)&&(a="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");var n=WebAssembly.instantiate(function(t){for(var r=new Uint8Array(t.length),a=0;a<t.length;++a){var n=t.charCodeAt(a);r[a]=n>96?n-71:n>64?n-65:n>47?n+4:n>46?63:62}var A=0;for(a=0;a<t.length;++a)r[A++]=r[a]<60?e[r[a]]:64*(r[a]-60)+r[++a];return r.buffer.slice(0,A)}(a),{}).then((function(t){(r=t.instance).exports.__wasm_call_ctors()}));function A(t,e,a,n,A,o){var i=r.exports.sbrk,B=a+3&-4,E=i(B*n),C=i(A.length),s=new Uint8Array(r.exports.memory.buffer);s.set(A,C);var y=t(E,a,n,C,A.length);if(0==y&&o&&o(E,B,n),e.set(s.subarray(E,E+a*n)),i(E-i(0)),0!=y)throw new Error("Malformed buffer data: "+y)}var o={0:"",1:"meshopt_decodeFilterOct",2:"meshopt_decodeFilterQuat",3:"meshopt_decodeFilterExp",NONE:"",OCTAHEDRAL:"meshopt_decodeFilterOct",QUATERNION:"meshopt_decodeFilterQuat",EXPONENTIAL:"meshopt_decodeFilterExp"},i={0:"meshopt_decodeVertexBuffer",1:"meshopt_decodeIndexBuffer",2:"meshopt_decodeIndexSequence",ATTRIBUTES:"meshopt_decodeVertexBuffer",TRIANGLES:"meshopt_decodeIndexBuffer",INDICES:"meshopt_decodeIndexSequence"};return{ready:n,supported:!0,decodeVertexBuffer:function(t,e,a,n,i){A(r.exports.meshopt_decodeVertexBuffer,t,e,a,n,r.exports[o[i]])},decodeIndexBuffer:function(t,e,a,n){A(r.exports.meshopt_decodeIndexBuffer,t,e,a,n)},decodeIndexSequence:function(t,e,a,n){A(r.exports.meshopt_decodeIndexSequence,t,e,a,n)},decodeGltfBuffer:function(t,e,a,n,B,E){A(r.exports[i[B]],t,e,a,n,r.exports[o[E]])}}}(),z=1,j=2,q={};q[0]=L.PixelFormat.RGB_DXT1,q[z]=L.PixelFormat.RGBA_DXT3,q[j]=L.PixelFormat.RGBA_DXT5;var $,tt=0,et=!1;function rt(t,e){var r=t.data,a=r.byteLength,A=new Uint8Array(r),o=$._malloc(a);!function(t,e,r,a){var n,A=r/4,o=a%4,i=new Uint32Array(t.buffer,0,(a-o)/4),B=new Uint32Array(e.buffer);for(n=0;n<i.length;n++)B[A+n]=i[n];for(n=a-o;n<a;n++)e[r+n]=t[n]}(A,$.HEAPU8,o,a);var i=$._crn_get_dxt_format(o,a),B=q[i];if(!n.defined(B))throw new f.RuntimeError("Unsupported compressed format.");var E,C=$._crn_get_levels(o,a),s=$._crn_get_width(o,a),y=$._crn_get_height(o,a),d=0;for(E=0;E<C;++E)d+=L.PixelFormat.compressedTextureSizeInBytes(B,s>>E,y>>E);if(tt<d&&(n.defined(W)&&$._free(W),W=$._malloc(d),Z=new Uint8Array($.HEAPU8.buffer,W,d),tt=d),$._crn_decompress(o,a,W,d,0,C),$._free(o),n.defaultValue(t.bMipMap,!1)){var l=Z.slice(0,d);return e.push(l.buffer),new T.CompressedTextureBuffer(B,s,y,l)}var u=L.PixelFormat.compressedTextureSizeInBytes(B,s,y),P=Z.subarray(0,u),c=new Uint8Array(u);return c.set(P,0),e.push(c.buffer),new T.CompressedTextureBuffer(B,s,y,c)}var at,nt=1,At=0,ot=1,it=2,Bt=3,Et=0,Ct=1,st=2;new E.Color;var yt,dt=!1,lt=null;function ft(t,e,r,a,n,A){this.left=t,this.bottom=e,this.right=r,this.top=a,this.minHeight=n,this.maxHeight=A,this.width=r-t,this.length=a-e,this.height=A-n}function ut(t,e,r){var a=r,n=t.getUint32(a,!0),A=a+=Uint32Array.BYTES_PER_ELEMENT,o=new Uint8Array(e,a,n);return{dataViewByteOffset:A,byteOffset:a+=n*Uint8Array.BYTES_PER_ELEMENT,buffer:o}}function Pt(t,e,r,a){var n=t.getUint32(a+e,!0);a+=Uint32Array.BYTES_PER_ELEMENT;var A=r.subarray(a,a+n);return{string:s.getStringFromTypedArray(A),bytesOffset:a+=n}}function ct(t,e,r,a,n,A){var o=r,i=t.getUint16(r+a,!0);o+=Uint16Array.BYTES_PER_ELEMENT,A||(o+=Uint16Array.BYTES_PER_ELEMENT);for(var B=0;B<i;B++){var E=t.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var s=t.getUint16(o+a,!0);if(o+=Uint16Array.BYTES_PER_ELEMENT,t.getUint16(o+a,!0),o+=Uint16Array.BYTES_PER_ELEMENT,20==s||35==s);else{var y=E*s*Float32Array.BYTES_PER_ELEMENT,d=e.subarray(o,o+y);o+=y;var l="aTexCoord"+B,f=n.vertexAttributes,u=n.attrLocation;u[l]=f.length,f.push({index:u[l],typedArray:d,componentsPerAttribute:s,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:s*Float32Array.BYTES_PER_ELEMENT,normalize:!1})}}return{bytesOffset:o}}function pt(t,e,r,a,n){var A=r,o=t.getUint16(A+a,!0);A+=Uint16Array.BYTES_PER_ELEMENT,A+=Uint16Array.BYTES_PER_ELEMENT;for(var i=n.vertexAttributes,B=n.attrLocation,E=0;E<o;E++){var s=t.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;var y=t.getUint16(A+a,!0);if(A+=Uint16Array.BYTES_PER_ELEMENT,16===y){A-=Uint16Array.BYTES_PER_ELEMENT;var d=s*(y*Float32Array.BYTES_PER_ELEMENT+Uint16Array.BYTES_PER_ELEMENT),l=e.subarray(A,A+d);A+=d;var f=new Uint8Array(Float32Array.BYTES_PER_ELEMENT*y*s);n.instanceCount=s,n.instanceMode=y,n.instanceBuffer=f,n.instanceIndex=1;for(var u=Float32Array.BYTES_PER_ELEMENT*y+Uint16Array.BYTES_PER_ELEMENT,P=0;P<s;P++){var c=P*u+Uint16Array.BYTES_PER_ELEMENT,p=l.subarray(c,c+u);f.set(p,P*(u-Uint16Array.BYTES_PER_ELEMENT))}T=16*Float32Array.BYTES_PER_ELEMENT,B.uv2=i.length,i.push({index:B.uv2,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:T,instanceDivisor:1}),B.uv3=i.length,i.push({index:B.uv3,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:4*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv4=i.length,i.push({index:B.uv4,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:8*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.secondary_colour=i.length,i.push({index:B.secondary_colour,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:12*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1})}else{t.getUint16(A+a,!0),A+=Uint16Array.BYTES_PER_ELEMENT;d=s*y*Float32Array.BYTES_PER_ELEMENT;if(17===y||29===y){var T;f=e.subarray(A,A+d);n.instanceCount=s,n.instanceMode=y,n.instanceBuffer=f,n.instanceIndex=1,17===y?(T=17*Float32Array.BYTES_PER_ELEMENT,B.uv2=i.length,i.push({index:B.uv2,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:T,instanceDivisor:1}),B.uv3=i.length,i.push({index:B.uv3,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:4*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv4=i.length,i.push({index:B.uv4,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:8*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.secondary_colour=i.length,i.push({index:B.secondary_colour,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:12*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv6=i.length,i.push({index:B.uv6,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.UNSIGNED_BYTE,normalize:!0,offsetInBytes:16*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1})):29===y&&(T=29*Float32Array.BYTES_PER_ELEMENT,B.uv1=i.length,i.push({index:B.uv1,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:T,instanceDivisor:1,byteLength:d}),B.uv2=i.length,i.push({index:B.uv2,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:4*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv3=i.length,i.push({index:B.uv3,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:8*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv4=i.length,i.push({index:B.uv4,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:12*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv5=i.length,i.push({index:B.uv5,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:16*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv6=i.length,i.push({index:B.uv6,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:20*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv7=i.length,i.push({index:B.uv7,componentsPerAttribute:3,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:24*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.secondary_colour=i.length,i.push({index:B.secondary_colour,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.UNSIGNED_BYTE,normalize:!0,offsetInBytes:27*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv9=i.length,i.push({index:B.uv9,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.UNSIGNED_BYTE,normalize:!0,offsetInBytes:28*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}))}else{var L=s*y;n.instanceBounds=new Float32Array(L);for(var D=0;D<L;D++)n.instanceBounds[D]=t.getFloat32(A+a+D*Float32Array.BYTES_PER_ELEMENT,!0)}A+=d}}return{bytesOffset:A}}var Tt=new i.Cartesian3(40680631590769,40680631590769,40408299984661.445),Lt=new i.Cartesian3,Dt=new i.Cartesian3;function gt(t,e,r,a){var A=Math.cos(e);Lt.x=A*Math.cos(t),Lt.y=A*Math.sin(t),Lt.z=Math.sin(e),Lt=i.Cartesian3.normalize(Lt,Lt),i.Cartesian3.multiplyComponents(Tt,Lt,Dt);var o=Math.sqrt(i.Cartesian3.dot(Lt,Dt));return Dt=i.Cartesian3.divideByScalar(Dt,o,Dt),Lt=i.Cartesian3.multiplyByScalar(Lt,r,Lt),n.defined(a)||(a=new i.Cartesian3),i.Cartesian3.add(Dt,Lt,a)}var Ft=new i.Cartesian3,Mt=new i.Cartographic,mt=new A.Matrix4,It=new A.Matrix4;function vt(t,e,r,a,E,s,y,d){var l=a,f=e.getUint32(l+r,!0);if(E.verticesCount=f,l+=Uint32Array.BYTES_PER_ELEMENT,f<=0)return{bytesOffset:l};var u=e.getUint16(l+r,!0);l+=Uint16Array.BYTES_PER_ELEMENT;var P=e.getUint16(l+r,!0);P=u*Float32Array.BYTES_PER_ELEMENT,l+=Uint16Array.BYTES_PER_ELEMENT;var c=f*u*Float32Array.BYTES_PER_ELEMENT,p=t.subarray(l,l+c);l+=c;var T=E.vertexAttributes,L=E.attrLocation,D=void 0,g=void 0;if(y){D=new i.Cartographic,g=new i.Cartographic;var F=new Float32Array(2*f),M=new Float64Array(2*f);if(n.defined(d)){var m=new Float32Array(p.byteLength/4),I=new Float32Array(p.buffer,p.byteOffset,p.byteLength/4);J=3===u?i.Cartesian3.unpackArray(I):B.Cartesian4.unpackArray(I);var v=A.Matrix4.multiply(d.sphereMatrix,d.geoMatrix,mt),_=A.Matrix4.multiply(d.ellipsoidMatrix,d.geoMatrix,It);A.Matrix4.inverse(_,_);for(var S=new o.Ellipsoid(6378137,6378137,6378137),O=0,N=0,h=J.length;N<h;N++){var R=J[N];A.Matrix4.multiplyByPoint(v,R,Ft);var G=gt((k=S.cartesianToCartographic(Ft,Mt)).longitude,k.latitude,k.height,Ft);A.Matrix4.multiplyByPoint(_,G,R),3===u?(i.Cartesian3.pack(R,m,O),O+=3):(B.Cartesian4.pack(R,m,O),O+=4),M[2*N]=k.longitude,M[2*N+1]=k.latitude,0===N?(D.longitude=k.longitude,D.latitude=k.latitude,g.longitude=k.longitude,g.latitude=k.latitude):(D.longitude=Math.max(k.longitude,D.longitude),D.latitude=Math.max(k.latitude,D.latitude),g.longitude=Math.min(k.longitude,g.longitude),g.latitude=Math.min(k.latitude,g.latitude))}p=m}else for(var x=new i.Cartesian3,b=new i.Cartesian3,U=new Float32Array(p.buffer,p.byteOffset,p.byteLength/4),K=new i.Cartographic,H=0;H<f;H++)A.Matrix4.multiplyByPoint(s,i.Cartesian3.fromElements(U[3*H],U[3*H+1],U[3*H+2],x),b),K=i.Cartographic.fromCartesian(b),M[2*H]=K.longitude,M[2*H+1]=K.latitude,0===H?(D.longitude=K.longitude,D.latitude=K.latitude,g.longitude=K.longitude,g.latitude=K.latitude):(D.longitude=Math.max(K.longitude,D.longitude),D.latitude=Math.max(K.latitude,D.latitude),g.longitude=Math.min(K.longitude,g.longitude),g.latitude=Math.min(K.latitude,g.latitude));for(H=0;H<f;H++)F[2*H]=M[2*H]-g.longitude,F[2*H+1]=M[2*H+1]-g.latitude;L.aPosition=T.length,T.push({index:L.aPosition,typedArray:p,componentsPerAttribute:u,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:P,normalize:!1}),L.img=T.length,T.push({index:L.img,typedArray:F,componentsPerAttribute:2,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:2*Float32Array.BYTES_PER_ELEMENT,normalize:!1})}else{if(3===u&&n.defined(s)){b=new i.Cartesian3,U=new Float32Array(p.buffer,p.byteOffset,p.byteLength/4);for(var V=new Float32Array(p.byteLength/4+f),w=U.length,Y=(H=0,0);H<w;H+=3,Y+=4)V[Y]=U[H],V[Y+1]=U[H+1],V[Y+2]=U[H+2],A.Matrix4.multiplyByPoint(s,i.Cartesian3.fromElements(V[Y],V[Y+1],V[Y+2],x),b),V[Y+3]=i.Cartographic.fromCartesian(b).height;p=V,P=(u=4)*Float32Array.BYTES_PER_ELEMENT}if(n.defined(d)){var J;m=new Float32Array(p.byteLength/4),I=new Float32Array(p.buffer,p.byteOffset,p.byteLength/4);J=3===u?i.Cartesian3.unpackArray(I):B.Cartesian4.unpackArray(I);v=A.Matrix4.multiply(d.sphereMatrix,d.geoMatrix,mt),_=A.Matrix4.multiply(d.ellipsoidMatrix,d.geoMatrix,It);A.Matrix4.inverse(_,_);for(S=new o.Ellipsoid(6378137,6378137,6378137),O=0,N=0,h=J.length;N<h;N++){R=J[N];A.Matrix4.multiplyByPoint(v,R,Ft);var k;G=gt((k=S.cartesianToCartographic(Ft,Mt)).longitude,k.latitude,k.height,Ft);A.Matrix4.multiplyByPoint(_,G,R),3===u?(i.Cartesian3.pack(R,m,O),O+=3):(B.Cartesian4.pack(R,m,O),O+=4)}p=m}L.aPosition=T.length,T.push({index:L.aPosition,typedArray:p,componentsPerAttribute:u,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:P,normalize:!1})}return{bytesOffset:l,cartographicBounds:{max:D,min:g}}}function _t(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);if(A+=Uint32Array.BYTES_PER_ELEMENT,o<=0)return{bytesOffset:A};var i=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var B=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var E=o*i*Float32Array.BYTES_PER_ELEMENT,s=t.subarray(A,A+E);if(A+=E,!n.ignoreNormal){var y=n.vertexAttributes,d=n.attrLocation;d.aNormal=y.length,y.push({index:d.aNormal,typedArray:s,componentsPerAttribute:i,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:B,normalize:!1})}return{bytesOffset:A}}var St={0:Uint32Array.BYTES_PER_ELEMENT,1:Float32Array.BYTES_PER_ELEMENT,2:Float64Array.BYTES_PER_ELEMENT};function Ot(t,e,r,a,n){var A,o=a,i=e.getUint32(o+r,!0);if(o+=Uint32Array.BYTES_PER_ELEMENT,n.verticesCount,i>0){e.getUint16(o+r,!0),o+=Uint16Array.BYTES_PER_ELEMENT,o+=2*Uint8Array.BYTES_PER_ELEMENT;var B=i*Uint8Array.BYTES_PER_ELEMENT*4;A=M(t,o,o+B),o+=B;var E=n.vertexAttributes,s=n.attrLocation;s.aColor=E.length,E.push({index:s.aColor,typedArray:A,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.UNSIGNED_BYTE,offsetInBytes:0,strideInBytes:4,normalize:!0})}return{bytesOffset:o}}function Nt(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);return A+=Uint32Array.BYTES_PER_ELEMENT,o<=0?{bytesOffset:A}:(e.getUint16(A+r,!0),A+=Uint16Array.BYTES_PER_ELEMENT,A+=2*Uint8Array.BYTES_PER_ELEMENT,{bytesOffset:A+=o*Uint8Array.BYTES_PER_ELEMENT*4})}function ht(t,e,r,a,n){var A=n,o=[],i=r.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;for(var B=0;B<i;B++){var E={};3===t&&(r.getUint32(A+a,!0),A+=Uint32Array.BYTES_PER_ELEMENT);var C=r.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;var s=r.getUint8(A+a,!0);A+=Uint8Array.BYTES_PER_ELEMENT,r.getUint8(A+a,!0),A+=Uint8Array.BYTES_PER_ELEMENT;var y=r.getUint8(A+a,!0);if(A+=Uint8Array.BYTES_PER_ELEMENT,A+=Uint8Array.BYTES_PER_ELEMENT,C>0){var d=0,l=null;1===s||3===s?(d=C*Uint32Array.BYTES_PER_ELEMENT,l=e.subarray(A,A+d)):(d=C*Uint16Array.BYTES_PER_ELEMENT,l=e.subarray(A,A+d),C%2!=0&&(d+=2)),E.indicesTypedArray=l,A+=d}E.indicesCount=C,E.indexType=s,E.primitiveType=y;var f=[],u=r.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;for(var P=0;P<u;P++){var c=Pt(r,a,e,A),p=c.string;A=c.bytesOffset,f.push(p),E.materialCode=p}if(0===u&&(E.materialCode="OSGBEmpty"),o.push(E),0!==A%4)A+=4-A%4}return{bytesOffset:A,arrIndexPackage:o}}function Rt(t,e,r,a,E,s,y,l,f){var u,P,c=a,p=e.getUint32(c+r,!0);return E.nCompressOptions=p,c+=Uint32Array.BYTES_PER_ELEMENT,(p&d.VertexCompressOption.SVC_Vertex)==d.VertexCompressOption.SVC_Vertex?(u=function(t,e,r,a,E,s){var y=a,d=e.getUint32(y+r,!0);if(E.verticesCount=d,y+=Uint32Array.BYTES_PER_ELEMENT,d<=0)return{bytesOffset:y};var l=e.getUint16(y+r,!0);y+=Uint16Array.BYTES_PER_ELEMENT;var f=e.getUint16(y+r,!0);f=l*Int16Array.BYTES_PER_ELEMENT,y+=Uint16Array.BYTES_PER_ELEMENT;var u=e.getFloat32(y+r,!0);y+=Float32Array.BYTES_PER_ELEMENT;var P=new B.Cartesian4;P.x=e.getFloat32(y+r,!0),y+=Float32Array.BYTES_PER_ELEMENT,P.y=e.getFloat32(y+r,!0),y+=Float32Array.BYTES_PER_ELEMENT,P.z=e.getFloat32(y+r,!0),y+=Float32Array.BYTES_PER_ELEMENT,P.w=e.getFloat32(y+r,!0),y+=Float32Array.BYTES_PER_ELEMENT,E.vertCompressConstant=u,E.minVerticesValue=P;var c=d*l*Int16Array.BYTES_PER_ELEMENT,p=t.subarray(y,y+c);if(y+=c,n.defined(s)){var T=new Uint16Array(p.byteLength/2),L=new Uint16Array(p.buffer,p.byteOffset,p.byteLength/2),D=B.Cartesian4.unpackArray(L);for(let t=0,e=D.length;t<e;t++){let e=D[t];i.Cartesian3.multiplyByScalar(e,u,e),i.Cartesian3.add(e,P,e)}var g=A.Matrix4.multiply(s.sphereMatrix,s.geoMatrix,mt),F=A.Matrix4.multiply(s.ellipsoidMatrix,s.geoMatrix,It);A.Matrix4.inverse(F,F);var M=new o.Ellipsoid(6378137,6378137,6378137),m=0;for(let t=0,e=D.length;t<e;t++){let e=D[t];A.Matrix4.multiplyByPoint(g,e,Ft);let r=M.cartesianToCartographic(Ft,Mt),a=gt(r.longitude,r.latitude,r.height,Ft);A.Matrix4.multiplyByPoint(F,a,e),i.Cartesian3.subtract(e,P,e),i.Cartesian3.divideByScalar(e,u,e),B.Cartesian4.pack(e,T,m),m+=4}p=T}var I=E.vertexAttributes,v=E.attrLocation;return v.aPosition=I.length,I.push({index:v.aPosition,typedArray:p,componentsPerAttribute:l,componentDatatype:C.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:f,normalize:!1}),{bytesOffset:y}}(t,e,r,c,E,f),c=u.bytesOffset):(c=(u=vt(t,e,r,c,E,y,l,f)).bytesOffset,P=u.cartographicBounds),(p&d.VertexCompressOption.SVC_Normal)==d.VertexCompressOption.SVC_Normal?(u=function(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);if(A+=Uint32Array.BYTES_PER_ELEMENT,o<=0)return{bytesOffset:A};e.getUint16(A+r,!0),A+=Uint16Array.BYTES_PER_ELEMENT;var i=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var B=2*o*Int16Array.BYTES_PER_ELEMENT,E=t.subarray(A,A+B);if(A+=B,!n.ignoreNormal){var s=n.vertexAttributes,y=n.attrLocation;y.aNormal=s.length,s.push({index:y.aNormal,typedArray:E,componentsPerAttribute:2,componentDatatype:C.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:i,normalize:!1})}return{bytesOffset:A}}(t,e,r,c,E),c=u.bytesOffset):c=(u=_t(t,e,r,c,E)).bytesOffset,c=(u=Nt(0,e,r,c=(u=Ot(t,e,r,c,E)).bytesOffset)).bytesOffset,(p&d.VertexCompressOption.SVC_TexutreCoord)==d.VertexCompressOption.SVC_TexutreCoord?(u=function(t,e,r,a,n){n.texCoordCompressConstant=[],n.minTexCoordValue=[];var A=r,o=t.getUint16(r+a,!0);A+=Uint16Array.BYTES_PER_ELEMENT,A+=Uint16Array.BYTES_PER_ELEMENT;for(var i=0,E=0;E<o;E++){var s=t.getUint8(A+a,!0);A+=Uint8Array.BYTES_PER_ELEMENT,A+=3*Uint8Array.BYTES_PER_ELEMENT;var y=t.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;var d=t.getUint16(A+a,!0);A+=Uint16Array.BYTES_PER_ELEMENT,t.getUint16(A+a,!0),A+=Uint16Array.BYTES_PER_ELEMENT;var l=t.getFloat32(A+a,!0);A+=Float32Array.BYTES_PER_ELEMENT,n.texCoordCompressConstant.push(l);var f=new B.Cartesian4;f.x=t.getFloat32(A+a,!0),A+=Float32Array.BYTES_PER_ELEMENT,f.y=t.getFloat32(A+a,!0),A+=Float32Array.BYTES_PER_ELEMENT,f.z=t.getFloat32(A+a,!0),A+=Float32Array.BYTES_PER_ELEMENT,f.w=t.getFloat32(A+a,!0),A+=Float32Array.BYTES_PER_ELEMENT,n.minTexCoordValue.push(f);var u=y*d*Int16Array.BYTES_PER_ELEMENT,P=e.subarray(A,A+u),c=(A+=u)%4;0!==c&&(A+=4-c);var p="aTexCoord"+i,T=n.vertexAttributes,L=n.attrLocation;if(L[p]=T.length,T.push({index:L[p],typedArray:P,componentsPerAttribute:d,componentDatatype:C.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:d*Int16Array.BYTES_PER_ELEMENT,normalize:!1}),s){u=y*Float32Array.BYTES_PER_ELEMENT;var D=e.subarray(A,A+u);A+=u,n.texCoordZMatrix=!0,L[p="aTexCoordZ"+i]=T.length,T.push({index:L[p],typedArray:D,componentsPerAttribute:1,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:Float32Array.BYTES_PER_ELEMENT,normalize:!1})}i++}return{bytesOffset:A}}(e,t,c,r,E),c=u.bytesOffset):c=(u=ct(e,t,c,r,E,s)).bytesOffset,(p&d.VertexCompressOption.SVC_TexutreCoordIsW)==d.VertexCompressOption.SVC_TexutreCoordIsW&&(E.textureCoordIsW=!0),{bytesOffset:c=(u=pt(e,t,c,r,E)).bytesOffset,cartographicBounds:P}}function Gt(t,e,r,a,A,o,i,B,E,s){3===t&&(r.getUint32(A,!0),A+=Uint32Array.BYTES_PER_ELEMENT,n.defined(E)&&E||(B=void 0));var y,d=A;d=(y=vt(e,r,a,d,o,B,E,s)).bytesOffset;var l=y.cartographicBounds;if(d=(y=Ot(e,r,a,d=(y=_t(e,r,a,d,o)).bytesOffset,o)).bytesOffset,3!==t&&(d=(y=Nt(0,r,a,d)).bytesOffset),d=(y=pt(r,e,d=(y=ct(r,e,d,a,o,i)).bytesOffset,a,o)).bytesOffset,3===t&&(y=function(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);A+=Uint32Array.BYTES_PER_ELEMENT;for(var i=0;i<o;i++){var B=e.getUint32(A+r,!0);A+=Uint32Array.BYTES_PER_ELEMENT;var E=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var s=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var y=B*E*St[s],d=t.subarray(A,A+y);A+=y;var l=n.vertexAttributes,f=n.attrLocation,u="aCustom"+i;f[u]=l.length,l.push({index:f[u],typedArray:d,componentsPerAttribute:E,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,normalize:!1})}return{bytesOffset:A}}(e,r,a,d,o),d=y.bytesOffset),3==t){var f=Pt(r,a,e,d);d=f.bytesOffset,o.customVertexAttribute=JSON.parse(f.string);var u="aCustom"+o.customVertexAttribute.TextureCoordMatrix,P="aCustom"+o.customVertexAttribute.VertexWeight,c="aCustom"+o.customVertexAttribute.VertexWeight_1;n.defined(o.attrLocation[u])&&(o.attrLocation.aTextureCoordMatrix=o.attrLocation[u],delete o.attrLocation[u]),n.defined(o.attrLocation[P])&&(o.attrLocation.aVertexWeight=o.attrLocation[P],delete o.attrLocation[P]),n.defined(o.attrLocation[c])&&(o.attrLocation.aVertexWeight_1=o.attrLocation[c],delete o.attrLocation[c]);for(var p=Object.keys(o.attrLocation),T=p.length,L=0;L<T;++L){var D=p[L];-1!==D.indexOf("aCustom")&&delete o.attrLocation[D]}var g=(d+a)%4;g&&(g=4-g),d+=g}return 3===t&&(y=function(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);if(A+=Uint32Array.BYTES_PER_ELEMENT,o<=0)return{bytesOffset:A};var i=e.getUint16(A+r,!0);return A+=Uint16Array.BYTES_PER_ELEMENT,e.getUint16(A+r,!0),A+=Uint16Array.BYTES_PER_ELEMENT,{bytesOffset:A+=o*i*Float32Array.BYTES_PER_ELEMENT}}(0,r,a,d),d=y.bytesOffset),{bytesOffset:d,cartographicBounds:l}}function xt(t){return 0!==t.length&&"ClampGroundAndObjectLinePass"===t[0].materialCode}var bt=1,Ut=4,Kt=16,Ht=32,Vt=64,wt=128,Yt=512,Jt=1024;function kt(t,e,r,a,E,s,y,l,f,u,P,c,p){var T=t,L=0,D=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT,u=n.defaultValue(u,n.defaultValue.EMPTY_OBJECT);for(var g=void 0,F=0;F<D;F++){3===l&&(e.getUint32(L+r,!0),L+=Uint32Array.BYTES_PER_ELEMENT);var I=(kt=Pt(e,r,T,L)).string;if(n.defined(f)){var v=n.defaultValue(u[I],A.Matrix4.IDENTITY);g=new A.Matrix4,A.Matrix4.multiply(f,v,g)}n.defined(p)&&(p.geoMatrix=n.defaultValue(u[I],A.Matrix4.IDENTITY));var _=(L=kt.bytesOffset)%4;0!==_&&(L+=4-_);var S=At;if(S=e.getUint32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT,(fe={vertexAttributes:[],attrLocation:{},instanceCount:0,instanceMode:0,instanceIndex:-1}).ignoreNormal=a.ignoreNormal,3===l)switch(S){case Et:S=ot;break;case Ct:S=Bt;break;case st:S=it}if(S===Bt){3===l&&(e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT),l>=2&&(e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT);var O,N={};N.posUniqueID=e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT,N.normalUniqueID=e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT,N.colorUniqueID=e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT,N.secondColorUniqueID=e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT,3===l?(O=e.getUint32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT):(O=e.getUint16(L+r,!0),L+=Int16Array.BYTES_PER_ELEMENT);for(var h=[],R=0;R<O;R++){var G=e.getInt32(L+r,!0);h.push(G),L+=Int32Array.BYTES_PER_ELEMENT}N.texCoordUniqueIDs=h;var x=[];if(3===l){var b=e.getUint32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;for(var U=0;U<b;U++){var K=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT,x.push(K)}}N.vertexAttrUniqueIDs=x;var H=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;var V=[],w={};if(H>0){var Y=(J=Pt(e,r,T,L)).string;L=J.bytesOffset,w.materialCode=Y,V.push(w)}3===l&&((zt=(L+r)%4)&&(zt=4-zt),L+=zt);var J,k=new Object,W=e.getUint32(L+r,!0),Z=M(T,L+=Int32Array.BYTES_PER_ELEMENT,L+W);if(H>0?m.dracoDecodeMesh(at,Z,W,fe,w,N,g,P,k,p):m.dracoDecodePointCloud(at,Z,W,fe,N),n.defined(k.min)&&n.defined(k.max)||(k=void 0),L+=W,3===l)(zt=(L+r)%4)&&(zt=4-zt),(zt=((L=(J=Pt(e,r,T,L+=zt)).bytesOffset)+r)%4)&&(zt=4-zt),L+=zt;a[I]={vertexPackage:fe,arrIndexPackage:V,cartographicBounds:k}}else if(S==it&&3==l){var X=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT;var z=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT,fe.minVerticesValue=new B.Cartesian4,fe.minTexCoordValue=[new o.Cartesian2,new o.Cartesian2],fe.texCoordCompressConstant=[new i.Cartesian3,new i.Cartesian3];V=[];for(var j=0;j<z;j++){var q=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT,fe.vertCompressConstant=e.getFloat32(L+r,!0),L+=Float32Array.BYTES_PER_ELEMENT,fe.minVerticesValue.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,fe.minVerticesValue.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,fe.minVerticesValue.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT;var $=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var tt=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var et=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var rt=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var nt=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var yt=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var dt=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var lt=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT,fe.minTexCoordValue[0].x=et,fe.minTexCoordValue[0].y=rt,fe.minTexCoordValue[1].x=dt,fe.minTexCoordValue[1].y=lt,fe.texCoordCompressConstant[0].x=$,fe.texCoordCompressConstant[0].y=tt,fe.texCoordCompressConstant[1].x=nt,fe.texCoordCompressConstant[1].y=yt;var ft=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;k=new Object;for(var ut=0;ut<ft;ut++){var ct=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;var pt=ct,Tt=0;pt!=Yt&&pt!=Jt||(Tt=e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT);var Lt=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;var Dt=new Uint8Array(e.buffer,L+r,Lt);(zt=((L+=Uint8Array.BYTES_PER_ELEMENT*Lt)+r)%4)&&(zt=4-zt),L+=zt,Zt(q,pt,Tt,Dt,fe,X,p,P,k,g)}L=(Qt=Pt(e,r,T,L)).bytesOffset,fe.customVertexAttribute=JSON.parse(Qt.string);var gt="aCustom"+fe.customVertexAttribute.TextureCoordMatrix,Ft="aCustom"+fe.customVertexAttribute.VertexWeight,Mt="aCustom"+fe.customVertexAttribute.VertexWeight_1;n.defined(fe.attrLocation[gt])&&(fe.attrLocation.aTextureCoordMatrix=fe.attrLocation[gt],j===z-1&&delete fe.attrLocation[gt]),n.defined(fe.attrLocation[Ft])&&(fe.attrLocation.aVertexWeight=fe.attrLocation[Ft],j===z-1&&delete fe.attrLocation[Ft]),n.defined(fe.attrLocation[Mt])&&(fe.attrLocation.aVertexWeight_1=fe.attrLocation[Mt],j===z-1&&delete fe.attrLocation[Mt]);for(var mt=(jt=Object.keys(fe.attrLocation)).length,It=0;It<mt;++It){-1!==(qt=jt[It]).indexOf("aCustom")&&delete fe.attrLocation[qt]}(zt=(L+r)%4)&&(zt=4-zt),L+=zt;var vt=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;for(var _t=0;_t<vt;_t++){w={};var St=e.getInt32(L+r,!0);if(L+=Int32Array.BYTES_PER_ELEMENT,St>0){var Ot=e.getInt8(L+r,!0);L+=Int8Array.BYTES_PER_ELEMENT,e.getInt8(L+r,!0),L+=Int8Array.BYTES_PER_ELEMENT;var Nt=e.getInt8(L+r,!0);L+=Int8Array.BYTES_PER_ELEMENT,e.getInt8(L+r,!0),L+=Int8Array.BYTES_PER_ELEMENT;var bt,Ut,Kt=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT,13!==Nt?(bt=new Uint8Array(e.buffer,L+r,Kt),L+=Uint8Array.BYTES_PER_ELEMENT*Kt):(bt=new Uint32Array(e.buffer,L+r,Kt),L+=Uint32Array.BYTES_PER_ELEMENT*Kt),(zt=(L+r)%4)&&(zt=4-zt),L+=zt,13!==Nt?(Ut=C.ComponentDatatype.createTypedArray(C.ComponentDatatype.UNSIGNED_BYTE,St*Uint32Array.BYTES_PER_ELEMENT),Q.decodeIndexBuffer(Ut,St,Uint32Array.BYTES_PER_ELEMENT,bt)):Ut=bt;var Ht,Vt=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT,w.indexType=Ot,0===Ot?Ht=new Uint16Array(St):1===Ot&&(Ht=new Uint32Array(St)),w.indicesCount=St;var wt=new Uint32Array(Ut.buffer,Ut.byteOffset,Ut.byteLength/4);Ht.set(wt,0),w.indicesTypedArray=Ht,w.primitiveType=Nt;for(ut=0;ut<Vt;ut++){var kt;Y=(kt=Pt(e,r,T,L)).string;L=kt.bytesOffset,w.materialCode=Y}if(V.length>0&&13!==Nt){var Wt=fe.preVertexCount;w.indicesTypedArray=wt.map((function(t){return t+Wt})),w.indexType=1}V.push(w),(zt=(L+r)%4)&&(zt=4-zt),L+=zt}}}fe.nCompressOptions=X,2===V.length&&13===V[1].primitiveType&&V[1].indicesCount>=3&&(Xt=d.S3MEdgeProcessor.createEdgeDataByIndices(fe,V[1],s)),n.defined(k.min)&&n.defined(k.max)||(k=void 0),a[I]={vertexPackage:fe,arrIndexPackage:V,edgeGeometry:Xt,cartographicBounds:k}}else{var Xt;if(S===ot||S===At)L=(kt=Gt(l,T,e,r,L,fe,E,g,P,p)).bytesOffset,k=kt.cartographicBounds;else if(S===it&&(L=(kt=Rt(T,e,r,L,fe,E,g,P,p)).bytesOffset,k=kt.cartographicBounds,3==l)){var Qt;L=(Qt=Pt(e,r,T,L)).bytesOffset,fe.customVertexAttribute=JSON.parse(Qt.string);var zt;gt="aCustom"+fe.customVertexAttribute.TextureCoordMatrix,Ft="aCustom"+fe.customVertexAttribute.VertexWeight,Mt="aCustom"+fe.customVertexAttribute.VertexWeight_1;n.defined(fe.attrLocation[gt])&&(fe.attrLocation.aTextureCoordMatrix=fe.attrLocation[gt],delete fe.attrLocation[gt]),n.defined(fe.attrLocation[Ft])&&(fe.attrLocation.aVertexWeight=fe.attrLocation[Ft],delete fe.attrLocation[Ft]),n.defined(fe.attrLocation[Mt])&&(fe.attrLocation.aVertexWeight_1=fe.attrLocation[Mt],delete fe.attrLocation[Mt]);var jt;for(mt=(jt=Object.keys(fe.attrLocation)).length,It=0;It<mt;++It){var qt;-1!==(qt=jt[It]).indexOf("aCustom")&&delete fe.attrLocation[qt]}(zt=(L+r)%4)&&(zt=4-zt),L+=zt}xt(V=(kt=ht(l,T,e,r,L)).arrIndexPackage)&&(fe.clampRegionEdge=!0),2===V.length&&13===V[1].primitiveType&&V[1].indicesCount>=3&&(Xt=d.S3MEdgeProcessor.createEdgeDataByIndices(fe,V[1],s)),L=kt.bytesOffset,n.defined(k)&&n.defined(k.min)&&n.defined(k.max)||(k=void 0),a[I]={vertexPackage:fe,arrIndexPackage:V,edgeGeometry:Xt,cartographicBounds:k}}if(3!==l&&n.defined(y)&&y){var $t=e.getUint16(L+r,!0);if(L+=Uint16Array.BYTES_PER_ELEMENT,1===$t){var te=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT;var ee,re=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT,e.getFloat32(L+r,!0),L+=Float32Array.BYTES_PER_ELEMENT;var ae=new Array(te),ne=new Array(te),Ae=new Array(te),oe=new Array(te);for(ee=0;ee<te;ee++){var ie=e.getFloat32(L+r,!0);L+=Float32Array.BYTES_PER_ELEMENT,ae[ee]=ie;var Be=e.getUint16(L+r,!0);L+=Uint16Array.BYTES_PER_ELEMENT,ne[ee]=Be;var Ee=e.getUint16(L+r,!0);L+=Uint16Array.BYTES_PER_ELEMENT,Ae[ee]=Ee;for(var Ce=Ee*re,se=new Array(Ce),ye=0;ye<Ce;ye++){gt=e.getFloat32(L+r,!0);L+=Float32Array.BYTES_PER_ELEMENT,se[ye]=gt}oe[ee]=se}}var de=new i.Cartesian3,le=new i.Cartesian3;de.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,de.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,de.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,le.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,le.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,le.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,a[I].min=de,a[I].max=le;var fe=a[I].vertexPackage;n.defined(fe.instanceBuffer)&&2===l&&(fe.instanceBounds=new Float32Array(6),i.Cartesian3.pack(de,fe.instanceBounds,0),i.Cartesian3.pack(le,fe.instanceBounds,3))}if(3===l){var ue=new i.Cartesian3;ue.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,ue.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,ue.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT;var Pe=new i.Cartesian3;Pe.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,Pe.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,Pe.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT;var ce=new i.Cartesian3;ce.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,ce.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,ce.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT;var pe=new i.Cartesian3;pe.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,pe.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,pe.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT}}}function Wt(t,e,r){var a=t.typedArray,n=new r(a.length+e.length);n.set(a,0),n.set(e,a.length),t.typedArray=n}function Zt(t,e,r,a,B,E,s,y,d,l){var f,u=0,P=B.vertexAttributes,c=B.attrLocation;switch(e){case Ut:case Kt:case Ht:u=2*Uint16Array.BYTES_PER_ELEMENT,0!=(16&E)||e!==Kt&&e!==Ht||(u=2*Float32Array.BYTES_PER_ELEMENT),f=C.ComponentDatatype.createTypedArray(C.ComponentDatatype.UNSIGNED_BYTE,t*u);break;case Vt:case wt:u=4*Uint8Array.BYTES_PER_ELEMENT,f=C.ComponentDatatype.createTypedArray(C.ComponentDatatype.UNSIGNED_BYTE,4*t);break;case Yt:case Jt:u=Float32Array.BYTES_PER_ELEMENT*r,f=C.ComponentDatatype.createTypedArray(C.ComponentDatatype.UNSIGNED_BYTE,t*r*4);break;default:u=4*Uint16Array.BYTES_PER_ELEMENT,f=C.ComponentDatatype.createTypedArray(C.ComponentDatatype.UNSIGNED_BYTE,t*u)}switch(Q.decodeVertexBuffer(f,t,u,a,a.length),e){case bt:var p=new Uint16Array(f.buffer,0,f.length/2),T=C.ComponentDatatype.SHORT;if(n.defined(s)){var L=i.Cartesian3.unpackArray(p);for(let t=0,e=L.length;t<e;t++){let e=L[t];i.Cartesian3.multiplyByScalar(e,B.vertCompressConstant,e),i.Cartesian3.add(e,B.minVerticesValue,e)}var D=A.Matrix4.multiply(s.sphereMatrix,s.geoMatrix,mt),g=A.Matrix4.multiply(s.ellipsoidMatrix,s.geoMatrix,It);A.Matrix4.inverse(g,g);var F=new o.Ellipsoid(6378137,6378137,6378137);for(let t=0,e=L.length;t<e;t++){let e=L[t];A.Matrix4.multiplyByPoint(D,e,Ft);let r=F.cartesianToCartographic(Ft,Mt),a=gt(r.longitude,r.latitude,r.height,Ft);A.Matrix4.multiplyByPoint(g,a,e)}var M=new Array(3*L.length);i.Cartesian3.packArray(L,M),p=new Float32Array(M),T=C.ComponentDatatype.FLOAT}if(void 0!==(R=c.aPosition)?(Wt(P[R],p,Uint16Array),B.preVertexCount=B.verticesCount,B.verticesCount+=t):(c.aPosition=P.length,P.push({index:c.aPosition,typedArray:p,componentsPerAttribute:4,componentDatatype:T,offsetInBytes:0,strideInBytes:0,normalize:!1}),B.verticesCount=t),!n.defined(s)&&y){for(var m=new i.Cartographic,I=new i.Cartographic,v=new Float32Array(2*t),_=new Float64Array(2*t),S=new i.Cartesian3,O=new i.Cartesian3,N=new i.Cartographic,h=0;h<t;h++)A.Matrix4.multiplyByPoint(l,i.Cartesian3.fromElements(p[4*h]*B.vertCompressConstant+B.minVerticesValue.x,p[4*h+1]*B.vertCompressConstant+B.minVerticesValue.y,p[4*h+2]*B.vertCompressConstant+B.minVerticesValue.z,S),O),N=i.Cartographic.fromCartesian(O),_[2*h]=N.longitude,_[2*h+1]=N.latitude,0===h?(m.longitude=N.longitude,m.latitude=N.latitude,I.longitude=N.longitude,I.latitude=N.latitude):(m.longitude=Math.max(N.longitude,m.longitude),m.latitude=Math.max(N.latitude,m.latitude),I.longitude=Math.min(N.longitude,I.longitude),I.latitude=Math.min(N.latitude,I.latitude));for(h=0;h<t;h++)v[2*h]=_[2*h]-I.longitude,v[2*h+1]=_[2*h+1]-I.latitude;c.img=P.length,P.push({index:c.img,typedArray:v,componentsPerAttribute:2,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:2*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),d.max=m,d.min=I}break;case Ut:var R=c.aNormal,G=new Int16Array(f.buffer,0,f.length/2);void 0!==R?Wt(P[R],G,Uint16Array):(c.aNormal=P.length,P.push({index:c.aNormal,typedArray:G,componentsPerAttribute:2,componentDatatype:C.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case Kt:var x=new Uint16Array(f.buffer,0,f.length/2),b=(R=c.aTexCoord0,T=C.ComponentDatatype.SHORT,Uint16Array);0==(16&E)&&(T=C.ComponentDatatype.FLOAT,b=Float32Array,x=new Float32Array(f.buffer,0,f.length/4)),void 0!==R?Wt(P[R],x,b):(c.aTexCoord0=P.length,P.push({index:c.aTexCoord0,typedArray:x,componentsPerAttribute:2,componentDatatype:T,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case Ht:x=new Uint16Array(f.buffer,0,f.length/2),R=c.aTexCoord1,T=C.ComponentDatatype.SHORT,b=Uint16Array;0==(16&E)&&(T=C.ComponentDatatype.FLOAT,b=Float32Array,x=new Float32Array(f.buffer,0,f.length/4)),void 0!==R?Wt(P[R],x,b):(c.aTexCoord1=P.length,P.push({index:c.aTexCoord1,typedArray:x,componentsPerAttribute:2,componentDatatype:T,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case Vt:void 0!==(R=c.aColor)?Wt(P[R],f,Uint8Array):(c.aColor=P.length,P.push({index:c.aColor,typedArray:f,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.UNSIGNED_BYTE,offsetInBytes:0,strideInBytes:0,normalize:!0}));break;case wt:void 0!==(R=c.aSecondColor)?Wt(P[R],f,Uint8Array):(c.aSecondColor=P.length,P.push({index:c.aSecondColor,typedArray:f,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.BYTE,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case Yt:x=new Float32Array(f.buffer,0,f.length/4);void 0!==(R=c.aCustom0||c.aVertexWeight)?Wt(P[R],x,Float32Array):(c.aCustom0=P.length,P.push({index:c.aCustom0,typedArray:x,componentsPerAttribute:r,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case Jt:var U=new Float32Array(f.buffer,0,f.length/4);void 0!==(R=c.aCustom1||c.aTextureCoordMatrix)?Wt(P[R],U,Float32Array):(c.aCustom1=P.length,P.push({index:c.aCustom1,typedArray:U,componentsPerAttribute:r,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,normalize:!1}))}}function Xt(t,e,r,a,o){var i={},B=[],E=new A.Matrix4,C=t;o=n.defaultValue(o,{});for(var s=0;s<16;s++)E[s]=e.getFloat64(r+a,!0),r+=Float64Array.BYTES_PER_ELEMENT;i.matrix=E,i.skeletonNames=B;var y=e.getUint32(r+a,!0);r+=Uint32Array.BYTES_PER_ELEMENT;for(var d=0;d<y;d++){var l=Pt(e,a,C,r),f=l.string;r=l.bytesOffset,B.push(f),o[f]=E}return{byteOffset:r,geode:i}}function Qt(t){var e=t.indexOf("Geometry");if(-1===e)return t;var r=t.substring(e,t.length);return t.replace(r,"")}function zt(t,e,r,a,B,E,C){var s={},d=r.getFloat32(a+B,!0);a+=Float32Array.BYTES_PER_ELEMENT;var l=r.getUint16(a+B,!0);a+=Uint16Array.BYTES_PER_ELEMENT,s.rangeMode=l,s.rangeList=d;var f=new i.Cartesian3;f.x=r.getFloat64(a+B,!0),a+=Float64Array.BYTES_PER_ELEMENT,f.y=r.getFloat64(a+B,!0),a+=Float64Array.BYTES_PER_ELEMENT,f.z=r.getFloat64(a+B,!0),a+=Float64Array.BYTES_PER_ELEMENT;var u=r.getFloat64(a+B,!0);if(a+=Float64Array.BYTES_PER_ELEMENT,n.defined(C)){var P=A.Matrix4.clone(C.sphereMatrix,mt),c=A.Matrix4.clone(C.ellipsoidMatrix,It);A.Matrix4.inverse(c,c);var p=new o.Ellipsoid(6378137,6378137,6378137);A.Matrix4.multiplyByPoint(P,f,Ft);let t=p.cartesianToCartographic(Ft,Mt),e=gt(t.longitude,t.latitude,t.height,Ft);A.Matrix4.multiplyByPoint(c,e,f)}if(s.boundingSphere=new y.BoundingSphere(f,u),3===t){var T=new i.Cartesian3;T.x=r.getFloat64(a+B,!0),a+=Float64Array.BYTES_PER_ELEMENT,T.y=r.getFloat64(a+B,!0),a+=Float64Array.BYTES_PER_ELEMENT,T.z=r.getFloat64(a+B,!0),a+=Float64Array.BYTES_PER_ELEMENT;var L=new i.Cartesian3;L.x=r.getFloat64(a+B,!0),a+=Float64Array.BYTES_PER_ELEMENT,L.y=r.getFloat64(a+B,!0),a+=Float64Array.BYTES_PER_ELEMENT,L.z=r.getFloat64(a+B,!0),a+=Float64Array.BYTES_PER_ELEMENT;var D=new i.Cartesian3;D.x=r.getFloat64(a+B,!0),a+=Float64Array.BYTES_PER_ELEMENT,D.y=r.getFloat64(a+B,!0),a+=Float64Array.BYTES_PER_ELEMENT,D.z=r.getFloat64(a+B,!0),a+=Float64Array.BYTES_PER_ELEMENT;var g=new i.Cartesian3;g.x=r.getFloat64(a+B,!0),a+=Float64Array.BYTES_PER_ELEMENT,g.y=r.getFloat64(a+B,!0),a+=Float64Array.BYTES_PER_ELEMENT,g.z=r.getFloat64(a+B,!0),a+=Float64Array.BYTES_PER_ELEMENT,s._obb={xExtent:L,yExtent:D,zExtent:g,obbCenter:T}}var F=e,M=(v=Pt(r,B,F,a)).string;a=v.bytesOffset,M=Qt(M=(M=M.replace(/(\.s3mblock)|(\.s3mbz)|(\.s3mb)/gi,"")).replace(/\\/gi,"/")),s.childTile=M,s.geodes=[];var m=r.getUint32(a+B,!0);a+=Uint32Array.BYTES_PER_ELEMENT;for(var I=0;I<m;I++){var v;a=(v=Xt(e,r,a,B,E)).byteOffset,s.geodes.push(v.geode)}return 3===t&&(a=(v=Pt(r,B,F,a)).bytesOffset),{pageLOD:s,bytesOffset:a}}function jt(t,e,r,a,n,A){var o=0,i={},B=[],E=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;for(var C=0;C<E;C++){var s=zt(t,e,r,o,a,n,A);o=s.bytesOffset,B.push(s.pageLOD)}return i.pageLods=B,i}function qt(t,e,r){var a=t.vertexAttributes,n=t.attrLocation,A=a.length;n["aTextureBatchId"+r]=A,a.push({index:A,typedArray:e,componentsPerAttribute:1,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0})}function $t(t,e,r,a){for(var A=r.length,o=0;o<A;o++)for(var i=r[o],B=i.subName.split("_")[0],E=i.subVertexOffsetArr,C=0;C<E.length;C++){var s=E[C],y=s.geoName,d=s.offset,l=s.count,f=s.texUnitIndex,u=e[y].vertexPackage.verticesCount,P=a[y];n.defined(P)||(P=a[y]={});var c=P[f];n.defined(c)||(c=P[f]=new Float32Array(u),p.arrayFill(c,-1));var T=n.defined(t)?t[B]:o;p.arrayFill(c,T,d,d+l)}}function te(t,e,r){var a=t.vertexAttributes,n=t.attrLocation,A=a.length;n[1===r?"instanceId":"batchId"]=A,a.push({index:A,typedArray:e,componentsPerAttribute:1,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,instanceDivisor:r})}function ee(t,e,r,a,A){var o=0,i=t,B=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;for(var E=0;E<B;E++){var C=Pt(e,r,i,o),s=C.string;o=C.bytesOffset;var y=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var l={};a[s].pickInfo=l;var f=a[s].edgeGeometry;if(-1==a[s].vertexPackage.instanceIndex){for(var u=new Float32Array(a[s].vertexPackage.verticesCount),P=0;P<y;P++){var c=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var T=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var L=0,D=0;l[c]={batchId:P};for(var g=0;g<T;g++)D=e.getUint32(o+r,!0),o+=Uint32Array.BYTES_PER_ELEMENT,L=e.getUint32(o+r,!0),o+=Uint32Array.BYTES_PER_ELEMENT,p.arrayFill(u,P,D,D+L);l[c].vertexColorOffset=D,l[c].vertexCount=L}te(a[s].vertexPackage,u,void 0)}else{var F=a[s].vertexPackage.instanceCount;a[s].vertexPackage.instanceBuffer,a[s].vertexPackage.instanceMode;var M=new Float32Array(F),m=0;for(P=0;P<y;P++){var I=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;T=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;for(g=0;g<T;g++){var v=e.getUint32(o+r,!0);if(o+=Uint32Array.BYTES_PER_ELEMENT,M[m]=m,void 0===l[I]&&(l[I]={vertexColorCount:1,instanceIds:[],vertexColorOffset:m}),l[I].instanceIds.push(v),m++,3===A){L=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT}}}te(a[s].vertexPackage,M,1)}f=a[s].edgeGeometry;if(n.defined(f)){var _,S,O=f.regular.instancesData,N=d.S3MEdgeProcessor.RegularInstanceStride;if(n.defined(O))for(S=O.length,_=0;_<S;_+=N){var h=O[_+9];O[_+9]=u[h]}var R=f.silhouette.instancesData;if(N=d.S3MEdgeProcessor.SilhouetteInstanceStride,n.defined(R))for(S=R.length,_=0;_<S;_+=N){h=R[_+12];R[_+12]=u[h]}}}}function re(t){return t<1e-10&&t>-1e-10}function ae(t,e,r,a,A,o,i,B){var E=new DataView(t),C=new Uint8Array(t),y=E.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var d=s.getStringFromTypedArray(C,r,y);d=d.replace(/(\.s3mblock)|(\.s3mbz)|(\.s3mb)/gi,""),r+=y;var l=E.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;for(var f=0;f<l;f++){var u={},p=E.getFloat32(r,!0);r+=Float32Array.BYTES_PER_ELEMENT;var T=E.getUint16(r,!0);r+=Uint16Array.BYTES_PER_ELEMENT,u.rangeMode=T,u.rangeList=p;var L={};L.x=E.getFloat64(r,!0),r+=Float64Array.BYTES_PER_ELEMENT,L.y=E.getFloat64(r,!0),r+=Float64Array.BYTES_PER_ELEMENT,L.z=E.getFloat64(r,!0),r+=Float64Array.BYTES_PER_ELEMENT;var D=E.getFloat64(r,!0);r+=Float64Array.BYTES_PER_ELEMENT,u.boundingSphere={center:L,radius:D},y=E.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT;var g=s.getStringFromTypedArray(C,r,y);r+=y,g=Qt(g=g.replace(/(\.s3mblock)|(\.s3mbz)|(\.s3mb)/gi,"")),u.childTile=g}var F={},M=E.getFloat32(r,!0);r+=Float32Array.BYTES_PER_ELEMENT;M>=3&&(E.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT),E.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT;var m=E.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var I=new Uint8Array(t,r,m),v=r+m,_=c.pako.inflate(I).buffer;B.push(_),E=new DataView(_);C=new Uint8Array(_);r=0;var S=E.getUint32(r,!0),O=ut(E,_,r+=Uint32Array.BYTES_PER_ELEMENT),N=O.buffer;r=O.byteOffset;var h=jt(M,N,E,O.dataViewByteOffset),R=r%4;0!==R&&(r+=4-R),kt((O=ut(E,_,r)).buffer,E,O.dataViewByteOffset,F,!1,void 0,void 0,M),r=O.byteOffset,3!==M&&((O=ut(E,_,r)).buffer,r=O.byteOffset);var G={};!function(t,e,r,a,A,o,i,B,E,C){var y=B,d=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;for(var l={},f=0;f<d;f++){var u=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var c=s.getStringFromTypedArray(o,y-B,u),p=(y+=u)%4;0!==p&&(y+=4-p),i.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var T=i.getUint8(y,!0);y+=Uint8Array.BYTES_PER_ELEMENT;var L=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var D=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var g=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var F=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var M,m=i.getUint32(y,!0);if(y+=Uint32Array.BYTES_PER_ELEMENT,a&&T){var I=y-B;M=o.subarray(I,I+F),y+=F}var v=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;for(var _=[],S=0;S<v;S++){u=i.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var O=s.getStringFromTypedArray(o,y-B,u);y+=u,_.push(O),r[O]=c}var N=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var h=[];for(S=0;S<N;S++){u=i.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var R=s.getStringFromTypedArray(o,y-B,u);y+=u,h.push(R)}var G=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var x=[],b=void 0,U=c;if(a)b=e[c]={};else{var K=r[c];for(U=K;n.defined(K)&&K!==c;)U=K,K=r[K];n.defined(U)&&(b=e[U])}var H=0;for(S=0;S<G;S++){u=i.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var V=s.getStringFromTypedArray(o,y-B,u);if(y+=u,a){var w=V.split("_")[0];n.defined(b[w])?H++:b[w]=S-H}var Y=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var J=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var k=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var W=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var Z=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;for(var X=[],Q=0;Q<Z;Q++){u=i.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var z=s.getStringFromTypedArray(o,y-B,u);y+=u;var j=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var q=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var $=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT,X.push({geoName:z,offset:j,count:q,texUnitIndex:$})}x.push({subName:V,offsetX:Y,offsetY:J,width:k,height:W,subVertexOffsetArr:X})}$t(b,t,x,l);var tt=!1;n.defined(M)&&g===P.S3MPixelFormat.CRN_DXT5&&et&&(M=rt({data:M},C).bufferView,tt=!0),E[c]={id:c,rootTextureName:U,width:L,height:D,compressType:g,size:F,format:m,textureData:M,subTexInfos:x,requestNames:h,isDXT:tt}}for(var z in l)if(l.hasOwnProperty(z)){var at=t[z].vertexPackage,nt=l[z];for(var $ in nt)nt.hasOwnProperty($)&&qt(at,nt[$],$)}}(F,a,A,o,0,(O=ut(E,_,r)).buffer,E,O.dataViewByteOffset,G,B),r=O.byteOffset;var x=E.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var b=C.subarray(r,r+x),U=s.getStringFromTypedArray(b);r+=x;var H=JSON.parse(U);(3===M&&(S=E.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT),(S&nt)==nt)&&(ee((O=ut(E,_,r)).buffer,E,O.dataViewByteOffset,F,M),r=O.byteOffset);if(1==M){var V=h.pageLods,w=!0;for(f=0;f<V.length;f++){var Y=V[f];w=""===Y.childTile;for(var J=Y.geodes,k=0;k<J.length;k++)for(var W=J[k].skeletonNames,Z=0;Z<W.length;Z++){var Q=W[Z];if(w){var z=F[Q].vertexPackage;z.boundingSphere=K.calcBoundingSphereInWorker(1,z)}}}}i[d]={result:!0,groupNode:h,geoPackage:F,matrials:H,texturePackage:G,version:X.S3M4,dataVersion:M,rootBatchIdMap:a,ancestorMap:A},v<e&&ae(t,e,v,a,A,!1,i,B)}function ne(e,r){var a=e.buffer;if(e.isOSGB){if(n.defined(yt)||new Promise((function(e,r){t(["./OSGBToS3M-caf1aa52"],e,r)})).then((function(t){(yt=t.default).onRuntimeInitialized=function(){dt=!0},lt=yt.cwrap("OSGBToS3MB","number",["number","number","number","number"])})),!dt)return null;var A;switch(e.suffix){case"dae":case"DAE":A=4;break;case"x":case"X":A=2;break;default:A=0}a=function(t,e,r){var a=yt._malloc(20*e),n=yt._malloc(Uint8Array.BYTES_PER_ELEMENT*e);yt.HEAPU8.set(t,n/Uint8Array.BYTES_PER_ELEMENT);var A=lt(n,e,a,r),o=new Uint8Array(yt.HEAPU8.buffer,a,A);return t=null,t=new Uint8Array(o).buffer,yt._free(a),yt._free(n),t}(new Uint8Array(a),a.byteLength,A)}var o=e.isS3MZ,i=e.fileType,B=e.supportCompressType,E=e.bVolume,C=e.isS3MBlock,y=e.modelMatrix,l=e.materialType,f=e.isCoverImageryLayer,u=e.transformPar,p=null,T=null,L=null;if(E&&e.volbuffer.byteLength<8&&(E=!1),E){var D=e.volbuffer,g=new Uint8Array(D,8),F=c.pako.inflate(g).buffer,M=new Float64Array(F,0,1),m=new Uint32Array(F,48,1);if(0===M[0]||3200===m[0]||3201===m[0]){var I=0;0==M[0]&&(I=8),r.push(F);var v=new Float64Array(F,I,6),_=v[0],S=v[1],O=v[2],N=v[3],h=v[4]<v[5]?v[4]:v[5],R=v[4]>v[5]?v[4]:v[5];T={left:_,top:S,right:O,bottom:N,minHeight:h,maxHeight:R,width:(p=new ft(_,N,O,S,h,R)).width,length:p.length,height:p.height};var G=new Uint32Array(F,48+I,7),x=G[0],b=G[1],U=G[2],H=G[3];L={nFormat:x,nSideBlockCount:b,nBlockLength:U,nLength:H,nWidth:G[4],nHeight:G[5],nDepth:G[6],imageArray:new Uint8Array(F,76+I,H*H*4)}}}var V=0,w={};w.ignoreNormal=e.ignoreNormal;var Y=e.rootBatchIdMap||{},J=e.ancestorMap||{},k={},W=new DataView(a),Z=W.getFloat32(V,!0);if(V+=Float32Array.BYTES_PER_ELEMENT,C)return W.getUint32(V,!0),V+=Uint32Array.BYTES_PER_ELEMENT,ae(a,a.byteLength,V,Y,J,e.isRoot,k,r),k;var Q=!1;if(Z>=3&&(W.getUint32(V,!0),V+=Uint32Array.BYTES_PER_ELEMENT),Z>=2&&(W.getUint32(V,!0),V+=Uint32Array.BYTES_PER_ELEMENT),re(Z-1)||re(Z-2)||re(Z-3)||Z>2.09&&Z<2.11){var z=W.getUint32(V,!0);V+=Uint32Array.BYTES_PER_ELEMENT;var j=new Uint8Array(a,V,z);a=c.pako.inflate(j).buffer,r.push(a),W=new DataView(a),V=0}else if(Z>1.199&&Z<1.201){z=W.getUint32(V,!0);V+=Uint32Array.BYTES_PER_ELEMENT,r.push(a)}else{Q=!0,V=0;z=W.getInt32(V,!0);if(V+=Int32Array.BYTES_PER_ELEMENT,V+=Uint8Array.BYTES_PER_ELEMENT*z,o){W.getUint32(V,!0),V+=Uint32Array.BYTES_PER_ELEMENT;g=new Uint8Array(a,V);a=c.pako.inflate(g).buffer,r.push(a),W=new DataView(a),V=0}}var q=W.getUint32(V,!0),$=ut(W,a,V+=Uint32Array.BYTES_PER_ELEMENT),tt=$.buffer;V=$.byteOffset;var et={},rt=jt(Z,tt,W,$.dataViewByteOffset,et,u),at=V%4;0!==at&&(V+=4-at);var At=Z>2.09&&3!==Z;if(kt(($=ut(W,a,V)).buffer,W,$.dataViewByteOffset,w,Q,r,At,Z,y,et,f,e.fileType,u),V=$.byteOffset,At)for(var ot=0;ot<rt.pageLods.length;ot++)for(var it=rt.pageLods[ot],Bt=it.geodes,Et=0;Et<Bt.length;Et++)for(var Ct=Bt[Et].skeletonNames,st=0;st<Ct.length;st++){var ct=Ct[st];n.defined(w[ct].max)&&(n.defined(it.max)?(it.max.x=Math.max(w[ct].max.x,it.max.x),it.max.y=Math.max(w[ct].max.y,it.max.y),it.max.z=Math.max(w[ct].max.z,it.max.z),it.min.x=Math.min(w[ct].min.x,it.min.x),it.min.y=Math.min(w[ct].min.y,it.min.y),it.min.z=Math.min(w[ct].min.z,it.min.z)):(it.max=w[ct].max,it.min=w[ct].min))}3!==Z&&(($=ut(W,a,V)).buffer,V=$.byteOffset);var pt={};!function(t,e,r,a,n,A){var o=0,i=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;for(var B=0;B<i;B++){var E=Pt(r,a,e,o),C=E.string,s=(o=E.bytesOffset)%4;0!==s&&(o+=4-s);var y=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var l=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var f=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var u=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var c=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var p=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var T=e.subarray(o,o+c);o+=c;var L=null,D=u;u===d.S3MCompressType.enrS3TCDXTN&&1!=t?(d.DXTTextureDecode.decode(L,l,f,T,p),L=p>P.S3MPixelFormat.BGR||p===P.S3MPixelFormat.LUMINANCE_ALPHA?new Uint8Array(l*f*4):new Uint16Array(l*f),d.DXTTextureDecode.decode(L,l,f,T,p),A.push(L.buffer),u=0):L=T,n[C]={id:C,width:l,height:f,compressType:u,oriCompressType:D,nFormat:p,imageBuffer:L,mipmapLevel:y}}}(B,($=ut(W,a,V)).buffer,W,$.dataViewByteOffset,pt,r),V=$.byteOffset;var Tt=W.getUint32(V,!0);V+=Uint32Array.BYTES_PER_ELEMENT;var Lt=new Uint8Array(a).subarray(V,V+Tt),Dt=s.getStringFromTypedArray(Lt);V+=Tt,Dt=Dt.replace(/\n\0/,"");var gt=JSON.parse(Dt);(3===Z&&(q=W.getUint32(V,!0),V+=Uint32Array.BYTES_PER_ELEMENT),(q&nt)==nt)&&ee(($=ut(W,a,V)).buffer,W,$.dataViewByteOffset,w,Z);if(1==Z){var Ft=rt.pageLods,Mt=!0;for(ot=0;ot<Ft.length;ot++){var mt=Ft[ot];Mt=""===mt.childTile;for(var It=mt.geodes,vt=0;vt<It.length;vt++){Ct=It[vt].skeletonNames;for(var _t=0;_t<Ct.length;_t++){var St=Ct[_t];if(Mt){var Ot=w[St].vertexPackage;Ot.boundingSphere=K.calcBoundingSphereInWorker(i,Ot)}}}}}return"BatchPBR"===l&&function(t,e,r){for(var a in delete t.ignoreNormal,t)if(t.hasOwnProperty(a)){var A=t[a],o=A.arrIndexPackage;if(o.length<1)continue;if(1===o.length||2===o.length&&13===o[1].primitiveType){var i=A.vertexPackage.attrLocation.aTextureCoordMatrix;if(void 0!==i){if((B=(O=A.vertexPackage.vertexAttributes[i]).typedArray)[0]<0)continue}else if(void 0!==(i=A.vertexPackage.attrLocation.aTextureCoordMatrix||A.vertexPackage.attrLocation.aTexCoord0)){O=A.vertexPackage.vertexAttributes[i];var B=new Float32Array(O.typedArray.buffer,O.typedArray.byteOffset,O.typedArray.byteLength/4);if(3===O.componentsPerAttribute&&B[2]<0)continue}}var E,C,s=0,y={},l=void 0;for(E=0,C=o.length;E<C;E++)13!==o[E].primitiveType?s+=o[E].indicesTypedArray.byteLength:l=o[E],0===E&&(y.indicesCount=0,y.indexType=o[E].indexType,y.primitiveType=o[E].primitiveType,y.materialCode=o[E].materialCode);y.indicesCount=s/2;var f=A.vertexPackage.verticesCount>65535?new Uint32Array(s/2):new Uint16Array(s/2),u=0;for(E=0,C=o.length;E<C;E++)if(13!==(N=o[E]).primitiveType){var P=0===N.indexType?Uint16Array:Uint32Array,c=0===N.indexType?N.indicesTypedArray.byteLength/2:N.indicesTypedArray.byteLength/4,p=new P(N.indicesTypedArray.buffer,N.indicesTypedArray.byteOffset,c);f.set(p,u),u+=p.length}y.indicesTypedArray=f,A.arrIndexPackage=[y],n.defined(l)&&(A.arrIndexPackage.push(l),A.edgeGeometry=d.S3MEdgeProcessor.createEdgeDataByIndices(A.vertexPackage,l));var T=2*o.length*4,L=new Float32Array(T),D={};for(E=0,C=r.material.length;E<C;E++)D[(h=r.material[E].material).id]=h;for(E=0,C=o.length;E<C;E++)if(h=D[(N=o[E]).materialCode]){var g=h.pbrMetallicRoughness;if(g){L[8*E]=g.metallicFactor,L[8*E+1]=g.roughnessFactor,L[8*E+2]=h.alphaCutoff;var F=""===h.alphaMode?0:1,M="none"===h.cullMode?0:1;L[8*E+3]=M|F<<16,L[8*E+4]=g.emissiveFactor.x,L[8*E+5]=g.emissiveFactor.y,L[8*E+6]=g.emissiveFactor.z,L[8*E+7]=0,h.pbrIndex=E}}var m="PBRMaterialParam_"+a;for(E=0,C=r.material.length;E<C;E++)if((h=r.material[E].material).id===y.materialCode){h.textureunitstates.push({textureunitstate:{addressmode:{u:0,v:0,w:0},filteringoption:0,filtermax:2,filtermin:2,id:m,texmodmatrix:[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],url:""}});break}var I,v,_=A.vertexPackage,S=_.attrLocation.aTexCoord1;if(void 0!==S){var O=_.vertexAttributes[S];I=new Float32Array(2*_.verticesCount),O.typedArray=I}else I=new Float32Array(2*_.verticesCount),S=_.vertexAttributes.length,_.attrLocation.aTexCoord1=S,_.vertexAttributes.push({index:S,typedArray:I,componentsPerAttribute:2,componentDatatype:5126,offsetInBytes:0,strideInBytes:8,normalize:!1});for(void 0!==(S=_.attrLocation.aColor)&&(v=(O=_.vertexAttributes[S]).typedArray),E=0,C=o.length;E<C;E++){var N,h;if((h=D[(N=o[E]).materialCode])&&h.pbrMetallicRoughness)for(var R=h.pbrMetallicRoughness.baseColor,G=void 0!==v,x=h.pbrIndex,b=(f=N.indicesTypedArray,0),U=(f=0===N.indexType?new Uint16Array(f.buffer,f.byteOffset,f.byteLength/2):new Uint32Array(f.buffer,f.byteOffset,f.byteLength/4)).length;b<U;b++){var K=f[b];I[2*K]=x,G&&(v[4*K]=255*R.x,v[4*K+1]=255*R.y,v[4*K+2]=255*R.z,v[4*K+3]=255*R.w)}}e[m]={id:m,width:2*o.length,height:1,compressType:0,nFormat:25,imageBuffer:L,mipmapLevel:0}}}(w,pt,gt),{result:!0,groupNode:rt,geoPackage:w,matrials:gt,texturePackage:pt,version:X.S3M4,dataVersion:Z,volImageBuffer:L,volBounds:T}}function Ae(){n.defined($)&&n.defined(at)&&($.onRuntimeInitialized=function(){et=!0},self.onmessage=e(ne),self.postMessage(!0))}return function(r){if("undefined"==typeof WebAssembly)return self.onmessage=e(ne),void self.postMessage(!0);var A=r.data.webAssemblyConfig;return n.defined(A)?a.FeatureDetection.isInternetExplorer()?t([y.buildModuleUrl("ThirdParty/Workers/ie-webworker-promise-polyfill.js")],(function(e){return self.Promise=e,-1!==A.modulePath.indexOf("crunch")?t([A.modulePath],(function(t){n.defined(A.wasmBinaryFile)?(n.defined(t)||(t=self.Module),$=t,Ae()):($=t,Ae())})):t([A.modulePath],(function(t){n.defined(A.wasmBinaryFile)?(n.defined(t)||(t=self.DracoDecoderModule),t(A).then((function(t){at=t,Ae()}))):(at=t(),Ae())}))})):-1!==A.modulePath.indexOf("crunch")?t([A.modulePath],(function(t){n.defined(A.wasmBinaryFile)?(n.defined(t)||(t=self.Module),$=t,Ae()):($=t,Ae())})):t([A.modulePath],(function(t){n.defined(A.wasmBinaryFile)?(n.defined(t)||(t=self.DracoDecoderModule),t(A).then((function(t){at=t,Ae()}))):(at=t(),Ae())})):void 0}}));
