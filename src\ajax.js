import axios from 'axios'
export default {
	// AjaxHtpp() {
	// 	// const http = 'http://192.168.9.18:29302'
	// 	// 全国过程
	// 	// const http = 'http://10.193.2.119:29301'
	// 	// 公司外网
	// 	// const http = 'http://117.15.170.44:20231
	// 	const http = window.wgParameter.publicUrl
	// 		const params = {
	// 			userid: '28'
	// 		}
	// 		axios.post(http + '/dataTransfer/pageJump', params).then(
	// 			(res) => {
	// 				axios.post(http + '/tDtfUserRoles/getUserid?user=' + res.data.data.token).then(
	// 					(res) => {
	// 						// console.log(res.data.result)
	// 						if (res.data.status === 200) {
	// 							sessionStorage.setItem('sdszhyj_dwmc', res.data.result.DWMC)
	// 							sessionStorage.setItem('sdszhyj_csmc', res.data.result.CSMC)
	// 							sessionStorage.setItem('sdszhyj_userid"', res.data.result.USERID)
	// 						}
	// 					}
	// 				)
	// 			}
	// 		)

	// 	}

	AjaxHtpp() {
		// 内网
		const http = window.wgParameter.publicUrl
		axios.post(http + '/tDtfUserRoles/getUserid?user=' + sessionStorage.getItem('token')).then(
			(res) => {
				const data = JSON.parse(JSON.stringify(res))
				if (data.data.status === 200) {
					sessionStorage.setItem('sdszhyj_dwmc', data.data.result.DWMC)
					sessionStorage.setItem('sdszhyj_csmc', data.data.result.CSMC)
					sessionStorage.setItem('sdszhyj_userid"', data.data.result.USERID)
				}
			}
		)
	}
}