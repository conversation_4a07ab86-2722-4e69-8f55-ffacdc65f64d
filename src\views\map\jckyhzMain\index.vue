<template>
  <div>
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div class="pro-leftTitle">
            <div
              v-if="showBackAdds"
              class="maps-zhedNav"
              @click="backCurrentDom"
            >
              <img
                class="mapites-backImg"
                :src="require('@/assets/' + 'map/settingImg/backAdds.png')"
                alt=""
              />
            </div>
            <span />
            交叉跨越
            <div
              v-if="!showBackAdds"
              class="maps-zhedNav"
              @click="showEveryItemSet"
            >
              <img
                v-show="!isFoldArea"
                class="mapites-zhed"
                :src="require('@/assets/' + 'map/settingImg/zhedie.png')"
                alt=""
              />
              <img
                v-show="isFoldArea"
                class="mapites-zhed"
                :src="require('@/assets/' + 'map/settingImg/zkzhedie.png')"
                alt=""
              />
            </div>
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img
            class="settingImg"
            :src="require('@/assets/' + 'map/settingImg/useSetting.png')"
            alt=""
          />
          <p>启用</p>
        </div>
      </div>
      <div v-show="isFoldArea" class="pro-addForm">
        <div class="map-showNav">基本信息</div>
        <van-row>
          <van-field
            v-model="crossOver.mark"
            label="编号"
            placeholder="请输入交叉跨越编号"
          />
        </van-row>
        <div class="map-showNav">选型信息</div>
        <!--        <van-row>-->
        <!--          &lt;!&ndash;跨越物级别&ndash;&gt;-->
        <!--          <div :style="{display: 'flex'}">-->
        <!--            <div :style="{marginLeft:'1rem',fontSize: '0.9rem',width: '5rem', marginTop: '0.3rem'}">跨越物级别</div>-->
        <!--            <div :style="{marginLeft:'1.5rem',marginTop: '1rem'}">-->
        <!--              <van-checkbox v-model="crossOver.leverl">重点跨越物</van-checkbox>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </van-row>-->
        <van-row>
          <!--跨越物类别-->
          <van-field
            readonly
            clickable
            :value="crossOver.type"
            label="跨越物类别"
            placeholder="请选择跨越物类别"
            @click="settingObj.crossOver.typeVis = true"
          />
          <van-popup
            v-model="settingObj.crossOver.typeVis"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="跨越物类别"
              value-key="key"
              :columns="crossOverType"
              @confirm="onConfirmKywSel($event)"
              @cancel="settingObj.crossOver.typeVis = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--跨越物高度-->
          <van-field
            v-model="crossOver.high"
            label="跨越物高度"
            placeholder="请输入跨越物高度"
          />
        </van-row>
        <van-row>
          <!--跨越物宽度-->
          <van-field
            v-model="crossOver.width"
            label="跨越物宽度"
            placeholder="请输入跨越物宽度"
          />
        </van-row>
        <van-row>
          <!--跨越物角度-->
          <van-field
            v-model="crossOver.angle"
            type="digit"
            label="跨越物角度"
            placeholder="请输入跨越物角度"
          />
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false,
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的id
    editData: {
      type: Object,
      defaults: () => {},
    },
  },
  watch: {
    "crossOver.angle": {
      handler(newVal) {
        if (newVal > 180) {
          this.crossOver.angle = 180;
        }
      },
      deep: true,
    },
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal;
      },
      deep: true,
      immediate: true,
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal;
        if (data.moudleType === "JCKYHZ") {
          // 查询电缆线路类型
          this.crossOver.imgList = [];
          this.crossOver.audioList = [];
          this.crossOver.leverl = data.crossoverLevel === "1";
          this.crossOver.type = data.crossoverType;
          this.crossOver.angle = data.crossoverHigh;
          this.crossOver.high = data.angle;
          this.crossOver.width = data.crossoverWidth;
          this.crossOver.mark = data.mark; // 编号
          this.crossOver.message = data.note;
          for (const k in data.imgList) {
            const objs = {
              url: data.imgList[k].path,
              isImage: true,
              isSaveReport: data.imgList[k].isSaveReport,
            };
            this.crossOver.imgList.push(objs);
          }
          for (const s in data.voiceList) {
            const objs = {
              content: data.voiceList[s].path,
            };
            this.crossOver.audioList.push(objs);
          }
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      isFoldArea: false,
      settingObj: {
        // 跨越物级别
        crossOver: {
          typeVis: false,
        },
      },
      crossOverType: [
        {
          key: "通讯线",
          value: "通讯线",
          type: "TXX",
        },
        {
          key: "电力线",
          value: "电力线",
          type: "DDX",
        },
        {
          key: "道路(普通道路)",
          value: "道路(普通道路)",
          type: "DLPTDL",
        },
        {
          key: "道路(高速道路)",
          value: "道路(高速道路)",
          type: "DLGSDL",
        },
        {
          key: "耕地",
          value: "耕地",
          type: "GD",
        },
        {
          key: "水塘",
          value: "水塘",
          type: "ST",
        },
        {
          key: "房屋",
          value: "房屋",
          type: "FW",
        },
        {
          key: "铁路",
          value: "铁路",
          type: "TL",
        },
        {
          key: "河沟",
          value: "河沟",
          type: "HG",
        },
        {
          key: "经济作物",
          value: "经济作物",
          type: "JJZW",
        },
      ], // 跨越物类别
      // 跨越物
      crossOver: {
        leverl: false, // 跨越物级别
        type: "通讯线", // 跨越物类别
        typeId: "",
        high: "1.5", // 跨越物高度
        width: "2", // 跨越物宽度
        mark: "", // 编号
        imgList: [], // 文件列表
        message: "", // 备注信息
        angle: "",
        audioList: [], // 语音列表
      },
    };
  },
  mounted() {},
  methods: {
    backCurrentDom() {
      this.$emit("backCurrentDom");
      // 跨越物
      this.crossOver.leverl = false;
      this.crossOver.type = "通讯线";
      this.crossOver.typeId = "";
      this.crossOver.high = "1.5";
      this.crossOver.width = "2";
      this.crossOver.imgList = [];
      this.crossOver.message = "";
      this.crossOver.angle = "";
      this.crossOver.audioList = [];
      this.settingObj.crossOver.typeVis = false;
    },
    showEveryItemSet() {
      this.isFoldArea = !this.isFoldArea;
    },
    /**
     * 跨越物级别
     */
    onConfirmKywSel(item) {
      this.crossOver.type = item.value;
      this.settingObj.crossOver.typeVis = false;
    },
    /**
     * 获取语音数据
     */
    getAudioData(data) {
      this.crossOver.audioList = data.aduioList;
    },
    submitData() {
      const parma = {
        type: 6,
        param: this.crossOver,
        visParam: this.settingObj.crossOver,
      };
      this.$emit("submitChildData", parma);
    },
    /**
     * 获取上传图片数据
     */
    getImgData(data) {
      this.crossOver.imgList = data.imgList;
    },
    /**
     * 获取备注信息
     * @param data
     */
    getMsgData(data) {
      this.crossOver.message = data.message;
    },
  },
};
</script>

<style lang="sass" scoped>
</style>

