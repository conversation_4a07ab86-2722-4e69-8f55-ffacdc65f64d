<template>
  <!-- 结算统计 -->
  <div class="index flex-c h100">
    <!-- 标题 -->
    <!-- 标题 -->
    <div class="main-header">结算统计</div>
    <div
      class=""
      style="width: 100%; height: 1px; border-bottom: 1px solid #eee"
    ></div>
    <div class="query-form-box">
      <el-form ref="form" :model="form" :inline="true" @submit.native.prevent>
        <el-row>
          <el-col :span="5">
            <el-form-item
              label="建设单位(市公司):"
              prop="jsdw"
              label-width="140px"
            >
              <el-select
                v-model="form.jsdw"
                clearable
                placeholder="请选择"
                @change="chooseJsdw"
              >
                <el-option
                  v-for="item in jsdwOptions"
                  :key="item.id"
                  :label="item.value"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item
              label="建设单位(县公司):"
              prop="countryId"
              label-width="140px"
            >
              <el-select
                v-model="form.countryId"
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in jsdwOptionsXgs"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item
              label="结算时间:"
              prop="jssj"
              label-width="140px"
              placeholder="请选择"
            >
              <el-date-picker
                v-model="form.jssj"
                clearable
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="lastSearchArea">
            <el-form-item label="建设性质:" prop="jsxz" label-width="140px">
              <el-select v-model="form.jsxz" clearable placeholder="请选择">
                <el-option
                  v-for="item in jsxzOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="项目名称:" prop="xmmc" label-width="140px">
              <el-input
                v-model="form.xmmc"
                clearable
                placeholder="请输入项目名称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="项目编码:" prop="xmbm" label-width="140px">
              <el-input
                v-model="form.xmbm"
                clearable
                placeholder="请输入项目编码"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" class="lastSearchArea">
            <el-button
              class="blue-btn"
              :loading="loading"
              @click="query()"
              icon="el-icon-search"
              >查询</el-button
            >
            <el-button @click="clearForm()" icon="el-icon-refresh"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
      <!-- <vxe-form :collapseStatus.sync="collapseStatus3" :data="form" title-align="right" title-width="80"
				prevent-submit custom-layout ref="form" @collapse="collapseHandle">

				<vxe-form-item title="建设单位" field="jsdw" :item-render="{}">
					<el-select v-model="form.jsdw" clearable placeholder="请选择">
						<el-option v-for="(item,i) in jsdwOptions" :key="i" :label="item.VALUE" :value="item.ID">
						</el-option>
					</el-select>
				</vxe-form-item>
				<vxe-form-item title="结算时间" field="jssj" :item-render="{}">
					<el-date-picker v-model="form.jssj" clearable type="daterange" value-format="yyyy-MM-dd"
						range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期">
					</el-date-picker>
				</vxe-form-item>
				<vxe-form-item title="建设性质" field="jsxz" :item-render="{}">
					<el-select v-model="form.jsxz" clearable placeholder="请选择">
						<el-option label="线路新建" value="线路新建"></el-option>
						<el-option label="线路改造" value="线路改造"></el-option>
						<el-option label="台区新建" value="台区新建"></el-option>
						<el-option label="台区改造" value="台区改造"></el-option>
					</el-select>
				</vxe-form-item>
				<vxe-form-item title="项目名称" field="xmmc" :item-render="{}" folding>
					<el-input v-model="form.xmmc" clearable></el-input>
				</vxe-form-item>
				<vxe-form-item title="项目编码" field="xmbm" :item-render="{}" folding>
					<el-input v-model="form.xmbm" clearable></el-input>
				</vxe-form-item>
				<div class="fr">
					<vxe-form-item align="center" collapse-node>
						<template #default>
							<el-button class="blue-btn" :loading="loading" @click="query()">查询</el-button>
							<el-button :loading="loading" @click="clearForm()">重置</el-button>
						</template>
					</vxe-form-item>
				</div>
			</vxe-form> -->
    </div>
    <div class="driver"></div>
    <el-button
      style="width: 98px; margin: 0 16px 16px 16px"
      @click="exportChange()"
    >
      <i class="el-icon-download"></i>
      全部导出
    </el-button>
    <div class="tablesArea">
      <ux-grid
        border
        :tree-config="{
          children: 'children',
          indent: 5,
          trigger: 'cell',
        }"
        :data="tableData"
        ref="plxTable"
        size="small"
        v-loading="loading"
        :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
        style="width: 98%; margin: 0px 16px 0 16px"
        :cell-style="{ 'text-align': 'center' }"
        :height="tableHeight"
        show-overflow
        keep-source
        :edit-config="{
          trigger: 'click',
          mode: 'cell',
          activeMethod: dbclick,
          showIcon: false,
        }"
      >
        <!-- <ux-table-column type="selection" width="55"> </ux-table-column> -->
        <ux-table-column
          field="xh"
          title="序号"
          fixed="left"
          tree-node
          width="85"
        >
        </ux-table-column>
        <ux-table-column
          field="projectCode"
          title="项目编码"
          fixed="left"
          width="180"
          resizable
        >
          <template slot-scope="scope">
            <span
              v-html="keyWordHandle(scope.row.projectCode, form.xmbm)"
            ></span>
          </template>
        </ux-table-column>
        <ux-table-column
          field=""
          title="项目名称"
          fixed="left"
          width="250"
          resizable
        >
          <template slot-scope="scope">
            <span
              v-html="
                keyWordHandle(
                  scope.row.gcxz == '项目包'
                    ? scope.row.xmmc
                    : scope.row.projectName,
                  form.xmmc,
                  form.xmmc
                )
              "
            ></span>
          </template>
        </ux-table-column>
        <ux-table-column
          field="voltageLevel"
          title="电压等级（kV）"
          width="80"
          resizable
        >
        </ux-table-column>
        <ux-table-column field="gcxz" title="工程性质" width="80" resizable>
        </ux-table-column>
        <ux-table-column field="xmdl" title="建设性质" width="90" resizable>
        </ux-table-column>
        <ux-table-column field="" title="建设规模" width="150" resizable>
          <ux-table-column field="" title="" width="150" resizable>
            <ux-table-column field="dtgs" title="单体个数" width="60" resizable>
            </ux-table-column>
          </ux-table-column>
          <ux-table-column field="" title="10kV" width="150" resizable>
            <ux-table-column
              field="numberoftransformers"
              title="变压器数量（台）"
              :edit-render="{ autofocus: '.el-input__inner' }"
              width="90"
              resizable
            >
              <template v-slot:header="scope">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="此列单体工程可单击单元格编辑"
                  placement="top"
                >
                  <span
                    ><i class="el-icon-edit-outline"></i
                    >{{ scope.column.title }}</span
                  >
                </el-tooltip>
              </template>
              <template v-slot:edit="scope">
                <el-input
                  @blur="saveEvent(scope.row)"
                  @keyup.enter.native="$event.target.blur()"
                  size="mini"
                  v-model="scope.row.numberoftransformers"
                  placeholder=""
                >
                </el-input>
              </template>
            </ux-table-column>
            <ux-table-column
              field="substationcapacity"
              title="变电容量（千伏安）"
              :edit-render="{ autofocus: '.el-input__inner' }"
              width="90"
              resizable
            >
              <template v-slot:header="scope">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="此列单体工程可单击单元格编辑"
                  placement="top"
                >
                  <span
                    ><i class="el-icon-edit-outline"></i
                    >{{ scope.column.title }}</span
                  >
                </el-tooltip>
              </template>
              <template v-slot:edit="scope">
                <el-input
                  @blur="saveEvent(scope.row)"
                  @keyup.enter.native="$event.target.blur()"
                  size="mini"
                  v-model="scope.row.substationcapacity"
                  placeholder=""
                >
                </el-input>
              </template>
            </ux-table-column>
          </ux-table-column>
          <ux-table-column field="" title="10kV" width="150" resizable>
            <ux-table-column
              field="lengthofoverheadline"
              title="架空线路长度（km）"
              :edit-render="{ autofocus: '.el-input__inner' }"
              width="90"
              resizable
            >
              <template v-slot:header="scope">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="此列单体工程可单击单元格编辑"
                  placement="top"
                >
                  <span
                    ><i class="el-icon-edit-outline"></i
                    >{{ scope.column.title }}</span
                  >
                </el-tooltip>
              </template>
              <template v-slot:edit="scope">
                <el-input
                  @blur="saveEvent(scope.row)"
                  @keyup.enter.native="$event.target.blur()"
                  size="mini"
                  v-model="scope.row.lengthofoverheadline"
                  placeholder=""
                >
                </el-input>
              </template>
            </ux-table-column>
            <ux-table-column
              field="cablelinelength"
              title="电缆线路长度（km）"
              :edit-render="{ autofocus: '.el-input__inner' }"
              width="90"
              resizable
            >
              <template v-slot:header="scope">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="此列单体工程可单击单元格编辑"
                  placement="top"
                >
                  <span
                    ><i class="el-icon-edit-outline"></i
                    >{{ scope.column.title }}</span
                  >
                </el-tooltip>
              </template>
              <template v-slot:edit="scope">
                <el-input
                  @blur="saveEvent(scope.row)"
                  @keyup.enter.native="$event.target.blur()"
                  size="mini"
                  v-model="scope.row.cablelinelength"
                  placeholder=""
                >
                </el-input>
              </template>
            </ux-table-column>
          </ux-table-column>

          <ux-table-column field="" title="0.4kV" width="150" resizable>
            <ux-table-column
              field="lowoverheadline"
              title="架空线路长度（km）"
              :edit-render="{ autofocus: '.el-input__inner' }"
              width="90"
              resizable
            >
              <template v-slot:header="scope">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="此列单体工程可单击单元格编辑"
                  placement="top"
                >
                  <span
                    ><i class="el-icon-edit-outline"></i
                    >{{ scope.column.title }}</span
                  >
                </el-tooltip>
              </template>
              <template v-slot:edit="scope">
                <el-input
                  @blur="saveEvent(scope.row)"
                  @keyup.enter.native="$event.target.blur()"
                  size="mini"
                  v-model="scope.row.lowoverheadline"
                  placeholder=""
                >
                </el-input>
              </template>
            </ux-table-column>
            <ux-table-column
              field="lowcablelinelength"
              title="电缆线路长度（km）"
              :edit-render="{ autofocus: '.el-input__inner' }"
              width="90"
              resizable
            >
              <template v-slot:header="scope">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="此列单体工程可单击单元格编辑"
                  placement="top"
                >
                  <span
                    ><i class="el-icon-edit-outline"></i
                    >{{ scope.column.title }}</span
                  >
                </el-tooltip>
              </template>
              <template v-slot:edit="scope">
                <el-input
                  @blur="saveEvent(scope.row)"
                  @keyup.enter.native="$event.target.blur()"
                  size="mini"
                  v-model="scope.row.lowcablelinelength"
                  placeholder=""
                >
                </el-input>
              </template>
            </ux-table-column>
          </ux-table-column>
        </ux-table-column>
        <ux-table-column
          field="csSjpssj"
          title="初设评审时间"
          width="120"
          resizable
        >
        </ux-table-column>
        <ux-table-column
          field="csCjpsyjsj"
          title="初设审核意见时间"
          width="150"
          resizable
        >
        </ux-table-column>
        <ux-table-column
          field="csPsyjwh"
          title="审核意见文号"
          width="100"
          resizable
        >
        </ux-table-column>
        <ux-table-column
          field="csSjpfrq"
          title="初设批复时间"
          width="100"
          resizable
        >
        </ux-table-column>
        <ux-table-column
          field="csPfwh"
          title="初设批复文号"
          width="100"
          resizable
        >
        </ux-table-column>
        <ux-table-column
          field="serviceFrameworkNumber"
          title="服务框架编号"
          width="100"
          resizable
        >
        </ux-table-column>
        <ux-table-column
          field="biddingtime"
          title="招标时间"
          width="120"
          resizable
        >
        </ux-table-column>
        <ux-table-column
          field="commencementtime"
          title="开工时间"
          width="120"
          resizable
        >
        </ux-table-column>
        <ux-table-column
          field="productiontime"
          title="竣工投产时间"
          width="120"
          resizable
        >
        </ux-table-column>
        <ux-table-column
          field="settlementTime"
          title="结算时间"
          width="120"
          resizable
        >
        </ux-table-column>
        <ux-table-column
          field="cityName"
          title="所属市公司"
          width="100"
          resizable
        >
        </ux-table-column>
        <ux-table-column
          field="countyName"
          title="所属县公司"
          width="100"
          resizable
        >
        </ux-table-column>
        <!-- <ux-table-column
          field="index"
          title="所属县公司"

          width="150"
        >
        </ux-table-column> -->
        <ux-table-column
          field="designInstituteName"
          title="设计单位"
          width="100"
          resizable
        >
        </ux-table-column>
        <ux-table-column
          field="constructioncontrolunit"
          title="监理单位"
          width="100"
          resizable
        >
        </ux-table-column>
        <!-- <ux-table-column field="consultant" title="咨询单位" width="100">
        </ux-table-column> -->
        <ux-table-column field="sgdw" title="施工单位" width="100" resizable>
        </ux-table-column>
        <!-- 没有字段 -->
        <ux-table-column field="" title="咨询单位" width="100" resizable>
          <ux-table-column
            field="zxdwKyjd"
            title="可研阶段"
            width="100"
            resizable
          >
            <!-- :edit-render="{ autofocus: '.el-input__inner' }" -->
            <!-- <template v-slot:edit="scope">
              <el-input
                @blur="saveEvent(scope.row)"
                @keyup.enter.native="$event.target.blur()"
                size="mini"
                v-model="scope.row.zxdwKyjd"
              >
              </el-input>
            </template> -->
          </ux-table-column>
          <ux-table-column
            field="zxdwCsps"
            title="初设评审"
            width="100"
            resizable
          >
            <!-- <template v-slot:edit="scope">
              <el-input
                @blur="saveEvent(scope.row)"
                @keyup.enter.native="$event.target.blur()"
                size="mini"
                v-model="scope.row.zxdwCsps"
              >
              </el-input>
            </template> -->
          </ux-table-column>
          <ux-table-column
            field="zxdwJssh"
            title="结算审核"
            width="100"
            resizable
          >
            <!-- :edit-render="{ autofocus: '.el-input__inner' }" -->
            <!-- <template v-slot:edit="scope">
              <el-input
                @blur="saveEvent(scope.row)"
                @keyup.enter.native="$event.target.blur()"
                size="mini"
                v-model="scope.row.zxdwJssh"
              >
              </el-input>
            </template> -->
          </ux-table-column>
          <ux-table-column
            field="zxdwQt"
            title="其他"
            :edit-render="{ autofocus: '.el-input__inner' }"
            width="150"
            resizable
          >
            <template v-slot:header="scope">
              <el-tooltip
                class="item"
                effect="dark"
                content="此列单体工程可单击单元格编辑"
                placement="top"
              >
                <span
                  ><i class="el-icon-edit-outline"></i
                  >{{ scope.column.title }}</span
                >
              </el-tooltip>
            </template>
            <template v-slot:edit="scope">
              <!--v-if去判断双击的是不是当前单元格-->
              <el-input
                @blur="saveEvent(scope.row)"
                @keyup.enter.native="$event.target.blur()"
                size="mini"
                v-model="scope.row.zxdwQt"
              >
              </el-input>
            </template>
          </ux-table-column>
        </ux-table-column>
        <ux-table-column field="" title="结算审核专家" width="150" resizable>
          <ux-table-column
            field="jsshzjGcshfzr"
            title="工程审核负责人"
            :edit-render="{ autofocus: '.el-input__inner' }"
            width="150"
            resizable
          >
            <template v-slot:header="scope">
              <el-tooltip
                class="item"
                effect="dark"
                content="此列单体工程可单击单元格编辑"
                placement="top"
              >
                <span
                  ><i class="el-icon-edit-outline"></i
                  >{{ scope.column.title }}</span
                >
              </el-tooltip>
            </template>
            <template v-slot:edit="scope">
              <!--v-if去判断双击的是不是当前单元格-->
              <el-input
                @blur="saveEvent(scope.row)"
                @keyup.enter.native="$event.target.blur()"
                size="mini"
                v-model="scope.row.jsshzjGcshfzr"
              >
              </el-input>
            </template>
          </ux-table-column>
          <ux-table-column
            field="jsshzjZyshr"
            title="专业审核人"
            :edit-render="{ autofocus: '.el-input__inner' }"
            width="150"
            resizable
          >
            <template v-slot:header="scope">
              <el-tooltip
                class="item"
                effect="dark"
                content="此列单体工程可单击单元格编辑"
                placement="top"
              >
                <span
                  ><i class="el-icon-edit-outline"></i
                  >{{ scope.column.title }}</span
                >
              </el-tooltip>
            </template>
            <template v-slot:edit="scope">
              <!--v-if去判断双击的是不是当前单元格-->
              <el-input
                @blur="saveEvent(scope.row)"
                @keyup.enter.native="$event.target.blur()"
                size="mini"
                v-model="scope.row.jsshzjZyshr"
              >
              </el-input>
            </template>
          </ux-table-column>
        </ux-table-column>
        <ux-table-column field="" title="批准概算（元）" width="150" resizable>
          <ux-table-column
            field="pzGsdttz"
            title="动态投资"
            width="90"
            resizable
          >
          </ux-table-column>
          <ux-table-column
            field="pzGsjttz"
            title="静态投资"
            width="90"
            resizable
          >
          </ux-table-column>
          <ux-table-column
            field="pzGsjzgcf"
            title="其中：建筑工程费"
            width="90"
            resizable
          >
          </ux-table-column>
          <ux-table-column
            field="pzGssbgzf"
            title="其中：设备购置费"
            width="90"
            resizable
          >
          </ux-table-column>
          <ux-table-column
            field="pzGsazgcf"
            title="其中：安装工程费"
            width="90"
            resizable
          >
          </ux-table-column>
          <ux-table-column
            field="pzGsqtfy"
            title="其中：其他费用"
            width="90"
            resizable
          >
          </ux-table-column>
        </ux-table-column>

        <ux-table-column field="" title="结算审核（元）" width="90" resizable>
          <ux-table-column
            field="jsShdttz"
            title="动态投资"
            width="90"
            resizable
          >
          </ux-table-column>
          <ux-table-column
            field="jsShjttz"
            title="静态投资"
            width="90"
            resizable
          >
          </ux-table-column>
          <ux-table-column
            field="jsShjzgcf"
            title="其中：建筑工程费"
            width="90"
            resizable
          >
          </ux-table-column>
          <ux-table-column
            field="jsShsbgzf"
            title="其中：设备购置费"
            width="90"
            resizable
          >
          </ux-table-column>
          <ux-table-column
            field="jsShazgcf"
            title="其中：安装工程费"
            width="90"
            resizable
          >
          </ux-table-column>
          <ux-table-column
            field="jsShqtfy"
            title="其中：其他费用"
            width="90"
            resizable
          >
          </ux-table-column>
        </ux-table-column>

        <ux-table-column
          field=""
          title="结算审核较概算变化率"
          width="150"
          resizable
        >
          <ux-table-column field="" title="静态投资" width="90" resizable>
            <ux-table-column
              field="jtChangeMoney"
              title="变化金额（元）"
              width="90"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="jtChangeRate"
              title="变化率（%）"
              width="90"
              resizable
            >
            </ux-table-column>
          </ux-table-column>
          <ux-table-column
            field=""
            title="其中：建筑工程费"
            width="150"
            resizable
          >
            <ux-table-column
              field="buildChangeMoney"
              title="变化金额（元）"
              width="90"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="buildChangeRate"
              title="变化率（%）"
              width="90"
              resizable
            >
            </ux-table-column>
          </ux-table-column>
          <ux-table-column
            field=""
            title="其中：设备购置费"
            width="150"
            resizable
          >
            <ux-table-column
              field="shebeiChangeMoney"
              title="变化金额（元）"
              width="90"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="shebeiChangeRate"
              title="变化率（%）"
              width="90"
              resizable
            >
            </ux-table-column>
          </ux-table-column>
          <ux-table-column
            field=""
            title="其中：安装工程费"
            width="150"
            resizable
          >
            <ux-table-column
              field="installChangeMoney"
              title="变化金额（元）"
              width="90"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="installChangeRate"
              title="变化率（%）"
              width="90"
              resizable
            >
            </ux-table-column>
          </ux-table-column>
          <ux-table-column
            field=""
            title="其中：其他费用"
            width="150"
            resizable
          >
            <ux-table-column
              field="otherChangeMoney"
              title="变化金额（元）"
              width="90"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="otherChangeRate"
              title="变化率（%）"
              width="90"
              resizable
            >
            </ux-table-column>
          </ux-table-column>
        </ux-table-column>
        <ux-table-column
          field=""
          title="与初设原则有无重大变化"
          width="150"
          resizable
        >
          <ux-table-column field="" title="静态投资" width="150" resizable>
            <ux-table-column
              field="jtbhnum"
              title="重大变化项目数（次数、变更几次）"
              width="100"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="jtfy"
              title="费用（元）"
              width="90"
              resizable
            >
            </ux-table-column>
          </ux-table-column>

          <ux-table-column
            field=""
            title="其中：建筑工程费"
            width="150"
            resizable
          >
            <ux-table-column
              field="jzbhnum"
              title="重大变化项目数"
              width="100"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="jzfy"
              title="费用（元）"
              width="90"
              resizable
            >
            </ux-table-column>
          </ux-table-column>

          <ux-table-column
            field=""
            title="其中：设备购置费"
            width="150"
            resizable
          >
            <ux-table-column
              field="sbbhnum"
              title="重大变化项目数"
              width="100"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="sbfy"
              title="费用（元）"
              width="90"
              resizable
            >
            </ux-table-column>
          </ux-table-column>

          <ux-table-column
            field=""
            title="其中：安装工程费"
            width="150"
            resizable
          >
            <ux-table-column
              field="azbhnum"
              title="重大变化项目数"
              width="100"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="azfy"
              title="费用（元）"
              width="90"
              resizable
            >
            </ux-table-column>
          </ux-table-column>

          <ux-table-column
            field=""
            title="其中：其他费用"
            width="150"
            resizable
          >
            <ux-table-column
              field="qtbhnum"
              title="重大变化项目数"
              width="100"
              resizable
            >
            </ux-table-column>
            <ux-table-column
              field="qtfy"
              title="费用（元）"
              width="90"
              resizable
            >
            </ux-table-column>
          </ux-table-column>
        </ux-table-column>
        <ux-table-column
          field="jstjRemark"
          title="备注"
          :edit-render="{ autofocus: '.el-input__inner' }"
          width="150"
          resizable
        >
          <template v-slot:header="scope">
            <el-tooltip
              class="item"
              effect="dark"
              content="此列单体工程可单击单元格编辑"
              placement="top"
            >
              <span
                ><i class="el-icon-edit-outline"></i
                >{{ scope.column.title }}</span
              >
            </el-tooltip>
          </template>
          <template v-slot:edit="scope">
            <!--v-if去判断双击的是不是当前单元格-->
            <el-input
              @blur="saveEvent(scope.row)"
              @keyup.enter.native="$event.target.blur()"
              size="mini"
              v-model="scope.row.jstjRemark"
            >
            </el-input>
          </template>
        </ux-table-column>
      </ux-grid>
    </div>
    <el-pagination
      style="margin: 10px"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="->,total, sizes, prev, pager, next"
      :total="total"
      background
    >
    </el-pagination>
  </div>
</template>

<script>
import {
  getJstj,
  editJstj,
  resquest,
  getJsdw,
  getuserInfo,
  getCounty,
} from "@/api/api"
import { set } from "vue"
export default {
  name: "jstj",
  data () {
    return {
      tableHeight: 0,
      // timer: 0,
      // 用一个字符串来保存当前双击的是哪一个单元格
      currentCell: null,
      form: {
        jsdw: "",
        jsxz: "",
        xmmc: "",
        xmbm: "",
        csmc: "",
        dwmc: "",
        jssj: [],
        countryId: "",
      },
      cityId: "",
      countryId: "",
      pageNum: 1,
      pageSize: 10,
      total: 0,
      jsdwOptions: [],
      jsxzOptions: [
        {
          value: "线路新建",
          label: "线路新建",
        },
        {
          value: "线路改造",
          label: "线路改造",
        },
        {
          value: "台区新建",
          label: "台区新建",
        },
        {
          value: "台区改造",
          label: "台区改造",
        },
      ],
      jsdwOptionsXgs: [],
      loading: false,
      tableData: [
        /*{
            id: '1',
            xmmc: '配电网工程结算审核情况统计表',
            projectCode: '3214214124123',
            jstjRemark: '111',
            gcxz: '项目包',
            tsqksm: '',
            children: [
              {
                id: '1.1',
                projectName: '配电网工程结算审核情况统计表',
                projectCode: '3214214124123',
                jstjRemark: '111',
                gcxz: '单体工程',
                tsqksm: '',
              },
            ],
          },
          {
            id: '2',
            xmmc: '结算审核报告',
            projectCode: '3214214124123',
            gcxz: '项目包',
            tsqksm: '',
            children: [
              {
                id: '2.1',
                projectName: '配电网工程结算审核情况统计表',
                projectCode: '3214214124123',
                jstjRemark: '111',
                tsqksm: '',
                gcxz: '单体工程',
              },
            ],
          },*/
      ],
      multipleSelection: [],
      downloadAllUrl: resquest + "/settlementStatistics/download",
      collapseStatus3: true,
      rows: [],
    }
  },
  created () {
    this.cityId = this.$route.query.dsgs || ""
    this.countryId = this.$route.query.xgs || ""
    console.log()
  },
  mounted () {
    this.setTablesHeight()
    const token = sessionStorage.getItem("bhneToken")
    getuserInfo(token).then((res) => {
      this.form.csmc = res.data.result.USERID
      this.form.dwmc = res.data.result.DWMC
      this.getList()
    })
    this.getJsdwList()
    window.onresize = function () {
      that.setTablesHeight()
    }
  },
  beforeDestroy () {
    // this.timer && clearTimeout(this.timer)
    // window.removeEventListener('resize', this.onResize)
  },
  methods: {
    setTablesHeight () {
      this.$nextTick(() => {
        const tablesAreaHeight = document.getElementsByClassName('driver')[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        this.tableHeight = windowsAreaHeight - tablesAreaHeight - 120
      })
    },
    chooseJsdw (val) {
      console.log(this.form.dwmc)
      console.log(val, "lll")
      let params = {
        optId: val,
        dwmc: this.form.dwmc,
      }
      getCounty(params).then((res) => {
        this.jsdwOptionsXgs = res.data.result || []
       
      })
    },
    getJsdwList () {
      let key = this.cityId ? "dsgs" : this.countryId ? "xgs" : ""
      let id = this.cityId || this.countryId || ""
      let data = {
        key,
        id,
      }
      getJsdw(data).then((res) => {
        console.log(res.data.data, "建设单位")
        this.jsdwOptions = res.data.data
      })
    },
    // 斑马纹效果
    xxxTableRowClassName ({ row, rowIndex }) {
      // if (rowIndex % 2 == 0) {
      //   return 'statistics-warning-row'
      // } else {
      //   return ''
      // }
    },
    // 设置表格高度
    // setTableHeight() {
    //   this.$nextTick(() => {
    //     let rect = this.$refs.tablecontent.getBoundingClientRect()
    //     this.tableHeight = rect.height
    //   })
    // },
    // onResize() {
    //   this.timer && clearTimeout(this.timer)
    //   this.timer = setTimeout(() => {
    //     this.setTableHeight()
    //   }, 300)
    // },
    getRowKey (row) {
      console.log(row)
      // return row.taskId
      return row.id
    },
    clearForm () {
      // this.$refs['form'].resetFields()
      this.form = {
        jsdw: "",
        jsxz: "",
        xmmc: "",
        xmbm: "",
        csmc: "",
        dwmc: "",
        jssj: [],
      }
      this.countryId = ""
      this.query()
      //重置的时候 让数据全部收起
      // if (this.tableData.length > 0) {
      //   this.forArr(this.tableData, false)
      // }
    },
    //列表展开和收起
    forArr (arr, isExpand) {
      arr.forEach((i) => {
        this.$refs.table.toggleRowExpansion(i, isExpand)
        if (i.itemList) {
          this.forArr(i.itemList, isExpand)
        }
      })
    },
    collapseHandle () {
      this.setTableHeight()
    },
    // 查询关键字变色
    keyWordHandle (e, tags) {
      e = e + ""
      if (tags !== null && tags !== "") {
        let reg = new RegExp("(" + tags + ")", "g")
        if (e !== null && e !== "") {
          return e.replace(reg, "<font style='color:red'>$1</font>")
        } else {
          return e
        }
      } else {
        return e
      }
    },

    query () {
      this.pageNum = 1
      this.getList()
    },
    getRow (row) {
      return row.id
    },
    getList () {
      this.loading = true
      let jskssj = ""
      let jsjssj = ""
      if (this.form.jssj.length > 1) {
        jskssj = this.form.jssj[0]
        jsjssj = this.form.jssj[1]
      }
      let data = {
        current: this.pageNum,
        size: this.pageSize,
        jskssj,
        jsjssj,
        jsdw: this.form.jsdw,
        jsxz: this.form.jsxz,
        xmmc: this.form.xmmc,
        xmbm: this.form.xmbm,
        cityId: this.cityId,
        countryId: this.countryId,
      }
      console.log(data, "dada")
      getJstj(data)
        .then((res) => {
          this.tableData = res.data.data
          // this.$refs.plxTable.loadData(res.data.data)
          this.total = res.data.total
          if (this.rows.length > 0) {
            this.rows.forEach((i) => {
              const obj = this.tableData.find((item) => item.id == i.id)
              this.$refs.plxTable.setTreeExpand(obj, true)
            })
          }
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleSizeChange (val) {
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.pageNum = val
      this.getList()
    },
    //批量导出
    exportChange () {
      // if (this.tableData.length == 0) {
      //   this.$message.info('暂无数据')
      //   return
      // }
      console.log("download")
      let jskssj = ""
      let jsjssj = ""
      if (this.form.jssj.length > 1) {
        jskssj = this.form.jssj[0]
        jsjssj = this.form.jssj[1]
      }
      let data = {
        current: this.pageNum,
        size: this.pageSize,
        jskssj,
        jsjssj,
        jsdw: this.form.jsdw,
        jsxz: this.form.jsxz,
        xmmc: this.form.xmmc,
        xmbm: this.form.xmbm,
        cityId: this.cityId,
        countryId: this.countryId,
      }

      function changeParam (param) {
        return JSON.stringify(param)
          .replace(/:/g, "=")
          .replace(/,/g, "&")
          .replace(/{/g, "?")
          .replace(/}/g, "")
          .replace(/"/g, "")
      }
      window.open(`${this.downloadAllUrl}${changeParam(data)}`)

      // if (this.multipleSelection.length == 0)
      //   return this.$message.info('请至少选择一项')
      // console.log(this.multipleSelection)
    },
    // 获得当前双击的单元格的横竖index，然后拼接成一个唯一字符串用于判断，并赋给currentCell
    // 拼接后类似这样："1,0","1,1",
    dbclick (row) {
      if (row.row.gcxz != "单体工程") {
        this.$message.info("只能编辑单体工程")
        return false
      } else return true
    },
    submit (e, ctx, close) {
      /*let data = {
          projectList: [
            {
              taskID: e.taskId,
              jsshzjGcshfzr: e.jsshzjGcshfzr,
              jsshzjZyshr: e.jsshzjZyshr,
              jstjRemark: e.jstjRemark,
              zxdwKyjd: e.zxdwKyjd,
              zxdwCsps: e.zxdwCsps,
              zxdwJssh: e.zxdwJssh,
              zxdwQt: e.zxdwQt,

            },
          ],
          structuredDataList: [
            {
              singlePrjWbs: e.projectCode,
              numberoftransformers: e.numberoftransformers,
              substationcapacity: e.substationcapacity,
              lengthofoverheadline: e.lengthofoverheadline,
              cablelinelength: e.cablelinelength,
              lowoverheadline: e.lowoverheadline,
              lowcablelinelength: e.lowcablelinelength,
            },
          ],
        }*/
      let data = {
        structuredDataList: [
          {
            singlePrjWbs: e.projectCode,
            projectCode: e.projectCode,
            numberoftransformers: e.numberoftransformers,
            substationcapacity: e.substationcapacity,
            lengthofoverheadline: e.lengthofoverheadline,
            cablelinelength: e.cablelinelength,
            lowoverheadline: e.lowoverheadline,
            lowcablelinelength: e.lowcablelinelength,
            id: e.id,
            jsshzjGcshfzr: e.jsshzjGcshfzr,
            jsshzjZyshr: e.jsshzjZyshr,
            jstjRemark: e.jstjRemark,
            zxdwKyjd: e.zxdwKyjd,
            zxdwCsps: e.zxdwCsps,
            zxdwJssh: e.zxdwJssh,
            zxdwQt: e.zxdwQt,
          },
        ],
      }
      // data.projectList = JSON.stringify(data.projectList)
      data.structuredDataList = JSON.stringify(data.structuredDataList)
      editJstj(data)
        .then((res) => {
          if (res.data.status == "000000") {
            this.$refs.plxTable.clearActived()
            // this.$refs.plxTable.reloadRow(e, null, null)
            this.rows = []
            this.rows = this.$refs.plxTable.getTreeExpandRecords()
            this.loading = false
            this.getList()
            this.$message.success(res.data.message)
          } else {
            this.getList()
            this.$message.error("修改失败")
          }
        })
        .catch((err) => {
          console.log(err)

          this.getList()
          // this.currentCell = null
          // ctx.confirmButtonLoading = false
          // close()
          this.$message.error("修改失败")
        })
    },

    // 点击保存
    saveEvent (row) {
      this.loading = true
      setTimeout(() => {
        // 判断是否发生改变
        console.log(this.$refs.plxTable.isUpdateByRow(row))
        if (this.$refs.plxTable.isUpdateByRow(row)) {
          // 局部保存，并将行数据恢复到初始状态（如果 第二个参数record 为空则不改动行数据，只恢复状态）
          // 必须执行这个，不然下次点击保存，还是保存成功哦！状态没改变哦
          // this.$refs.plxTable.reloadRow(row, null, null)

          this.submit(row)
        } else {
          // this.$message({
          //   message: '保存失败，你没改变当前行的数据',
          //   type: 'error',
          // })
          this.loading = false
        }
      }, 300)
    },
  },
};
</script>
<style lang="scss" scoped>
.el-icon-edit-outline {
  color: #526ade;
  margin-right: 3px;
}
</style>
