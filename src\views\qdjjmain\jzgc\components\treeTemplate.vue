<template>
  <div>
    <el-tree
      class="video-tree"
      :data="treeData"
      :props="defaultProps"
      highlight-current
      :expand-on-click-node="false"
      @node-click="handleNodeClick"
    >
      <span slot-scope="{ node, data }">
        <i
          v-if="data.children.length > 0"
          style="color: #e38d13"
          class="el-icon-folder"
        />
        <i v-else class="el-icon-tickets" style="color: #0044cc" />
        <span :title="node.label">{{ node.label }}</span>
      </span>
    </el-tree>
  </div>
</template>

<script>
export default {
  name: 'TreeTemplate',
  props: {
    treeData: {
      type: Array,
      default: function () {
        return []
      },
    },
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'inventoryName',
      },
    }
  },
  methods: {
    handleNodeClick(node, data) {
      this.$emit('handleNodeClick', node, data)
    },
  },
}
</script>

<style lang="scss" scoped>
.el-tree-node:focus {
  .el-tree-node__content {
    background-color: #69a8ea !important;
  }
}
.el-tree--highlight-current {
  .el-tree-node.is-current {
    .el-tree-node__content {
      background-color: #69a8ea;
    }
  }
}
.video-tree {
  height: 60vh;
  overflow: auto;
}
</style>
