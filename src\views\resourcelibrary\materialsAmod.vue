<template>
  <div class="app-container" style="width: 100%">
    <div class="main-header">标准化信息管理</div>
    <div class="driver"></div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane lazy label="物料信息维护" name="material">
        <div class="">
          <div class="item-content">
            <MaterialTool
              :tabKey="activeName"
              v-if="activeName === 'material'"
            />
          </div>
          <div class="item-content">
            <el-container>
              <el-aside
                :style="{ height: tableHeight + 'px' }"
                style="width: 240px; overflow-y: scroll"
              >
                <Treeleft
                  :tabKey="activeName"
                  v-if="activeName === 'material'"
                  v-on:treeclick="treeclick"
                />
              </el-aside>
              <el-container>
                <el-main>
                  <el-table
                    v-loading="loading"
                    :data="materialData"
                    :header-cell-style="{
                      background: '#f7f8fa',
                      color: '#606266',
                    }"
                    height="30vh"
                    align="center"
                    style="width: 98%"
                    :row-class-name="rowIndex"
                    class="widthFull"
                  >
                    <el-table-column type="selection" width="55">
                    </el-table-column>
                    <el-table-column
                      type="index"
                      align="center"
                      label="序号"
                      width="60"
                    >
                    </el-table-column>
                    <el-table-column prop="MaterialName" label="物料名称">
                    </el-table-column>
                    <el-table-column prop="Spec" label="物料型号">
                    </el-table-column>
                    <el-table-column prop="MaterialCodeERP" label="ERP编码">
                    </el-table-column>
                    <el-table-column prop="DesignUnit" label="单位">
                    </el-table-column>
                    <el-table-column prop="Voltage" label="电压等级">
                    </el-table-column>
                    <!-- <el-table-column prop="MaterialsTypeKey" label="物料类型" width="80">
                        </el-table-column> -->
                    <el-table-column
                      prop="IsDonor"
                      label="采购来源"
                      :formatter="formatterIsDonor"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="Weight"
                      label="重量"
                      :formatter="formatterWeight"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="TechnicalProtocol"
                      label="技术规范ID"
                    >
                    </el-table-column>
                    <el-table-column prop="" label="信息来源">
                    </el-table-column>
                  </el-table>
                  <el-pagination
                    style="margin: 10px"
                    background
                    :current-page="queryParams1.pageNum"
                    :page-sizes="pageSize"
                    :page-size="queryParams1.pageSize"
                    layout="total, sizes, prev, pager, next"
                    @current-change="handleCurrentChange"
                    @size-change="handleSizeChange"
                    :total="total1"
                  >
                  </el-pagination>
                  <el-row :gutter="12" style="margin-top: 10px">
                    <el-col :span="8">
                      <el-card class="box-card">
                        <div class="content-title">设计属性</div>
                        <div class="item-content">
                          <el-form
                            :label-position="labelPosition"
                            label-width="100px"
                          >
                            <el-form-item label="物料简称：">
                              <el-input
                                v-model="form.user"
                                placeholder="物料简称"
                              ></el-input>
                            </el-form-item>
                            <el-form-item label="设计单位：">
                              <el-input
                                v-model="form.user"
                                placeholder="设计单位"
                              ></el-input>
                            </el-form-item>
                            <el-form-item label="统计单位：">
                              <el-input
                                v-model="form.user"
                                placeholder="统计单位"
                              ></el-input>
                            </el-form-item>
                            <el-form-item label="换算系数：">
                              <el-input
                                v-model="form.user"
                                placeholder="换算系数"
                              ></el-input>
                            </el-form-item>
                            <el-form-item label="是否线材：">
                              <el-input
                                v-model="form.user"
                                placeholder="是否线材"
                              ></el-input>
                            </el-form-item>
                          </el-form>
                        </div>
                      </el-card>
                    </el-col>
                    <el-col :span="8">
                      <el-card class="box-card">
                        <div class="content-title">物料属性</div>
                        <div class="item-content">
                          <el-table
                            v-loading="loading"
                            :data="materialProperty"
                            align="center"
                            class="widthFull"
                          >
                            <el-table-column prop="MaterialName" label="属性名">
                            </el-table-column>
                            <el-table-column prop="Spec" label="属性值">
                            </el-table-column>
                          </el-table>
                        </div>
                      </el-card>
                    </el-col>
                    <el-col :span="8">
                      <el-card class="box-card">
                        <div class="content-title">技经属性</div>
                        <div class="item-content">
                          <el-form
                            :label-position="labelPosition"
                            label-width="100px"
                          >
                            <el-form-item label="运输类型：">
                              <el-input
                                v-model="form.user"
                                placeholder="运输类型"
                              ></el-input>
                            </el-form-item>
                            <el-form-item label="损耗：">
                              <el-input
                                v-model="form.user"
                                placeholder="损耗"
                              ></el-input>
                            </el-form-item>
                            <el-form-item label="调整系数：">
                              <el-input
                                v-model="form.user"
                                placeholder="调整系数"
                              ></el-input>
                            </el-form-item>
                            <el-form-item label="运输重量：">
                              <el-input
                                v-model="form.user"
                                placeholder="运输重量"
                              ></el-input>
                            </el-form-item>
                            <el-form-item label="原价：">
                              <el-input
                                v-model="form.user"
                                placeholder="原价"
                              ></el-input>
                            </el-form-item>
                            <el-form-item label="市场价：">
                              <el-input
                                v-model="form.user"
                                placeholder="市场价"
                              ></el-input>
                            </el-form-item>
                          </el-form>
                        </div>
                      </el-card>
                    </el-col>
                  </el-row>
                </el-main>
              </el-container>
            </el-container>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane lazy label="模块信息维护" name="module">
        <div class="">
          <div class="item-content">
            <ModuleTool :tabKey="activeName" v-if="activeName === 'module'" />
          </div>
          <div class="item-content">
            <el-container>
              <el-aside
                :style="{ height: tableRighTabsHeight + 'px' }"
                style="width: 240px; overflow-y: scroll"
              >
                <Treeleft
                  :tabKey="activeName"
                  v-if="activeName === 'module'"
                  v-on:treeclick="treeclick"
                />
              </el-aside>
              <el-container>
                <el-main>
                  <el-table
                    :data="moduleData"
                    v-loading="loading"
                    :height="tableRighTabsHeight"
                    :header-cell-style="{
                      background: '#f7f8fa',
                      color: '#606266',
                    }"
                    :row-class-name="rowIndex"
                    align="center"
                    style="width: 98%"
                    class="widthFull"
                  >
                    <el-table-column
                      type="index"
                      label="序号"
                      align="center"
                      width="50"
                    >
                    </el-table-column>
                    <el-table-column type="selection" width="55">
                    </el-table-column>
                    <!-- <el-table-column prop="ModuleTypeKey" label="模块类别" width="100">
                        </el-table-column> -->
                    <el-table-column prop="ModuleName" label="模块名称">
                    </el-table-column>
                    <el-table-column prop="ModuleCode" label="模块编码">
                    </el-table-column>
                    <el-table-column prop="Voltage" label="电压等级">
                    </el-table-column>
                    <el-table-column prop="Level" label="模块层级">
                    </el-table-column>
                    <el-table-column prop="isStandard" label="是否标准模块">
                    </el-table-column>
                    <el-table-column prop="StateFlag" label="原有标志">
                    </el-table-column>
                    <el-table-column prop="UseInfer" label="推导优先">
                    </el-table-column>
                  </el-table>
                  <el-pagination
                    style="margin: 10px"
                    background
                    :current-page="queryParams2.pageNum"
                    :page-sizes="pageSize"
                    :page-size="queryParams2.pageSize"
                    layout="total, sizes, prev, pager, next"
                    @current-change="handleCurrentChangeRight"
                    @size-change="handleSizeChangeRight"
                    :total="total2"
                  >
                  </el-pagination>
                </el-main>
              </el-container>
            </el-container>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {
  getDrawingList,
  getModuleList,
  getMaterialsProjectList,
} from '@/views/basicParameter/data-static/data.js'
import Treeleft from './components/Treeleft.vue'
import MaterialTool from './components/material-tool.vue'
import ModuleTool from './components/module-tool.vue'
import DrawingTool from './components/drawing-tool.vue'
export default {
  name: 'ResourceLibrary',
  components: { Treeleft, MaterialTool, ModuleTool, DrawingTool },
  data () {
    return {
      name: '',
      labelPosition: 'right',
      activeName: 'material',
      form: {
        selectVersion: 1,
      },
      loading: true,
      tableHeight: 0,
      tableRighTabsHeight: 0,
      // 总条数
      total1: 0,
      total2: 0,
      total3: 0,
      pageSize: [10, 20, 50, 100],
      // 查询参数
      queryParams1: {
        pageNum: 1,
        pageSize: 10,
        search: '',
      },
      queryParams2: {
        pageNum: 1,
        pageSize: 10,
        search: '',
      },
      queryParams3: {
        pageNum: 1,
        pageSize: 10,
        search: '',
      },
      materialData: [],
      moduleData: [],
      drawingData: [],
      materialProperty: [],
      rules: {},
      version: [
        { Id: 1, Name: '20221024' },
        { Id: 2, Name: '20220915' },
      ],
    }
  },
  mounted () {
    this.setTablesHeight()
    const that = this
    window.onresize = function () {
      that.setTablesHeight()
    }
  },
  created () {
    this.loading = false
  },
  methods: {
    setTablesHeight () {
      this.$nextTick(() => {
        const tablesAreaHeight = document
          .getElementsByClassName('driver')[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        this.tableHeight = windowsAreaHeight - tablesAreaHeight - 180
        this.tableRighTabsHeight = windowsAreaHeight - tablesAreaHeight - 250
      })
    },
    rowIndex ({ row, rowIndex }) {
      //增加索引
      if (this.activeName === 'material') {
        row.index =
          rowIndex +
          1 +
          (this.queryParams1.pageNum - 1) * this.queryParams1.pageSize
      } else if (this.activeName === 'module') {
        row.index =
          rowIndex +
          1 +
          (this.queryParams2.pageNum - 1) * this.queryParams2.pageSize
      } else if (this.activeName === 'drawing') {
        row.index =
          rowIndex +
          1 +
          (this.queryParams3.pageNum - 1) * this.queryParams3.pageSize
      }
    },
    handleCurrentChange (val) {
      this.queryParams1.pageNum = val
      this.getCurrentMaterialsProjectList()
    },
    handleSizeChange (val) {
      this.queryParams1.pageSize = val
      this.getCurrentMaterialsProjectList()
    },
    handleCurrentChangeRight (val) {
      this.queryParams2.pageNum = val
      this.getCurrentModuleList()
    },
    handleSizeChangeRight (val) {
      this.queryParams2.pageSize = val
      this.getCurrentModuleList()
    },
    formatterWeight (row, column, cellValue, index) {
      if (cellValue == 0) return ''
      return cellValue.toFixed(2)
    },
    formatterIsDonor (row, column, cellValue, index) {
      if (cellValue === undefined || cellValue === 0) return '甲供'
      else if (cellValue === 1) return '乙供'
      return ''
    },
    treeclick (nodekey, tabkey) {
      if (tabkey === 'material') {
        if (this.queryParams1.search != nodekey) {
          this.queryParams1.pageNum = 1
        }
        this.queryParams1.search = nodekey
        this.getCurrentMaterialsProjectList()
      } else if (tabkey === 'module') {
        if (this.queryParams2.search != nodekey) {
          this.queryParams2.pageNum = 1
        }
        this.queryParams2.search = nodekey
        this.getCurrentModuleList()
      } else if (tabkey === 'drawing') {
        if (this.queryParams3.search != nodekey) {
          this.queryParams3.pageNum = 1
        }
        this.queryParams3.search = nodekey
        this.getCurrentDrawingList()
      }
    },
    getCurrentMaterialsProjectList () {
      this.loading = true
      console.log('获取物料')
      let response = getMaterialsProjectList(this.queryParams1)
      this.materialData = response.rows
      this.total1 = response.total
      this.loading = false
    },
    getCurrentModuleList () {
      this.loading = true
      console.log('获取模块')
      let response = getModuleList(this.queryParams2)
      this.moduleData = response.rows
      this.total2 = response.total
      this.loading = false
    },
    getCurrentDrawingList () {
      this.loading = true
      console.log('获取图纸')
      let response = getDrawingList(this.queryParams3)
      this.drawingData = response.rows
      this.total3 = response.total
      this.loading = false
    },
    handleClick (tab, event) { },
  },
}
</script>

<style scoped lang="scss">
::v-deep .el-tabs {
  margin: 0 16px;
  .el-tabs__item.is-active {
    color: #333333 !important;
  }
  .el-tabs__item:hover {
    color: #526ade !important;
  }
  .el-tabs__active-bar {
    background: #526ade !important;
  }
}
.app-container {
  background-color: #fff;
}
.widthFull {
  width: 100%;
}
aside {
  padding: 8px;
}
.el-main {
  padding: 0;
  padding-left: 16px !important;
  overflow: hidden;
}
.content-title {
  position: relative;
  padding-left: 10px;
  color: #526ade;
}
.content-title::after {
  content: "";
  position: absolute;
  left: 0;
  top: 50%; /* 将竖杠放置在元素的垂直中心位置 */
  transform: translateY(-50%);
  width: 5px; /* 竖杠的宽度 */
  height: 17px; /* 竖杠的高度 */
  background-color: #526ade; /* 竖杠的颜色 */
  border-radius: 5px; /* 添加弧度 */
  content: "";
}
::v-deep.widthFull .cell {
  font-size: 14px !important;
}
</style>
