<template>
  <div class="index flex-c h100">
     <div style="height:100vh;width:100vw;background:#fff;  position: absolute;left:0;top:0;z-index:99" v-show="!showPage"></div>
    <!-- 标题 -->
    <div class="main-header">用户管理</div>
    <div class="" style="width: 100%;border-bottom: 1px solid #eee;">
    </div>
    <!-- 表单 -->
    <div class="query-form-box">
      <el-form ref="form" :model="form" :inline="true" label-width="80px">
        <el-row>
          <el-col :span="5">
            <el-form-item label="用户名称:" prop="pcnd">
              <el-input v-model="form.uname" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="账号名称:" prop="pcnd">
              <el-input v-model="form.code" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14" class="lastSearchArea">
            <el-button class="blue-btn" @click="query()">查询</el-button>
            <!-- <el-button class="" @click="newConst()">新建</el-button> -->
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="driver"></div>
    <!-- 项目列表 -->
    <div class="tablesArea">
      <el-table :data="tableData" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }" ref="tablecontent"
        :height="tableHeight" style="width: 98%;margin: 0px 16px 0 16px;margin-top: 10px;">
        <el-table-column type="index" :index="indexMethod" label="序号" align="center" :resizable="false" width="60">
        </el-table-column>
        <el-table-column prop="uaccount" label="账号" align="center">
        </el-table-column>
        <el-table-column prop="uname" label="用户名" align="center">
        </el-table-column>
        <el-table-column prop="uphone" label="手机号" align="center" min-width="100">
        </el-table-column>
        <el-table-column prop="email" label="邮箱" align="center" min-width="100">
        </el-table-column>
        <el-table-column prop="roleName" label="角色" align="center" min-width="150">
        </el-table-column>
        <el-table-column prop="dwmc" label="单位名称" align="center" min-width="150">
        </el-table-column>
        <el-table-column prop="orgname" label="所在单位名称" align="center" min-width="150">
        </el-table-column>
        <el-table-column prop="porgname" label="上级单位名称" align="center" min-width="150">
        </el-table-column>
        <el-table-column label="操作" align="center" width="150px">
          <template slot-scope="scope">
            <span class="">
              <div class="el-buttonStyle">
                <span @click="Detid(scope.row)">
                  修改
                </span>
              </div>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination style="margin: 10px" background :current-page="form.limit" :page-sizes="pageSize"
      :page-size="form.page" layout="total, sizes, prev, pager, next" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" :total="total">
    </el-pagination>
    <el-dialog title="角色信息" :visible.sync="dialogFormVisible" width="30%">
      <el-form :model="formModify">
        <el-form-item label="账号:" label-width="100px">
          <el-input v-model="formModify.uaccount" clearable disabled></el-input>
        </el-form-item>
        <el-form-item label="用户名:" label-width="100px">
          <el-input v-model="formModify.uname" clearable disabled></el-input>
        </el-form-item>
        <el-form-item label="手机号:" label-width="100px">
          <el-input v-model="formModify.uphone" clearable disabled></el-input>
        </el-form-item>
        <el-form-item label="邮箱:" label-width="100px">
          <el-input v-model="formModify.uemail" clearable disabled></el-input>
        </el-form-item>
        <el-form-item label="角色名称:" label-width="100px">
          <el-select v-model="formModify.roleName" clearable placeholder="请选择" @change="roleNameId">
            <el-option v-for="item in xgsOptions" :key="item.id" :label="item.rname" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" class="blue-btn" @click="dialogBtn">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="新建角色信息" :visible.sync="newdialogForm" width="30%">
      <el-form :model="newUserForm">
        <el-form-item label="账号:" label-width="100px">
          <el-input v-model="newUserForm.uaccount" clearable></el-input>
        </el-form-item>
        <el-form-item label="用户名:" label-width="100px">
          <el-input v-model="newUserForm.uname" clearable></el-input>
        </el-form-item>
        <el-form-item label="手机号:" label-width="100px">
          <el-input v-model="newUserForm.uphone" clearable></el-input>
        </el-form-item>
        <el-form-item label="邮箱:" label-width="100px">
          <el-input v-model="newUserForm.uemail" clearable></el-input>
        </el-form-item>
        <el-form-item label="角色名称:" label-width="100px">
          <el-select v-model="newUserForm.roleName" clearable placeholder="请选择" @change="roleNameId">
            <el-option v-for="item in xgsOptions" :key="item.id" :label="item.rname" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="newdialogForm = false">取 消</el-button>
        <el-button type="primary" class="blue-btn" @click="newdialogBtn">确 定</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>
import {
  resquest,
getuserInfo,
  queryUserList,
  selectUserList,
  editUserList
} from '@/api/api'
import axios from 'axios'
export default {
  data () {
    return {
      url: resquest,
      tableHeight: 0,
      newdialogForm: false,
      form: {
        uname: '',
        code: "",
        page: 10,
        limit: 1
      },
      formModify: {
        uname: '',
        id: '',
        uaccount: '',
        uphone: '',
        uemail: '',
        roleName: '',
        roleId: ''
      },
      newUserForm: {
        uname: '',
        id: '',
        uaccount: '',
        uphone: '',
        uemail: '',
        roleName: '',
        roleId: ''
      },

      xgsOptions: [],
      pageSize: [10, 20, 50, 100], //分页页数
      total: 0, //总共页数
      tableData: [],
      dialogFormVisible: false,
    }
  },
  mounted () {
    this.setTablesHeight()

    const that = this
     const token = sessionStorage.getItem("bhneToken")
     const pageType = sessionStorage.getItem('bhnePageType')
    getuserInfo(token).then((res) => {
      if (Object.keys(res.data.result).length == 0) {
        this.$message.error("获取用户信息失败，请重新进入页面")
      }
        let menuTypes = res.data.result.zmenu.split(',')

      if (menuTypes.includes(pageType)) {
        console.log("允许进入");
        this.showPage=true
      }else{
          console.log("无权限")
         this.$message.warning("无权限")
         this.showPage=false
      }
      this.userType = res.data.result.rank
      this.userId = res.data.result.USERID
      this.getList()
    })
    window.onresize = function () {
      that.setTablesHeight()
    }
  },
  methods: {
    setTablesHeight () {
      this.$nextTick(() => {
        const tablesAreaHeight = document.getElementsByClassName('driver')[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        this.tableHeight = windowsAreaHeight - tablesAreaHeight - 80
      })
    },
    getList () {
      let params = {
        uname: this.form.uname,
        // page: this.form.page,
        page: this.form.limit,
        // rows: this.form.limit,
        rows: this.form.page,
        code: this.form.code,
      }
      queryUserList(params).then(res => {
        console.log(res, "测试测试")
        this.tableData = res.data.rows
        this.total = res.data.total
      })
      // axios.get(this.url + '/t-dtf-userinfo/list?uname=' + this.form.uname + '&page=' + this.form.page +
      // 	'&rows=' + this.form.limit).then(
      // 	(res) => {
      // 		this.tableData = res.data.rows
      // 		this.total = res.data.total
      // 	}
      // )
    },
    query () {
      this.getList()
    },
    // 新建用户
    newConst () {
      this.newdialogForm = true
    },
    newdialogBtn () {
      this.newdialogForm = true
      console.log(this.newUserForm, "表单信息")

    },
    // table列表序号索引
    indexMethod (index) {
      return (this.form.limit - 1) * 10 + index + 1
    },
    // 修改
    Detid (row) {
      this.formModify = row
      this.dialogFormVisible = true
      selectUserList().then(res => {
        console.log(res, "查下拉框")
        this.xgsOptions = res.data.rows
      })
      // 角色下拉
      // axios.get(this.url + '/t-dtf-roleinfo/list').then(
      // 	(res) => {
      // 		this.xgsOptions = res.data.rows
      // 	}
      // )
    },
    handleSizeChange (val) {
      this.form.page = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.form.limit = val
      this.getList()
    },
    roleNameId (val) {
      this.formModify.roleId = val
    },
    dialogBtn () {
      const params = {
        id: this.formModify.id,
        roleId: this.formModify.roleId
      }
      editUserList(params).then(res => {
        console.log(res, '编辑')
        if (res.data.message === "成功") {
          this.$message({
            message: '修改成功!',
            type: 'success'
          })
        } else {
          this.$message.error('修改失败!')
        }
        this.getList()
      })
      //   axios.post(this.url + '/t-dtf-userinfo/edit', params).then(
      //     (res) => {
      //       if (res.data.message === "成功") {
      //         this.$message({
      //           message: '修改成功!',
      //           type: 'success'
      //         });
      //       } else {
      //         this.$message.error('修改失败!');
      //       }
      //       this.getList()
      //     }
      //   )
      this.dialogFormVisible = false
    }
  }
}
</script>

<style>
.el-dialog__body {
  padding: 24px !important;
}
.el-dialog__footer {
  padding: 16px 24px !important;
}
</style>
