<template>
  <div>
    <div class="wcqBox">
        <img src="@/assets/img/<EMAIL>" alt="">
        <p class="tipTitle">403</p>
        <p class="tipText">用户无权限，请联系运维人员分配权限</p>
        <el-button type="primary" @click="goMain" size="medium" >返回首页</el-button>
    </div>
  </div>
</template>

<script>
export default {
data(){
    return{

    }
},
methods:{
    goMain(){
        console.log("返回首页");
    }
}
}
</script>

<style lang="scss" scoped>
.wcqBox{
width: 100vw;
height: 100vh;
display: flex;
justify-content: center;
align-items: center;
flex-direction: column;
}
.tipText{
color: #646566;
font-family: Microsoft YaHei;
font-weight: regular;
font-size: 14px;
line-height: 20px;
letter-spacing: 0px;
text-align: left;

}
.tipTitle{
    color: #323233;
font-family: Microsoft YaHei;
font-weight: regular;
font-size: 24px;
line-height: 22px;
letter-spacing: 0px;
text-align: left;

}
.el-button--primary{
border-radius: 2px;
background: #526ADE;
}
</style>