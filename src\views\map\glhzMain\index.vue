<template>
  <div>
    <!--光缆绘制没有数据暂时屏蔽2022.12.22-->
    <!--光缆绘制-->
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div v-if="showBackAdds" class="maps-zhedNav" @click="backCurrentDom">
            <img
              class="mapites-backImg"
              :src="require('@/assets/' + 'map/settingImg/backAdds.png')"
              alt=""
            />
          </div>
          <span />
          同杆并架
          <div v-if="!showBackAdds" class="maps-zhedNav" @click="submitData">
            <img
              v-show="!isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zhedie.png')"
              alt=""
            />
            <img
              v-show="isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zkzhedie.png')"
              alt=""
            />
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img
            class="settingImg"
            :src="require('@/assets/' + 'map/settingImg/useSetting.png')"
            alt=""
          />
          <p>启用</p>
        </div>
      </div>
      <div v-show="isFoldArea" class="pro-addForm">
        <div v-show="isShowNav" class="map-showNav">基本信息</div>
        <van-row v-show="isShowNav">
          <van-col :span="12">
            <van-field
              v-model="startPointMark"
              label="开始点编号"
              disabled
              placeholder="请输入杆塔编号"
            />
          </van-col>
          <van-col :span="12">
            <van-field
              v-model="endPointMark"
              disabled
              label="结束点编号"
              placeholder="请输入杆塔编号"
            />
          </van-col>
        </van-row>
        <div class="map-showNav">选型信息</div>
        <van-row>
          <van-col :span="12">
            <!--光缆型号-->
            <van-field
              readonly
              clickable
              :value="opticCable.opticModel"
              label="光缆型号"
              placeholder="光缆型号"
              @click="settingObj.opticCable.opticModelVis = true"
            />
            <van-popup
              v-model="settingObj.opticCable.opticModelVis"
              position="bottom"
            >
              <van-picker
                show-toolbar
                title="光缆型号"
                value-key="name"
                :columns="opticCableType"
                @confirm="onConfirmGlSel(0, $event)"
                @cancel="settingObj.opticCable.opticModelVis = false"
              />
            </van-popup>
          </van-col>
          <van-col :span="12">
            <!--光缆状态-->
            <van-field
              readonly
              clickable
              :value="opticCable.opticState"
              label="光缆状态"
              placeholder="选择光缆状态"
              @click="settingObj.opticCable.opticStateVis = true"
            />
            <van-popup
              v-model="settingObj.opticCable.opticStateVis"
              position="bottom"
            >
              <van-picker
                show-toolbar
                title="光缆状态"
                value-key="key"
                :columns="mainLineState"
                @confirm="onConfirmGlSel(1, $event)"
                @cancel="settingObj.opticCable.opticStateVis = false"
              />
            </van-popup>
          </van-col>
        </van-row>
        <van-row>
          <van-col :span="12">
            <!--光缆余线-->
            <van-field
              v-model="opticCable.opticName"
              label="光缆余线"
              placeholder="请输入光缆余线"
            />
          </van-col>
          <van-col :span="12">
            <!--光缆根数-->
            <van-field
              readonly
              clickable
              :value="opticCable.opticNum"
              label="光缆根数"
              placeholder="选择光缆根数"
              @click="settingObj.opticCable.opticNumVis = true"
            />
            <van-popup
              v-model="settingObj.opticCable.opticNumVis"
              position="bottom"
            >
              <van-picker
                show-toolbar
                title="光缆根数"
                value-key="key"
                :columns="opticNum"
                @confirm="onConfirmGlSel(2, $event)"
                @cancel="settingObj.opticCable.opticNumVis = false"
              />
            </van-popup>
          </van-col>
        </van-row>
        <div v-if="isShowNav" class="map-showNav">其他信息</div>
        <van-row v-if="isShowNav">
          <!--经度-->
          <van-field v-model="startLngtitude" label="起始点经度" disabled />
        </van-row>
        <van-row v-if="isShowNav">
          <!--纬度-->
          <van-field v-model="startLattitude" label="起始点纬度" disabled />
        </van-row>
        <van-row v-if="isShowNav">
          <!--高程-->
          <van-field v-model="endLngtitude" label="结束点经度" disabled />
        </van-row>
        <van-row v-if="isShowNav">
          <!--高程-->
          <van-field v-model="endLattitude" label="结束点纬度" disabled />
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { apiget,apipost } from "@/utils/mapRequest";
export default {
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false,
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的id
    editData: {
      type: Object,
      defaults: () => {},
    },
  },
  watch: {
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal;
      },
      deep: true,
      immediate: true,
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal;
        this.addParam.opticCable.imgList = [];
        this.addParam.opticCable.audioList = [];
        this.getAwaitTowerOrLineType(6, "GLXH", "", "");
        this.chooseItems = "架空线路";
        this.selectCheck.objVis.showOpticCable = true;
        this.selectCheck.showEveryItems.showOpticCable = true;
        this.addParam.opticCable.opticNum = data.glNum;
        this.addParam.opticCable.opticState = data.state;
        this.addParam.opticCable.opticModelId = data.lineModelId;
        this.addParam.opticCable.opticModel = data.lineModel;
        this.addParam.opticCable.opticName = data.lineLength;
        this.addParam.opticCable.message = data.note;
        for (const k in data.imgList) {
          const objs = {
            url: data.imgList[k].path,
            isImage: true,
            isSaveReport: data.imgList[k].isSaveReport,
          };
          this.addParam.opticCable.imgList.push(objs);
        }
        for (const s in data.voiceList) {
          const objs = {
            content: data.voiceList[s].path,
          };
          this.addParam.opticCable.audioList.push(objs);
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      isFoldArea: false,
      startPointMark: "", // 开始点编号
      endPointMark: "", // 结束点编号
      startLngtitude: 0, // 有起始点存在情况的开始点经度
      startLattitude: 0, // 有起始点存在情况的开始点纬度
      endLngtitude: 0, // 有起始点存在情况的结束点经度
      endLattitude: 0, // 有起始点存在情况的结束点纬度
      settingObj: {
        // 光缆
        opticCable: {
          opticNumVis: false, // 光缆根数
          opticStateVis: false, // 光缆状态
          opticModelVis: false, // 光缆型号
        },
      },
      // 光缆
      opticCable: {
        opticNum: "", // 光缆根数
        opticName: "", // 光缆余线
        opticState: "", // 光缆状态
        opticModel: "", // 光缆型号
        opticModelId: "", // 光缆型号id
        imgList: [], // 文件列表
        message: "", // 备注信息
        audioList: [], // 语音列表
      },
      opticCableType: [], // 光缆型号 这里从后台读 杆塔型号和导线型号都从后端读
      mainLineState: [
        {
          key: "新建",
          value: "新建",
        },
        {
          key: "原有",
          value: "原有",
        },
        {
          key: "改造",
          value: "改造",
        },
        {
          key: "拆除",
          value: "拆除",
        },
      ], // 电缆线路状态
      opticNum: [
        {
          key: "1",
          value: "1",
        },
        {
          key: "2",
          value: "2",
        },
        {
          key: "3",
          value: "3",
        },
        {
          key: "4",
          value: "4",
        },
      ], // 光缆根数
    };
  },
  mounted() {},
  methods: {
    submitData() {
      const parma = {
        type: 4,
        param: this.opticCable,
        visParam: this.settingObj.opticCable,
      };
      this.$emit("submitChildData", parma);
    },
    /**
     * 光缆绘制
     */
    onConfirmGlSel(type, item) {
      const val = item.value;
      switch (type) {
        case 0:
          this.addParam.opticCable.opticModel = item.spec;
          this.addParam.opticCable.opticModelId = item.materialsprojectid;
          this.settingObj.opticCable.opticModelVis = false;
          break;
        case 1:
          this.addParam.opticCable.opticState = val;
          this.settingObj.opticCable.opticStateVis = false;
          break;
        case 2:
          this.addParam.opticCable.opticNum = val;
          this.settingObj.opticCable.opticNumVis = false;
          break;
      }
    },
    backCurrentDom() {
      this.$emit("backCurrentDom");
      // this.getFirstTowerOrLineType(0, 1, 1, '', '', '', '', '10kV')
      // // // 查询导线型号类型
      // this.getFirstTowerOrLineType(1, 2, 1, '', '', '', '', '10kV')
      // 光缆
      this.opticCable.opticNum = "1";
      this.opticCable.opticName = "1";
      this.opticCable.opticState = "新建";
      this.opticCable.opticModel = "";
      this.opticCable.opticModelId = "";
      this.opticCable.imgList = [];
      this.opticCable.message = "";
      this.opticCable.audioList = [];
      this.settingObj.opticCable.opticNumVis = false;
      this.settingObj.opticCable.opticStateVis = false;
      this.settingObj.opticCable.opticModelVis = false;
    },
    getFirstTowerOrLineType(
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        // 光缆型号
        that.settingObj.opticCableType = res.data;
        that.opticCable.opticModel = res.data[0].spec;
        that.opticCable.opticModelId = res.data[0].materialsprojectid;
      });
    },
    getAwaitTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName,
      selectVal,
      selectLevelTwoVal
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        that.settingObj.opticCableType = res.data;
      });
    },
  },
};
</script>

<style lang="sass" scoped>
</style>

