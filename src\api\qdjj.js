import http from '../utils/http'
import qs from 'qs'
import axios from 'axios'
// import ip from '/public/ip.json'
/**
 *  @parms resquest 请求地址 例如：http://************:8088/request/...
 *  @param '/testIp'代表vue-cil中config，index.js中配置的代理
 */
const resquest = process.env.VUE_APP_API_publicUrl


export function login (params) {
	params = qs.stringify(params)
	return http.post(`${resquest}/tDtfUserRoles/getUser`, params)
}

export function getMenuChildren (params) {
	params = qs.stringify(params)
	return http.post(`${resquest}/tDtfUserRoles/getUserid`, params)
}

  /**
   * 获取工程量
   */

  export function GetProjectCost (params) {
	return http.post(`${resquest}/bom-project-data/search/`, params)
}

export {
	resquest
}
