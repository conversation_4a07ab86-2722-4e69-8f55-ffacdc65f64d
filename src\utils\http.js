/****   http.js   ****/
// 导入封装好的axios实例
import axiosInstance from './request'

const http = {
  /**
   * methods: 请求
   * @param url 请求地址
   * @param params 请求参数
   */
  get (url, params) {
    console.log(params)
    const config = {
      method: 'get',
      url: url,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
    }
    if (params) config.params = params
    return axiosInstance(config)
  },
  post (url, params) {
    const config = {
      method: 'post',
      url: url,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
    }
    if (params) config.data = params
    return axiosInstance(config)
  },
  postnoJM (url, params) {
    const config = {
      method: 'post',
      url: url,
      noDoDecrypt: true,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
    }
    if (params) config.data = params
    return axiosInstance(config)
  },
  // 上传文件
  postFile (url, params) {
    const config = {
      method: 'post',
      url: url,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      
      noDoDecrypt: true,
      data: params
    }

    return axiosInstance(config)
  },
  put (url, params) {
    const config = {
      method: 'put',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      url: url,
    }
    if (params) config.params = params
    return axiosInstance(config)
  },
  delete (url, params) {
    const config = {
      method: 'delete',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      url: url,
    }
    if (params) config.params = params
    return axiosInstance(config)
  },
  //接口里面必须传responseType和Content-Type
  downloadPost (url, params) {
    const config = {
      method: 'post',
      url: url,
      responseType: 'blob', //传blob类型
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
    }
    if (params) config.data = params
    return axiosInstance(config)
  },
  // 获取馈线设备信息
  getObtain (url, params) {
    const config = {
      method: 'post',
      url: url,
      headers: {
        'Content-Type': 'application/json',
      },
    }
    if (params) config.data = params
    config.headers['Content-Type'] = 'application/json'
    return axiosInstance(config)
  },
  postFormData (url, params) {
    const config = {
      method: 'post',
      url: url,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
    }
    if (params) config.data = params
    config.headers['Content-Type'] = 'application/json;charset=UTF-8'
    return axiosInstance(config)
  }
}
//导出
export default http
