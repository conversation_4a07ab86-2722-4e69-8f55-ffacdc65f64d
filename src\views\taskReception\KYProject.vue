<template>
  <div class="index flex-c h100">
     <div style="height:100vh;width:100vw;background:#fff;  position: absolute;left:0;top:0;z-index:99" v-show="!showPage"></div>
    <!-- 标题 -->
    <div class="main-header">可研任务接收</div>
    <div class="" style="width: 100%; height: 1px; border-bottom: 1px solid #eee"></div>
    <!-- 表单 -->
    <div class="query-form-box">
      <el-form ref="form" :model="form" :inline="true" label-width="80px">
        <el-row>
          <el-col :span="5">
            <el-form-item label="年度:" prop="pcnd">
              <el-date-picker v-model="form.pcnd" format="yyyy" value-format="yyyy" type="year" placeholder="选择年">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="地市公司:" prop="dsgs">
              <el-select v-model="form.dsgs" clearable placeholder="请选择" @change="CityPoint">
                <el-option v-for="item in dsgsOptions" :key="item.cityid" :label="item.cityname" :value="item.cityid">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="县公司:" prop="xgs">
              <el-select v-model="form.xgs" clearable placeholder="请选择">
                <el-option v-for="item in xgsOptions" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="lastSearchArea">
            <el-form-item label="工程名称:" prop="projectName">
              <el-input v-model="form.projectName" clearable placeholder="请输入工程名称"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="状态:" prop="projState">
              <el-select v-model="form.projState" clearable placeholder="请选择">
                <el-option label="待处理" value="01">待处理</el-option>
                <el-option label="处理中" value="02">处理中</el-option>
                <el-option label="已提交" value="05" v-show="userType=='3'">已提交</el-option>
                <el-option label="已提交" value="06" v-show="userType=='2'">已提交</el-option>
                <el-option label="评审通过" value="04">评审通过</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="19" class="lastSearchArea">
            <el-button class="blue-btn" @click="query()">查询</el-button>
            <el-button @click="clearForm()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="driver"></div>
    <div class="actionAreaPage">
      <el-upload class="uploadAction" action="test" accept=".xlsx,.xls" :before-upload="beforeDesignUpload" :show-file-list="false" :http-request="uploadFilesProject">
        <div><el-button>
            <div class="buttonArea"><img src="@/assets/img/批量导入.svg" alt="" style="margin-right:3px">
              <div> 批量导入</div>
            </div>
          </el-button>
        </div>
      </el-upload>

      <!-- <el-button @click="downLoadTemplates" style="margin-left:12px">
        <div class="buttonArea"><img src="@/assets/img/下载.svg" alt="" style="margin-right:3px">
          <div> 模板下载</div>
        </div>
      </el-button> -->
      <el-button @click="removeListPorj" style="margin-left:12px">
        <div class="buttonArea"><img src="@/assets/img/回退.svg" alt="" style="margin-right:3px">
          <div> 批量删除</div>
        </div>
      </el-button>
    </div>
    <!-- 项目列表 -->
    <div class="tablesArea">
      <el-table :data="tableData"  ref="table" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }" :height="tableHeight" highlight-current-row @selection-change="handleSelectionChange" style="width: 98%; margin: 0px 16px 0 16px" :row-key="getRowKeys">
        <el-table-column type="selection" :reserve-selection="true" width="55" fixed="left">
        </el-table-column>
        <el-table-column type="index" label="序号" align="center" :index="indexMethod" :resizable="false" width="60">
        </el-table-column>
        <el-table-column prop="pcnd" label="年度" align="left" width="200px">
        </el-table-column>
        <el-table-column prop="projectName" label="工程名称" align="left">
        </el-table-column>
        <el-table-column prop="cityName" label="地市公司" align="left" width="130px">
        </el-table-column>
        <el-table-column prop="countyName" label="县公司" align="left" width="130px">
        </el-table-column>
        <el-table-column prop="voltageLevel" label="电压等级" align="left" width="100px">
        </el-table-column>
        <el-table-column label="适用深度规范" align="left" width="140px">
          <template slot-scope="scope">
            <span v-if="scope.row.projectType == '01'">架空</span>
            <span v-if="scope.row.projectType == '02'">电缆</span>
            <span v-if="scope.row.projectType == '03'">配变</span>
          </template>
        </el-table-column>
        <!-- <el-table-column
          prop="address"
          label="编辑"
          align="center"
          width="100px"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              class="el-buttonStyle"
              @click="handleEdit(scope.row)"
              >编辑
            </el-button>
          </template>
        </el-table-column> -->
        <el-table-column prop="projState" label="任务状态" align="left" width="160px">
          <template slot-scope="scope">
            <span v-if="(scope.row.projState == '01' || scope.row.projState == '')" style="color: skyblue">待处理</span>
            <span v-else-if="scope.row.projState == '02'" style="color: orangered">处理中</span>
            <span v-else-if="scope.row.projState == '06' && userType == '3'" style="color: royalblue">已提交（地市公司评审）</span>
            <span v-else-if="scope.row.projState == '06' && userType == '2'" style="color: royalblue">已提交（地市公司评审）</span>
            <span v-else-if="scope.row.projState == '05' && userType == '3'" style="color: royalblue">已提交(县公司评审)</span>
            <span v-else-if="scope.row.projState == '04'" style="color: green">评审通过</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="left" width="250px">
          <template slot-scope="scope">
            <span class="directiveHandle">
              <div :class="'el-buttonStyle'">
                <span @click="handleEdit(scope.row)"> 编辑 </span>
              </div>
              <span class="el-buttonDriver">|</span>
              <div :class="'el-buttonStyle'">
                <span @click="removeProj(scope.row)"> 删除 </span>
              </div>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination style="margin: 10px" background :current-page="form.page" :page-sizes="pageSize" :page-size="form.perPage" layout="total, sizes, prev, pager, next" @current-change="handleCurrentChange" @size-change="handleSizeChange"
      :total="total">
    </el-pagination>
    <!-- 编辑数据 -->
    <el-dialog title="编辑数据" :visible.sync="editDialog" width="25%" center>
      <el-form ref="formEdit" :model="formEdit" :inline="true">
        <el-form-item label="批次年度:" prop="pcnd" label-width="120px">
          <el-date-picker v-model="formEdit.pcnd" format="yyyy" value-format="yyyy" type="year" placeholder="选择批次年度">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划开工时间:" prop="pcnd" label-width="120px">
          <el-date-picker v-model="formEdit.kyjhkgsj" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" placeholder="选择计划开工时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划授产时间:" prop="pcnd" label-width="120px">
          <el-date-picker v-model="formEdit.kyjhtcsj" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" placeholder="选择计划授产时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialog = false">取 消</el-button>
        <el-button class="blue-btn" @click="editDialogQuery">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 现状数据 -->

    <!-- 馈线ID -->
    <el-dialog title="馈线ID查看" width="95%" style="height: 100vh" v-if="feederDialog" :visible.sync="feederDialog">
      <div style="height: 500px; overflow-y: auto" v-loading="fullscreenLoading" element-loading-text="拼命加载中">
        <!-- <FeederDialog :ProCode="cuttentProCode" v-if="flag"></FeederDialog> -->
        <json-viewer class="jsonview" :value="feederData" :expand-depth="jsonformat.expandDepth" :copyable="jsonformat.copyable">
        </json-viewer>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="feederDialog = false">取 消</el-button>
        <el-button class="blue-btn" @click="feederDialogQuery">保 存</el-button>
      </span>
    </el-dialog>
    <!-- 成果分析 -->
    <el-dialog title="选择解析内容" width="25%" style="height: 100vh" :visible.sync="resultDialog">
      <div style="height: 100%">
        <ResultsDialog :ProCode="cuttentresult" v-if="flag"></ResultsDialog>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSettlement,
  getCity,
  getCounty,
  getXQEditPull,
  EditSave,
  handleReturn,
  handleSubt,
  getURLSJ,
  getuserInfo,
  uadateProstate,
  feeederObtainCurrent,
  gethandleObtain,
  handleSubtCS,
  handleReturnCS,
  importFileProjectInfo,
  downLoadTemplate
} from "@/api/api"
import FeederDialog from "@/views/projectAgent/KYcomonents/tree"
import ResultsDialog from "@/views/projectAgent/KYcomonents/table"
import { EDITMixins } from "@/utils/EDITMixins.js"
export default {
  components: {
    FeederDialog,
    ResultsDialog,
  },
  mixins: [EDITMixins],
  data() {
    return {
      flag: true,
      showPage:false,
      jsonformat: {
        expandDepth: 2,
        copyable: true,
      },
      fullscreenLoading: false,
      form: {
        //表单参数
        id: "",
        projState: "",
        stageType: "1",
        projectName: "",
        pcnd: '',
        BATCH: "",
        dsgs: "",
        xgs: "",
        xmName: "",
        tgzt: "09",
        csmc: "",
        dwmc: "",
        page: 1,
        perPage: 10,
        code: '',
        source: ''
      },
      xgsOptions: [], //县公司下拉数据
      dsgsOptions: [], //城市下拉
      jsdwOptions: [], //批次名次下拉数据
      pageSize: [10, 20, 50, 100], //分页页数
      q: {
        page: 1,
        limit: 5,
      },
      total: 0, //总共页数
      tableData: [],
      tableHeight: 0,
      formEdit: {
        //编辑
        taskID: "", //主键id
        kyjhkgsj: null,
        kyjhtcsj: null,
        pcnd: new Date().getFullYear() + '',
      },
      cuttentProCode: "", //馈线ID
      cuttentresult: "", //成果分析
      gridData: [
        {
          id: "1",
          name: "断面数据生成",
          taskid: "1",
          pro: 0,
          isSuccess: false,
        },
        {
          id: "2",
          name: "图模数据生成",
          taskid: "2",
          pro: 0,
          isSuccess: false,
        },
        {
          id: "3",
          name: "接收CIM数据",
          taskid: "3",
          pro: 0,
          isSuccess: false,
        },
        {
          id: "4",
          name: "接收SVG数据",
          taskid: "4",
          pro: 0,
          isSuccess: false,
        },
        {
          id: "5",
          name: "架空线路图模解析",
          taskid: "5",
          pro: 0,
          isSuccess: false,
        },
        {
          id: "6",
          name: "电缆线路图模解析",
          taskid: "6",
          pro: 0,
          isSuccess: false,
        },
        {
          id: "7",
          name: "配电站房图模解析",
          taskid: "7",
          pro: 0,
          isSuccess: false,
        },
        {
          id: "8",
          name: "生成zip",
          taskid: "8",
          pro: 0,
          isSuccess: false,
        },
      ], //问题清单table
      editDialog: false, //编辑
      obtainDialog: false, //现状数据
      feederDialog: false, //馈线ID
      resultDialog: false, //成果分析
      userId: "",
      feederData: [],
      timer: null,
      isProgressExecuting: false,
      isComplete: false,
      isErr: false, //判断是否正常返回
      taskID: "",
      interval: null,
      alertCofirm: false,
      userType: ''
    }
  },
  watch: {
    obtainDialog(val) {
      console.log(val, "watch")
      if (!val) {
        console.log("清空进度条")
        this.gridData.forEach((item) => {
          item.pro = 0
          item.isSuccess = false
        })
      }
    },
  },
  mounted() {
    this.setTablesHeight()
    const token = sessionStorage.getItem("bhneToken")
     const pageType = sessionStorage.getItem('bhnePageType')
    getuserInfo(token).then((res) => {
      if (Object.keys(res.data.result).length == 0) {
        this.$message.error("获取用户信息失败，请重新进入页面")
      }
       let menuTypes = res.data.result.zmenu.split(',')

      if (menuTypes.includes(pageType)) {
        console.log("允许进入");
        this.showPage=true
      }else{
          console.log("无权限")
         this.$message.warning("无权限")
         this.showPage=false
      }
      this.userType = res.data.result.rank
      this.form.csmc = res.data.result.USERID
      this.form.dwmc = res.data.result.DWMC
      this.userId = res.data.result.USERID
      this.getList()
    })
    this.showPage=true
    const that = this
    window.onresize = function () {
      that.setTablesHeight()
    }
  },
  methods: {
    beforeDesignUpload(file) {
      let fileName = file.name
      let pos = fileName.lastIndexOf('.')
      let lastName = fileName.substring(pos, fileName.length)
      const ext = lastName.toLowerCase()
      // if (ext !== '.doc' && ext !== '.docx' && ext != '.xls' && ext != '.xlsx' && ext != '.txt' && ext !=
      //   '.bmp' && ext != '.word') {
      //   this.$message.error('文件必须为.doc')
      //   return
      // }
    },
    setTablesHeight() {
      this.$nextTick(() => {
        const tablesAreaHeight = document.getElementsByClassName('driver')[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        this.tableHeight = windowsAreaHeight - tablesAreaHeight - 120
      })
    },
    // 获取列表
    getList() {
      getSettlement(this.form)
        .then((res) => {
          this.tableData = res.data.data
          this.total = res.data.total
        })
        .catch(() => { })
      // 获取城市下拉
      if (this.dsgsOptions.length === 0) {
        const params = {
          optId: this.form.dwmc,
        }
        getCity(params)
          .then((res) => {
            this.dsgsOptions = res.data.result
          })
          .catch(() => { })
      }
    },
    // 城市点击获取县下拉
    CityPoint(val) {
      const params = {
        optId: val,
        dwmc: this.form.dwmc,
      }
      getCounty(params)
        .then((res) => {
          this.xgsOptions = res.data.result
        })
        .catch(() => { })
    },
    // table列表序号索引
    indexMethod(index) {
      return (this.form.page - 1) * this.form.perPage + index + 1
    },
    // 查询
    query() {
      this.getList()
    },
    // 重置
    clearForm() {
      this.form = {
        //表单参数
        id: "",
        projState: "",
        stageType: "1",
        projectName: "",
        pcnd: "",
        BATCH: "",
        dsgs: "",
        xgs: "",
        xmName: "",
        tgzt: "09",
        csmc: this.form.csmc,
        dwmc: this.form.dwmc,
        page: 1,
        perPage: 10,
        source: ''
      }
      this.getList()
    },
    // 复选框
    handleCurrentChange(row) {
      this.form.page = row
      this.getList()
    },
    handleSizeChange(row) {
      this.form.perPage = row
      this.getList()
    },
    // 提交
    handleSubt(row) {
      this.$confirm("是否确定提交?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: "warning",
      })
        .then(() => {
          let params = { taskid: row.taskID, rank: this.userType }
          handleSubtCS(params)
            .then((res) => {
              if (res.data.message == "success") {
                this.$message({
                  message: "提交成功",
                  type: "success",
                })
              } else {
                this.$message.error("提交失败!")
              }
              this.getList()
            })
            .catch(() => { })
        })
        .catch(() => {

        })
    },
    // 启动设计软件
    handleEamine(row) {
      getURLSJ(row.taskID)
        .then((res) => {
          if (res.data.message == "success") {
            window.location = encodeURI(
              "BhneSJRJ://taskID=" +
              row.taskID +
              "&userID=" +
              this.userId +
              "&token=" +
              "11" +
              "&stageType=1"
            )
          } else {
            this.$message.error("启动失败!")
          }
          console.log(res)
        })
        .catch(() => { })
      // this.$router.push({
      //   path: "/onlineDesign",
      //   query: { taskID: row.taskID, routerType: '/projectAgent/KYProject' },
      // })
    },
    // 退回
    handleReturn(row) {
      this.$confirm("是否确定退回?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: "warning",
      })
        .then(() => {
          let params = {
            rank: this.userType,
            taskid: row.taskID
          }
          handleReturnCS(params)
            .then((res) => {
              if (res.data.message == "success") {
                this.$message({
                  message: "退回成功",
                  type: "success",
                })
              } else {
                this.$message.error("退回失败!")
              }
              this.getList()
            })
            .catch(() => { })
        })
        .catch(() => {

        })
    },
    // table 编辑按钮
    handleEdit(row) {
      this.formEdit.taskID = row.taskID
      this.formEdit.pcnd = row.pcnd
      this.editDialog = true
      getXQEditPull()
        .then((res) => {
          this.pcnd = res.data.result.pcnd
        })
        .catch(() => { })
    },
    // 编辑弹框确认按钮
    editDialogQuery() {
      console.log('this.formEdit', this.formEdit)
      EditSave(this.formEdit)
        .then((res) => {
          if (res.data.message === "success") {
            this.$message({
              message: "修改成功",
              type: "success",
            })
            this.getList()
          } else {
            this.$message.error("修改失败!")
          }
        })
        .catch(() => { })
      this.editDialog = false
    },
    increaseProgress() {
      if (!this.isProgressExecuting) {
        this.isProgressExecuting = true
        this.executeProgress()
      }
    },
    async executeProgress() {
      for (let i = 0; i < this.gridData.length; i++) {
        const task = this.gridData[i]
        await this.increaseTaskProgress(task)
      }

      // 在所有任务完成后触发事件

      setTimeout(() => {
        this.triggerEvent()
      }, 500)
      // 清除定时器
      clearInterval(this.timer)
      this.timer = null
      this.isProgressExecuting = false
    },
    increaseTaskProgress(task) {
      return new Promise((resolve) => {
        if (this.isComplete) {
          this.interval = setInterval(() => {
            const increment = Math.floor(Math.random() * 30) + 1
            task.pro = Math.min(task.pro + increment, 100)

            if (task.pro === 100) {
              task.isSuccess = true
              clearInterval(this.interval)
              resolve() // 当任务完成时解析Promise
            }
          }, 50) // 每秒增加一次进度
        } else {
          this.interval = setInterval(() => {
            const increment = Math.floor(Math.random() * 30) + 1
            task.pro = Math.min(task.pro + increment, 100)
            if (task.pro === 100) {
              task.isSuccess = true
              clearInterval(this.interval)
              resolve() // 当任务完成时解析Promise
            }
          }, 190) // 每秒增加一次进度
        }
      })
    },
    //进度条加载完毕后触发
    triggerEvent() {
      if (this.isComplete) {
        this.obtainDialog = false
        this.gridData.forEach((item) => {
          item.pro = 0
          item.isSuccess = false
        })
        if (this.isErr) {
          this.$message({
            message: "获取成功",
            type: "success",
          })
        } else {
          this.$message({
            message: "获取失败",
            type: "warning",
          })
        }

        console.log(this.obtainDialog, "dldldl")
      }
    },

    handleCofirm(row) {
      if (row.lineID === '') return this.$message.warning("未获取到有效数据")
      this.taskID = row.taskID
      this.$confirm("现状数据只能获取一次,是否确定?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: "warning",
      })
        .then(() => {
          this.handleObtain()
        })
        .catch(() => {

        })
    },
    // table 现状数据
    handleObtain(row) {
      this.isComplete = false
      this.alertCofirm = false
      this.timer = setInterval(this.increaseProgress, 100)
      this.obtainDialog = true
      gethandleObtain(this.taskID).then((res) => {
        if (res.data.status == "200") {
          this.isComplete = true
          this.isErr = true
          this.getList()
          uadateProstate(this.taskID).then((res) => {
            console.log(res, "更新状态")
          })
          // coordinateTransformation(this.taskID).then((res) => {
          //   console.log("坐标转换", res)
          // })
        } else {
          this.isComplete = true
          this.obtainDialog = false
          this.isErr = false
        }

      }).catch(() => {
        this.isComplete = true
        this.obtainDialog = false
        this.isErr = false
      })
      // this.getList();
      //     uadateProstate(this.taskID).then((res) => {
      //       console.log(res, "更新状态");
      //     });
    },
    // 馈线ID
    handleFeeder(row) {
      this.cuttentProCode = row.taskID
      this.feederDialog = true
      this.fullscreenLoading = true
      feeederObtainCurrent(row.taskID).then((res) => {
        console.log(res, " res")
        this.feederData = res.data.result.data
        this.fullscreenLoading = false
      })
    },
    // 馈线保存按钮
    feederDialogQuery() {
      this.feederDialog = false
    },
    // 成果分析
    handleResults(row) {
      this.cuttentresult = row.taskID
      this.resultDialog = true
      this.flag = false
      this.$nextTick(() => {
        this.flag = true
      })
    },
  },
}
</script>

<style>
.progresswrapper {
  width: 100%;
  height: 20px;
  color: white;
  margin-left: 3%;
  background: #ffffff;
  position: relative;
}

.pro {
  width: 90%;
  height: 100%;
  background: #00b83f;
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 40px 40px;
  animation: progressbar 2s linear infinite;
}

@keyframes progressbar {
  0% {
    background-position: 40px 0;
  }

  100% {
    background-position: 0 0;
  }
}

.actionAreaPage {
  padding: 0 16px 12px 16px;
  display: flex;
}
.buttonArea {
  height: 18px;
  display: flex;
  line-height: 18px;
}
::v-deep .el-dialog__body {
  padding: 24px !important;
}
::v-deep .el-dialog__footer {
  padding: 16px 24px !important;
}
</style>
