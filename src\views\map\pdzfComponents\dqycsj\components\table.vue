<template>
  <div class="dqycsj-tables">
    <!--按钮区域-->
    <div class="dqycsj-btns">
      <div class="dqycsj-saveBtn" @click="addDqycsj">
        添加<i class="el-icon-circle-plus-outline"></i>
      </div>
      <div class="dqycsj-delBtn" @click="removeData">
        删除<i class="el-icon-delete"></i>
      </div>
    </div>
    <!--列表-->
    <el-table
      :max-height="600"
      :data="tableData"
      size="mini"
      border
      @selection-change="handleSelectionChange"
      style="width: 100%">
      <el-table-column
        type="selection"
        width="55">
      </el-table-column>
      <el-table-column
        type="index"
        label="序号"
        align="center"
        width="60">
      </el-table-column>
      <el-table-column
        prop="materialcodeerp"
        label="物料编码"
      >
      </el-table-column>
      <el-table-column
        prop="materialdescription"
        label="物料描述"
      >
      </el-table-column>
      <el-table-column
        prop="extensiondescription"
        label="扩展描述">
      </el-table-column>
      <el-table-column
        prop="intervalname"
        label="方案名称">
      </el-table-column>
      <el-table-column
        prop="intervalstate"
        label="设备状态">
      </el-table-column>
      <el-table-column
        prop="number"
        label="间隔编号">
        <template slot-scope="{ row }">
          <el-input v-model="row.intervalnumber"></el-input>
        </template>
      </el-table-column>
    </el-table>
    <!--间隔基本信息-->
    <el-dialog title="间隔基本信息" top="3vh" :visible.sync="setVisible" width="70%" :append-to-body="true">
      <div class="dyycsj-info">
        <div class="dyycsj-infoTop">
          <div class="dyycsj-infoLeft">
            <div class="dyycsj-infoTtitle">物料选择</div>
            <div class="dyycsj-infoTArea">
              <el-form :model="selectForm" ref="ruleForm" :inline="true" class="demo-ruleForm">
                <el-form-item label="电压等级" prop="region">
                  <el-select v-model="selectForm.dydj" placeholder="请选择电压等级">
                    <el-option :label="item.value" :value="item.value" v-for="item in dydjOptions" :key="item.value + '1'"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="设备类别" prop="region">
                  <el-select v-model="selectForm.sblb" placeholder="请选择设备类别" @change="changeSblb">
                    <el-option v-for="item in sblbOptions" :key="item.key" :value="item.key" :label="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-form>
              <el-table
                :max-height="600"
                :data="wlxzTableData"
                border
                :highlight-current-row="true"
                @current-change="handleCurrentRowChange"
                size="mini"
                style="width: 100%">
                <el-table-column
                  type="index"
                  label="序号"
                  align="center"
                  width="60">
                </el-table-column>
                <el-table-column
                  prop="materialcodeerp"
                  label="物料编码"
                >
                </el-table-column>
                <el-table-column
                  prop="technicalprotocol"
                  label="扩展编码"
                >
                </el-table-column>
                <el-table-column
                  prop="materialdescription"
                  label="物料描述">
                </el-table-column>
                <el-table-column
                  prop="extensiondescription"
                  label="扩展描述">
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="dyycsj-infoRight">
            <div class="dyycsj-infoTtitle">图形预览</div>
            <div class="dyycsj-infoTArea" :style="{height: '280px'}">
              <img :src="imgUrl" alt="请点击表格数据" :style="{height: '100%', width: '100%'}">
            </div>
          </div>
        </div>
        <div class="dyycsj-infoBot">
          <div class="dyycsj-infoLeft">
            <div class="dyycsj-infoTtitle">方案选择</div>
            <div class="dyycsj-infoTArea">
              <el-table
                size="small"
                :max-height="600"
                :data="faxzTableData"
                :highlight-current-row="true"
                @current-change="handleFaChange"
                border
                style="width: 100%">
                <el-table-column
                  type="index"
                  label="序号"
                  align="center"
                  width="60">
                </el-table-column>
                <el-table-column
                  prop="intervalname"
                  label="方案名称"
                >
                </el-table-column>
                <el-table-column
                  prop="number"
                  label="间隔数量">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.number" @blur="saveEditVal"></el-input>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="dyycsj-infoRight">
            <div class="dyycsj-infoTtitle">间隔参数</div>
            <div class="dyycsj-infoTArea" :style="{height: '300px', overflowY: 'scroll'}">
              <el-form ref="envForm" :model="envVar" label-width="120px" class="demo-ruleForm submitForm">
                <el-form-item
                  v-for="(domain) in envVar['envVariables' + clickFaIndex]"
                  :label="domain.name"
                  :key="domain.name +'_'"
                >
                  <el-input v-model="domain.value" @input="onInput()"></el-input>
                </el-form-item>
                </el-form>
            </div>
          </div>
        </div>
      </div>
      <div class="dqycsj-footer">
        <div class="submitBtn" @click="submiteInfo">确 定</div>
      </div>
    </el-dialog>
    <!--保存自定义方案-->
    <el-dialog title="自定义方案" :visible.sync="directiveVisible" width="50%" :append-to-body="true">
      <el-form :model="directiveForm" ref="ruleForm" :inline="true" class="demo-ruleForm">
        <el-form-item label="方案编号:">
          <el-input v-model="directiveForm.fabh" :disabled="true" placeholder="方案编号"></el-input>
        </el-form-item>
        <el-form-item label="方案名称:" prop="region">
          <el-input v-model="directiveForm.name" placeholder="方案名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="addDirectiveProject">添加</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :max-height="600"
        :data="zdyfaTableData"
        border
        style="width: 100%">
        <el-table-column
          type="index"
          label="序号"
          align="center"
          width="60">
        </el-table-column>
        <el-table-column
          prop="customschemename"
          label="自定义方案名称"
        >
        </el-table-column>
        <el-table-column
          label="操作"
          width="100">
          <template slot-scope="scope">
            <el-button @click="overPro(scope.row)" type="text" size="small">覆盖</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <div class="dqycsj-footer">
      <div class="submitBtn" @click="submiteDirectiveInfo">保存</div>
    </div>
  </div>
</template>

<script>
  import { apipostGetImg, apipost } from '@/utils/mapRequest'
  export default {
    components: {},
    props: {
      // tabs类型
      tabsType: {
        default: '',
        type: String
      },
      // 传过来得工程id
      clickPointId: {
        default: '',
        type: String
      },
    },
    data() {
      return {
        isSaveData: false, // 保存过后再去查列表
        tableData: [],
        chooseTableData: [],
        allTableData: [], // 高压低压下所有的数据
        wlxzTableData: [], // 物料选择得表格内容
        faxzTableData: [], // 方案选择得表格内容
        zdyfaTableData: [], // 自定义方案得列表数据
        dydjOptions: [], // 电压等级
        sblbOptions: [], // 设备类别
        setVisible: false,
        directiveVisible: false,
        selectForm: {
          dydj: '', // 电压等级
          sblb: '', // 设备类别
        },
        directiveForm: {
          name: '', // 方案名称
          fabh: '', // 方案编号
        },
        imgUrl: '',
        clickFaIndex: 0, // 点击方案当前行的索引
        envVar: {
        },
      };
    },
    mounted() {
      switch (this.tabsType) {
        case '高压':
          this.dydjOptions = [
            {
              value: '10kV'
            },
            {
              value: '20kV'
            },
          ]
          this.selectForm.dydj = '10kV'
          break
        case '低压':
          this.dydjOptions = [
            {
              value: '380V'
            }
          ]
          this.selectForm.dydj = '380V'
          break
      }
      this.imgUrl = require('@/assets/map/settingImg/noImage.png')
      this.getSblb()
      const valtage = this.tabsType === '高压' ? '1' : '0'
      this.getTabsData(valtage)
    },
    methods: {
      removeData() {
        if(this.chooseTableData.length === 0) {
          this.$message.warning('请至少选择一条数据')
        } else {
          let newArr = []
          for(let j in this.chooseTableData) {
            newArr.push(this.chooseTableData[j].customschemeid)
          }
          let params={id:newArr.join(',')}
          apipost(`/mapModel/deleteCustomSchema`,params).then((res)=>{
            const valtage = this.tabsType === '高压' ? '1' : '0'
            this.getTabsData(valtage)
          })
        }
      },
      getSblb() {
        let params={voltage:this.selectForm.dydj}
        apipost(`/mapModel/queryMaterialType`,params).then((res)=>{
          this.sblbOptions = res.result
        })
      },
      onInput() {
        this.$forceUpdate()
      },
      /**
       * 物料选择点击当前行
       */
      handleCurrentRowChange(val) {
        let params={materiasprojectid:val.materialsprojectid}
        apipost(`/mapModel/baseMaterialIdByInterval`,params).then((res)=>{
          for(const j in res.result) {
            res.result[j]['number'] = ''
          }
          this.faxzTableData = res.result
        })
      },
      /**
       * 方案选择点击当前行
       */
      handleFaChange(val) {
        for(let j in this.faxzTableData) {
          if(val.intervalid == this.faxzTableData[j].intervalid) {
            this.clickFaIndex = j
          }
        }
        // 查看图片
        let params={taskid:val.intervalid}
        apipostGetImg(`/mapModel/downloadIntervalPicture`,params).then((res)=>{
          const reader = new FileReader()
          reader.onload = (e) => {
            this.imgUrl = e.target.result
          }
          reader.readAsDataURL(res)
          // 处理方式2——直接使用URL.createObjectURL
          // res.data必须是Blob类型数据
          this.imgUrl = URL.createObjectURL(res)
        })
        let paramsB={intervalid:val.intervalid}
        apipost(`/mapModel/baseIntervalIdByIntervalParameter`,paramsB).then((res)=>{
          this.envVar['envVariables' + this.clickFaIndex] = res.result
          console.log(this.envVar)
        })
      },
      submiteInfo() {
        let arrParam = []
        const valtage = this.tabsType === '高压' ? '1' : '0'
        for(let j in this.faxzTableData) {
          let obj = {
            num: this.faxzTableData[j].number,
            intervalId: this.faxzTableData[j].intervalid,
            data: this.envVar['envVariables' + this.clickFaIndex]
          }
          arrParam.push(obj)
        }
        // 调保存接口
        const param = {
          isgy: valtage,
          data: arrParam
        }
        apipost(`/mapModel/saveIntervalParameter`, param).then((res)=>{
          this.setVisible = false
          this.isSaveData = true
          const valtage = this.tabsType === '高压' ? '1' : '0'
          this.getTabsData(valtage)
        })
      },
      getTabsData(val) {
        apipost(`/mapModel/queryCustomSchema?isgy=${val}`).then((res)=>{
          this.tableData = res.result
          if(this.tableData.length !== 0) {
            this.$emit('isSubmit', this.tableData)
          }
          // 这里查完高压的顺带查下低压的
          const newVal = val === '0' ? '1' : '0'
          apipost(`/mapModel/queryCustomSchema?isgy=${newVal}`).then((res)=>{
            this.allTableData = []
            this.allTableData = this.tableData.concat(res.result)
          })
        })
      },
      handleSelectionChange(val) {
        this.chooseTableData = val
      },
      // 覆盖方案
      overPro(row) {
        let newArr = []
        for(let j in this.allTableData) {
          const obj = {
            "id": this.allTableData[j].customschemeid,
            "code": this.allTableData[j].number
          }
          newArr.push(obj)
        }
        const param = {
          tempSchema: row.customschemename,
          schema: this.directiveForm.fabh,
          data: newArr
        }
        apipost(`/mapModel/saveCustomSchema`, param).then((res)=>{
          this.getZdyFaTable(this.directiveForm.fabh)
        })
      },
      changeSblb(val) {
        let params={voltage:this.selectForm.dydj,key:val}
        apipost(`/mapModel/queryMaterialInfo`,params).then((res)=>{
          this.wlxzTableData = res.result
          this.envVar.envVariables = []
          this.imgUrl = require('@/assets/map/settingImg/noImage.png')
          this.faxzTableData = []
        })
      },
      submiteDirectiveInfo() {
        this.directiveVisible = true
        let paramsQ={pointid:this.clickPointId}
        apipost(`/mapModel/querySchema`,paramsQ).then((res)=>{
          this.directiveForm.fabh = res.result[0].code
          this.getZdyFaTable(this.directiveForm.fabh)
        })
      },
      addDqycsj() {
        this.setVisible = true
      },
      getZdyFaTable(val) {
          let params={schema:val}
        apipost(`/mapModel/queryTempSchema`,params).then((res)=>{
          this.zdyfaTableData = res.result
        })
      },
      saveEditVal() {
      },
      // 添加自定义方案
      addDirectiveProject() {
        let newArr = []
        for(let j in this.allTableData) {
          const obj = {
            "id": this.allTableData[j].customschemeid,
            "code": this.allTableData[j].number
          }
          newArr.push(obj)
        }
        const param = {
          tempSchema: this.directiveForm.name,
          schema: this.directiveForm.fabh,
          data: newArr
        }
        apipost(`/mapModel/saveCustomSchema`, param).then((res)=>{
          this.getZdyFaTable(this.directiveForm.fabh)
          this.$message.success('添加成功')
          this.directiveForm.name = ''
        })
      }
    },
  }

</script>

<style lang="scss" scoped>
  .dqycsj-btns{
    display: flex;
    .dqycsj-saveBtn, .dqycsj-delBtn{
      width: 80px;
      background: #526ade;
      color: #ffff;
      border-radius: 10px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 12px;
    }
    .dqycsj-delBtn{
      margin-left: 12px;
    }
  }
  .dqycsj-footer{
    position: relative;
    display: flex;
    justify-content: end;
    margin: 12px 0 12px 0;
    .submitBtn{
      cursor: pointer;
      width: 80px;
      background: #526ADE;
      border-radius: 20px;
      display: block;
      color: white;
      text-align: center;
      height: 30px;
      line-height: 30px;
    }
  }
  /*弹框详情*/
  .dyycsj-info{
    .dyycsj-infoTop,.dyycsj-infoBot{
      display: flex;
      .dyycsj-infoLeft{
        flex: 1;
      }
      .dyycsj-infoRight{
        width: 400px;
      }
      .dyycsj-infoTArea{
        padding: 12px;
      }
      .dyycsj-infoTtitle{
        border-left: 2px solid #526ade;
        padding-left: 10px;
      }
    }
  }
  ::v-deep{
    .submitForm{
      .el-form-item{
        display: flex;
      }
      .el-form-item__content{
        margin-left: 0 !important;
      }
    }
  }
</style>

