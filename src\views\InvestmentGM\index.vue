<template>
  <div class="index flex-c h100">
    <!-- 标题 -->
    <div class="main-header">投资规模</div>
    <div
      class=""
      style="width: 100%; height: 1px; background-color: #eee"
    ></div>
    <!-- 表单 -->
    <div class="query-form-box">
      <el-form ref="form" :model="form" :inline="true" label-width="80px">
        <el-row>
          <el-col :span="5">
            <el-form-item label="年度:" prop="pcnd">
              <el-date-picker
                v-model="form.pcnd"
                type="year"
                format="yyyy"
                value-format="yyyy"
                placeholder="选择日期"
                @change="NDPoint"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="批次名次:" prop="batch">
              <el-select v-model="form.batch" clearable placeholder="请选择">
                <el-option
                  v-for="item in batchOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="地市公司:" prop="dsgs">
              <el-select
                v-model="form.dsgs"
                clearable
                placeholder="请选择"
                @change="CityPoint"
              >
                <el-option
                  v-for="item in dsgsOptions"
                  :key="item.cityid"
                  :label="item.cityname"
                  :value="item.cityid"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="lastSearchArea">
            <el-form-item label="县公司:" prop="xgs">
              <el-select v-model="form.xgs" clearable placeholder="请选择">
                <el-option
                  v-for="item in xgsOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="工程名称:" prop="projectName">
              <el-input v-model="form.projectName" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="19" class="lastSearchArea">
            <el-button class="blue-btn" @click="query()" icon="el-icon-search"
              >查询</el-button
            >
            <el-button @click="clearForm()" icon="el-icon-refresh"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="driver"></div>
    <div class="cp-tableInfo">
      <div class="cp-btns">
        <div class="cp-handleBtn" @click="TJBtn">
          <span class="cp-texts">
            <img
              style="width: 16px; height: 16px"
              :src="require('@/assets/main/统计.png')"
              alt=""
            />
            <span style="margin-left: 5px"> 统计 </span>
          </span>
        </div>
      </div>
    </div>
    <!-- 项目列表 -->
    <div class="tablesArea">
      <el-table
        :data="tableData"
        :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
        :height="tableHeight"
        @selection-change="handleSelectionChange"
        style="width: 98%; margin: 0px 16px 0 16px; margin-top: 10px"
      >
        <el-table-column type="index" width="60" label="序号" align="center">
        </el-table-column>
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column prop="pcnd" label="年度" align="center" width="150">
        </el-table-column>
        <el-table-column
          prop="batchname"
          label="项目批次"
          align="center"
          width="150px"
        >
        </el-table-column>
        <el-table-column
          prop="dwmc"
          label="地市公司"
          align="center"
          width="130px"
        >
        </el-table-column>
        <el-table-column prop="xgsmc" label="县公司" align="center" width="130px">
        </el-table-column>
        <el-table-column
          prop="stagetype"
          label="工程阶段"
          align="center"
          width="150px"
        >
        </el-table-column>
        <el-table-column prop="xmzs" label="项目申报数量(个)" align="center">
        </el-table-column>
        <el-table-column prop="ztz" label="申报资金(万元)" align="center">
        </el-table-column>
        <el-table-column prop="xmrks" label="评审通过数量(个)" align="center">
        </el-table-column>
        <el-table-column prop="rkztz" label="批复资金(万元)" align="center">
        </el-table-column>
        <el-table-column prop="dwid" label="评审通过率" align="center">
        </el-table-column>
      </el-table>
    </div>
    <div class="cp-tableInfo" v-show="isShowEcarts">
      <div class="cp-btns" style="margin-top: 0">
        <div class="cp-handleBtn" @click="closeEcharts">
          <span class="cp-texts">
            <img
              style="width: 16px; height: 16px"
              :src="require('@/assets/main/hideEcharts.png')"
              alt=""
            />
            <span style="margin-left: 5px"> 关闭图表 </span>
          </span>
        </div>
      </div>
      <div style="display: flex">
        <div
          class=""
          style="
            width: 50%;
            height: 300px;
            overflow: auto;
            overflow-x: auto;
            overflow-y: auto;
          "
        >
          <div style="width: 100%; text-align: left">
            <p>
              <span @click="SumChartsZ()">数量</span>|<span @click="TZChartsZ()"
                >投资</span
              >
            </p>
          </div>
          <div id="main" style="width: 95%; height: 240px" ref="charts"></div>
        </div>
        <div
          style="
            margin-left: 2%;
            width: 48%;
            height: 300px;
            overflow: auto;
            overflow-x: auto;
            overflow-y: auto;
          "
        >
          <div style="width: 100%; text-align: left">
            <p>
              <span @click="SumCharts()">数量</span>|<span @click="TZCharts()"
                >投资</span
              >
            </p>
          </div>
          <div>
            <div style="width: 600px; height: 240px" ref="chartsSum"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getTZGMList,
  getCity,
  getCounty,
  getPCSel,
  getTZBING,
  getuserInfo,
} from '@/api/api'
import * as echarts from 'echarts'
export default {
  data () {
    return {
      bhnePageType: '',
      form: { //表单参数
        pcnd: '',
        batch: '',
        dsgs: '',
        xgs: '',
        dwmc: '',
        projState: '',
        state: '',
        stageType: '',
        projectName: '',
      },
      xgsOptions: [], //县公司下拉数据
      dsgsOptions: [], //城市下拉
      isShowEcarts: true,
      pageSize: [10, 20, 50, 100], //分页页数
      q: {
        page: 1,
        limit: 5
      },
      total: 0, //总共页数
      showTableType: 0, // 0是不展示echarts 1展示echarts
      tableData: [],
      tableHeight: 200,
      batchOptions: [],
      rowList: [],
      xmzs: [],
      dwid: [],
      NameList: [],
      rowListB: [],
      rowListZ: []
    }
  },
  mounted () {
    this.showTableType = 0
    this.setTablesHeight(this.showTableType)
    const token = sessionStorage.getItem('bhneToken')
    getuserInfo(token).then((res) => {
      this.form.csmc = res.data.result.USERID
      this.form.dwmc = res.data.result.DWMC
      this.getList()
    })
    const that = this
    window.onresize = function () {
      that.setTablesHeight(that.showTableType)
    }
  },
  updated () {
    this.echartsSum()
  },
  methods: {
    setTablesHeight (type) {
      this.$nextTick(() => {
        const tablesAreaHeight = document.getElementsByClassName('driver')[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        switch (type) {
          case 0:
            this.tableHeight = windowsAreaHeight - tablesAreaHeight - 300
            break
          case 1:
            this.tableHeight = windowsAreaHeight - tablesAreaHeight - 680
            break
        }
      })
    },
    // 查询列表
    getList () {
      getTZGMList(this.form)
        .then((res) => {
          this.tableData = res.data.result
        })
        .catch(() => { })
      if (this.dsgsOptions.length === 0) {
        // 获取城市下拉
        const params = {
          optId: this.form.dwmc,
        }
        getCity(params)
          .then((res) => {
            this.dsgsOptions = res.data.result
          })
          .catch(() => { })
      }
      // 饼图
      const param = {
        dsgs: this.form.dwmc,
      }
      getTZBING(param)
        .then((res) => {
          this.rowListB = res.data.result.num
          this.rowListZ = res.data.result.ztz
        })
        .catch(() => { })
    },
    // 饼图数量
    echartsSum () {
      let mychart = echarts.init(this.$refs.chartsSum)
      var optionline1
      optionline1 = {
        color: ["#1AFD9C", "#FF359A", "#0080FF", "#FF9224", '#921AFF', '#2894FF'],
        title: {
          text: '各类型项目统计',
          subtext: '',
          left: '20%',
          top: '4%'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          right: '-10%',
          top: '34%'
        },
        grid: {
          left: '2%',
          right: '12%',
          bottom: '4%',
          top: "3%",
          containLabel: true
        },
        series: [{
          name: '饼图使用动态数据',
          type: 'pie',
          radius: '90%',
          top: '20%',
          left: '-40%',
          data: this.rowListB,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 12,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      //注意，必须用这种语法来传递数据
      mychart.setOption(optionline1)
    },
    // 城市点击获取县下拉
    CityPoint (val) {
      const params = {
        optId: val,
        dwmc: this.form.dwmc,
      }
      getCounty(params)
        .then((res) => {
          console.log(res)
          this.xgsOptions = res.data.result
         
        })
        .catch(() => { })
    },
    NDPoint () {
      // 获取批次名词
      const param = {
        optId: this.form.pcnd,
      }
      getPCSel(param)
        .then((res) => {
          console.log(res)
          this.batchOptions = res.data.result
        })
        .catch(() => { })
    },
    // 查询
    query () {
      this.getList()
    },
    // 重置
    clearForm () {
      this.form = {
        pcnd: '',
        batch: '',
        dsgs: '',
        xgs: '',
        dwmc: '',
        projState: '',
        state: '',
        stageType: '',
        projectName: '',
      }
      this.getList()
    },
    // 复选框
    handleSelectionChange (val) {
      this.rowList = val
    },
    // 数量饼图
    SumCharts () {
      this.echartsSum()
    },
    // 投资饼图
    TZCharts () {
      let mychart = echarts.init(this.$refs.chartsSum)
      var optionline1
      optionline1 = {
        color: ["#1AFD9C", "#FF359A", "#0080FF", "#FF9224", '#921AFF', '#2894FF'],
        title: {
          text: '各类型项目统计',
          subtext: '',
          left: '20%',
          top: '4%'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          right: '-10%',
          top: '34%'
        },
        grid: {
          left: '2%',
          right: '12%',
          bottom: '4%',
          top: "3%",
          containLabel: true
        },
        series: [{
          name: '饼图使用动态数据',
          type: 'pie',
          radius: '90%',
          top: '20%',
          left: '-40%',
          data: this.rowListZ,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 12,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      //注意，必须用这种语法来传递数据
      mychart.setOption(optionline1)
    },
    closeEcharts () {
      this.isShowEcarts = false
      this.showTableType = 0
      this.setTablesHeight(this.showTableType)
    },
    // 编辑按钮
    handleEdit () {
      this.dialogProblem = true
    },
    SumChartsZ () {
      this.TJBtn()
    },
    TZChartsZ () {
      if (this.form.pcnd === '' || this.form.batch === '') {
        this.$message({
          showClose: true,
          message: '请选择年度和批次名称',
          type: 'warning'
        })
      } else {
        this.getList()
        if (this.rowList.length > 0) {
          let dwmcArr = []
          let rkztzArr = []
          let ztzArr = []
          this.rowList.forEach(dept => {
            dwmcArr.push(dept.dwmc)
            rkztzArr.push(dept.rkztz)
            ztzArr.push(dept.ztz)
          })
          let mychart = echarts.init(this.$refs.charts)
          var optionline
          optionline = {
            title: {
              text: '投资',
              subtext: '',
              top: '5%'
            },
            grid: {
              left: 10,
              right: 10,
              bottom: 10,
              top: 120,
              containLabel: true,
            },

            tooltip: {
              trigger: 'axis',
              show: true
            },
            legend: {
              data: ['申报资金', '批复资金'],
              top: '10%',
            },
            toolbox: {
              feature: {
                dataView: {
                  show: true,
                  readOnly: false
                },
                magicType: {
                  show: true,
                  type: ['line', 'bar']
                },
                restore: {
                  show: true
                },
                saveAsImage: {
                  show: true
                }
              }
            },
            calculable: true,
            //X轴上柱子的数据
            xAxis: [{
              type: 'category',
              data: []
            }],
            yAxis: [{
              type: 'value'
            }],
            series: [{
              name: '申报资金',
              type: 'bar',
              data: [

              ],
              //在柱子顶端展示数据的最大值最小值旗袍
              markPoint: {
                data: [{
                  type: 'max',
                  name: 'Max'
                },
                {
                  type: 'min',
                  name: 'Min'
                }
                ]
              },
              //展示数据平均值线
              markLine: {
                data: [{
                  type: 'average',
                  name: 'Avg'
                }]
              },
              //用于在每根柱子上展示数据
              label: {
                show: true,
                position: 'inside',
                color: 'black'
              },
            },
            {
              name: '批复资金',
              type: 'bar',
              data: [

              ],
              markPoint: {
                data: [{
                  type: 'max',
                  name: 'Max'
                },
                {
                  type: 'min',
                  name: 'Min'
                }
                ]
              },
              markLine: {
                data: [{
                  type: 'average',
                  name: 'Avg'
                }]
              }
            },

            ]
          }
          //注意，必须用这种语法来传递数据
          optionline.xAxis[0].data = dwmcArr
          optionline.series[0].data = rkztzArr
          optionline.series[1].data = ztzArr
          mychart.setOption(optionline)
          this.showTableType = 1
          this.setTablesHeight(this.showTableType)
        } else {
          this.$message({
            showClose: true,
            message: '请选择要展示的项目',
            type: 'warning'
          })
        }
      }
    },
    TJBtn () {
      if (this.form.pcnd === '' || this.form.batch === '' || this.form.dsgs === '' || this.form.xgs === '') {
        this.$message({
          showClose: true,
          message: '查询条件未选',
          type: 'warning'
        })
      } else {
        this.getList()
        if (this.rowList.length > 0) {
          let dwmcArr = []
          let sbslArr = []
          let wcslArr = []
          this.rowList.forEach(dept => {
            dwmcArr.push(dept.dwmc)
            sbslArr.push(dept.xmzs)
            wcslArr.push(dept.dwid)
          })
          let mychart = echarts.init(this.$refs.charts)
          var optionline
          optionline = {
            title: {
              text: '工程数',
              subtext: '',
              top: '5%'
            },
            grid: {
              left: 10,
              right: 10,
              bottom: 10,
              top: 120,
              containLabel: true,
            },

            tooltip: {
              trigger: 'axis',
              show: true
            },
            legend: {
              data: ['申报数量', '通过数量'],
              top: '10%',
            },
            toolbox: {
              feature: {
                dataView: {
                  show: true,
                  readOnly: false
                },
                magicType: {
                  show: true,
                  type: ['line', 'bar']
                },
                restore: {
                  show: true
                },
                saveAsImage: {
                  show: true
                }
              }
            },
            calculable: true,
            //X轴上柱子的数据
            xAxis: [{
              type: 'category',
              data: []
            }],
            yAxis: [{
              type: 'value'
            }],
            series: [{
              name: '申报数量',
              type: 'bar',
              data: [

              ],
              //在柱子顶端展示数据的最大值最小值旗袍
              markPoint: {
                data: [{
                  type: 'max',
                  name: 'Max'
                },
                {
                  type: 'min',
                  name: 'Min'
                }
                ]
              },
              //展示数据平均值线
              markLine: {
                data: [{
                  type: 'average',
                  name: 'Avg'
                }]
              },
              //用于在每根柱子上展示数据
              label: {
                show: true,
                position: 'inside',
                color: 'black'
              },
            },
            {
              name: '通过数量',
              type: 'bar',
              data: [

              ],
              markPoint: {
                data: [{
                  type: 'max',
                  name: 'Max'
                },
                {
                  type: 'min',
                  name: 'Min'
                }
                ]
              },
              markLine: {
                data: [{
                  type: 'average',
                  name: 'Avg'
                }]
              }
            },

            ]
          }
          //注意，必须用这种语法来传递数据
          optionline.xAxis[0].data = dwmcArr
          optionline.series[0].data = sbslArr
          optionline.series[1].data = wcslArr
          mychart.setOption(optionline)
        } else {
          this.$message({
            showClose: true,
            message: '请勾选项目',
            type: 'error'
          })
        }
      }
    }
  }
}
</script>

<style  lang="scss" scoped>
.cp-tableInfo {
  background: #ffffff;
  .cp-checkNav {
    width: 400px;
    background: #f5f6fa;
    padding-left: 32px;
    height: 36px;
  }
  .cp-btns {
    background: #ffffff;
    margin: 0 0 16px 16px;
    display: flex;
    flex: 1;
    align-items: center;
    padding-right: 32px;
    justify-content: flex-start;
    margin-left: 12px;
    color: #7286e8;
    .cp-handleBtn {
      height: 26px;
      font-size: 12px;
      display: flex;
      color: #333333;
      background: #ffffff;
      border-radius: 5px;
      padding: 0 12px;
      align-items: center;
      cursor: pointer;
      border: 1px solid #abbdf7;
    }
    .cp-handleBtn:nth-child(2) {
      margin-left: 10px;
    }
    .cp-drawer {
      margin: 0 10px;
      color: #ccc;
    }

    .cp-handleUnBtn:hover {
      color: #7487e4;
      border-color: #7487e4;
    }

    .cp-handleUnBtn:active {
      color: #0c6b66;
      border-color: #0c6b66;
    }

    .cp-handleUnBtn:focus {
      color: #0c6b66;
      border-color: #0c6b66;
    }

    .cp-handleBtn {
      height: 26px;
      font-size: 12px;
      display: flex;
      color: #333333;
      background: #ffffff;
      border-radius: 5px;
      padding: 0 12px;
      align-items: center;
      cursor: pointer;
      border: 1px solid #abbdf7;
      .cp-icons {
        margin-right: 4px;

        .iconfont {
          font-size: 12px !important;
        }
      }

      .cp-texts {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        /*padding: 0 5px;*/
      }
    }

    .cp-handleUnBtn {
      height: 26px;
      font-size: 12px;
      display: flex;
      border-radius: 20px;
      border: 1px solid #7487e4;
      align-items: center;
      background-color: #fff;
      padding: 0 12px;
      cursor: pointer;
      color: #7487e4;

      .cp-icons {
        margin-right: 2px;

        .iconfont {
          font-size: 12px !important;
        }
      }
    }
  }
}
</style>

