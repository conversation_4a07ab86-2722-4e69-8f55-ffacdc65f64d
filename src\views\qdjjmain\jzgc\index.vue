<template>
  <div>
    <!-- <div class="grid-content">
      <el-row class="cost-header-btn">
        <el-button size="small" type="warning" @click="importSorce">导入资源</el-button>
        <el-button size="small" type="warning" @click="addRoot">添加节点</el-button>
      </el-row>
    </div> -->
    <div
      class="cost-table"
      style="font-size: 12px; border-block-color: #51b4f7"
    >
      <el-tabs
        v-model="activeName"
        type="border-card"
        @tab-click="tabClick"
        :style="{ height: '100%' }"
      >
        <el-tab-pane label="建筑工程" name="1">
          <table-template
            ref="multipleTable"
            :table-data1="tableData1"
            :max-level="maxLevel1"
            :is-show-cell="true"
            @contextMenu="contextMenu"
            @selectRow="selectRow"
            @editClick="editClick"
            @deleteRow="deleteRow"
          >
            <detail-template
              :qd-props="qdProps"
              :qd-max-index="qdMaxIndex"
              :worker-qd-detail="workerQdDetail"
              :show-type="showType"
              :material-info="materialDetailtForm"
              :equip-info="equipDetailtForm"
              :t-f-info="tFInfo"
              :ration-info="rationDetailInfo"
              :ration-r-c-j-info="rationDetailRCJInfo"
              :ration-resize-info="rationDetailResizeInfo"
              :rcj-max-index="rcjMaxIndex"
              @SaveDetailTemplateQd="SaveDetailTemplateQd"
              @getFeature="getFeature"
              @getDetailTFParams="getDetailTFParams"
              @handleSelectionChange="handleDetailSelectionChange"
            />
          </table-template>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import TableTemplate from './components/tableTemplate.vue'
import { searchParentNum } from '@/utils/tools.js'
import { mapState } from 'vuex'
import { apipost } from '@/utils/mapRequest'
export default {
  name: 'jzinfo',
  components: {
    TableTemplate,
  },
  computed: {
    ...mapState('storeModules/dataDic/', [
      'TChart',
      'JZInfo', // 土方土质数据
    ]),
  },
  data() {
    return {
      isShowJRG: false, // 计日工显示的数据
      parentNum: '', // 记录选中节点的父节点的数量
      isShowRelevance: false, // 关联级量是否展示
      selectMachineRow: '', // 选中材机库列表
      machineTableData: [], // 材机库列表数据
      machineTreeData: [], // 材机库树形数据
      selectmMachineVersion: '', // 选中材机库版本
      selectmMachineVersionList: [], // 材机库版本列表
      machineVisible: false, // 材机信息弹出框
      feetVisible: false, // 费用预览弹出框
      rcjMaxIndex: 1, // 点击定额，人材机数据数量
      tFInfo: [], // 点击土方信息
      AdjustcoefficientTableData: [], // 调整系数列表数据
      materialDetailtForm: {}, // 点击主材工程量信息
      equipDetailtForm: {}, // 点击设备工程量信息
      rationDetailInfo: {}, // 点击定额工程量信息
      rationDetailRCJInfo: [], // 点击定额工程量信息人材机
      rationDetailResizeInfo: [], // 点击定额工程量信息调整系数
      setAdjustcoefficientVisible: false, // 定额调整系数弹出
      setConcreteVisible: false, // 设置混凝土页面弹出
      setConcrete: '', // 设置混凝土页面弹出
      sourceActiveName: '1', // 导入自选tab选中model
      workerQdDetail: {}, // 点击清单，计算规则等
      qdMaxIndex: 1, // 点击清单，特征数据数量
      qdProps: [], // 点击清单，特征属性数据
      workerContent: '', // 清单工作内容
      calculateRule: '', // 清单计算规则
      holeList: [], // 坑类型
      cutMethodsList: [], // 挖土方式
      srcUrl: '1', // 图形地址
      showType: '清单', // 点击是列表的类型【清单、主材、设备、定额】
      tFForm: {
        // 土方信息
        tChartInfo: '', // 图纸信息
        tFormula: '', // 计算公式
        tParamTable: [], // 参数说明
        tHoleInfo: '', // 坑类型
        tCutMethodInfo: '', // 挖土方式
        tRock: true, // 岩石
        tBridlePath: '', // 马道
      },
      workUnitList: [
        'kg',
        'g',
        't',
        'kW.h',
        'km',
        'm',
        'm²',
        'm³',
        '工日',
        '套',
        '个',
        '块',
        '台',
        '组',
        '片',
        '项',
      ], // 单位数组
      rightTFVisible: false, // 自定义土方弹出
      rightQdVisible: false, // 自定义清单弹出
      rightStatistical: false, // 自定义清单弹出
      rightSelectValue: {}, // 右击列表选中值
      freeList: [], // 取费模板列表
      isShowFree: false, // 是否显示取费表选项
      qdTreeData: [], // 清单树数据
      qdTableData: [], // 清单列表数据
      qdSelectRow: '', // 选中清单
      tzTableData: [], // 清单特征列表数据
      qdActiveName: 'first', // 清单特征选中tabs
      selectDicMain: '', // 清单版本下拉框选择
      selectDicMainList: [], // 清单版本下拉框数组
      selectMaterialLibrary: '', // 物料下拉框版本
      selectMaterialList: [], // 物料版本下拉框数组
      materialTreeData: [], // 物料树列表
      selectMaterialTreeNode: {}, // 选中物料树形节点
      materialTableData: [], // 物料详情数组
      materialSelectRow: [], // 物料选中
      selectRationVersion: '', // 定额版本选中
      selectRationVersionList: [], // 定额版本数组
      RationTreeData: [], // 定额树列表
      rationSelectRow: '', // 选中定额
      rationTableData: [], // 定额详情数组
      isItemActive: false, // 清单是否可点击
      rootForm: {
        // 节点信息
        projectWorkloadId: '',
        workSort: '',
        workNumber: '',
        workName: '',
        workCode: '',
        workKillProperty: '',
        workParentId: '',
        workType: '',
        workMaterialsType: '',
        projectDataId: '',
        workRate: '',
        dataNumber: '',
        workSpecification: '',
        workUnit: '',
        workFormulaMode: '',
        workSize: '',
        workPrice: '',
        workTotalPrice: '',
        workFeeTable: '',
        workFeeTableId: '',
        workAssociated: '',
        workMenuYs: '',
        workUnitPrice: '',
      },
      tableData: [],
      addRootVisible: false, // 节点信息弹出模态框
      sourceVisible: false, // 资源信息弹出模态框
      menuVisible: false, // 节点弹出
      tableData1: [], // 建筑
      maxLevel1: 0,
      tableData2: [], // 安装
      maxLevel2: 0,
      tableData3: [], // 拆除
      maxLevel3: 0,
      tableData4: [], // 余物
      maxLevel4: 0,
      tableData5: [], // 计日工
      maxLevel5: 0,
      checked: true,
      activeName: '1', // tabs默认选中
      selectRow1: '', // 建筑选中
      selectRow2: '', // 安装选中
      selectRow3: '', // 拆除选中
      selectRow4: '', // 余物选中
      selectRow5: '', // 计日工选中
      jzFeeList: [], // 费用预览
      feeCodeObj: {}, // 费用结算结果
      StatisticalGJL: {
        projectWorkloadId: '',
        statisticalMethods: '所有',
        statisticalRange: '合并',
      },
      StatisticsEngineeringParam: {
        projectDataId: '',
        workParentId: '',
        workType: '',
      },
    }
  },
  mounted() {
    this.getProjectWork('1', 1)
  },
  methods: {
    /**
     * 费用预览*
     */
    feeView() {
      const selectRow = this.getSelectWorkerValue()

      // const data = {
      //   projectId:  this.$route.query.projectId
      // }
      // apipost('bom_project_work/calculateCost/',data).then(res => {
      //     console.log(res)
      //     this.GetTableList = res.data
      //     this.jzFeeList = res.data

      //     })

      // GcAddCost.calculateCostJz({ projectWorkloadId: selectRow.projectWorkloadId }).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       const jzResult = res.data.data
      //       GcAddCost.getJZfeeJzs(selectRow.workFeeTableId).then(res1 => {
      //         if (res1.data) {
      //           if (res1.data.code === 1001) {
      //             this.getFeeList(res1.data.data, jzResult)
      //             this.jzFeeList = res1.data.data
      //             this.feetVisible = true
      //           }
      //         }
      //       })
      //     }
      //   }
      // })
    },

    /**
     * 当计算式变化时
     * 计算合价数量
     */
    formulaModeBlur() {
      /** 修改选中项的数量、计算式、合价**/
      this.rootForm.workFormulaMode.replace(/ /g, '')
      const workFormulaMode = this.rootForm.workFormulaMode.replace(
        '*父关联',
        ''
      )
      if (
        /^[\d|\-|\+|\*|\/|\.|\(|\)]+$/.test(workFormulaMode) &&
        !/[\-|\+|\*|\/]{2,}/.test(workFormulaMode) &&
        !/\D\./.test(workFormulaMode)
      ) {
        this.rootForm.workSize = eval(workFormulaMode).toFixed(2)

        if (this.rootForm.workAssociated === '是') {
          this.rootForm.workSize =
            this.rootForm.workSize * parseFloat(this.parentNum)
        }

        if (this.rootForm.workPrice) {
          this.rootForm.workTotalPrice = isNaN(
            parseFloat(this.rootForm.workPrice) * this.rootForm.workSize
          )
            ? 0
            : parseFloat(this.rootForm.workPrice) * this.rootForm.workSize
        } else {
          this.rootForm.workPrice = '0'
          this.rootForm.workTotalPrice = '0'
        }

        /** 当选中的节点下有子节点时，设计到父关联的子节点需要修改熟练与合价**/
        if (this.rootForm.workMenuList.length > 0) {
          for (const item of this.rootForm.workMenuList) {
            if (item.workAssociated === '是') {
              item.workFormulaMode.replace(/ /g, '')
              const workFormulaModeItem = item.workFormulaMode.replace(
                '*父关联',
                ''
              )
              if (
                /^[\d|\-|\+|\*|\/|\.|\(|\)]+$/.test(workFormulaModeItem) &&
                !/[\-|\+|\*|\/]{2,}/.test(workFormulaModeItem) &&
                !/\D\./.test(workFormulaModeItem)
              ) {
                item.workSize = eval(workFormulaModeItem).toFixed(2)
                item.workSize =
                  item.workSize * parseFloat(this.rootForm.workSize)

                if (item.workPrice) {
                  item.workTotalPrice = isNaN(
                    parseFloat(item.workPrice) * item.workSize
                  )
                    ? 0
                    : parseFloat(item.workPrice) * item.workSize
                } else {
                  item.workPrice = '0'
                  item.workTotalPrice = '0'
                }
              }
            }
          }
        }
      } else {
        this.$message({
          showClose: true,
          message: '计算式不合法',
          type: 'error',
          duration: 1000,
        })
      }
    },

    /**
     * 当数量与单价变化
     * 计算合价数量
     */
    numModelBlur() {
      if (this.rootForm.workPrice) {
        this.rootForm.workTotalPrice = isNaN(
          parseFloat(this.rootForm.workPrice) * this.rootForm.workSize
        )
          ? 0
          : parseFloat(this.rootForm.workPrice) * this.rootForm.workSize
      } else {
        this.rootForm.workPrice = '0'
        this.rootForm.workTotalPrice = '0'
      }

      /** 当选中的节点下有子节点时，设计到父关联的子节点需要修改熟练与合价**/
      if (this.rootForm.workMenuList.length > 0) {
        for (const item of this.rootForm.workMenuList) {
          if (item.workAssociated === '是') {
            item.workFormulaMode.replace(/ /g, '')
            const workFormulaModeItem = item.workFormulaMode.replace(
              '*父关联',
              ''
            )
            if (
              /^[\d|\-|\+|\*|\/|\.|\(|\)]+$/.test(workFormulaModeItem) &&
              !/[\-|\+|\*|\/]{2,}/.test(workFormulaModeItem) &&
              !/\D\./.test(workFormulaModeItem)
            ) {
              item.workSize = eval(workFormulaModeItem).toFixed(2)
              item.workSize = item.workSize * parseFloat(this.rootForm.workSize)

              if (item.workPrice) {
                item.workTotalPrice = isNaN(
                  parseFloat(item.workPrice) * item.workSize
                )
                  ? 0
                  : parseFloat(item.workPrice) * item.workSize
              } else {
                item.workPrice = '0'
                item.workTotalPrice = '0'
              }
            }
          }
        }
      }
    },

    /**
     * 模板赋值*
     */
    getFeeList(result, fee) {
      this.getDeepFee(fee)
      result.forEach((item) => {
        item.jzType = this.feeCodeObj[item.bomProjectEngineeringfeeDetaiCode]
        if (item.workMenuList.length > 0) {
          this.getFeeList(item.workMenuList, fee)
        }
      })
    },

    /**
     * 将新建对象赋值*
     */
    getDeepFee(fee) {
      for (const i in fee) {
        if (typeof fee[i] === 'object') {
          this.feeCodeObj[i] = fee[i].value
          this.getDeepFee(fee[i])
        } else {
          this.feeCodeObj[i] = fee[i]
        }
      }
    },

    /**
     * 图形下拉框改变事件*
     */
    tChartChange(id) {
      const child = this.TChart.find((item) => {
        return item.key === id
      })
      const childInfo = child.children
      this.tFForm.tCutMethodInfo = '' // 赋值前清空
      this.tFForm.tHoleInfo = '' // 赋值前清空
      this.srcUrl = child.id
      this.tFForm.tFormula = childInfo.formula
      this.tFForm.tParamTable = childInfo.parameter
      this.holeList = childInfo.holeType
      this.cutMethodsList = childInfo.cutMethods
      if (childInfo.holeType.length > 0) {
        this.tFForm.tHoleInfo = childInfo.holeType[0].holeKey
      }
      if (childInfo.cutMethods.length > 0) {
        this.tFForm.tCutMethodInfo = childInfo.cutMethods[0].cutKey
      }
    },

    /**
     * 取费下拉框改变值
     * 为列表赋值【名称，非IDs】
     */
    selectWorkName(id) {
      this.rootForm.workFeeTable = this.freeList.find((item) => {
        return item.bomProjectEngineeringfeeId === id
      }).bomProjectEngineeringfeeName
    },

    /**
     * Tabs选中事件
     * @param 点击值
     */
    tabClick(row) {
      // 增加效率，防止每次切换都需要查询数据库
      let isRefresh = 1
      switch (row.name) {
        case '1':
          if (this.tableData1.length > 0) {
            isRefresh = 0
          }
          break
        case '2':
          if (this.tableData2.length > 0) {
            isRefresh = 0
          }
          break
        case '3':
          if (this.tableData3.length > 0) {
            isRefresh = 0
          }
          break
        case '4':
          if (this.tableData4.length > 0) {
            isRefresh = 0
          }
          break
        case '5':
          if (this.tableData5.length > 0) {
            isRefresh = 0
          }
          break
      }
      this.getProjectWork(row.name, isRefresh)
    },

    /**
     * 导入资源Tabs选中事件
     * @param 点击值
     */
    importSourceTabClick(row) {
      if (row.name === '2') {
        if (this.selectMaterialList.length === 0) {
          // GcAddCost.getMaterialLibrary().then(res => {
          //   if (res.data) {
          //     if (res.data.code === 1001) {
          //       this.selectMaterialList = res.data.data
          //       this.selectMaterialLibrary = this.selectMaterialList[0].id
          //       this.getMaterialType()
          //     }
          //   }
          // })
        }
      } else if (row.name === '3') {
        //   GcAddCost.getRationVersion().then(res => {
        //     if (res.data) {
        //       if (res.data.code === 1001) {
        //         this.selectRationVersionList = res.data.data
        //         this.selectRationVersion = res.data.data[0].uuid
        //         this.getRationType()
        //       }
        //     }
        //   })
      }
    },

    /**
     * 获取定额类型
     * @param 点击值
     */
    getRationType() {
      // GcAddCost.getRationType(this.selectRationVersion).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.RationTreeData = res.data.data
      //     }
      //   }
      // })
    },

    /**
     * 导入资源Tabs选中事件
     * @param 点击值
     */
    getMaterialType() {
      // GcAddCost.getMaterialType(this.selectMaterialLibrary).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.materialTreeData = res.data.data
      //     }
      //   }
      // })
    },

    /**
     * 获取工程量
     * @param 工程量类型
     * @param 是否刷新 0:否，1：是
     */
    getProjectWork(workType, isRefresh) {
      const data = {
        projectId: this.$route.query.projectId,
      }
      console.log(this.$route.query.projectId)
      // 增加效率，防止每次切换都需要查询数据库

      this.tableData1 = this.JZInfo[0].data
      this.maxLevel1 = this.JZInfo[0].data.count
      // })
    },

    /**
     * 录入资源点击事件
     */
    importSorce() {
      this.qdSelectRow = ''
      const selectCommentRow = this.getSelectWorkerValue()
      if (
        selectCommentRow.workMaterialsType !== '节点' ||
        (selectCommentRow.workMaterialsType === '节点' &&
          (selectCommentRow.workMenuList.length === 0 ||
            selectCommentRow.workMenuList[0].workMaterialsType !== '节点'))
      ) {
        if (this.activeName !== '5') {
          // GcAddCost.getDicMain().then(res => {
          //   if (res.data) {
          //     if (res.data.code === 1001) {
          //       this.selectDicMainList = res.data.data
          //       if (res.data.data) {
          //         this.selectDicMain = res.data.data[0].bomProjectInventoryDicMainId
          //         this.getInventoryDic(this.selectDicMain)
          //       }
          //     }
          //   }
          // })
          this.sourceVisible = true
        } else {
          // GcAddCost.getmachineVersion().then(res => {
          //   if (res.data) {
          //     if (res.data.code === 1001) {
          //       this.selectmMachineVersionList = res.data.data
          //       if (res.data.data.length > 0) {
          //         this.selectmMachineVersion = res.data.data[0].bomWoodMachineVersionId
          //         this.getMachineVersionDic(this.selectmMachineVersion)
          //       }
          //     }
          //   }
          // })
          this.machineVisible = true
        }
      } else {
        this.$message({
          showClose: true,
          message: 'sorry~~~,您选中的节点无新增权限',
          type: 'error',
        })
      }
    },

    /**
     * 获取材机库数据(树形图)
     * @param 清单类型ID
     */
    getMachineVersionDic(id) {
      // GcAddCost.getVersionTree(id).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.machineTreeData = res.data.data
      //     }
      //   }
      // })
    },

    /**
     * 获取清单数据(树形图)
     * @param 清单类型ID
     */
    getInventoryDic(mainID) {
      // GcAddCost.getDic({ 'inventoryParentId': mainID }).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.qdTreeData = res.data.data
      //     }
      //   }
      // })
    },

    /**
     * 获取清单详情数据(点击树形节点，列表展示)
     * @param 清单类型ID
     */
    handleNodeClick(row) {
      // GcAddCost.getInventoryDicSearch(row.bomProjectInventoryDicId).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.qdTableData = res.data.data
      //     }
      //   }
      // })
    },

    /**
     * 获取物料详情数据(点击树形节点，列表展示)
     * @param 清单类型ID
     */
    handleMaterialNodeClick(row) {
      this.materialSelectRow = ''
      this.selectMaterialTreeNode = row
      const params = {
        materialstypekey: row.bomMaterialTypeKey,
        version: this.selectMaterialLibrary,
      }
      this.materialTableData = new Array()
      // GcAddCost.getMaterial(params).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.materialTableData = res.data.data
      //     }
      //   }
      // })
    },

    /**
     * 获取定额详情数据(点击树形节点，列表展示)
     * @param 清单类型ID
     */
    handleRationNodeClick(row) {
      this.rationSelectRow = ''
      const params = {
        rtid: row.rtid,
      }
      // GcAddCost.getRation(params).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.rationTableData = res.data.data
      //     }
      //   }
      // })
    },

    /**
     * 点击人材机列表，选中列
     * @param 清单类型ID
     */
    machineCellClick(row) {
      this.selectMachineRow = row
    },

    /**
     * 点击清单列表，显示特征
     * @param 清单类型ID
     */
    qdCellClick(row) {
      this.qdSelectRow = row
      this.workerContent = row.inventoryWorkContent
      this.calculateRule = row.inventoryRule
      // GcAddCost.getInventorySearch(row.bomProjectInventoryDicId).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.tzTableData = res.data.data
      //     }
      //   }
      // })
    },

    /**
     * 点击物料列表
     * @param 清单类型ID
     */
    materialCellClick(row) {
      this.materialSelectRow = row
    },

    /**
     * 点击定额列表，显示特征
     * @param 清单类型ID
     */
    qdRationCellClick(row) {
      this.rationSelectRow = row
    },

    /**
     * 点击材机库列表，显示材机信息
     * @param 清单类型ID
     */
    handleMachineNodeClick(row) {
      // GcAddCost.getMachineList(row.bomWoodMachineVersionId).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.machineTableData = res.data.data
      //     }
      //   }
      // })
    },

    /**
     * 导入资源，保存定额【调整系数】
     */
    saveRation() {
      const _that = this
      // 父节点id
      const selectCommentRow = this.getSelectWorkerValue()
      const resizeListItems = new Array()
      this.$refs.AdjustcoefficientRef.selection.forEach((item) => {
        const resItem = {
          deResizeCondition: item.racontent,
          deResizeConsumption: item.raconsumptioncoefficient,
          deResizeMaterials: item.ramaterialcoefficient,
          deResizeN: '',
          deResizeNumber: '',
          deResizePerson: item.raartificialcoefficient,
          deResizeRigid: item.ramachinecoefficient,
          deResizeType: item.ratype,
        }
        resizeListItems.push(resItem)
      })

      const deListItems = new Array()
      const deItem = {
        deBetonType: this.setConcrete,
        deMaterialsCoef: this.rationSelectRow.materialcoefficient,
        dePersonCoef: this.rationSelectRow.artificialcoefficient,
        oldRg: this.rationSelectRow.artificialcoefficient,
        oldCl: this.rationSelectRow.materialcoefficient,
        oldJx: this.rationSelectRow.machinecoefficient,
        deRigidCoef: this.rationSelectRow.machinecoefficient,
        deMaterialsMoney: this.rationSelectRow.rmaterialcost,
        dePersonMoney: this.rationSelectRow.rartificialcost,
        deQuotoCoef: '1',
        deQuotoMoney: this.rationSelectRow.rbaseprice,
        deQuotoScope: '',
        deRigidMoney: this.rationSelectRow.rmachinecost,
        oldDeId: this.rationSelectRow.rid,
        resizeList: resizeListItems,
      }
      deListItems.push(deItem)

      const projectWorkerParam = {
        dataNumber: '1',
        projectDataId: localStorage.getItem('projectId'),
        workFormulaMode: '1',
        workName: this.rationSelectRow.rcaption,
        workNumber: this.rationSelectRow.rnumber,
        workSize: '1',
        workPrice: (
          parseFloat(deItem.deQuotoCoef) *
          (parseFloat(deItem.deMaterialsCoef) *
            parseFloat(deItem.deMaterialsMoney) +
            parseFloat(deItem.dePersonCoef) * parseFloat(deItem.dePersonMoney) +
            parseFloat(deItem.deRigidCoef) * parseFloat(deItem.deRigidMoney))
        ).toFixed(2),
        workTotalPrice: (
          parseFloat(deItem.deQuotoCoef) *
          (parseFloat(deItem.deMaterialsCoef) *
            parseFloat(deItem.deMaterialsMoney) +
            parseFloat(deItem.dePersonCoef) * parseFloat(deItem.dePersonMoney) +
            parseFloat(deItem.deRigidCoef) * parseFloat(deItem.deRigidMoney))
        ).toFixed(2),
        workMaterialsType: '定额',
        workParentId: selectCommentRow.projectWorkloadId,
        workMenuYs: parseInt(selectCommentRow.workMenuYs) + 1,
        workType: this.activeName,
        workUnit: this.rationSelectRow.runit,
        workAssociated: '否',
        deList: deListItems,
      }

      // GcAddCost.addWorkloadRation(projectWorkerParam).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.$message({
      //         showClose: true,
      //         message: '保存成功',
      //         type: 'success',
      //         duration: 2000,
      //         onClose: function() {
      //           _that.setAdjustcoefficientVisible = false
      //           _that.getProjectWork(_that.activeName, 1)
      //         }
      //       })
      //     }
      //   }
      // })
    },

    /**
     * 导入资源，保存定额【混凝土】
     */
    saveWorkerRation() {
      const _that = this
      const param = {
        rid: this.rationSelectRow.rid,
      }
      // GcAddCost.getAdjustCoefficient(param).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001 && res.data.data.length > 0) { // 存在调整系数需要设置后进行保存
      //       this.setAdjustcoefficientVisible = true
      //       this.setConcreteVisible = false
      //       this.AdjustcoefficientTableData = res.data.data
      //     } else { // 不存在调整系数时，直接保存
      //       const selectCommentRow = this.getSelectWorkerValue()

      //       const deListItems = new Array()
      //       const deItem = {
      //         deBetonType: this.setConcrete,
      //         deMaterialsCoef: this.rationSelectRow.materialcoefficient,
      //         deMaterialsMoney: this.rationSelectRow.rmaterialcost,
      //         dePersonCoef: this.rationSelectRow.artificialcoefficient,
      //         dePersonMoney: this.rationSelectRow.rartificialcost,
      //         oldRg: this.rationSelectRow.artificialcoefficient,
      //         oldCl: this.rationSelectRow.materialcoefficient,
      //         oldJx: this.rationSelectRow.machinecoefficient,
      //         deQuotoCoef: '1',
      //         deQuotoMoney: this.rationSelectRow.rbaseprice,
      //         deQuotoScope: '',
      //         deRigidCoef: this.rationSelectRow.machinecoefficient,
      //         deRigidMoney: this.rationSelectRow.rmachinecost,
      //         oldDeId: this.rationSelectRow.rid
      //       }
      //       deListItems.push(deItem)

      //       const projectWorkerParam = {
      //         dataNumber: '1',
      //         projectDataId: localStorage.getItem('projectId'),
      //         workFormulaMode: '1',
      //         workName: this.rationSelectRow.rcaption,
      //         workNumber: this.rationSelectRow.rnumber,
      //         workSize: '1',
      //         workPrice: (parseFloat(deItem.deQuotoCoef) * ((parseFloat(deItem.deMaterialsCoef) * parseFloat(deItem.deMaterialsMoney)) + (parseFloat(deItem.dePersonCoef) * parseFloat(deItem.dePersonMoney)) + (parseFloat(deItem.deRigidCoef) * parseFloat(deItem.deRigidMoney)))).toFixed(2),
      //         workTotalPrice: (parseFloat(deItem.deQuotoCoef) * ((parseFloat(deItem.deMaterialsCoef) * parseFloat(deItem.deMaterialsMoney)) + (parseFloat(deItem.dePersonCoef) * parseFloat(deItem.dePersonMoney)) + (parseFloat(deItem.deRigidCoef) * parseFloat(deItem.deRigidMoney)))).toFixed(2),
      //         workMaterialsType: '定额',
      //         workUnit: this.rationSelectRow.runit,
      //         workParentId: selectCommentRow.projectWorkloadId,
      //         workMenuYs: parseInt(selectCommentRow.workMenuYs) + 1,
      //         workType: this.activeName,
      //         workAssociated: '否',
      //         deList: deListItems
      //       }

      //       GcAddCost.addWorkloadRation(projectWorkerParam).then(res => {
      //         if (res.data) {
      //           if (res.data.code === 1001) {
      //             this.$message({
      //               showClose: true,
      //               message: '保存成功',
      //               type: 'success',
      //               duration: 2000,
      //               onClose: function() {
      //                 _that.setConcreteVisible = false
      //                 _that.getProjectWork(_that.activeName, 1)
      //               }
      //             })
      //           }
      //         }
      //       })
      //     }
      //   }
      // })
    },

    /**
     * 选中调整系数，改变定额人材机系数
     */
    handleDetailSelectionChange(newPeopleRate, newMaterialRate, newRigidRate) {
      this.rationDetailInfo.dePersonCoef = newPeopleRate
      this.rationDetailInfo.deMaterialsCoef = newMaterialRate
      this.rationDetailInfo.deRigidCoef = newRigidRate
    },

    /**
     * 插入定额
     */
    insertRation() {
      const _that = this
      if (this.rationSelectRow !== '') {
        const param = {
          rid: this.rationSelectRow.rid,
        }
        //   GcAddCost.getRationMmmRelation(param).then(res => {
        //     if (res.data) {
        //       if (res.data.code === 1001) {
        //         const rationData = res.data.data
        //         let isflag = 0 // 弹框类型，0：定额系数调整；1：混凝土设置；2：混凝土+定额系数调整
        //         for (let i = 0; i < rationData.length; i++) {
        //           if (rationData[i].rcjtype === '未计价材料' && rationData[i].rcjname.indexOf('现浇混凝土') > -1) {
        //             isflag = 1
        //             continue
        //           }
        //         }
        //         switch (isflag) {
        //           case 0:
        //             this.saveWorkerRation()
        //             break
        //           case 1:
        //             this.setConcreteVisible = true
        //             break
        //         }
        //         this.sourceVisible = false
        //       }
        //     }
        //   })
      } else {
        this.$message({
          showClose: true,
          message: 'sorry~~~,请选择要添加的定额',
          type: 'error',
        })
      }
    },

    /**
     * 插入物料
     */
    insertMaterial() {
      const _that = this
      // 父节点id
      const selectCommentRow = this.getSelectWorkerValue()
      this.rootForm.workParentId = selectCommentRow.projectWorkloadId
      // 判断主材和设备
      if (this.materialSelectRow.ismainText === '主材') {
        const zcList = {
          materialId: this.materialSelectRow.materialsprojectid,
          coding: this.materialSelectRow.materialcodeerp,
          zcName: this.materialSelectRow.materialname,
          zcNounicPrice: this.materialSelectRow.price,
          zcSupplier: this.materialSelectRow.isdonorText,
          zcUnicPrice: this.materialSelectRow.taxprice,
          zcWeight: this.materialSelectRow.weight,
          zcCarriageTypep: this.selectMaterialTreeNode.bomMaterialTypeName,
        }
        const ZcArray = new Array()
        ZcArray.push(zcList)
        const projectWorkerParam = {
          dataNumber: '1',
          projectDataId: localStorage.getItem('projectId'),
          workFormulaMode: '1',
          workName: this.materialSelectRow.materialname,
          workSpecification: this.materialSelectRow.spec,
          workUnit: this.materialSelectRow.erpunit,
          workTotalPrice: parseFloat(zcList.zcNounicPrice),
          workSize: '1',
          workPrice: zcList.zcNounicPrice,
          workMaterialsType: '主材',
          workParentId: this.rootForm.workParentId,
          workMenuYs: parseInt(selectCommentRow.workMenuYs) + 1,
          workType: this.activeName,
          workAssociated: '否',
          zcList: ZcArray,
        }
        // 请求主材
        //   GcAddCost.addWorkAndZc(projectWorkerParam).then(res => {
        //     if (res.data.code === 1001) {
        //       this.$message({
        //         showClose: true,
        //         message: '添加节点成功',
        //         type: 'success',
        //         duration: 2000,
        //         onClose: function() {
        //           _that.getProjectWork(_that.activeName, 1)
        //         }
        //       })
        //       this.sourceVisible = false
        //     }
        //   })
      } else if (this.materialSelectRow.ismainText === '设备') {
        const SbList = {
          materialId: this.materialSelectRow.materialsprojectid,
          sbName: this.materialSelectRow.materialname,
          sbNounicPrice: this.materialSelectRow.price,
          sbSupplier: this.materialSelectRow.isdonorText,
          sbUnicPrice: this.materialSelectRow.taxprice,
          sbWeight: this.materialSelectRow.weight,
          sbCarriageTypep: this.selectMaterialTreeNode.bomMaterialTypeName,
        }
        const SbArray = new Array()
        SbArray.push(SbList)
        const projectWorkerParam = {
          dataNumber: '1',
          projectDataId: localStorage.getItem('projectId'),
          workFormulaMode: '1',
          workName: this.materialSelectRow.materialname,
          workNumber: this.materialSelectRow.materialcodeerp,
          workSpecification: this.materialSelectRow.spec,
          workTotalPrice: parseFloat(SbList.sbNounicPrice),
          workUnit: this.materialSelectRow.erpunit,
          workSize: '1',
          workPrice: SbList.sbNounicPrice,
          workMaterialsType: '设备',
          workParentId: this.rootForm.workParentId,
          workMenuYs: parseInt(selectCommentRow.workMenuYs) + 1,
          workType: this.activeName,
          workAssociated: '否',
          sbList: SbArray,
        }
        // 请求设备
        //   GcAddCost.addWorkAndSb(projectWorkerParam).then(res => {
        //     if (res.data.code === 1001) {
        //       this.$message({
        //         showClose: true,
        //         message: '插入设备节点成功',
        //         type: 'success',
        //         duration: 2000,
        //         onClose: function() {
        //           _that.getProjectWork(_that.activeName, 1)
        //         }
        //       })
        //       this.sourceVisible = false
        //     }
        //   })
      }
    },

    /**
     * 插入清单
     */
    insertQd() {
      const _that = this
      if (this.qdSelectRow !== '') {
        const selectCommentRow = this.getSelectWorkerValue()

        if (selectCommentRow.workMaterialsType === '节点') {
          this.rootForm.workName = this.qdSelectRow.inventoryName
          this.rootForm.workCode = this.qdSelectRow.inventoryNum
          this.rootForm.workUnit = this.qdSelectRow.inventoryUnit
          this.rootForm.workNumber =
            selectCommentRow.workNumber + this.qdSelectRow.inventoryNum
          let total = 1
          selectCommentRow.workMenuList.forEach((item) => {
            if (item.workCode === this.qdSelectRow.inventoryNum) {
              total++
            }
          })
          if (total.toString().split('').length === 1) {
            total = '0' + total
          }
          this.rootForm.workNumber = this.rootForm.workNumber + total
          this.rootForm.workContent = this.workerContent
          this.rootForm.workType = this.activeName
          this.rootForm.workSize = '1'
          this.rootForm.workFormulaMode = '1'
          this.rootForm.workMaterialsType = '清单'
          this.rootForm.workParentId = selectCommentRow.projectWorkloadId
          this.rootForm.projectDataId = localStorage.getItem('projectId')
          this.rootForm.workMenuYs = parseInt(selectCommentRow.workMenuYs) + 1
          this.rootForm.workFormulaRule = this.qdSelectRow.calculateRule
          const array = new Array()
          this.tzTableData.forEach((item) => {
            const loadList = {
              listTzz: item.bomProjectInventoryId,
              listName: item.inventoryTraitContent,
            }
            array.push(loadList)
          })
          this.rootForm.workloadList = array

          // GcAddCost.addWorkAndList(this.rootForm).then(res => {
          //   if (res.data) {
          //     if (res.data.code === 1001) {
          //       this.$message({
          //         showClose: true,
          //         message: '清单插入成功',
          //         type: 'success',
          //         duration: 2000,
          //         onClose: function() {
          //           _that.getProjectWork(_that.activeName, 1)
          //         }
          //       })
          //       this.sourceVisible = false
          //     }
          //   }
          // })
        } else {
          this.$message({
            showClose: true,
            message: 'sorry~~~,该节点没有权限插入清单',
            type: 'error',
          })
        }
      } else {
        this.$message({
          showClose: true,
          message: 'sorry~~~,请选择要添加的清单',
          type: 'error',
        })
      }
    },

    /**
     * 插入人材机
     */
    insertMachine() {
      const _that = this
      const selectCommentRow = this.getSelectWorkerValue()
      this.rootForm.workName = this.selectMachineRow.bomWoodMachineName
      this.rootForm.workPrice = this.selectMachineRow.bomWoodMachinePrice
      this.rootForm.workUnit = this.selectMachineRow.bomWoodMachineUnit
      this.rootForm.workSize = '1'
      this.rootForm.workType = this.activeName
      this.rootForm.workTotalPrice = this.selectMachineRow.bomWoodMachinePrice
      this.rootForm.workMaterialsType = this.selectMachineRow.bomWoodMachineType
      this.rootForm.workNumber = this.selectMachineRow.bomWoodMachineCode
      this.rootForm.workParentId = selectCommentRow.projectWorkloadId
      this.rootForm.projectDataId = localStorage.getItem('projectId')
      this.rootForm.workMenuYs = parseInt(selectCommentRow.workMenuYs) + 1
      this.rootForm.workAssociated = '否'

      // GcAddCost.addProjectWork(this.rootForm).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.$message({
      //         showClose: true,
      //         message: '添加节点成功',
      //         type: 'success',
      //         duration: 2000,
      //         onClose: function() {
      //           _that.getProjectWork(_that.activeName, 1)
      //         }
      //       })
      //       this.sourceVisible = false
      //     }
      //   }
      // })
    },

    /**
     * 编辑列表事件
     * @param row
     */
    editClick(row) {
      // 判断是否展示取费表
      if (row.workMenuYs === '1' || this.activeName === '5') {
        this.isShowFree = false
      } else {
        if (
          row.workMaterialsType === '清单' ||
          row.workMaterialsType === '节点'
        ) {
          this.isShowFree = true
        } else {
          this.isShowFree = false
        }
      }

      // 判断是否展示关联级量
      if (
        row.workMaterialsType === '清单' ||
        row.workMaterialsType === '节点' ||
        this.activeName === '5'
      ) {
        this.isShowRelevance = false
      } else {
        this.isShowRelevance = true
      }

      if (this.activeName === '5') {
        this.isShowJRG = true
      } else {
        this.isShowJRG = false
      }

      if (this.isShowFree) {
        switch (this.activeName) {
          case '1':
            // 获取建筑取费
            //   GcAddCost.getJZFee().then(res => {
            //     if (res.data) {
            //       if (res.data.data) {
            //         this.freeList = res.data.data
            //       }
            //     }
            //   })
            // parentNum
            break
          case '2':
            // 获取安装取费
            //   GcAddCost.getAZFee().then(res => {
            //     if (res.data) {
            //       if (res.data.data) {
            //         this.freeList = res.data.data
            //       }
            //     }
            //   })
            break
          case '3':
          case '4':
            // 获取拆除取费【拆除和余物相同】
            //   GcAddCost.getCCFee().then(res => {
            //     if (res.data) {
            //       if (res.data.data) {
            //         this.freeList = res.data.data
            //       }
            //     }
            //   })
            break
          case '5':
            // 获取计日工取费【建筑+安装】
            //   GcAddCost.getJZAZFee().then(res => {
            //     if (res.data) {
            //       if (res.data.data) {
            //         this.freeList = res.data.data
            //       }
            //     }
            //   })
            break
        }
      }

      // 不加载取费表时需要查询父数量
      switch (this.activeName) {
        case '1':
          this.parentNum = searchParentNum(this.tableData1, row.workParentId)
          break
        case '2':
          this.parentNum = searchParentNum(this.tableData2, row.workParentId)
          break
        case '3':
          this.parentNum = searchParentNum(this.tableData3, row.workParentId)
          break
        case '4':
          this.parentNum = searchParentNum(this.tableData4, row.workParentId)
          break
      }

      this.addRootVisible = true
      this.rootForm = row
    },

    /**
     * 选择关联关系
     */
    selectWorkRelevance(row) {
      if (row === '否') {
        this.rootForm.workFormulaMode = this.rootForm.workFormulaMode.replace(
          '*父关联',
          ''
        )
      } else if (row === '是') {
        this.rootForm.workFormulaMode =
          this.rootForm.workFormulaMode + '*父关联'
      }
    },

    /**
     * 编辑保存事件
     */
    editSave() {
      // GcAddCost.editProjectWork(this.rootForm).then(res => {
      //   const _that = this
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.$message({
      //         showClose: true,
      //         message: '保存成功',
      //         type: 'success',
      //         duration: 1000,
      //         onClose: function() {
      //           _that.addRootVisible = false
      //         }
      //       })
      //     }
      //   }
      // })
    },

    /**
     * 删除选中数据事件
     * @param row
     */
    deleteRow(row) {
      const _that = this
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'confirmBtn',
        cancelButtonClass: 'cancleBtn',
        type: 'warning',
      }).then(() => {
        //   GcAddCost.delProjectWork(row.projectWorkloadId).then(res => {
        //     if (res.data) {
        //       if (res.data.code === 1001) {
        //         this.$message({
        //           showClose: true,
        //           message: '删除成功',
        //           type: 'success',
        //           duration: 2000,
        //           onClose: function() {
        //             _that.getProjectWork(_that.activeName, 1)
        //           }
        //         })
        //       }
        //     }
        //   })
      })
    },

    /**
     * 右击列表事件
     * @param row
     * @param level
     */
    contextMenu(row, level, event) {
      this.rightSelectValue = row
      if (
        row.workMaterialsType === '节点' &&
        (row.workMenuList.length === 0 ||
          row.workMenuList[0].workMaterialsType !== '节点')
      ) {
        this.menuVisible = true
        const menu = document.querySelector('#menu')
        menu.style.display = 'block'
        menu.style.left = event.clientX - 0 + 'px'
        menu.style.top = event.clientY + 0 + 'px'
        this.isItemActive = true
      } else if (row.workMaterialsType === '清单') {
        this.menuVisible = true
        this.isItemActive = false
        const menu1 = document.querySelector('#menu')
        menu1.style.display = 'block'
        menu1.style.left = event.clientX - 0 + 'px'
        menu1.style.top = event.clientY + 0 + 'px'
      }
    },

    /**
     * 添加节点事件
     */
    addRoot() {
      const _that = this
      const selectCommentRow = this.getSelectWorkerValue()
      if (selectCommentRow.workMaterialsType === '节点') {
        this.rootForm.workName = '新建节点'
        this.rootForm.workType = this.activeName
        this.rootForm.workMaterialsType = '节点'
        this.rootForm.workParentId = selectCommentRow.projectWorkloadId
        this.rootForm.projectDataId = localStorage.getItem('projectId')
        this.rootForm.workMenuYs = parseInt(selectCommentRow.workMenuYs) + 1

        //   GcAddCost.addProjectWork(this.rootForm).then(res => {
        //     if (res.data) {
        //       if (res.data.code === 1001) {
        //         this.$message({
        //           showClose: true,
        //           message: '添加节点成功',
        //           type: 'success',
        //           duration: 2000,
        //           onClose: function() {
        //             _that.getProjectWork(_that.activeName, 1)
        //           }
        //         })
        //       }
        //     }
        //   })
      } else {
        if (selectCommentRow.workMaterialsType) {
          this.$message({
            showClose: true,
            message: 'sorry~~~,您选中的节点无新增权限',
            type: 'error',
          })
        } else {
          this.$message({
            showClose: true,
            message: 'sorry~~~,您未选中任何数据',
            type: 'error',
          })
        }
      }
    },

    /**
     * 获取清单列表
     */
    getFeature(projectWorkloadId) {
      // GcAddCost.getWorkList(projectWorkloadId).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001 && res.data.data.length > 0) {
      //       this.qdProps = res.data.data
      //       this.qdMaxIndex = res.data.data.length
      //     }
      //   }
      // })
    },
    // 统计工程量
    Engineering() {
      this.StatisticsEngineeringParam.projectDataId =
        localStorage.getItem('projectId')
      // GcAddCost.Engineering(this.StatisticsEngineeringParam).then(res => {
      //   console.log(res.data)
      //   if (res.data.code === 1001) {
      //     this.$message({
      //       showClose: true,
      //       message: '统计工程量成功',
      //       type: 'success',
      //       duration: 2000
      //     })
      //     this.getProjectWork('1', 1)
      //   }
      // })
    },
    /**
     * 列表选中事件
     * @param row
     */
    selectRow(row) {
      // 点击获取一行信息传给统计统计工程量按钮时参数
      this.StatisticsEngineeringParam.projectDataId = row.projectDataId
      this.StatisticsEngineeringParam.workParentId = row.workParentId
      this.StatisticsEngineeringParam.workType = row.workType
      this.StatisticalGJL.projectWorkloadId = row.projectWorkloadId
      this.menuVisible = false // 将右击弹框取消
      switch (this.activeName) {
        case '1':
          this.selectRow1 = row
          break
        case '2':
          this.selectRow2 = row
          break
        case '3':
          this.selectRow3 = row
          break
        case '4':
          this.selectRow4 = row
          break
        case '5':
          this.selectRow5 = row
          break
      }
      this.showType = row.workMaterialsType
      switch (row.workMaterialsType) {
        case '清单':
          this.workerQdDetail = {
            workerContent: row.workContent,
            calculateRule: row.workFormulaRule,
            projectWorkloadId: row.projectWorkloadId,
          }
          // GcAddCost.getWorkList(row.projectWorkloadId).then(res => {
          //   if (res.data) {
          //     if (res.data.code === 1001 && res.data.data.length > 0) {
          //       this.qdProps = res.data.data
          //       this.qdMaxIndex = res.data.data.length
          //     } else {
          //       this.qdProps = new Array()
          //       this.qdProps.push({
          //         listName: '',
          //         listValue: ''
          //       })
          //     }
          //   }
          // })
          break
        case '主材':
          // GcAddCost.getWorkAndZc(row.projectWorkloadId).then(res => {
          //   if (res.data) {
          //     if (res.data.code === 1001) {
          //       if (res.data.data !== null) {
          //         this.materialDetailtForm = res.data.data
          //       } else {
          //         this.materialDetailtForm = {
          //           coding: '',
          //           materialId: '',
          //           projectDataId: '',
          //           projectType: '',
          //           projectWorkloadId: row.projectWorkloadId,
          //           projectWorkloadZcId: '',
          //           quantity: '',
          //           spec: '',
          //           unit: '',
          //           zcCarriageTypep: '1',
          //           zcCustody: '是',
          //           zcDelivery: '是',
          //           zcLoss: '0',
          //           zcManager: '',
          //           zcName: '',
          //           zcNounicPrice: '',
          //           zcPackCoe: '',
          //           zcProEst: '是',
          //           zcSiteWay: '',
          //           zcStartDate: '',
          //           zcSupplier: '甲供',
          //           zcType: '',
          //           zcUnicPrice: '',
          //           zcUpdateDate: '',
          //           zcUpload: '',
          //           zcUserId: '',
          //           zcUserUnitId: '',
          //           zcWeight: '0'
          //         }
          //       }
          //     }
          //   }
          // })
          break
        case '设备':
          // GcAddCost.getWorkAndSb(row.projectWorkloadId).then(res => {
          //   if (res.data) {
          //     if (res.data.code === 1001) {
          //       if (res.data.data.length > 0) {
          //         this.equipDetailtForm = res.data.data[0]
          //       } else {
          //         this.equipDetailtForm = {
          //           coding: '',
          //           materialId: '',
          //           projectDataId: '',
          //           projectWorkloadId: '',
          //           projectWorkloadSbId: row.projectWorkloadId,
          //           quantity: '',
          //           sbCarriageRate: '0',
          //           sbCarriageTypep: '',
          //           sbDelivery: '是',
          //           sbName: '',
          //           sbNounicPrice: '',
          //           sbProEst: '否',
          //           sbStartDate: '2',
          //           sbSupplier: '甲供',
          //           sbType: '',
          //           sbUnicPrice: '',
          //           sbUpdateDate: '',
          //           sbUserId: '',
          //           sbUserUnitId: '',
          //           spec: '',
          //           unit: '',
          //           unitweight: ''
          //         }
          //       }
          //     }
          //   }
          // })
          break
        case '土方':
          this.getDetailTFParams(row.projectWorkloadId)
          break
        case '定额':
          this.getDetailRationParams(row.projectWorkloadId)
          break
      }
    },

    /**
     * 获取定额详情
     */
    getDetailRationParams(projectWorkloadId) {
      // GcAddCost.getWorkloadDe(projectWorkloadId).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.rationDetailInfo = res.data.data.BomProjectWorkloadDe[0]
      //       console.log(res)
      //       this.rationDetailRCJInfo = res.data.data.BomProjectWorkloadDeRcj
      //       this.rcjMaxIndex = res.data.data.BomProjectWorkloadDeRcj.length
      //       this.rationDetailResizeInfo = res.data.data.BomProjectWorkloadDeResize
      //       this.rationResizeBaseData = {
      //         peoplerate: this.rationDetailInfo.dePersonCoef,
      //         materialrate: this.rationDetailInfo.deMaterialsCoef,
      //         rigidrate: this.rationDetailInfo.deRigidCoef
      //       }
      //     }
      //   }
      // })
    },

    /**
     * 获取土方详情
     */
    getDetailTFParams(projectWorkloadId) {
      //     GcAddCost.getWorkloadTf(projectWorkloadId).then(res => {
      //       if (res.data) {
      //         if (res.data.code === 1001) {
      //           this.tFInfo = res.data.data
      //         }
      //       }
      //     })
    },

    /**
     * 保存点击工程量清单，底部工作内容和计算规则
     */
    SaveDetailTemplateQd() {
      const selectCommentRow = this.getSelectWorkerValue()

      selectCommentRow.workContent = this.workerQdDetail.workerContent
      selectCommentRow.workFormulaRule = this.workerQdDetail.calculateRule
      // GcAddCost.editProjectWork(selectCommentRow).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.$message({
      //         showClose: true,
      //         message: '保存成功',
      //         type: 'success',
      //         duration: 2000
      //       })
      //     }
      //   }
      // })
    },

    /**
     * 右击自定义添加
     * @param dataType
     */
    rightAddRoot(dataType) {
      this.menuVisible = false // 将右击弹框取消
      const _that = this
      // 清单与土方需要配置数据弹出页面，其他直接加载到节点中
      switch (dataType) {
        case '清单':
          this.rightQdVisible = true
          break
        case '土方':
          this.rightTFVisible = true
          break
        default:
          this.rootForm.workName = '自定义' + dataType
          this.rootForm.workType = this.activeName
          this.rootForm.workMaterialsType = dataType
          this.rootForm.workParentId = this.rightSelectValue.projectWorkloadId
          this.rootForm.projectDataId = localStorage.getItem('projectId')
          this.rootForm.workMenuYs =
            parseInt(this.rightSelectValue.workMenuYs) + 1

          // GcAddCost.addProjectWork(this.rootForm).then(res => {
          //   if (res.data) {
          //     if (res.data.code === 1001) {
          //       this.$message({
          //         showClose: true,
          //         message: '添加成功',
          //         type: 'success',
          //         duration: 2000,
          //         onClose: function() {
          //           _that.getProjectWork(_that.activeName, 1)
          //         }
          //       })
          //     }
          //   }
          // })
          break
      }
    },

    /**
     * 自定义清单保存
     */
    rightQDSave() {
      const _that = this
      this.rootForm.workType = this.activeName
      this.rootForm.workMaterialsType = '清单'
      this.rootForm.workFormulaMode = '1'
      this.rootForm.workSize = '1'
      this.rootForm.workAssociated = '否'
      this.rootForm.workParentId = this.rightSelectValue.projectWorkloadId
      this.rootForm.projectDataId = localStorage.getItem('projectId')
      this.rootForm.workMenuYs = parseInt(this.rightSelectValue.workMenuYs) + 1

      // GcAddCost.addProjectWork(this.rootForm).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.$message({
      //         showClose: true,
      //         message: '添加成功',
      //         type: 'success',
      //         duration: 2000,
      //         onClose: function() {
      //           _that.getProjectWork(_that.activeName, 1)
      //         }
      //       })
      //       this.rightQdVisible = false
      //     }
      //   }
      // })
    },

    /**
     * 自定义土方保存
     */
    rightTFSave() {
      const _that = this
      this.rootForm.workName = '自定义土方'
      this.rootForm.workType = this.activeName
      this.rootForm.workMaterialsType = '土方'
      this.rootForm.workParentId = this.rightSelectValue.projectWorkloadId
      this.rootForm.projectDataId = localStorage.getItem('projectId')
      this.rootForm.workMenuYs = parseInt(this.rightSelectValue.workMenuYs) + 1
      this.rootForm.workFormulaMode = '1'
      this.rootForm.workSize = '1'
      this.rootForm.workAssociated = '否'
      this.rootForm.workloadTf = {
        tfGraph: this.tFForm.tChartInfo,
        tfFormula: this.tFForm.tFormula,
        tfPitType: this.tFForm.tHoleInfo,
        tfSize: '1',
        tfWtMain: this.tFForm.tCutMethodInfo,
        tfMd: this.tFForm.tBridlePath,
        tfTock: this.tFForm.tRock,
      }
      this.rootForm.tfParams = this.tFForm.tParamTable

      // GcAddCost.addTFProjectWork(this.rootForm).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.$message({
      //         showClose: true,
      //         message: '添加成功',
      //         type: 'success',
      //         duration: 2000,
      //         onClose: function() {
      //           _that.rightTFVisible = false
      //           _that.getProjectWork(_that.activeName, 1)
      //         }
      //       })
      //     }
      //   }
      // })
    },

    /**
     * 获取开发Tabs中选中的数据
     * @returns {{}}
     */
    getSelectWorkerValue() {
      let selectCommentRow = {}
      switch (this.activeName) {
        case '1':
          selectCommentRow = this.selectRow1
          break
        case '2':
          selectCommentRow = this.selectRow2
          break
        case '3':
          selectCommentRow = this.selectRow3
          break
        case '4':
          selectCommentRow = this.selectRow4
          break
        case '5':
          selectCommentRow = this.selectRow5
          break
      }
      return selectCommentRow
    },
    // 统计钢筋量按钮
    Statistical() {
      this.rightStatistical = true
    },
    // 取消统计钢筋量弹框
    Statisticaldialog() {
      this.rightStatistical = false
    },
    // 提交统计钢筋量弹框
    StatisticalBtn() {
      if (this.StatisticalGJL.projectWorkloadId === '') {
        this.$message({
          message: '请选择一条工程量',
          type: 'warning',
        })
      } else {
        //   GcAddCost.StatisticalGJL(this.StatisticalGJL).then(res => {
        //     this.getProjectWork('1', 1)
        //   })
      }
      this.rightStatistical = false
    },
  },
}
</script>
<style lang="scss">
::v-deep {
  .el-tabs {
    height: 100%;
  }
}
/*工程量信息弹框样式*/
.el-dialog-new {
  .el-dialog__body {
    padding: 20px 20px 0px 20px;

    .el-form-item {
      margin-bottom: 12px;
    }
  }

  .el-form-item {
    margin-right: 10px;
  }

  .el-dialog__footer {
    padding: 0px 20px 20px;
  }
}

/*资源弹框样式*/
.el-dialog-source {
  .el-dialog__body {
    padding: 0px;
  }
}

/*资源信息输入框样式*/
.el-input-new {
  .el-input__inner {
    height: 28px;
    line-height: 28px;
  }

  .el-input__icon {
    line-height: 28px;
  }
}

.el-detail-form {
  .el-form-item__label {
    text-align: center;
    background-color: #51b4f7;
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
    color: white;
    line-height: 28px;
    margin-right: 10px;
  }

  .el-form-item__content {
    line-height: 28px;
  }
}
</style>
<style lang="scss" scoped>
.menu_item {
  pointer-events: none;
  color: #8b929b;
}

.menu {
  height: 227px;
  width: 195px;
  position: absolute;
  border-radius: 8px;
  border: 1px solid #bbbbbb;
  background-color: #303133;
  color: white;

  li {
    list-style: none;
    padding: 8px 20px;
    font-size: 14px;

    &:hover {
      color: #51b4f7;
      cursor: pointer;
    }
  }
}

.grid-content {
  width: 98%;
  margin-left: 20px;
  margin-top: 20px;
  background: white;
  height: 60px;
  box-shadow: 0 5px 23px #cccccc;
}

.cost-header-btn {
  padding-top: 15px;
  margin-left: 20px;

  a {
    margin-left: 10px;

    &:hover {
      color: red;
    }
  }
}

::v-deep .cost-table {
  height: calc(100vh - 6rem);
  .el-tabs {
    height: 100%;
  }
}
.Fy-addFy-add {
  width: 40%;
  height: auto;
  position: absolute;
  left: 30%;
  top: 18%;
  border: 1px solid #ccc;
  background: white;
  z-index: 9999;
}

.Add-header {
  width: 100%;
  height: 60px;
  background: #51b4f7;
  display: flex;
  justify-content: space-between;

  h3 {
    color: white;
    line-height: 60px;
    margin-left: 2%;
  }

  p {
    color: white;
    line-height: 60px;
    margin-right: 2%;
    font-weight: bolder;
  }
}

.el-dialog-p {
  font-size: 18px;
  line-height: 30px;
  letter-spacing: 1px;
}

.el-input-marng {
  margin-left: 10px;
}
</style>
