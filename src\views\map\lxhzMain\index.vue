<template>
  <div>
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div class="pro-leftTitle">
            <div
              v-if="showBackAdds"
              class="maps-zhedNav"
              @click="backCurrentDom"
            >
              <img
                class="mapites-backImg"
                :src="require('@/assets/' + 'map/settingImg/backAdds.png')"
                alt=""
              />
            </div>
            <span />
            拉线
            <div
              v-if="!showBackAdds"
              class="maps-zhedNav"
              @click="showEveryItemSet"
            >
              <img
                v-show="!isFoldArea"
                class="mapites-zhed"
                :src="require('@/assets/' + 'map/settingImg/zhedie.png')"
                alt=""
              />
              <img
                v-show="isFoldArea"
                class="mapites-zhed"
                :src="require('@/assets/' + 'map/settingImg/zkzhedie.png')"
                alt=""
              />
            </div>
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img
            class="settingImg"
            :src="require('@/assets/' + 'map/settingImg/useSetting.png')"
            alt=""
          />
          <p>启用</p>
        </div>
      </div>
      <div v-show="isFoldArea" class="pro-addForm">
        <div class="map-showNav">基本信息</div>
        <van-row>
          <van-field
            v-model="stay.mark"
            label="编号"
            placeholder="请输入拉线编号"
          />
        </van-row>
        <div class="map-showNav">选型信息</div>
        <van-row>
          <!--拉线状态-->
          <van-field
            readonly
            clickable
            :value="stay.state"
            label="状态"
            placeholder="请选择状态"
            @click="settingObj.stay.state = true"
          />
          <van-popup v-model="settingObj.stay.state" round position="bottom">
            <van-picker
              show-toolbar
              title="状态"
              value-key="key"
              :columns="mainLineState"
              @confirm="onConfirmLxSel(0, $event)"
              @cancel="settingObj.stay.state = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--拉线电压等级-->
          <van-field
            readonly
            clickable
            :value="stay.voltage"
            label="电压等级"
            placeholder="请选择电压等级"
            @click="settingObj.stay.voltage = true"
          />
          <van-popup v-model="settingObj.stay.voltage" round position="bottom">
            <van-picker
              show-toolbar
              title="电压等级"
              value-key="key"
              :columns="voltage"
              @confirm="onConfirmLxSel(1, $event)"
              @cancel="settingObj.stay.voltage = false"
            />
          </van-popup>
        </van-row>
        <!--拉线类型-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="stay.stayType"
            label="拉线类型"
            placeholder="请选择拉线类型"
            @click="settingObj.stay.stayType = true"
          />
          <van-popup v-model="settingObj.stay.stayType" round position="bottom">
            <van-picker
              show-toolbar
              title="拉线类型"
              value-key="key"
              :columns="stayType"
              @confirm="onConfirmLxSel(2, $event)"
              @cancel="settingObj.stay.stayType = false"
            />
          </van-popup>
        </van-row>
        <!--拉线方案-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="stay.stayPlan"
            label="拉线方案"
            placeholder="请选择拉线方案"
            @click="settingObj.stay.stayPlan = true"
          />
          <van-popup v-model="settingObj.stay.stayPlan" round position="bottom">
            <van-picker
              show-toolbar
              title="拉线方案"
              value-key="moduleName"
              :columns="stayPlan"
              @confirm="onConfirmLxSel(3, $event)"
              @cancel="settingObj.stay.stayPlan = false"
            />
          </van-popup>
        </van-row>
        <div v-if="isShowNav" class="map-showNav">其他信息</div>
        <van-row v-if="isShowNav">
          <!--经度-->
          <van-field v-model="lngtitude" label="经度" disabled />
        </van-row>
        <van-row v-if="isShowNav">
          <!--纬度-->
          <van-field v-model="lattitude" label="纬度" disabled />
        </van-row>
        <van-row v-if="isShowNav">
          <!--高程-->
          <van-field v-model="highNum" label="高程" disabled />
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { apiget,apipost } from "@/utils/mapRequest";
export default {
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false,
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的id
    editData: {
      type: Object,
      defaults: () => {},
    },
    remodeState: {
      type: Boolean,
      defaults: false,
    },
  },
  watch: {
    // 监听改前改后状态
    remodeState: {
      handler(newVal) {
        if (newVal) {
          // 改后不显示拆除的数据
          this.mainLineState = this.gtState.slice(0, 3);
          this.stay.state = "新建";
        } else {
          // 改前不显示新建的数据
          this.mainLineState = this.gtState.slice(1);
          this.stay.state = "原有";
        }
      },
      deep: true,
      immediate: true,
    },
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal;
      },
      deep: true,
      immediate: true,
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal;
        if (data.moudleType === "LXHZ") {
          this.lngtitude = data.longitude;
          this.lattitude = data.latitude;
          this.highNum = data.high;
          // 查询拉线绘制
          let euqipTypeLXHZ;
          for (const j in this.settingObj.stayType) {
            if (data.legendTypeKey === this.settingObj.stayType[j].value) {
              euqipTypeLXHZ = this.settingObj.stayType[j].type;
            }
          }
          this.getAwaitTowerOrLineType(4, euqipTypeLXHZ);
          this.stay.imgList = [];
          this.stay.audioList = [];
          this.stay.state = data.state;
          this.stay.stayType = data.legendTypeKey;
          this.stay.voltage = data.voltage;
          this.stay.stayPlanId = data.lxProgram;
          this.stay.stayPlan = data.lxProgramSpec;
          this.stay.mark = data.mark; // 编号
          this.stay.message = data.note;
          for (const k in data.imgList) {
            const objs = {
              url: data.imgList[k].path,
              isImage: true,
              isSaveReport: data.imgList[k].isSaveReport,
            };
            this.stay.imgList.push(objs);
          }
          for (const s in data.voiceList) {
            const objs = {
              content: data.voiceList[s].path,
            };
            this.stay.audioList.push(objs);
          }
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      isFoldArea: false,
      userId: "",
      lngtitude: "", // 每个点绑定的经度
      lattitude: "", // 每个点绑定的纬度
      highNum: "", // 高程
      settingObj: {
        // 拉线
        stay: {
          state: false, // 状态
          voltage: false, // 电压等级
          stayType: false, // 拉线类型
          stayPlan: false, // 拉线方案
        },
      },
      stayType: [
        {
          key: "单拉线",
          value: "单拉线",
          type: "DLX",
        },
        {
          key: "V型拉线",
          value: "V型拉线",
          type: "VXLX",
        },
        {
          key: "弓形拉线",
          value: "弓形拉线",
          type: "GXLX",
        },
      ], // 拉线类型
      // 拉线
      stay: {
        state: "新建", // 状态
        voltage: "10kV", // 电压等级
        stayType: "单拉线", // 拉线类型
        stayPlan: "", // 拉线方案
        stayPlanId: "", // 拉线方案
        imgList: [], // 文件列表
        message: "", // 备注信息
        audioList: [], // 语音列表
      },
      stayPlan: [], // 拉线拉线方案 从后台读
      mainLineState: [], // 电缆线路状态
      gtState: [
        {
          key: "新建",
          value: "新建",
        },
        {
          key: "原有",
          value: "原有",
        },
        {
          key: "改造",
          value: "改造",
        },
        {
          key: "拆除",
          value: "拆除",
        },
      ],
      voltage: [
        {
          key: "10kV",
          value: "10kV",
        },
        {
          key: "0.4kV",
          value: "0.4kV",
        },
      ], // 电压等级
    };
  },
  mounted() {
    this.userId = "cf3f4ab8-eedd-478d-b73d-d46dfe15b334";
    this.getFirstTowerOrLineType("", "4", "", "DLX", "", "");
  },
  methods: {
    showEveryItemSet() {
      this.isFoldArea = !this.isFoldArea;
    },
    /**
     * 提交数据
     */
    submitData() {
      const parma = {
        type: 10,
        param: this.stay,
        visParam: this.settingObj.stay,
      };
      this.$emit("submitChildData", parma);
    },
    backCurrentDom() {
      this.$emit("backCurrentDom");
      this.getFirstTowerOrLineType("", 4, "", "DLX", "", "", "");
      // 拉线
      const stateText = this.remodeState ? "新建" : "原有";
      this.stay.state = stateText;
      this.stay.voltage = "10kV";
      this.stay.stayType = "单拉线";
      this.stay.stayPlan = "";
      this.stay.stayPlanId = "";
      this.stay.imgList = [];
      this.stay.message = "";
      this.stay.audioList = [];
      this.settingObj.stay.state = false;
      this.settingObj.stay.voltage = false;
      this.settingObj.stay.stayType = false;
      this.settingObj.stay.stayPlan = false;
    },
    /**
     * 获取语音数据
     */
    getAudioData(data) {
      this.stay.audioList = data.aduioList;
    },
    /**
     * 获取上传图片数据
     */
    getImgData(data) {
      this.stay.imgList = data.imgList;
    },
    /**
     * 获取备注信息
     * @param data
     */
    getMsgData(data) {
      this.stay.message = data.message;
    },
    /**
     * 拉线
     */
    onConfirmLxSel(type, item) {
      const val = item.value;
      switch (type) {
        case 0:
          this.stay.state = val;
          this.settingObj.stay.state = false;
          break;
        case 1:
          this.stay.voltage = val;
          this.settingObj.stay.voltage = false;
          break;
        case 2:
          this.stay.stayType = val;
          this.getFirstTowerOrLineType("", 4, "", item.type, "", "", "");
          this.settingObj.stay.stayType = false;
          break;
        case 3:
          this.stay.stayPlan = item.moduleName;
          this.stay.stayPlanId = item.moduleID;
          this.settingObj.stay.stayPlan = false;
          break;
      }
    },
    getFirstTowerOrLineType(
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          that.stayPlan = res.data;
          if (res.data.length === 0) {
            that.stay.stayPlan = "";
            that.stay.stayPlanId = "";
          } else {
            that.stay.stayPlan = res.data[0].moduleName;
            that.stay.stayPlanId = res.data[0].moduleID;
          }
        }
      });
    },
    getAwaitTowerOrLineType(moduleType, materialsTypeKey) {
      const that = this;
      const param = {
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        userId: this.userId,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          that.stayPlan = res.data;
        }
      });
    },
  },
};
</script>

<style lang="sass" scoped>
</style>

