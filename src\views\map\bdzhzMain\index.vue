<template>
  <div>
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div v-if="showBackAdds" class="maps-zhedNav" @click="backCurrentDom">
            <img
              class="mapites-backImg"
              :src="require('@/assets/' + 'map/settingImg/backAdds.png')"
              alt=""
            />
          </div>
          <span />
          变电站
          <div
            v-if="!showBackAdds"
            class="maps-zhedNav"
            @click="showEveryItemSet"
          >
            <img
              v-show="!isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zhedie.png')"
              alt=""
            />
            <img
              v-show="isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zkzhedie.png')"
              alt=""
            />
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img
            class="settingImg"
            :src="require('@/assets/' + 'map/settingImg/useSetting.png')"
            alt=""
          />
          <p>启用</p>
        </div>
      </div>
      <div v-show="isFoldArea" class="pro-addForm">
        <div class="map-showNav">基本信息</div>
        <van-row>
          <!--变电站编号-->
          <van-field
            v-model="changeCable.mark"
            label="变电站编号"
            placeholder="请输入变电站编号"
          />
        </van-row>
        <div class="map-showNav">选型信息</div>
        <van-row>
          <!--变电站状态-->
          <van-field
            readonly
            clickable
            :value="changeCable.state"
            label="状态"
            placeholder="请选择状态"
            @click="settingObj.changeCable.state = true"
          />
          <van-popup
            v-model="settingObj.changeCable.state"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="状态"
              value-key="key"
              :columns="cableChannelsState"
              @confirm="onConfirmBdz(0, '', $event)"
              @cancel="settingObj.changeCable.state = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--变电站电压-->
          <van-field
            readonly
            clickable
            :value="changeCable.valtage"
            label="变电站型号"
            placeholder="请选择变电站型号"
            @click="settingObj.changeCable.valtage = true"
          />
          <van-popup
            v-model="settingObj.changeCable.valtage"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="变电站型号"
              value-key="key"
              :columns="changeVoltage"
              @confirm="onConfirmBdz(1, '', $event)"
              @cancel="settingObj.cableChannels.valtage = false"
            />
          </van-popup>
        </van-row>
        <!--通道类别-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="changeCable.aisleType"
            label="通道类别"
            placeholder="请选择通道类别"
            @click="settingObj.changeCable.aisleType = true"
          />
          <van-popup
            v-model="settingObj.changeCable.aisleType"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="通道类别"
              value-key="moduletypename"
              :columns="tdlaying"
              @confirm="onConfirmBdz(2, '', $event)"
              @cancel="settingObj.changeCable.aisleType = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--破路面类型-->
          <van-field
            readonly
            clickable
            :value="changeCable.plmType"
            label="破路面类型"
            placeholder="请选择破路面类型"
            @click="settingObj.changeCable.plmType = true"
          />
          <van-popup
            v-model="settingObj.changeCable.plmType"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="破路面类型"
              value-key="key"
              :columns="plmType"
              @confirm="onConfirmBdz(3, '', $event)"
              @cancel="settingObj.changeCable.plmType = false"
            />
          </van-popup>
        </van-row>
        <div v-if="isShowNav" class="map-showNav">其他信息</div>
        <van-row v-if="isShowNav">
          <!--经度-->
          <van-field v-model="lngtitude" label="经度" disabled />
        </van-row>
        <van-row v-if="isShowNav">
          <!--纬度-->
          <van-field v-model="lattitude" label="纬度" disabled />
        </van-row>
        <!--电缆线路电缆设置-->
        <van-row>
          <div class="pro-addTitle">
            电缆设置
            <van-icon
              name="add-o"
              size="14"
              :style="{ marginLeft: '0.8rem', top: '0.7rem' }"
              @click="addCableItem"
            >
              添加
            </van-icon>
          </div>
          <div :style="{ width: '100%', overflowX: 'scroll' }">
            <table
              border="1"
              cellspacing="0"
              class="map-table"
              cellpadding="0"
              align="center"
            >
              <tr>
                <th :style="{ width: '5rem' }">线路名称</th>
                <th :style="{ width: '12rem' }">电缆型号</th>
                <th :style="{ width: '14rem' }">通道型号</th>
                <th :style="{ width: '12rem' }">终端头型号</th>
                <th :style="{ width: '12rem' }">中间头型号</th>
                <th :style="{ width: '5rem' }">状态</th>
                <th :style="{ width: '5rem' }">终端头</th>
                <th :style="{ width: '5rem' }">中间头</th>
                <th :style="{ width: '5rem' }">编辑</th>
              </tr>
              <tr v-for="(item, index) in changeCable.listArr">
                <!--电缆设置-->
                <td>
                  <van-field
                    v-model="item.lineName"
                    placeholder="请输入电路名称"
                  />
                </td>
                <td>
                  <!--电缆型号-->
                  <van-field
                    readonly
                    clickable
                    :value="item.lineModel"
                    class="map-dlxh"
                    placeholder="电缆型号"
                    @click="
                      settingObj.changeCable.listArr[index].lineModel = true
                    "
                  />
                  <van-popup
                    v-model="settingObj.changeCable.listArr[index].lineModel"
                    round
                    position="bottom"
                  >
                    <van-picker
                      show-toolbar
                      title="电缆型号"
                      value-key="name"
                      :columns="bdzModel"
                      @confirm="onConfirmBdz(4, index, $event)"
                      @cancel="
                        settingObj.changeCable.listArr[index].lineModel = false
                      "
                    />
                  </van-popup>
                </td>
                <td>
                  <!--通道型号-->
                  <van-field
                    readonly
                    clickable
                    class="map-dlxh"
                    :value="item.tdTypeSpec"
                    placeholder="请选择通道型号"
                    @click="settingObj.changeCable.listArr[index].tdType = true"
                  />
                  <van-popup
                    v-model="settingObj.changeCable.listArr[index].tdType"
                    round
                    position="bottom"
                  >
                    <van-picker
                      show-toolbar
                      title="通道型号"
                      value-key="moduleName"
                      :columns="bdztdxh"
                      @confirm="onConfirmBdz(5, index, $event)"
                      @cancel="
                        settingObj.changeCable.listArr[index].tdType = false
                      "
                    />
                  </van-popup>
                </td>
                <!--终端头型号-->
                <td>
                  <van-field
                    readonly
                    clickable
                    class="map-dlxh"
                    :value="item.zdtModel"
                    placeholder="终端头型号"
                    @click="
                      settingObj.changeCable.listArr[index].zdtModel = true
                    "
                  />
                  <van-popup
                    v-model="settingObj.changeCable.listArr[index].zdtModel"
                    round
                    position="bottom"
                  >
                    <van-picker
                      show-toolbar
                      title="终端头型号"
                      value-key="moduleName"
                      :columns="bdzzdtList"
                      @confirm="onConfirmBdz(6, index, $event)"
                      @cancel="
                        settingObj.changeCable.listArr[index].zdtModel = false
                      "
                    />
                  </van-popup>
                </td>
                <!--中间头型号-->
                <td>
                  <van-field
                    readonly
                    clickable
                    class="map-dlxh"
                    :value="item.zjtModel"
                    placeholder="中间头型号"
                    @click="
                      settingObj.changeCable.listArr[index].zjtModel = true
                    "
                  />
                  <van-popup
                    v-model="settingObj.changeCable.listArr[index].zjtModel"
                    round
                    position="bottom"
                  >
                    <van-picker
                      show-toolbar
                      title="中间头型号"
                      value-key="moduleName"
                      :columns="bdzzjtList"
                      @confirm="onConfirmBdz(7, index, $event)"
                      @cancel="
                        settingObj.changeCable.listArr[index].zjtModel = false
                      "
                    />
                  </van-popup>
                </td>
                <!--电缆状态-->
                <td>
                  <van-field
                    readonly
                    clickable
                    class="map-dlxh"
                    :value="item.lineState"
                    placeholder="状态"
                    @click="
                      settingObj.changeCable.listArr[index].lineState = true
                    "
                  />
                  <van-popup
                    v-model="settingObj.changeCable.listArr[index].lineState"
                    round
                    position="bottom"
                  >
                    <van-picker
                      show-toolbar
                      title="状态"
                      value-key="key"
                      :columns="cableLineState"
                      @confirm="onConfirmBdz(8, index, $event)"
                      @cancel="
                        settingObj.changeCable.listArr[index].lineState = false
                      "
                    />
                  </van-popup>
                </td>
                <!--终端头-->
                <td :style="{ paddingLeft: '1.8rem' }">
                  <van-checkbox v-model="item.terminal" />
                </td>
                <!--中间头-->
                <td :style="{ textAlign: 'center' }">
                  <van-field v-model="item.middle" placeholder="中间头" />
                </td>
                <td :style="{ textAlign: 'center', color: 'red' }">
                  <span @click="removeMainLine(2, index)">删除</span>
                </td>
              </tr>
            </table>
          </div>
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { Toast } from "vant";

export default {
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false,
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的id
    editData: {
      type: Object,
      defaults: () => {},
    },
    // 通道型号
    tdxhList: {
      type: Array,
      defaults: () => [],
    },
    // 电缆线路
    dlxlList: {
      type: Array,
      defaults: () => [],
    },
    // 通道类别
    tdlbList: {
      type: Array,
      defaults: () => [],
    },
    // 中间头型号
    zjtTypeList: {
      type: Array,
      defaults: () => [],
    },
    // 终端头型号
    zdtTypeList: {
      type: Array,
      defaults: () => [],
    },
    remodeState: {
      type: Boolean,
      defaults: false,
    },
  },
  watch: {
    // 监听改前改后状态
    remodeState: {
      handler(newVal) {
        if (newVal) {
          // 改后不显示拆除的数据
          this.cableLineState = this.lineState.slice(0, 2);
          this.cableChannelsState = this.cableState.slice(0, 2);
          this.changeCable.state = "新建";
          for (const j in this.changeCable.listArr) {
            this.changeCable.listArr[j].lineState = "新建";
          }
        } else {
          // 改前不显示新建的数据
          this.cableLineState = this.lineState.slice(1);
          this.cableChannelsState = this.cableState.slice(1);
          this.changeCable.state = "原有";
          for (const j in this.changeCable.listArr) {
            this.changeCable.listArr[j].lineState = "原有";
          }
        }
      },
      deep: true,
      immediate: true,
    },
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal;
      },
      deep: true,
      immediate: true,
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal;
        if (data.moudleType === "BDZHZ") {
          this.lngtitude = data.longitude;
          this.lattitude = data.latitude;
          this.settingObj.changeCable.listArr = [];
          this.changeCable.listArr = [];
          this.changeCable.imgList = [];
          this.changeCable.audioList = [];
          this.$nextTick(() => {
            for (const j in data.listArr) {
              const obj = {
                lineName: data.listArr[j].lineName, // 线路名称
                lineState: data.listArr[j].state, // 线路状态
                tdTypeSpec: data.listArr[j].tdTypeSpec, // 通道型号
                tdType: data.listArr[j].tdType, // 通道型号
                lineModel: data.listArr[j].lineModel, // 导线型号
                zjtModel: data.listArr[j].zjtModelSpec, // 导线id
                zjtModelId: data.listArr[j].zjtModelId, // 导线id
                zdtModel: data.listArr[j].zdtModelSpec, // 导线id
                zdtModelId: data.listArr[j].zdtModelId, // 导线id
                terminal: data.listArr[j].terminal === "1", // 终端头
                lineLength: data.listArr[j].moduleId, // 导线id
                middle: data.listArr[j].middle, // 导线id
              };
              const isShow_sevent = {
                lineState: false, // 线路状态
                tdType: false,
                lineModel: false, // 线路型号
                zjtModel: false, // 线路型号
                zdtModel: false, // 线路型号
                lineType: false,
              };
              this.settingObj.changeCable.listArr.push(isShow_sevent);
              this.electrical.changeCable.push(obj);
            }
            this.changeCable.state = data.state;
            this.changeCable.aisleType = data.category;
            this.changeCable.plmType = data.plmType;
            this.changeCable.valtage = data.voltage;
            this.changeCable.loactionMsg = data.note;
            for (const k in data.imgList) {
              const objs = {
                url: data.imgList[k].path,
                isImage: true,
                isSaveReport: data.imgList[k].isSaveReport,
              };
              this.changeCable.imgList.push(objs);
            }
            for (const s in data.voiceList) {
              const objs = {
                content: data.voiceList[s].path,
              };
              this.changeCable.audioList.push(objs);
            }
          });
        }
      },
      deep: true,
    },
    // 通道型号
    tdxhList: {
      handler(newVal) {
        this.bdztdxh = newVal;
        this.changeCable.listArr[0].tdTypeSpec = newVal[0].moduleName;
        this.changeCable.listArr[0].tdType = newVal[0].moduleID;
      },
      deep: true,
    },
    // 电缆线路
    dlxlList: {
      handler(newVal) {
        this.bdzModel = newVal;
        this.changeCable.listArr[0].lineModel = newVal[0].name;
        this.changeCable.listArr[0].lineModelId = newVal[0].id;
      },
      deep: true,
    },
    // 通道类别
    tdlbList: {
      handler(newVal) {
        this.changeCable.aisleType = newVal[0].moduletypename;
        this.tdlaying = newVal;
      },
      deep: true,
    },
    // 中间头型号
    zjtTypeList: {
      handler(newVal) {
        this.changeCable.listArr[0].zjtModel = newVal[0].moduleName;
        this.changeCable.listArr[0].zjtModelId = newVal[0].moduleID;
        this.bdzzjtList = newVal;
      },
      deep: true,
    },
    // 终端头型号
    zdtTypeList: {
      handler(newVal) {
        this.changeCable.listArr[0].zdtModel = newVal[0].moduleName;
        this.changeCable.listArr[0].zdtModelId = newVal[0].moduleID;
        this.bdzzdtList = newVal;
      },
      deep: true,
    },
  },
  data() {
    return {
      lngtitude: "",
      lattitude: "",
      isFoldArea: false,
      bdztdxh: [], // 变电站通道型号
      bdzzjtList: [], // 变电站中间头
      bdzzdtList: [], // 变电站终端头
      bdzModel: [], // 变电站电缆型号
      tdlaying: [], // 通道类别
      plmType: [
        {
          key: "混凝土路面(150mm以下)",
          value: "混凝土路面(150mm以下)",
        },
        {
          key: "混凝土路面(250mm以下)",
          value: "混凝土路面(250mm以下)",
        },
        {
          key: "沥青路面",
          value: "沥青路面",
        },
        {
          key: "花岗石路面",
          value: "花岗石路面",
        },
        {
          key: "彩砖路面",
          value: "彩砖路面",
        },
        {
          key: "瓷砖路面",
          value: "瓷砖路面",
        },
        {
          key: "彩砖路面",
          value: "彩砖路面",
        },
        {
          key: "绿化带",
          value: "绿化带",
        },
      ], // 破路面类型
      changeVoltage: [
        {
          key: "35kV",
          value: "35kV",
        },
        {
          key: "110kV",
          value: "110kV",
        },
        {
          key: "220kV",
          value: "220kV",
        },
      ], // 电压等级
      cableLineState: [], // 电缆线路状态
      lineState: [
        {
          key: "新建",
          value: "新建",
        },
        {
          key: "原有",
          value: "原有",
        },
        {
          key: "拆除",
          value: "拆除",
        },
      ],
      cableChannelsState: [], // 土建路径状态
      cableState: [
        {
          key: "新建",
          value: "新建",
        },
        {
          key: "原有",
          value: "原有",
        },
      ],
      settingObj: {
        changeCable: {
          state: false, // 状态
          valtage: false, // 电压
          aisleType: false,
          plmType: false,
          listArr: [
            {
              tdType: false, // 电缆型号
              lineType: false, // 通道型号
              lineState: false, // 线路状态
              lineModel: false, // 线路型号
              zjtModel: false, // 线路型号
              zdtModel: false, // 线路型号
            },
          ],
        },
      },
      changeCable: {
        state: "新建",
        valtage: "35kV",
        mark: "编号1",
        aisleType: "",
        plmType: "混凝土路面",
        loactionMsg: "",
        imgList: [],
        audioList: [],
        listArr: [
          {
            tdType: "", // 通道型号
            tdTypeSpec: "", // 通道型号
            lineName: "线路1", // 线路名称
            lineState: "新建", // 线路状态
            lineModel: "", // 线路型号
            lineModelId: "", // 线路型号id
            zjtModel: "", // 中间头类型
            zjtModelId: "", // 中间头类型id
            zdtModel: "", // 终端头类型
            zdtModelId: "", // 终端头类型id
            terminal: true, // 终端头
            lineLength: 0,
            middle: "250", // 中间头
          },
        ],
      },
    };
  },
  mounted() {},
  methods: {
    addCableItem() {
      const stateText = this.remodeState ? "新建" : "原有";
      const item_two = {
        lineName: "线路1", // 线路名称
        lineType: this.dlxlList[0].name, // 型号
        lineModelId: this.dlxlList[0].id, // 型号
        lineState: stateText, // 状态
        terminal: true, // 终端头
        zdtModel: this.bdzzjtList[0].moduleName, // 终端头型号
        zdtModelId: this.bdzzjtList[0].moduleID, // 终端头型号
        zjtModel: this.bdzzdtList[0].moduleName, // 中间头型号
        zjtModelId: this.bdzzdtList[0].moduleID, // 终端头型号
        lineLength: 0,
        middle: "250", // 中间头
      };
      const isshow_two = {
        zdtModel: false, // 中间头下拉是否使用
        zjtModel: false, // 中间头是否使用
        tdType: false, // 通道型号
        cableLineModel: false, // 电缆型号
        cableLineState: false, // 电缆状态
      };
      this.addParam.cableLine.listArr.push(item_two);
      this.settingObj.cableLine.listArr.push(isshow_two);
    },
    /**
     * 提交数据
     */
    submitData() {
      const parma = {
        type: 20,
        param: this.changeCable,
        visParam: this.settingObj.changeCable,
      };
      this.$emit("submitChildData", parma);
    },
    backCurrentDom() {
      this.$emit("backCurrentDom");
      // 变电站通道型号
      this.bdztdxh = this.tdxhList;
      this.changeCable.listArr[0].tdTypeSpec = this.tdxhList[0].moduleName;
      this.changeCable.listArr[0].tdType = this.tdxhList[0].moduleID;
      // 变电站电缆线路
      this.bdzModel = this.dlxlList;
      this.changeCable.listArr[0].lineModel = this.dlxlList[0].name;
      this.changeCable.listArr[0].lineModelId = this.dlxlList[0].id;
      // 变电站通道类别
      this.changeCable.aisleType = this.tdlbList[0].moduletypename;
      this.tdlaying = this.tdlbList;
      // 变电站中间头型号
      this.changeCable.listArr[0].zjtModel = this.zjtTypeList[0].moduleName;
      this.changeCable.listArr[0].zjtModelId = this.zjtTypeList[0].moduleID;
      this.bdzzjtList = this.zjtTypeList;
      // 变电站终端头型号
      this.changeCable.listArr[0].zdtModel = this.zdtTypeList[0].moduleName;
      this.changeCable.listArr[0].zdtModelId = this.zdtTypeList[0].moduleID;
      this.bdzzdtList = this.zdtTypeList;
      // 变电站
      this.changeCable.valtage = "35kV";
      const stateText = this.remodeState ? "新建" : "原有";
      this.changeCable.state = stateText;
      this.changeCable.mark = "编号1";
      this.changeCable.plmType = "混凝土路面(150mm以下)";
      this.changeCable.listArr = [
        {
          lineName: "线路1", // 线路名称
          lineModel: "",
          lineModelId: "",
          lineState: stateText, // 状态
          terminal: true, // 终端头
          lineLength: 0,
          middle: "250", // 中间头
        },
      ];
      this.settingObj.changeCable.state = false;
      this.settingObj.changeCable.valtage = false;
      this.settingObj.changeCable.listArr = [
        {
          lineState: false, // 线路状态
          tdType: false, // 通道型号
          lineType: false, // 线路状态
          lineModel: false, // 线路型号
          zjtModel: false, // 线路型号
          zdtModel: false, // 线路型号
        },
      ];
    },
    showEveryItemSet() {
      this.isFoldArea = !this.isFoldArea;
    },
    /**
     * 获取语音数据
     */
    getAudioData(data) {
      this.changeCable.audioList = data.aduioList;
    },
    /**
     * 获取上传图片数据
     */
    getImgData(data) {
      this.changeCable.imgList = data.imgList;
    },
    /**
     * 获取备注信息
     * @param data
     */
    getMsgData(data) {
      this.changeCable.message = data.message;
    },
    /**
     * 变电站
     */
    onConfirmBdz(type, index, item) {
      const val = item.value;
      switch (type) {
        case 0:
          // 变电站状态
          this.changeCable.state = item.value;
          this.settingObj.changeCable.state = false;
          break;
        case 1:
          // 变电站类型
          this.changeCable.valtage = item.value;
          this.settingObj.changeCable.valtage = false;
          break;
        case 2:
          // 变电站通道类别
          this.changeCable.aisleType = item.moduletypename;
          this.settingObj.changeCable.aisleType = false;
          break;
        case 3:
          // 变电站破路面类型
          this.changeCable.plmType = val;
          this.settingObj.changeCable.plmType = false;
          break;
        case 4:
          // 变电站电缆型号
          this.changeCable.listArr[index].lineModel = item.name;
          this.changeCable.listArr[index].lineModelId = item.id;
          this.settingObj.changeCable.listArr[index].lineModel = false;
          break;
        case 5:
          // 变电站通道型号
          this.changeCable.listArr[index].tdTypeSpec = item.moduleName;
          this.changeCable.listArr[index].tdType = item.moduleID;
          this.settingObj.changeCable.listArr[index].tdType = false;
          break;
        case 6:
          // 终端头型号
          this.changeCable.listArr[index].zdtModel = item.moduleName;
          this.changeCable.listArr[index].zdtModelId = item.moduleID;
          this.settingObj.changeCable.listArr[index].zdtModel = false;
          break;
        case 7:
          // 中间头型号
          this.changeCable.listArr[index].zjtModel = item.moduleName;
          this.changeCable.listArr[index].zjtModelId = item.moduleID;
          this.settingObj.changeCable.listArr[index].zjtModel = false;
          break;
        case 8:
          // 中间头型号
          this.changeCable.listArr[index].lineState = val;
          this.settingObj.changeCable.listArr[index].lineState = false;
          break;
      }
    },
    removeMainLine(type, index) {
      if (this.cableLine.listArr.length !== 1) {
        this.settingObj.cableLine.listArr.splice(index, 1);
        this.cableLine.listArr.splice(index, 1);
      } else {
        Toast.fail("最少有一条线路!");
      }
    },
  },
};
</script>

<style lang="sass" scoped>
</style>

