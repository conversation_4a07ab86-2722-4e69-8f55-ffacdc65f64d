<template>
  <div class="index flex-c h100">
    <!-- 标题 -->
    <div class="main-header">结算指标</div>
    <div class="" style="width: 100%; border-bottom: 1px solid #eee"></div>
    <!-- 表单 -->
    <div class="query-form-box">
      <el-form ref="form" :model="form" :inline="true" label-width="80px">
        <el-row>
          <el-col :span="5">
            <el-form-item label="年度:" prop="pcnd">
              <el-date-picker
                v-model="form.pcnd"
                type="year"
                format="yyyy"
                value-format="yyyy"
                placeholder="选择日期"
                @change="NDPoint"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="批次名次:" prop="batch">
              <el-select v-model="form.batch" clearable placeholder="请选择">
                <el-option
                  v-for="item in batchOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="地市公司:" prop="dsgs">
              <el-select
                v-model="form.dsgs"
                clearable
                placeholder="请选择"
                @change="CityPoint"
              >
                <el-option
                  v-for="item in dsgsOptions"
                  :key="item.cityid"
                  :label="item.cityname"
                  :value="item.cityid"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="lastSearchArea">
            <el-form-item label="县公司:" prop="xgs">
              <el-select v-model="form.xgs" clearable placeholder="请选择">
                <el-option
                  v-for="item in xgsOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 16px">
          <el-col :span="24" class="lastSearchArea">
            <el-button class="blue-btn" @click="query()" icon="el-icon-search"
              >查询</el-button
            >
            <el-button @click="clearForm()" icon="el-icon-refresh"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="driver"></div>
    <div class="cp-tableInfo">
      <div class="cp-btns">
        <div class="cp-handleBtn" @click="TJBtn">
          <span class="cp-texts">
            <img
              style="width: 16px; height: 16px"
              :src="require('@/assets/main/统计.png')"
              alt=""
            />
            <span style="margin-left: 5px"> 统计 </span>
          </span>
        </div>
      </div>
    </div>
    <!-- 项目列表 -->
    <div class="tablesArea">
      <el-table
        :data="tableData"
        :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
        :height="tableHeight"
        highlight-current-row
        @selection-change="handleSelectionChange"
        style="width: 98%; margin: 0 16px 0 16px"
      >
        <el-table-column
          type="index"
          :index="indexMethod"
          label="序号"
          align="center"
          :resizable="false"
          width="60"
        >
        </el-table-column>
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column prop="pcnd" label="年度" align="center" width="70">
        </el-table-column>
        <el-table-column
          prop="batchname"
          label="项目批次"
          align="center"
          width="160"
        >
        </el-table-column>
        <el-table-column
          prop="dwmc"
          label="地市公司"
          align="center"
          width="130px"
        >
        </el-table-column>
        <el-table-column
          prop="xgsmc"
          label="县公司"
          align="center"
          width="130px"
        >
        </el-table-column>
        <el-table-column
          prop="sbsl"
          label="结算申报数量(个)"
          align="center"
          width="180"
        >
        </el-table-column>
        <el-table-column
          prop="wcsl"
          label="结算完成数量(个)"
          align="center"
          width="180"
        >
        </el-table-column>
        <el-table-column prop="jsl" label="结算率" align="center" width="100">
        </el-table-column>
        <el-table-column
          prop="gais"
          label="审定概算金额(万元)"
          align="center"
          width="200"
        >
        </el-table-column>
        <el-table-column
          prop="js"
          label="审定结算金额(万元)"
          align="center"
          width="200"
        >
        </el-table-column>
        <el-table-column prop="jyl" label="概算结余率" align="center" width="120">
          <template slot-scope="scope">
            <span
              >{{
                scope.row.gais == "0"
                  ? "0"
                  : ((scope.row.gais - scope.row.js) / scope.row.gais) * 100
              }}%</span
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="jssl"
          label="结算及时率"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ (scope.row.wcsl / scope.row.sbsl) * 100 }}%</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      style="margin: 10px"
      background
      :current-page="form.pageIndex"
      :page-sizes="pageSize"
      :page-size="form.pageSize"
      layout="total, sizes, prev, pager, next"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      :total="total"
    >
    </el-pagination>
    <div class="cp-tableInfo" v-show="isShowEcarts">
      <div class="cp-btns" style="margin-top: 0">
        <div class="cp-handleBtn" @click="closeEcharts">
          <span class="cp-texts">
            <img
              style="width: 16px; height: 16px"
              :src="require('@/assets/main/hideEcharts.png')"
              alt=""
            />
            <span style="margin-left: 5px"> 关闭图表 </span>
          </span>
        </div>
      </div>
      <div
        class="echarts"
        style="
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: space-around;
          overflow-y: auto;
        "
      >
        <div class="" style="width: 50%">
          <div id="main" style="width: 100%; height: 340px" ref="charts"></div>
        </div>
        <div class="" style="width: 50%">
          <div id="main" style="width: 100%; height: 340px" ref="charts2"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getTJList,
  getCity,
  getCounty,
  getPCSel,
  getuserInfo
} from '@/api/api'
import * as echarts from 'echarts'
export default {
  data () {
    return {
      form: { //表单参数
        pcnd: new Date().getFullYear() + '',
        batch: '',
        dsgs: '',
        xgs: '',
        xmName: '',
        csmc: '',
        dwmc: '',
        projState: '',
        state: '',
        stageType: '',
        projectName: '',
        pageSize: 10,
        pageIndex: 1,
      },
      isShowEcarts: true,
      xgsOptions: [], //县公司下拉数据
      dsgsOptions: [], //城市下拉
      batchOptions: [],
      pageSize: [10, 20, 50, 100], //分页页数
      total: 0, //总共页数
      tableData: [],
      showTableType: 0, // 0是不展示echarts 1展示echarts
      tableHeight: 0,
      rowList: [],
      slsList: [],
      jsslList: [],
      slList: [],
      NameList: [],
    }
  },
  mounted () {
    this.showTableType = 0
    this.setTablesHeight(this.showTableType)
    const token = sessionStorage.getItem('bhneToken')
    getuserInfo(token).then((res) => {
      this.form.csmc = res.data.result.USERID
      this.form.dwmc = res.data.result.DWMC
      this.getList()
    })
    const that = this
    window.onresize = function () {
      that.setTablesHeight(that.showTableType)
    }
  },
  methods: {
    setTablesHeight (type) {
      this.$nextTick(() => {
        const tablesAreaHeight = document.getElementsByClassName('driver')[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        switch (type) {
          case 0:
            this.tableHeight = windowsAreaHeight - tablesAreaHeight - 120
            break
          case 1:
            this.tableHeight = windowsAreaHeight - tablesAreaHeight - 480
            break
        }
      })
    },
    RenderEcharts () {

    },
    getList () {
      getTJList(this.form)
        .then((res) => {
          console.log(res)
          this.tableData = res.data.data
          this.total = res.data.total
        })
        .catch(() => { })
      if (this.dsgsOptions.length === 0) {
        // 获取城市下拉
        const params = {
          optId: this.form.dwmc,
        }
        getCity(params)
          .then((res) => {
            this.dsgsOptions = res.data.result
          })
          .catch(() => { })
      }
    },
    // 城市点击获取县下拉
    CityPoint (val) {
      const params = {
        optId: val,
        dwmc: this.form.dwmc,
      }
      getCounty(params)
        .then((res) => {
          this.xgsOptions = res.data.result
          this.xgsOptions.unshift({
            id: "100",
            name: "市公司项目",
          })
        })
        .catch(() => { })
    },
    NDPoint () {
      // 获取批次名词
      const param = {
        optId: this.form.pcnd,
      }
      getPCSel(param)
        .then((res) => {
          console.log(res)
          this.batchOptions = res.data.result
        })
        .catch(() => { })
    },
    // 查询
    query () {
      this.getList()
    },
    closeEcharts () {
      this.isShowEcarts = false
      this.showTableType = 0
      this.setTablesHeight(this.showTableType)
    },
    // table列表序号索引
    indexMethod (index) {
      return (this.form.pageIndex - 1) * 10 + index + 1
    },
    // 重置
    clearForm () {
      this.form = {
        pcnd: '',
        batch: '',
        dsgs: '',
        xgs: '',
        xmName: '',
        dwmc: this.form.dwmc,
        projState: '',
        state: '',
        stageType: '',
        projectName: '',
        pageSize: 10,
        pageIndex: 1,
      }
      this.getList()
    },
    // 复选框
    handleSelectionChange (val) {
      this.rowList = val
    },
    handleCurrentChange (val) {
      this.form.pageIndex = val
      this.getList()
    },
    handleSizeChange (val) {
      this.form.pageSize = val
      this.getList()
    },
    // 统计
    TJBtn () {
      if (this.form.pcnd === '' || this.form.batch === '') {
        this.$message({
          showClose: true,
          message: '请选择年度和批次名称',
          type: 'warning'
        })
      } else {
        this.getList()
        if (this.rowList.length > 0) {
          this.isShowEcarts = true
          let dwmcArr = []
          let sbslArr = []
          let wcslArr = []
          this.rowList.forEach(dept => {
            dwmcArr.push(dept.dwmc)
            sbslArr.push(dept.sbsl)
            wcslArr.push(dept.wcsl)
          })
          let mychart = echarts.init(this.$refs.charts)
          var optionline
          optionline = {
            title: {
              text: '工程数',
              subtext: ''
            },
            grid: {
              top: 90,
            },
            tooltip: {
              trigger: 'axis',
              show: true
            },
            legend: {
              data: ['申报数量', '通过数量']
            },
            toolbox: {
              feature: {
                dataView: {
                  show: true,
                  readOnly: false
                },
                magicType: {
                  show: true,
                  type: ['line', 'bar']
                },
                restore: {
                  show: true
                },
                saveAsImage: {
                  show: true
                }
              }
            },
            calculable: true,
            //X轴上柱子的数据
            xAxis: [{
              type: 'category',
              data: []
            }],
            yAxis: [{
              type: 'value'
            }],
            series: [{
              name: '申报数量',
              type: 'bar',
              data: [

              ],
              //在柱子顶端展示数据的最大值最小值旗袍
              markPoint: {
                data: [{
                  type: 'max',
                  name: 'Max'
                },
                {
                  type: 'min',
                  name: 'Min'
                }
                ]
              },
              //展示数据平均值线
              markLine: {
                data: [{
                  type: 'average',
                  name: 'Avg'
                }]
              },
              //用于在每根柱子上展示数据
              label: {
                show: true,
                position: 'inside',
                color: 'black'
              },
            },
            {
              name: '通过数量',
              type: 'bar',
              data: [

              ],
              markPoint: {
                data: [{
                  type: 'max',
                  name: 'Max'
                },
                {
                  type: 'min',
                  name: 'Min'
                }
                ]
              },
              markLine: {
                data: [{
                  type: 'average',
                  name: 'Avg'
                }]
              }
            },

            ]
          }
          //注意，必须用这种语法来传递数据
          optionline.xAxis[0].data = dwmcArr
          optionline.series[0].data = sbslArr
          optionline.series[1].data = wcslArr
          mychart.setOption(optionline)
          let NameList = []
          let slList = []
          let slsList = []
          let jsslList = []
          this.rowList.forEach(dept => {
            slList.push(dept.sbsl)
            slsList.push(dept.js)
            NameList.push(dept.dwmc)
            jsslList.push(dept.jssl)
          })
          var mycharts = echarts.init(this.$refs.charts2)
          var optionline2
          optionline2 = {
            title: {
              text: '结算数',
              subtext: ''
            },
            grid: {
              top: 90,
            },
            tooltip: {
              trigger: 'axis',
              show: true
            },
            legend: {
              data: ['结算率', '结算及时率', '概算结余率'],
            },
            toolbox: {
              feature: {
                dataView: {
                  show: true,
                  readOnly: false
                },
                magicType: {
                  show: true,
                  type: ['line', 'bar']
                },
                restore: {
                  show: true
                },
                saveAsImage: {
                  show: true
                }
              }
            },
            calculable: true,
            //X轴上柱子的数据
            xAxis: [{
              type: 'category',
              data: [],

            }],
            yAxis: [{
              type: 'value',
            }],
            series: [{
              name: '结算率',
              type: 'bar',
              data: [

              ],
              //在柱子顶端展示数据的最大值最小值旗袍
              markPoint: {
                data: [{
                  type: 'max',
                  name: 'Max'
                },
                {
                  type: 'min',
                  name: 'Min'
                }
                ]
              },
              //展示数据平均值线
              markLine: {
                data: [{
                  type: 'average',
                  name: 'Avg'
                }]
              },
              //用于在每根柱子上展示数据
              label: {
                show: true,
                position: 'inside',
                color: 'black'
              },
            }, {
              name: '结算及时率',
              type: 'bar',
              data: [

              ],
              markPoint: {
                data: [{
                  type: 'max',
                  name: 'Max'
                },
                {
                  type: 'min',
                  name: 'Min'
                }
                ]
              },
              markLine: {
                data: [{
                  type: 'average',
                  name: 'Avg'
                }]
              }
            },
            {
              name: '概算结余率',
              type: 'bar',
              data: [

              ],
              //在柱子顶端展示数据的最大值最小值旗袍
              markPoint: {
                data: [{
                  type: 'max',
                  name: 'Max'
                },
                {
                  type: 'min',
                  name: 'Min'
                }
                ]
              },
              //展示数据平均值线
              markLine: {
                data: [{
                  type: 'average',
                  name: 'Avg'
                }]
              },
              //用于在每根柱子上展示数据
              label: {
                show: true,
                position: 'inside',
                color: 'black',
              },
            },
            ]
          }
          //注意，必须用这种语法来传递数据
          optionline2.xAxis[0].data = NameList
          optionline2.series[1].data = slList
          optionline2.series[0].data = jsslList
          optionline2.series[2].data = slsList
          mycharts.setOption(optionline2)
          this.showTableType = 1
          this.setTablesHeight(this.showTableType)
        } else {
          this.$message({
            showClose: true,
            message: '请选择要展示的项目',
            type: 'warning'
          })
        }
      }
    }
  }
}
</script>


<style  lang="scss" scoped>
.cp-tableInfo {
  background: #ffffff;
  .cp-checkNav {
    width: 400px;
    background: #f5f6fa;
    padding-left: 32px;
    height: 36px;
  }
  .cp-btns {
    background: #ffffff;
    margin: 0 0 16px 16px;
    display: flex;
    flex: 1;
    align-items: center;
    padding-right: 32px;
    justify-content: flex-start;
    margin-left: 12px;
    color: #7286e8;
    .cp-handleBtn {
      height: 26px;
      font-size: 12px;
      display: flex;
      color: #333333;
      background: #ffffff;
      border-radius: 5px;
      padding: 0 12px;
      align-items: center;
      cursor: pointer;
      border: 1px solid #abbdf7;
    }
    .cp-handleBtn:nth-child(2) {
      margin-left: 10px;
    }
    .cp-drawer {
      margin: 0 10px;
      color: #ccc;
    }

    .cp-handleUnBtn:hover {
      color: #7487e4;
      border-color: #7487e4;
    }

    .cp-handleUnBtn:active {
      color: #0c6b66;
      border-color: #0c6b66;
    }

    .cp-handleUnBtn:focus {
      color: #0c6b66;
      border-color: #0c6b66;
    }

    .cp-handleBtn {
      height: 26px;
      font-size: 12px;
      display: flex;
      color: #333333;
      background: #ffffff;
      border-radius: 5px;
      padding: 0 12px;
      align-items: center;
      cursor: pointer;
      border: 1px solid #abbdf7;
      .cp-icons {
        margin-right: 4px;

        .iconfont {
          font-size: 12px !important;
        }
      }

      .cp-texts {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        /*padding: 0 5px;*/
      }
    }

    .cp-handleUnBtn {
      height: 26px;
      font-size: 12px;
      display: flex;
      border-radius: 20px;
      border: 1px solid #7487e4;
      align-items: center;
      background-color: #fff;
      padding: 0 12px;
      cursor: pointer;
      color: #7487e4;

      .cp-icons {
        margin-right: 2px;

        .iconfont {
          font-size: 12px !important;
        }
      }
    }
  }
}
</style>
