window.sheetPivotTable = {
	"name": "PivotTable",
	"color": "",
	"config": {},
	"index": "7",
	"chart": [],
	"status": 0,
	"order": "7",
	"column": 18,
	"row": 36,
	"celldata": [{
		"r": 0,
		"c": 0,
		"v": "count:score"
	}, {
		"r": 0,
		"c": 1,
		"v": "science"
	}, {
		"r": 0,
		"c": 2,
		"v": "mathematics"
	}, {
		"r": 0,
		"c": 3,
		"v": "foreign language"
	}, {
		"r": 0,
		"c": 4,
		"v": "English"
	}, {
		"r": 0,
		"c": 5,
		"v": "total"
	}, {
		"r": 1,
		"c": 0,
		"v": "<PERSON>"
	}, {
		"r": 1,
		"c": 1,
		"v": 1
	}, {
		"r": 1,
		"c": 2,
		"v": 1
	}, {
		"r": 1,
		"c": 3,
		"v": 1
	}, {
		"r": 1,
		"c": 4,
		"v": 1
	}, {
		"r": 1,
		"c": 5,
		"v": 4
	}, {
		"r": 2,
		"c": 0,
		"v": "Joy"
	}, {
		"r": 2,
		"c": 1,
		"v": 1
	}, {
		"r": 2,
		"c": 2,
		"v": 1
	}, {
		"r": 2,
		"c": 3,
		"v": 1
	}, {
		"r": 2,
		"c": 4,
		"v": 1
	}, {
		"r": 2,
		"c": 5,
		"v": 4
	}, {
		"r": 3,
		"c": 0,
		"v": "Tim"
	}, {
		"r": 3,
		"c": 1,
		"v": 1
	}, {
		"r": 3,
		"c": 2,
		"v": 1
	}, {
		"r": 3,
		"c": 3,
		"v": 1
	}, {
		"r": 3,
		"c": 4,
		"v": 1
	}, {
		"r": 3,
		"c": 5,
		"v": 4
	}, {
		"r": 4,
		"c": 0,
		"v": "total"
	}, {
		"r": 4,
		"c": 1,
		"v": 3
	}, {
		"r": 4,
		"c": 2,
		"v": 3
	}, {
		"r": 4,
		"c": 3,
		"v": 3
	}, {
		"r": 4,
		"c": 4,
		"v": 3
	}, {
		"r": 4,
		"c": 5,
		"v": 12
	}],
	"ch_width": 4748,
	"rh_height": 1790,
	"luckysheet_select_save": [{
		"row": [0, 0],
		"column": [0, 0]
	}],
	"luckysheet_selection_range": [],
	"scrollLeft": 0,
	"scrollTop": 0,
	"isPivotTable": true,
	"pivotTable": {
		"pivot_select_save": {
			"left": 0,
			"width": 73,
			"top": 0,
			"height": 19,
			"left_move": 0,
			"width_move": 369,
			"top_move": 0,
			"height_move": 259,
			"row": [0, 12],
			"column": [0, 4],
			"row_focus": 0,
			"column_focus": 0
		},
		"pivotDataSheetIndex": 6, //The sheet index where the source data is located
		"column": [{
			"index": 3,
			"name": "subject",
			"fullname": "subject"
		}],
		"row": [{
			"index": 1,
			"name": "student",
			"fullname": "student"
		}],
		"filter": [],
		"values": [{
			"index": 4,
			"name": "score",
			"fullname": "count:score",
			"sumtype": "COUNTA",
			"nameindex": 0
		}],
		"showType": "column",
		"pivotDatas": [
			["count:score", "science", "mathematics", "foreign language", "English", "total"],
			["Alex", 1, 1, 1, 1, 4],
			["Joy", 1, 1, 1, 1, 4],
			["Tim", 1, 1, 1, 1, 4],
			["total", 3, 3, 3, 3, 12]
		],
		"drawPivotTable": false,
		"pivotTableBoundary": [5, 6]
	}
}

// export default sheetPivotTable;