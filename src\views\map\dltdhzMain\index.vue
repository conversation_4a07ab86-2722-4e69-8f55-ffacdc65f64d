<template>
  <div>
    <!--土建路径-->
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div v-if="showBackAdds" class="maps-zhedNav" @click="backCurrentDom">
            <img class="mapites-backImg" :src="require('@/assets/'+'map/settingImg/backAdds.png')" alt="">
          </div>
          <span />
          土建路径设计
          <div v-if="!showBackAdds" class="maps-zhedNav" @click="showEveryItemSet">
            <img v-show="!isFoldArea" class="mapites-zhed" :src="require('@/assets/'+'map/settingImg/zhedie.png')" alt="">
            <img v-show="isFoldArea" class="mapites-zhed" :src="require('@/assets/'+'map/settingImg/zkzhedie.png')" alt="">
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img class="settingImg" :src="require('@/assets/'+'map/settingImg/useSetting.png')" alt="">
          <p>启用</p>
        </div>
      </div>
      <div v-show="isFoldArea" class="pro-addForm">
        <div v-show="isShowNav" class="map-showNav">基本信息</div>
        <van-row v-show="isShowNav">
          <van-field
            v-model="startPointMark"
            label="开始点编号"
            disabled
            placeholder="请输入杆塔编号"
          />
        </van-row>
        <van-row v-show="isShowNav">
          <van-field
            v-model="endPointMark"
            disabled
            label="结束点编号"
            placeholder="请输入杆塔编号"
          />
        </van-row>
        <div class="map-showNav">选型信息</div>
        <van-row>
          <!--电缆通道线路名称-->
          <van-field
            v-model="cableChannels.name"
            label="线路名称"
            placeholder="请输入线路名称"
          />
        </van-row>
        <van-row>
          <!--电缆通道通道类别-->
          <van-field
            readonly
            clickable
            :value="cableChannels.laying"
            label="通道类别"
            placeholder="请选择通道类别"
            @click="settingObj.cableChannels.laying = true"
          />
          <van-popup v-model="settingObj.cableChannels.laying" round position="bottom">
            <van-picker
              show-toolbar
              title="通道类别"
              value-key="moduletypename"
              :columns="tdlaying"
              @confirm="onConfirmDltdSel(0, $event)"
              @cancel="settingObj.cableChannels.laying = false"
            />
          </van-popup>
        </van-row>
        <!--通道型号-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="cableChannels.model"
            label="通道型号"
            placeholder="请选择通道型号"
            @click="settingObj.cableChannels.model = true"
          />
          <van-popup v-model="settingObj.cableChannels.model" round position="bottom">
            <van-picker
              show-toolbar
              title="通道型号"
              value-key="moduleName"
              :columns="cableChannelsModel"
              @confirm="onConfirmDltdSel(1, $event)"
              @cancel="settingObj.cableChannels.model = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--状态-->
          <van-field
            readonly
            clickable
            :value="cableChannels.state"
            label="状态"
            placeholder="请选择状态"
            @click="settingObj.cableChannels.state = true"
          />
          <van-popup v-model="settingObj.cableChannels.state" round position="bottom">
            <van-picker
              show-toolbar
              title="状态"
              value-key="key"
              :columns="cableChannelsState"
              @confirm="onConfirmDltdSel(2, $event)"
              @cancel="settingObj.cableChannels.state = false"
            />
          </van-popup>
        </van-row>
        <div v-if="isShowNav" class="map-showNav">其他信息</div>
        <van-row v-if="isShowNav">
          <!--经度-->
          <van-field
            v-model="startLngtitude"
            label="起始点经度"
            disabled
          />
        </van-row>
        <van-row v-if="isShowNav">
          <!--纬度-->
          <van-field
            v-model="startLattitude"
            label="起始点纬度"
            disabled
          />
        </van-row>
        <van-row v-if="isShowNav">
          <!--高程-->
          <van-field
            v-model="endLngtitude"
            label="结束点经度"
            disabled
          />
        </van-row>
        <van-row v-if="isShowNav">
          <!--高程-->
          <van-field
            v-model="endLattitude"
            label="结束点纬度"
            disabled
          />
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { apiget ,apipost} from '@/utils/mapRequest'

export default {
  components: {},
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false
    },
    // 编辑的id
    editData: {
      type: Object,
      defaults: () => {
      }
    },
    // 通道型号
    tdxhList: {
      type: Array,
      defaults: () => []
    },
    // 通道类别
    tdlbList: {
      type: Array,
      defaults: () => []
    },
    remodeState: {
      type: Boolean,
      defaults: false
    }
  },
  data() {
    return {
      isFoldArea: false,
      startPointMark: '', // 开始点编号
      endPointMark: '', // 结束点编号
      startLngtitude: 0, // 有起始点存在情况的开始点经度
      startLattitude: 0, // 有起始点存在情况的开始点纬度
      endLngtitude: 0, // 有起始点存在情况的结束点经度
      endLattitude: 0, // 有起始点存在情况的结束点纬度
      settingObj: {
        // 电缆通道
        cableChannels: {
          laying: false, // 通道类别
          model: false, // 通道型号
          state: false // 状态
        }
      },
      // 电缆通道
      cableChannels: {
        name: '线路一', // 线路名称
        laying: '', // 通道类别
        model: '', // 通道型号
        modelId: '', // 通道型号
        state: '新建', // 状态
        imgList: [], // 文件列表
        message: '', // 备注信息
        audioList: [] // 语音列表
      },
      tdlaying: [],
      cableChannelsModel: [],
      cableChannelsState: [], // 电缆通道状态
      lineState: [
        {
          key: '新建',
          value: '新建'
        },
        {
          key: '原有',
          value: '原有'
        }
      ]
    }
  },
  watch: {
    // 监听改前改后状态
    remodeState: {
      handler(newVal) {
        if (newVal) {
          // 改后不显示拆除的数据
          this.cableChannelsState = this.lineState.slice(0, 2)
          this.cableChannels.state = '新建'
        } else {
          // 改前不显示新建的数据
          this.cableChannelsState = this.lineState.slice(1)
          this.cableChannels.state = '原有'
        }
      },
      deep: true,
      immediate: true
    },
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal
      },
      deep: true,
      immediate: true
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal
        if (data.moudleType === 'DLTDHZ') {
          this.getAwaitTowerOrLineType(0, '', 3, '', '', 'DLTD', '', '', '', data.methodName)
          this.cableChannels.imgList = []
          this.cableChannels.audioList = []
          this.cableChannels.name = data.lineName
          this.cableChannels.state = data.state
          this.cableChannels.laying = data.methodName
          this.cableChannels.modelId = data.tdType
          this.cableChannels.model = data.tdTypeSpec
          this.cableChannels.message = data.note
        }
      },
      deep: true
    },
    // 通道类别
    tdlbList: {
      handler(newVal) {
        this.cableChannels.laying = newVal[0].moduletypename
        this.tdlaying = newVal
      },
      deep: true
    },
    // 通道型号
    tdxhList: {
      handler(newVal) {
        this.cableChannelsModel = newVal
        this.cableChannels.model = newVal[0].moduleName
        this.cableChannels.modelId = newVal[0].moduleID
      },
      deep: true
    }
  },
  mounted() {

  },
  methods: {
    backCurrentDom() {
      this.$emit('backCurrentDom')
      // 通道型号
      this.cableChannelsModel = this.tdxhList
      this.cableChannels.model = this.tdxhList[0].moduleName
      this.cableChannels.modelId = this.tdxhList[0].moduleID
      // 通道类别
      this.cableChannels.laying = this.tdlbList[0].moduletypename
      this.tdlaying = this.tdlbList
      this.cableChannels.name = '线路一'
      this.cableChannels.laying = ''
      this.cableChannels.model = ''
      const stateText = this.remodeState ? '新建' : '原有'
      this.cableChannels.state = stateText
      this.cableChannels.imgList = []
      this.cableChannels.message = ''
      this.cableChannels.audioList = []
      this.settingObj.cableChannels.laying = false
      this.settingObj.cableChannels.model = false
      this.settingObj.cableChannels.state = false
    },
    /**
     * 提交数据
     */
    submitData() {
      const parma = {
        type: 16,
        param: this.cableChannels,
        visParam: this.settingObj.cableChannels
      }
      this.$emit('submitChildData', parma)
    },
    showEveryItemSet() {
      this.isFoldArea = !this.isFoldArea
    },
    /**
     * 电缆通道
     */
    onConfirmDltdSel(type, item) {
      const val = item.value
      switch (type) {
        case 0:
          this.cableChannels.laying = item.moduletypename
          this.getTowerOrLineType(29, '', 4, '', item.moduletypekey, '', '', '')
          this.settingObj.cableChannels.laying = false
          break
        case 1:
          this.cableChannels.model = item.moduleName
          this.cableChannels.modelId = item.moduleID
          this.settingObj.cableChannels.model = false
          break
        case 2:
          this.cableChannels.state = val
          this.settingObj.cableChannels.state = false
          break
      }
    },
    getTowerOrLineType(settype, type, moduleType, materialsTypeKey, moduleTypeKey, parentKey, moduleCode, voltage, moduleName) {
      const that = this
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: 'aaf86d47-14b7-4da6-9938-398e476a2a75' // 版本号暂时写死 版本号是后台给的
      }
      apipost('/moduleSelection/selectModuleData', param).then(function(res) {
        if (res.code === 1001) {
          // 电缆通道
          that.cableChannelsModel = res.data
          if (res.data.length === 0) {
            that.cableChannels.model = ''
            that.cableChannels.modelId = ''
          } else {
            that.cableChannels.model = res.data[0].moduleName
            that.cableChannels.modelId = res.data[0].moduleID
          }
        }
      })
    },
    async getAwaitTowerOrLineType(settype, type, moduleType, materialsTypeKey, moduleTypeKey, parentKey, moduleCode, voltage, moduleName, selectVal, selectLevelTwoVal) {
      const that = this
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: 'aaf86d47-14b7-4da6-9938-398e476a2a75' // 版本号暂时写死 版本号是后台给的
      }
      await apipost('/moduleSelection/selectModuleData', param).then(function(res) {
        if (res.code === 1001) {
          switch (settype) {
            case 0:
              // 电缆通道通道类别
              that.tdlaying = res.data
              let tdlbTyype
              for (const j in res.data) {
                if (selectVal === res.data[j].moduletypename) {
                  tdlbTyype = res.data[j].moduletypekey
                }
              }
              that.getAwaitTowerOrLineType(1, '', 4, '', tdlbTyype, '', '', '')
              break
            case 1:
              // 电缆通道通道型号
              that.cableChannelsModel = res.data
              break
          }
        }
      })
    }
  }
}

</script>

<style lang="sass" scoped>
</style>

