define(["./arrayFill-4513d7ad","./buildModuleUrl-4e1b81e7","./Cartesian2-47311507","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./CylinderGeometryLibrary-aa453214","./when-b60132fc","./GeometryAttribute-3a88ba31","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4","./PrimitiveType-a54dc62f","./Math-119be1a3","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./FeatureDetection-c3b71206","./Cartesian4-3ca25aab"],(function(e,t,i,a,r,n,o,u,s,f,d,b,l,c,m,p,y,_,h){"use strict";var v=new i.Cartesian2;function A(e){var t=(e=u.defaultValue(e,u.defaultValue.EMPTY_OBJECT)).length,i=e.topRadius,a=e.bottomRadius,r=u.defaultValue(e.slices,128),n=Math.max(u.defaultValue(e.numberOfVerticalLines,16),0);this._length=t,this._topRadius=i,this._bottomRadius=a,this._slices=r,this._numberOfVerticalLines=n,this._offsetAttribute=e.offsetAttribute,this._workerName="createCylinderOutlineGeometry"}A.packedLength=6,A.pack=function(e,t,i){return i=u.defaultValue(i,0),t[i++]=e._length,t[i++]=e._topRadius,t[i++]=e._bottomRadius,t[i++]=e._slices,t[i++]=e._numberOfVerticalLines,t[i]=u.defaultValue(e._offsetAttribute,-1),t};var R={length:void 0,topRadius:void 0,bottomRadius:void 0,slices:void 0,numberOfVerticalLines:void 0,offsetAttribute:void 0};return A.unpack=function(e,t,i){t=u.defaultValue(t,0);var a=e[t++],r=e[t++],n=e[t++],o=e[t++],s=e[t++],f=e[t];return u.defined(i)?(i._length=a,i._topRadius=r,i._bottomRadius=n,i._slices=o,i._numberOfVerticalLines=s,i._offsetAttribute=-1===f?void 0:f,i):(R.length=a,R.topRadius=r,R.bottomRadius=n,R.slices=o,R.numberOfVerticalLines=s,R.offsetAttribute=-1===f?void 0:f,new A(R))},A.createGeometry=function(r){var c=r._length,m=r._topRadius,p=r._bottomRadius,y=r._slices,_=r._numberOfVerticalLines;if(!(c<=0||m<0||p<0||0===m&&0===p)){var h,A=2*y,R=o.CylinderGeometryLibrary.computePositions(c,m,p,y,!1),C=2*y;if(_>0){var G=Math.min(_,y);h=Math.round(y/G),C+=G}var O,V=b.IndexDatatype.createTypedArray(A,2*C),g=0;for(O=0;O<y-1;O++)V[g++]=O,V[g++]=O+1,V[g++]=O+y,V[g++]=O+1+y;if(V[g++]=y-1,V[g++]=0,V[g++]=y+y-1,V[g++]=y,_>0)for(O=0;O<y;O+=h)V[g++]=O,V[g++]=O+y;var L=new f.GeometryAttributes;L.position=new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:R}),v.x=.5*c,v.y=Math.max(p,m);var w=new t.BoundingSphere(a.Cartesian3.ZERO,i.Cartesian2.magnitude(v));if(u.defined(r._offsetAttribute)){c=R.length;var D=new Uint8Array(c/3),E=r._offsetAttribute===d.GeometryOffsetAttribute.NONE?0:1;e.arrayFill(D,E),L.applyOffset=new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:D})}return new s.Geometry({attributes:L,indices:V,primitiveType:l.PrimitiveType.LINES,boundingSphere:w,offsetAttribute:r._offsetAttribute})}},function(e,t){return u.defined(t)&&(e=A.unpack(e,t)),A.createGeometry(e)}}));
