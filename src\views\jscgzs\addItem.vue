<template>
  <el-dialog
    :title="this.dataObj.info[currentIndex].name"
    :close-on-click-modal="false"
    :visible.sync="visible"
    lock-scroll
    width="40%"
    center
    @close="cancleForm()"
  >
    <div class="">
      <el-upload
        style="display: inline-block; margin-right: 16px"
        class="upload-demo"
        ref="upload"
        :action="url + '/tDtfFileModule/fileUpload'"
        multiple
        accept=""
        :data="getformData()"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :on-change="beforeUpload"
        :on-success="handleSuccess"
        :on-error="handleError"
        :file-list="fileList"
        :before-upload="beforeAvatarUpload"
        :on-progress="onProgress"
        :show-file-list="false"
      >
        <el-button slot="trigger" class="blue-btn">上传</el-button>
      </el-upload>
      <el-button type="success" @click="download(1)">下载</el-button>
    </div>
    <div class="table-content mt16">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        style="width: 100%"
        :header-cell-style="{
          'text-align': 'center',
          background: '#f8f8f9',
          // background: '#fff',
          color: '#000',
        }"
        :cell-style="{ 'text-align': 'center' }"
        :row-class-name="xxxTableRowClassName"
      >
        <el-table-column
          align="center"
          prop="name"
          show-overflow-tooltip
          label="文件"
          width=""
        >
        </el-table-column>
        <el-table-column prop="name" align="center" label="操作" width="100">
          <template slot-scope="scope">
            <el-button style="color: #fa3131" type="text" @click="delChange()"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <preview v-if="dialogFormVisible" ref="preview"></preview>
  </el-dialog>
</template>
<script>
import { getJscgDetail, jscgDownloadId, jscgFileDel } from '@/api/api'
import preview from './preview.vue'
import qs from 'qs'
// import axios from "axios";
import { resquest } from '@/api/api'
// import Cookies from "js-cookie";

export default {
  components: {
    preview,
  },
  props: {
    ProCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      visible: false,
      form: {},
      url: resquest,
      formData: {},
      dataObj: null,
      rules: {
        spbt: [
          {
            required: true,
            message: '请选择文件名称',
            trigger: 'blur',
          },
        ],
      },
      tableData: [
        // {
        //   name: '名称',
        //   fileList: [
        //     { name: 'food.jpeg', url: '' },
        //     { name: 'food2.jpeg', url: '' },
        //   ],
        //   flag: 1,
        // },
        // {
        //   name: '名称',
        //   fileList: [
        //     { name: 'food.jpeg', url: '' },
        //     { name: 'food2.jpeg', url: '' },
        //   ],
        //   flag: 2,
        // },
      ],
      isDetail: '',
      loading: false,
      fileList: [],
      // ProCode: '',
      // url: resquest + "/Spgl/addSp",
      // token
      dialogFormVisible: false,
      currentIndex: '',
      dataList: [],
    }
  },
  mounted() {},
  methods: {
    // 斑马纹效果
    xxxTableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 == 0) {
        return ''
      } else {
        return 'statistics-warning-row'
      }
    },
    getformData() {
      console.log(this.dataObj.info[this.currentIndex].flag)
      return {
        // flag: this.dataObj.info[i].flag,
        flag: this.dataObj.info[this.currentIndex].flag,
        proCode: this.ProCode,
      }
    },
    cancleForm() {
      // this.$refs['form'].resetFields()
      this.visible = false
      this.$emit('getList')
    },

    dataFormSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return
        console.log('haha')
      })
    },
    init(row, i) {
      // this.ProCode = ProCode
      this.currentIndex = i
      this.visible = true // 打开弹窗
      // this.isDetail = isDetail || false; // isDetail为true就是详情,没有isDetail就是false为编辑
      this.$nextTick(() => {
        // this.$refs['form'].resetFields() // 清空表单
        // 如果有id就是编辑查看，这里的请求用于做数据回填
      })
      this.dataObj = JSON.parse(JSON.stringify(row))
      console.log(this.dataObj)
      this.getData()
    },
    getData() {
      this.loading = true
      let data = {
        ProCode: this.ProCode,
        xh: this.dataObj.index,
      }
      getJscgDetail(data)
        .then((res) => {
          this.tableData = res.data.data.tableData[this.currentIndex].fileList
          this.dataList = res.data.data.tableData
          console.log(this.dataList)
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    download(type) {
      // this.btnloading = true
      // let data = {
      //   proCode: '1806CB210007-001',
      //   flag: row.index,
      // }
      let url =
        type == 1
          ? `${this.url}/tDtfFileModule/download?proCode=${this.ProCode}&flag=${
              this.dataList[this.currentIndex].index
            }`
          : ''
      console.log(url)
      if (!url) return this.$message.info('暂无文件')
      window.open(url)
      // config.data = qs.stringify(config.data)
      // jscgDownloadId(data)
      //   .then((res) => {
      //     this.$message.success('下载成功')
      //     this.btnloading = false
      //   })
      //   .finally(() => {
      //     this.btnloading = false
      //   })
      // this.axios({
      //   method: 'post',
      //   url: resquest + '/tDtfFileModule/download',
      //   data: qs.stringify(data),
      // }).then((res) => {
      //   // 假设 data 是返回来的二进制数据
      //   const data = res
      //   const url = window.URL.createObjectURL(
      //     new Blob([data], {
      //       type:
      //         'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      //     })
      //   )
      //   const link = document.createElement('a')
      //   link.style.display = 'none'
      //   link.href = url
      //   link.setAttribute('download', 'excel.xlsx')
      //   document.body.appendChild(link)
      //   link.click()
      //   document.body.removeChild(link)
      // })
    },
    delChange() {
      // this.loading = true
      let data = {
        // ProCode: '1806CB210007-001',
        id: this.tableData[this.currentIndex].id,
      }
      jscgFileDel(data)
        .then((res) => {
          if (res) {
            this.getData()
            this.$message.success('删除成功')
          }
          // this.loading = false
        })
        .catch(() => {
          // this.loading = false
        })
    },
    // 预览
    previewHandle(e) {
      return
      if (e.name.indexOf('.pdf') == -1) {
        this.$message.info('暂只支持在线预览pdf格式文件')
        return
      }
      this.dialogFormVisible = true // 控制弹出框显示
      this.$nextTick(() => {
        this.$refs.preview.init(e) // init（）是子组件函数
      })
    },
    submitUpload() {
      this.$refs.upload.submit()
    },
    handleRemove(file, fileList) {},
    handlePreview(file) {},
    beforeUpload(file, fileList) {
      // this.form.file = file.raw
      console.log('beforeUpload', file, fileList)
      if (file.status === 'ready') {
        this.loading = true
      }
    },
    beforeAvatarUpload(file) {
      return new Promise((resolve, reject) => {
        const isType =
          /\.(wmv|rm|rmvb|mov|mp4|flv|3gp|mkv|avi|f4v|webv|mepg)$/.test(
            file.type
          )
        const isLt2M = file.size / 1024 / 1024 < 200
        //文件上传之前的校验
        // if (!isType) {
        //   // 限制文件类型校验
        //   this.$message.error('视频上传格式不对！')
        //   return reject(false)
        // } else
        if (!isLt2M) {
          this.$message.error('文件大小不能超过 200MB!')
          return reject(false)
        } else {
          resolve(true)
        }
      })
    },
    // 上传中
    onProgress(event, file, fileList) {
      console.log('onProgress', event, file, fileList)
    },
    //上传成功
    handleSuccess(response, file, fileList) {
      this.loading = false
      console.info(fileList, response, 'fileList')
      this.getData()
    },
    // 上传失败
    handleError(err, file, fileList) {
      console.log(file, fileList)
      console.log('error', err)
      this.$message.error('上传失败')
      this.loading = false
    },
  },
}
</script>
<style lang="scss" scoped>
.file-item {
}

.el-upload-list__item:hover {
  background-color: rgba(230, 234, 239, 0.8);
}

.el-upload-list__item:first-child {
  margin-top: 0;
}

.el-icon-close {
  color: red;
}
</style>
