<template>
  <div>
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div class="pro-leftTitle">
            <span />
            低压下户绘制
            <div
              v-if="!showBackAdds"
              class="maps-zhedNav"
              @click="showEveryItemSet"
            >
              <img
                v-show="!isFoldArea"
                class="mapites-zhed"
                :src="require('@/assets/' + 'map/settingImg/zhedie.png')"
                alt=""
              />
              <img
                v-show="isFoldArea"
                class="mapites-zhed"
                :src="require('@/assets/' + 'map/settingImg/zkzhedie.png')"
                alt=""
              />
            </div>
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img
            class="settingImg"
            :src="require('@/assets/' + 'map/settingImg/useSetting.png')"
            alt=""
          />
          <p>启用</p>
        </div>
      </div>
      <div v-show="isFoldArea" class="pro-addForm">
        <div class="map-showNav">低压下户基本信息</div>
        <van-row>
          <van-field
            v-model="lowDoor.mark"
            label="编号"
            placeholder="请输入低压下户编号"
          />
        </van-row>
        <div class="map-showNav">低压下户选型信息</div>
        <!--下户分类-->
        <van-row>
          <!--下户分类-->
          <van-field
            readonly
            clickable
            :value="lowDoor.classify"
            label="下户分类"
            placeholder="请选择下户分类"
            @click="settingObj.lowDoor.classifyVis = true"
          />
          <van-popup v-model="settingObj.lowDoor.classifyVis" position="bottom">
            <van-picker
              show-toolbar
              title="下户分类"
              value-key="key"
              :columns="classify"
              @confirm="onConfirmDyxhSel(0, $event)"
              @cancel="settingObj.lowDoor.classifyVis = false"
            />
          </van-popup>
        </van-row>
        <!--电压等级-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="lowDoor.voltage"
            label="电压等级"
            placeholder="选择电压等级"
            @click="settingObj.lowDoor.voltageVis = true"
          />
          <van-popup v-model="settingObj.lowDoor.voltageVis" position="bottom">
            <van-picker
              show-toolbar
              title="电压等级"
              value-key="key"
              :columns="lowDoorVoltage"
              @confirm="onConfirmDyxhSel(1, $event)"
              @cancel="settingObj.lowDoor.voltageVis = false"
            />
          </van-popup>
        </van-row>
        <!--下户方案-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="lowDoor.plan"
            label="下户方案"
            placeholder="选择下户方案"
            @click="settingObj.lowDoor.planVis = true"
          />
          <van-popup v-model="settingObj.lowDoor.planVis" position="bottom">
            <van-picker
              show-toolbar
              title="下户方案"
              value-key="name"
              :columns="lowDoorPlan"
              @confirm="onConfirmDyxhSel(2, $event)"
              @cancel="settingObj.lowDoor.planVis = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--下户状态-->
          <van-field
            readonly
            clickable
            :value="lowDoor.state"
            label="下户状态"
            placeholder="选择下户状态"
            @click="settingObj.lowDoor.stateVis = true"
          />
          <van-popup v-model="settingObj.lowDoor.stateVis" position="bottom">
            <van-picker
              show-toolbar
              title="下户状态"
              value-key="key"
              :columns="cableLineState"
              @confirm="onConfirmDyxhSel(3, $event)"
              @cancel="settingObj.lowDoor.stateVis = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--下户线长度-->
          <van-field
            v-model="lowDoor.lineLength"
            label="下户线长(m)"
            placeholder="请输入下户线长度"
          />
        </van-row>
        <van-row>
          <!--户表类型-->
          <van-field
            readonly
            clickable
            :value="lowDoor.householdType"
            label="户表类型"
            placeholder="请选择户表类型"
            @click="settingObj.lowDoor.householdType = true"
          />
          <van-popup
            v-model="settingObj.lowDoor.householdType"
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="户表类型"
              value-key="key"
              :columns="lowDoorHb"
              @confirm="onConfirmDyxhSel(5, $event)"
              @cancel="settingObj.lowDoor.householdType = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--户表状态-->
          <van-field
            readonly
            clickable
            :value="lowDoor.householdState"
            label="户表状态"
            placeholder="选择户表状态"
            @click="settingObj.lowDoor.householdState = true"
          />
          <van-popup
            v-model="settingObj.lowDoor.householdState"
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="户表状态"
              value-key="key"
              :columns="cableLineState"
              @confirm="onConfirmDyxhSel(7, $event)"
              @cancel="settingObj.lowDoor.householdState = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--户表表位-->
          <van-field
            v-model="lowDoor.householdSite"
            label="户表表位"
            placeholder="请输入户表表位"
          />
        </van-row>
        <van-row>
          <!--户表表数-->
          <van-field
            v-model="lowDoor.householdNum"
            label="户表表数"
            placeholder="请输入户表表数"
          />
        </van-row>
        <!--下户线型号-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="lowDoor.lineModel"
            label="下户线型号"
            placeholder="选择下户线型号"
            @click="settingObj.lowDoor.lineModelVis = true"
          />
          <van-popup
            v-model="settingObj.lowDoor.lineModelVis"
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="下户线型号"
              value-key="moduleName"
              :columns="dyxhxhxxh"
              @confirm="onConfirmDyxhSel(4, $event)"
              @cancel="settingObj.lowDoor.lineModelVis = false"
            />
          </van-popup>
        </van-row>
        <!--户表型号-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="lowDoor.householdModel"
            label="户表型号"
            placeholder="请选择户表型号"
            @click="settingObj.lowDoor.householdModel = true"
          />
          <van-popup
            v-model="settingObj.lowDoor.householdModel"
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="户表型号"
              value-key="moduleName"
              :columns="dyxhHbxh"
              @confirm="onConfirmDyxhSel(6, $event)"
              @cancel="settingObj.lowDoor.householdModel = false"
            />
          </van-popup>
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { apiget,apipost } from "@/utils/mapRequest";

export default {
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false,
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的id
    editData: {
      type: Object,
      defaults: () => {},
    },
    // 杆塔型号默认值
    gtList: {
      type: Array,
      defaults: () => [],
    },
    // 导线型号默认值
    dxList: {
      type: Array,
      defaults: () => [],
    },
    remodeState: {
      type: Boolean,
      defaults: false,
    },
  },
  watch: {
    // 监听改前改后状态
    remodeState: {
      handler(newVal) {
        if (newVal) {
          // 改后不显示拆除的数据
          this.cableLineState = this.lineState.slice(0, 2);
          this.lowDoor.state = "新建";
          this.lowDoor.householdState = "新建";
        } else {
          // 改前不显示新建的数据
          this.cableLineState = this.lineState.slice(1);
          this.lowDoor.state = "原有";
          this.lowDoor.householdState = "原有";
        }
      },
      deep: true,
      immediate: true,
    },
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal;
      },
      deep: true,
      immediate: true,
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal;
        if (newVal.moduleType === "DYXHHZ") {
          this.lowDoor.imgList = [];
          this.lowDoor.audioList = [];
          // 220v和330v都按照0.4kv去查
          const valltegedyxh =
            data.listArr[0].voltage === "10kV" ? "10kV" : "0.4kV";
          // 查询下户线的下户方案 户表型号
          this.getAwaitTowerOrLineType(
            7,
            "",
            4,
            "",
            "DY_DDX",
            "",
            "",
            valltegedyxh
          );
          this.getAwaitTowerOrLineType(
            8,
            "",
            4,
            "",
            data.hbModuleId,
            "",
            "",
            ""
          );
          this.lowDoor.classify = data.listArr[0].xhType;
          this.lowDoor.voltage = data.listArr[0].voltage;
          this.lowDoor.plan = data.listArr[0].xhProgramSpec;
          this.lowDoor.planId = data.listArr[0].xhProgram;
          this.lowDoor.state = data.hbState;
          this.lowDoor.lineModel = data.listArr[0].lineModel;
          this.lowDoor.lineModelId = data.listArr[0].moduleId;
          this.lowDoor.lineLength = data.listArr[0].xhLineLength;
          this.lowDoor.householdType = data.hbType;
          this.lowDoor.householdModel = data.hbModuleSpec;
          this.lowDoor.householdModelId = data.hbModuleId;
          this.lowDoor.householdState = data.listArr[0].state;
          this.lowDoor.householdSite = data.hbLocation;
          this.lowDoor.householdNum = data.hbNum;
          this.lowDoor.mark = data.mark; // 编号
          this.lowDoor.message = data.note;
          for (const k in data.imgList) {
            const objs = {
              url: data.imgList[k].path,
              isImage: true,
              isSaveReport: data.imgList[k].isSaveReport,
            };
            this.lowDoor.imgList.push(objs);
          }
          for (const s in data.voiceList) {
            const objs = {
              content: data.voiceList[s].path,
            };
            this.lowDoor.audioList.push(objs);
          }
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      isFoldArea: false,
      cableLineState: [], // 电缆线路状态
      lineState: [
        {
          key: "新建",
          value: "新建",
        },
        {
          key: "原有",
          value: "原有",
        },
        {
          key: "拆除",
          value: "拆除",
        },
      ],
      settingObj: {
        // 低压下户
        lowDoor: {
          classifyVis: false, // 下户分类
          voltageVis: false, // 电压等级
          planVis: false, // 下户方案
          stateVis: false, // 状态
          lineModelVis: false, // 下户线型号
          lineLength: false, // 下户线长度
          householdType: false, // 户表类型
          householdModel: false, // 户表型号
          householdState: false, // 户表状态
        },
      },

      lowDoorVoltage: [
        {
          key: "380V",
          value: "380V",
        },
        {
          key: "220V",
          value: "220V",
        },
      ], // 低压下户电压
      classify: [
        {
          key: "下户线横担",
          value: "下户线横担",
          type: "XHXHD",
        },
        {
          key: "沿墙/电杆挂敷",
          value: "沿墙/电杆挂敷",
          type: "YQDGGF",
        },
        {
          key: "下户线户表",
          value: "下户线户表",
          type: "XHXHD",
        },
      ], // 下户分类
      // 低压下户
      lowDoor: {
        classify: "下户线横担", // 下户分类
        voltage: "380V", // 电压等级
        plan: "", // 下户方案
        planId: "", // 下户方案id
        state: "新建", // 状态
        lineModel: "", // 下户线型号
        lineModelId: "", // 下户线型号id
        lineLength: "25", // 下户线长度
        householdType: "单相表箱", // 户表类型
        householdModel: "", // 户表型号
        householdModelId: "", // 户表型号
        householdState: "新建", // 户表状态
        householdSite: "1", // 户表表位
        householdNum: "1", // 户表表数
        imgList: [], // 文件列表
        message: "", // 备注信息
        audioList: [], // 语音列表
      },
      lowDoorPlan: [], // 下户方案 这里从后台读
      lowDoorHb: [
        {
          key: "单相表箱",
          value: "单相表箱",
          type: "DY_DXBX",
        },
        {
          key: "三相表箱",
          value: "三相表箱",
          type: "DY_SXBX",
        },
      ],
      dyxhxhxxh: [], // 低压下户下户线型号
      dyxhHbxh: [], // 低压下路户表型号
    };
  },
  mounted() {},
  methods: {
    showEveryItemSet() {
      this.isFoldArea = !this.isFoldArea;
    },
    /**
     * 提交数据
     */
    submitData() {
      const parma = {
        type: 5,
        param: this.lowDoor,
        visParam: this.settingObj.lowDoor,
      };
      this.$emit("submitChildData", parma);
    },
    backCurrentDom() {
      this.$emit("backCurrentDom");
      // 低压下户
      this.lowDoor.classify = "下户线横担";
      this.lowDoor.voltage = "380V";
      this.lowDoor.plan = "";
      this.lowDoor.planId = "";
      const stateText = this.remodeState ? "新建" : "原有";
      this.lowDoor.state = stateText;
      this.lowDoor.lineModel = "";
      this.lowDoor.lineModelId = "";
      this.lowDoor.lineLength = "25";
      this.lowDoor.householdType = "单相表箱";
      this.lowDoor.householdTypeId = "";
      this.lowDoor.householdModel = "";
      this.lowDoor.householdModelId = "";
      this.lowDoor.householdState = stateText;
      this.lowDoor.householdSite = "1";
      this.lowDoor.householdNum = "1";
      this.lowDoor.imgList = [];
      this.lowDoor.message = "";
      this.lowDoor.audioList = [];
      this.settingObj.lowDoor.classifyVis = false;
      this.settingObj.lowDoor.voltageVis = false;
      this.settingObj.lowDoor.planVis = false;
      this.settingObj.lowDoor.stateVis = false;
      this.settingObj.lowDoor.lineModelVis = false;
      this.settingObj.lowDoor.lineLength = false;
      this.settingObj.lowDoor.householdType = false;
      this.settingObj.lowDoor.householdModel = false;
      this.settingObj.lowDoor.householdState = false;
    },
    /**
     * 获取语音数据
     */
    getAudioData(data) {
      this.lowDoor.audioList = data.aduioList;
    },
    /**
     * 获取上传图片数据
     */
    getImgData(data) {
      this.lowDoor.imgList = data.imgList;
    },

    getMsgData(data) {
      this.lowDoor.message = data.message;
    },
    getFirstTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          switch (settype) {
            case 0:
              if (voltage === "10kV") {
                // 主线路杆塔型号
                that.towerModel = res.data.tenSNG;
                that.mainLine.towerModel = res.data.tenSNG[0].name;
                that.mainLine.towerModelId = res.data.tenSNG[0].id;
              } else {
                // 主线路杆塔型号
                that.towerModel = res.data.lowSNG;
                that.mainLine.towerModel = res.data.lowSNG[0].name;
                that.mainLine.towerModelId = res.data.lowSNG[0].id;
              }
              break;
            case 1:
              if (voltage === "10kV") {
                // 主线路导线型号
                that.lineType = res.data.tenDx;
                that.mainLine.listArr[0].lineModel = res.data.tenDx[0].name;
                that.mainLine.listArr[0].lineModelId = res.data.tenDx[0].id;
              } else {
                // 主线路导线型号
                that.lineType = res.data.lowDx;
                that.mainLine.listArr[0].lineModel = res.data.lowDx[0].name;
                that.mainLine.listArr[0].lineModelId = res.data.lowDx[0].id;
              }
              break;
            case 3:
              // 下户方案
              that.lowDoor.plan = res.data[0].spec;
              that.lowDoor.planId = res.data[0].materialsprojectid;
              that.lowDoorPlan = res.data;
              break;
            case 4:
              // 下户线型号
              that.lowDoor.lineModel = res.data[0].moduleName;
              that.lowDoor.lineModelId = res.data[0].moduleID;
              that.dyxhxhxxh = res.data;
              break;
            case 5:
              // 下户分类护表型号
              that.lowDoor.householdModel = res.data[0].moduleName;
              that.lowDoor.householdModelId = res.data[0].moduleID;
              that.dyxhHbxh = res.data;
              break;
          }
        }
      });
    },
    /**
     * 低压下户
     */
    onConfirmDyxhSel(type, item) {
      const val = item.value;
      switch (type) {
        case 0:
          this.lowDoor.classify = val;
          this.settingObj.lowDoor.classifyVis = false;
          break;
        case 1:
          this.lowDoor.voltage = val;
          this.settingObj.lowDoor.voltageVis = false;
          break;
        case 2:
          this.lowDoor.plan = item.spec;
          this.lowDoor.planId = item.materialsprojectid;
          this.settingObj.lowDoor.planVis = false;
          break;
        case 3:
          this.lowDoor.state = val;
          this.settingObj.lowDoor.stateVis = false;
          break;
        case 4:
          this.lowDoor.lineModel = item.moduleName;
          this.lowDoor.lineModelId = item.moduleID;
          this.settingObj.lowDoor.lineModelVis = false;
          break;
        case 5:
          this.lowDoor.householdType = val;
          this.getTowerOrLineType("", 4, "", item.type, "", "", "");
          this.settingObj.lowDoor.householdType = false;
          break;
        case 6:
          this.lowDoor.householdModel = item.moduleName;
          this.lowDoor.householdModelId = item.moduleID;
          this.settingObj.lowDoor.householdModel = false;
          break;
        case 7:
          this.lowDoor.householdState = val;
          this.settingObj.lowDoor.householdState = false;
          break;
      }
    },
    getTowerOrLineType(
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          that.lowDoor.householdModel = res.data[0].moduleName;
          that.lowDoor.householdModelId = res.data[0].moduleID;
          that.settingObj.dyxhHbxh = res.data;
        }
      });
    },
    getAwaitTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName,
      selectVal,
      selectLevelTwoVal
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        switch (settype) {
          case 0:
            // 主线路杆塔型号
            if (voltage === "10kV") {
              that.towerModel = res.data.tenSNG;
            } else {
              that.towerModel = res.data.lowSNG;
            }
            break;
          case 1:
            // 主线路导线型号
            if (voltage === "10kV") {
              that.lineType = res.data.tenDx;
            } else {
              that.lineType = res.data.lowDx;
            }
            break;
          case 7:
            // 下户线型号
            that.settingObj.dyxhxhxxh = res.data;
            break;
          case 8:
            // 下户分类护表型号
            that.settingObj.dyxhHbxh = res.data;
            break;
        }
      });
    },
  },
};
</script>

<style lang="sass" scoped>
</style>

