<template>
  <div>
    <!--电缆分支箱-->
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div v-if="showBackAdds" class="maps-zhedNav" @click="backCurrentDom">
            <img
              class="mapites-backImg"
              :src="require('@/assets/' + 'map/settingImg/backAdds.png')"
              alt=""
            />
          </div>
          <span />
          电缆分支箱
          <div
            v-if="!showBackAdds"
            class="maps-zhedNav"
            @click="showEveryItemSet"
          >
            <img
              v-show="!isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zhedie.png')"
              alt=""
            />
            <img
              v-show="isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zkzhedie.png')"
              alt=""
            />
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img
            class="settingImg"
            :src="require('@/assets/' + 'map/settingImg/useSetting.png')"
            alt=""
          />
          <p>启用</p>
        </div>
      </div>
      <div v-show="isFoldArea" class="pro-addForm">
        <div class="map-showNav">基本信息</div>
        <van-row>
          <van-field
            v-model="cableBranch.mark"
            label="分支箱编号"
            placeholder="请输入电缆分支箱编号"
          />
        </van-row>
        <van-row>
          <!--电缆分支箱设备编号-->
          <van-field
            v-model="cableBranch.number"
            label="设备编号"
            placeholder="请输入设备编号"
          />
        </van-row>
        <div class="map-showNav">选型信息</div>
        <van-row>
          <!--电缆分支箱电压等级-->
          <van-field
            readonly
            clickable
            :value="cableBranch.voltage"
            label="电压等级"
            placeholder="请选择电压等级"
            @click="settingObj.cableBranch.voltage = true"
          />
          <van-popup
            v-model="settingObj.cableBranch.voltage"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="电压等级"
              value-key="key"
              :columns="voltage"
              @confirm="onConfirmDlfzxSel(0, $event)"
              @cancel="settingObj.cableBranch.voltage = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--电缆分支箱状态-->
          <van-field
            readonly
            clickable
            :value="cableBranch.state"
            label="状态"
            placeholder="请选择状态"
            @click="settingObj.cableBranch.state = true"
          />
          <van-popup
            v-model="settingObj.cableBranch.state"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="状态"
              value-key="key"
              :columns="cableLineState"
              @confirm="onConfirmDlfzxSel(3, $event)"
              @cancel="settingObj.cableBranch.state = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--电缆分支箱线路名称-->
          <van-field
            v-model="cableBranch.lineName"
            label="线路名称"
            placeholder="请输入线路名称"
          />
        </van-row>
        <van-row>
          <!--电缆分支箱-->
          <van-field
            readonly
            clickable
            :value="cableBranch.branch"
            label="电缆分支箱"
            placeholder="请选择电缆分支箱"
            @click="settingObj.cableBranch.branch = true"
          />
          <van-popup
            v-model="settingObj.cableBranch.branch"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="电缆分支箱"
              value-key="moduleName"
              :columns="cableBranchBox"
              @confirm="onConfirmDlfzxSel(2, $event)"
              @cancel="settingObj.cableBranch.branch = false"
            />
          </van-popup>
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { apiget,apipost } from "@/utils/mapRequest";
export default {
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false,
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的id
    editData: {
      type: Object,
      defaults: () => {},
    },
    remodeState: {
      type: Boolean,
      defaults: false,
    },
  },
  data() {
    return {
      isFoldArea: false, // 是否展开区域
      settingObj: {
        // 电缆分支箱
        cableBranch: {
          voltage: false, // 电压等级
          branch: false, // 电缆分支箱
          state: false, // 状态
        },
      },
      // 电缆分支箱
      cableBranch: {
        voltage: "10kV", // 电压等级
        branchId: "", // 电缆分支箱id
        state: "新建", // 状态
        number: "dlfzx001", // 设备编号
        lineName: "线路一", // 线路名称
        mark: "", // 编号
        imgList: [], // 文件列表
        message: "", // 备注信息
        audioList: [], // 语音列表
      },
      cableBranchBox: [], // 电缆分支箱 从后台读
      cableLineState: [], // 电缆线路状态
      lineState: [
        {
          key: "新建",
          value: "新建",
        },
        {
          key: "原有",
          value: "原有",
        },
        {
          key: "拆除",
          value: "拆除",
        },
      ],
      voltage: [
        {
          key: "10kV",
          value: "10kV",
        },
        {
          key: "0.4kV",
          value: "0.4kV",
        },
      ], // 电压等级
    };
  },
  watch: {
    remodeState: {
      handler(newVal) {
        if (newVal) {
          // 改后不显示拆除的数据
          this.cableLineState = this.lineState.slice(0, 2);
          this.cableBranch.state = "新建";
        } else {
          // 改前不显示新建的数据
          this.cableLineState = this.lineState.slice(1);
          this.cableBranch.state = "原有";
        }
      },
      deep: true,
      immediate: true,
    },
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal;
      },
      deep: true,
      immediate: true,
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal;
        if (newVal.moduleType === "DLFZXHZ") {
          // 根据电压判断ModuleTypeKey
          const ModuleTypeKey = data.voltage === "10kV" ? "10DLDJX" : "DLDJX";
          this.getAwaitTowerOrLineType(
            "",
            4,
            "",
            ModuleTypeKey,
            "",
            "",
            data.voltage
          );
          this.cableBranch.imgList = [];
          this.cableBranch.audioList = [];
          this.cableBranch.voltage = data.voltage;
          this.cableBranch.lineName = data.lineId;
          this.cableBranch.branchId = data.dlfzxName;
          this.cableBranch.branch = data.dlfzxNameSpec;
          this.cableBranch.state = data.state;
          this.cableBranch.number = data.equipBh;
          this.cableBranch.mark = data.mark; // 编号
          this.cableBranch.message = data.note;
          for (const k in data.imgList) {
            const objs = {
              url: data.imgList[k].path,
              isImage: true,
              isSaveReport: data.imgList[k].isSaveReport,
            };
            this.cableBranch.imgList.push(objs);
          }
          for (const s in data.voiceList) {
            const objs = {
              content: data.voiceList[s].path,
            };
            this.cableBranch.audioList.push(objs);
          }
        }
      },
      deep: true,
    },
  },
  mounted() {
    /* 电缆分支箱*/
    this.getFirstTowerOrLineType("", 4, "", "10DLDJX", "", "", "10kV");
  },
  methods: {
    showEveryItemSet() {
      this.isFoldArea = !this.isFoldArea;
    },
    /**
     * 提交数据
     */
    submitData() {
      const parma = {
        type: 15,
        param: this.cableBranch,
        visParam: this.settingObj.cableBranch,
      };
      this.$emit("submitChildData", parma);
    },
    backCurrentDom() {
      this.$emit("backCurrentDom");
      this.getFirstTowerOrLineType("", 4, "", "10DLDJX", "", "", "10kV");
      // 电缆分支箱
      this.cableBranch.voltage = "10kV";
      this.cableBranch.branch = "";
      const stateText = this.remodeState ? "新建" : "原有";
      this.cableBranch.state = stateText;
      this.cableBranch.number = "dlfzx001";
      this.cableBranch.lineName = "线路一";
      this.cableBranch.imgList = [];
      this.cableBranch.message = "";
      this.cableBranch.audioList = [];
      this.settingObj.cableBranch.voltage = false;
      this.settingObj.cableBranch.branch = false;
      this.settingObj.cableBranch.state = false;
    },
    /**
     * 电缆分支箱
     */
    onConfirmDlfzxSel(type, item) {
      const val = item.value;
      switch (type) {
        case 0:
          const moudeleType = item.value === "10kV" ? "10DLDJX" : "DLDJX";
          this.getTowerOrLineType("", 4, "", moudeleType, "", "", item.value);
          this.settingObj.cableBranch.voltage = false;
          break;
        case 2:
          this.cableBranch.branch = item.moduleName;
          this.cableBranch.branchId = item.moduleID;
          this.settingObj.cableBranch.branch = false;
          break;
        case 3:
          this.cableBranch.state = val;
          this.settingObj.cableBranch.state = false;
          break;
      }
    },
    /**
     * 获取语音数据
     */
    getAudioData(data) {
      this.cableBranch.audioList = data.aduioList;
    },
    /**
     * 获取上传图片数据
     */
    getImgData(data) {
      this.cableBranch.imgList = data.imgList;
    },
    /**
     * 获取备注信息
     * @param data
     */
    getMsgData(data) {
      this.cableBranch.message = data.message;
    },
    async getAwaitTowerOrLineType(
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName,
      selectVal,
      selectLevelTwoVal
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      await apipost("/moduleSelection/selectModuleData", param).then(function (
        res
      ) {
        if (res.code === 1001) {
          // 电缆分支箱型号
          that.settingObj.cableBranchBox = res.data;
        }
      });
    },
    getTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          // 电缆分支箱
          that.cableBranchBox = res.data;
          that.cableBranch.branch = res.data[0].moduleName;
          that.cableBranch.branchId = res.data[0].moduleID;
        }
      });
    },
    getFirstTowerOrLineType(
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          // 电缆分支箱
          that.cableBranch.branch = res.data[0].moduleName;
          that.cableBranch.branchId = res.data[0].moduleID;
          that.cableBranchBox = res.data;
        }
      });
    },
  },
};
</script>

<style lang="sass" scoped>
</style>

