<template>
  <div class="vala-main">
    <!-- <div class="grid-content bg-purple-dark">
        <el-row class="cost-header-btn">
          <el-upload
            :limit="1"
            class="upload-demo"
            :auto-upload="false"
            action=""
            :show-file-list="false"
            :on-change="BuildingImport"
            :file-list="fileList"
          >
            <el-button size="small" type="warning">导入模板</el-button>
          </el-upload>
          <div style="">
            <el-button size="small" type="primary" @click="BuildingExport">导出模板</el-button>
          </div>
          <el-button size="small" type="success" @click="OpenTk">一键调价</el-button>
        </el-row>
      </div> -->
    <div class="cost-table">
      <template>
        <el-table
          :data="GetTableList"
          border
          height="calc(150vh)"
          style="width: 100%"
          row-key="bomProjectFeeId"
          default-expand-all
          highlight-current-row
          :header-cell-style="{
            textAlign: 'center',
            background: '#dce0e2',
            color: '#fff',
          }"
          :tree-props="{ children: 'workMenuList', hasChildren: 'hasChildren' }"
        >
          <!-- <el-table-column fixed prop="date" width="50" hidden:true/> -->
          <el-table-column prop="feeName" label="费用名称" width="150" />
          <el-table-column
            prop="feeCode"
            align="center"
            label="代码"
            width="150"
          />
          <el-table-column
            prop="feeCalulatedMode"
            align="center"
            label="计算式"
            width="350"
          />
          <el-table-column
            prop="feeRate"
            align="center"
            label="费率(%)"
            width="100"
          />
          <el-table-column
            prop="feeMoney"
            align="center"
            label="金额"
            width="150"
          />
          <el-table-column prop="feeTxt" align="center" label="备注" />
          <!-- <el-table-column
              fixed="right"
              label="操作"
              width="100"
            >
              <template slot-scope="scope">
                <el-button type="text" style="width: 100%;color: #000000;" size="small" @click="EidtRow(scope.row)">编辑</el-button>
              </template>
            </el-table-column> -->
        </el-table>
      </template>
    </div>
    <el-dialog
      :title="dialog.titles"
      :visible.sync="dialog.Nextvisibles"
      width="30%"
      :before-close="handleClose"
      class="valuDialog"
    >
      <div class="demo-input-suffix">
        <p>费用名称：</p>
        <el-input
          v-model="EitdGcCost.feeName"
          placeholder="请输入内容"
          style="width: 45%"
        />
      </div>
      <div class="demo-input-suffix">
        <p>计算方式：</p>
        <el-input
          v-model="EitdGcCost.feeCalulatedMode"
          placeholder="请输入内容"
          style="width: 45%"
        />
      </div>
      <div class="demo-input-suffix">
        <p>费用率%：</p>
        <el-input
          v-model="EitdGcCost.feeRate"
          placeholder="请输入内容"
          style="width: 45%"
        />
      </div>
      <div class="demo-input-suffix">
        <p>代 码：</p>
        <el-input
          v-model="EitdGcCost.feeCode"
          placeholder="请输入内容"
          style="width: 45%; margin-left: 20px"
        />
      </div>
      <div class="demo-input-suffix">
        <p>备 注：</p>
        <el-input
          v-model="EitdGcCost.feeTxt"
          placeholder="请输入内容"
          style="width: 45%; margin-left: 20px"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <button class="close" @click="dialogVis">取 消</button>
        <button class="primary" @click="EitdGCBtn">保存</button>
      </span>
    </el-dialog>

    <el-dialog
      :title="dialog.title"
      :visible.sync="dialog.Nextvisible"
      width="40%"
      :before-close="handleClose"
      class="valuDialog"
    >
      <div class="vala-addProject">
        <div class="GCCost-box">
          <div class="GCCost-box-font">当前造价</div>
          <input
            v-model="Price.DQ"
            type="text"
            name=""
            value=""
            disabled
            style="background-color: gray"
          />
        </div>
        <div class="GCCost-box">
          <div class="GCCost-box-font">目标造价</div>
          <input
            v-model="Price.ZJ"
            type="text"
            name=""
            value=""
            @blur="onBsp($event)"
          />
        </div>
        <div class="GCCost-box">
          <div class="GCCost-box-font">调价方式</div>
          <input
            v-model="Price.FS"
            type="text"
            name=""
            disabled
            style="background-color: gray"
          />
        </div>
        <div class="GCCost-box">
          <div class="GCCost-box-font">调价比例</div>
          <input
            v-model="Price.BL"
            type="text"
            name=""
            value=""
            disabled
            style="width：46%;background-color:gray;"
          />
          <div class="GCCost-fu">%</div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <button class="close" @click="dialogVis">取 消</button>
        <button class="primary" @click="NextDialogVisible">保存</button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { apipost } from '@/utils/mapRequest'
// import GcCost from '@/api/GcCost'
// import {
//   PriceExport
// } from '@/api/VerifyCode'
export default {
  name: 'jsinfo',
  components: {},
  data () {
    return {
      GetTableList: [
        {
          feeName: '措施项目费',
          feeCode: 'CSF',
          feeCalulatedMode: '措施项目费二',
          feeRate: '100',
          feeMoney: '0',
          feeTxt: ''
        },
        {
          feeName: '分部分项工程费',
          feeCode: 'FBGCF',
          feeCalulatedMode: '建筑工程费+安装工程费',
          feeRate: '100',
          feeMoney: '0',
          feeTxt: ''
        },
        {
          feeName: '其中临时设施费',
          feeCode: 'LSF',
          feeCalulatedMode: '@工程取费.建筑.临时设施费+@工程取费.安装.临时设施费',
          feeRate: '100',
          feeMoney: '0',
          feeTxt: ''
        },
        {
          feeName: '其中安全文明施工费',
          feeCode: 'WMF',
          feeCalulatedMode: '@工程取费.建筑.安全文明施工费+@工程取费.安装.安全文明施工费',
          feeRate: '100',
          feeMoney: '0',
          feeTxt: ''
        },
        {
          feeName: '其中暂估价材料费',
          feeCode: 'ZGCLF',
          feeCalulatedMode: '@建筑.主材暂估价+@安装.主材暂估价',
          feeRate: '100',
          feeMoney: '0',
          feeTxt: ''
        },
        {
          feeName: '最高投标限价',
          feeCode: 'TBJ',
          feeCalulatedMode: 'FBGCF+TBSB+CSF+QTF',
          feeRate: '100',
          feeMoney: '0',
          feeTxt: ''
        },
        {
          feeName: '投标人采购设备费',
          feeCode: 'TBSB',
          feeCalulatedMode: '乙供设备费不含税*(1+工程税率/100)+乙供设备运杂费含税',
          feeRate: '100',
          feeMoney: '0',
          feeTxt: ''
        },
        {
          feeName: '其他项目费',
          feeCode: 'QTF',
          feeCalulatedMode: '其他项目费用',
          feeRate: '100',
          feeMoney: '0',
          feeTxt: ''
        },
        {
          feeName: '其中专业工程暂估',
          feeCode: 'GCZGJ',
          feeCalulatedMode: '@其他项目.GCZGJ',
          feeRate: '100',
          feeMoney: '0',
          feeTxt: ''
        },
        {
          feeName: '其中暂列金额',
          feeCode: 'ZLJE',
          feeCalulatedMode: '@其他项目.ZLJE',
          feeRate: '100',
          feeMoney: '0',
          feeTxt: ''
        },
      ], // 列表数据
      Price: {
        BL: '0',
        FS: '定额系数方式',
        ZJ: '',
        DQ: '',
      },
      input2: '',
      param: {
        deQuotoCoef: '',
        projectId: '',
      },
      EitdGcCost: {
        feeName: '', // 费用名称
        feeCode: '', // 代码
        feeCalulatedMode: '', // 计算方式
        feeRate: '', // 费率
        feeMoney: '', // 金额
        feeTxt: '',
      },
      dialog: {
        Nextvisible: false,
        Nextvisibles: false,
        title: '',
        titles: '',
        addProObj: {
          name: '', // 项目名称
        }, // 新增编辑时候的项目信息
      }, // dialog的所有配置项
      fileList: [], // 导入模板
      ArchitImport: {
        projectId: '', // 项目id
        files: null, // 文件
      },
    }
  },
  mounted () {
    this.GetTableLists()
  },
  methods: {
    GetTableLists () {
      let data = {
        projectId: this.$route.query.projectId
      }
      // apipost('fee/search/', data).then((res) => {
      //   console.log(res)
      //   this.GetTableList = res.data // 拿到最后一个数据
      //   const listPop = this.GetTableList.slice(-1)[0]
      //   if (res.data.length === 0) {
      //   } else {
      //     this.Price.DQ = listPop.feeMoney
      //   }
      // })
    },
    onBsp ($event) {
      const mun = this.Price.ZJ / this.Price.DQ
      if (mun == 'Infinity') {
        this.Price.BL = 0
        this.param.deQuotoCoef = 0
      } else {
        this.Price.BL = mun
        this.param.deQuotoCoef = mun
      }
    },
    OpenTk () {
      this.dialog.Nextvisible = true
      this.dialog.title = '一件调价'
    },
    // 点击保存一件调价弹框
    NextDialogVisible () {
      this.param.projectId = localStorage.getItem('projectId')
      // GcCost.ModifyPrice(this.param).then(res => {
      //   this.$message({
      //     showClose: true,
      //     message: '保存成功',
      //     type: 'success'
      //   })
      //   this.dialog.Nextvisible = false
      // })
    },
    // 编辑提交
    EitdGCBtn () {
      // GcCost.EitdGcCost(this.EitdGcCost).then(res => {
      //   this.$message({
      //     showClose: true,
      //     message: '保存成功',
      //     type: 'success'
      //   })
      //   this.dialog.Nextvisibles = false
      // })
    },
    // 导出
    BuildingExport () {
      const projectId = localStorage.getItem('projectId')
      // PriceExport(projectId).then(res => {
      //   console.log('工程费用导出模板', res.data)
      //   var name = new Date().getTime() + '工程费用导出模板.xlsx'
      //   var blob = new Blob([res.data])
      //   var url = window.URL.createObjectURL(blob)
      //   var aLink = document.createElement('a')
      //   aLink.style.display = 'none'
      //   aLink.href = url
      //   aLink.setAttribute('download', name)
      //   document.body.appendChild(aLink)
      //   aLink.click()
      //   document.body.removeChild(aLink) // 下载完成移除元素
      //   window.URL.revokeObjectURL(url) // 释放掉blob对象
      // })
    },
    // 导入
    BuildingImport (file, fileList) {
      this.ArchitImport.projectId = localStorage.getItem('projectId')
      this.fileList = fileList
      this.ArchitImport.files = this.fileList[0].raw
      console.log(this.ArchitImport)
      // GcCost.PriceImport(this.ArchitImport).then(res => {
      //   this.$message({
      //     message: '导入成功',
      //     type: 'success'
      //   })
      //   this.GetTableLists()
      // })
    },
    EidtRow (row) {
      console.log(row)
      this.EitdGcCost = row
      this.dialog.Nextvisibles = true
      this.dialog.titles = '编辑工程费用'
    },
    handleClose () {
      this.dialog.Nextvisibles = false
      this.dialog.Nextvisible = false
    },
    dialogVis () {
      this.dialog.Nextvisibles = false
      this.dialog.Nextvisible = false
    },
    editInfo (row) { },
  },
}
</script>

<style lang="scss" scoped="scoped">
.vala-main {
  width: 100%;
  height: 100%;
}

.grid-content {
  width: 98%;
  margin-left: 20px;
  margin-top: 20px;
  background: white;
  height: 50px;
  box-shadow: 0 5px 23px #cccccc;
}

.cost-header-btn {
  display: flex;
  padding-top: 10px;
  margin-left: 20px;
}

.el-button {
  margin-right: 10px;
  height: 30px;
}

.cost-table {
  width: 98%;
  margin-left: 20px;
  margin-top: 5px;
}

.vala-addProject {
  .vala-name {
    display: flex;
    border-bottom: 2px solid #ccc;
    padding-bottom: 10px;

    p {
      margin-top: 10px;
      width: 120px;
    }
  }
}

.GCCost-box {
  width: 98%;
  margin-left: 20px;
  display: flex;
  margin-bottom: 20px;

  .GCCost-box-font {
    width: 30%;
    text-align: center;
    background: #51b4f7;
    height: 40px;
    line-height: 40px;
    color: white;
    margin-left: 10%;
  }

  input {
    width: 50%;
  }
}

.GCCost-fu {
  width: 10%;
  height: 40px;
  line-height: 40px;
  margin-left: 2%;
}

.close {
  margin-right: 10px;
}
.demo-input-suffix {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
.demo-input-suffix p {
  line-height: 35px;
}
::v-deep .el-dialog__body {
  padding: 24px !important;
}
::v-deep .el-dialog__footer {
  padding: 16px 24px !important;
}
</style>
