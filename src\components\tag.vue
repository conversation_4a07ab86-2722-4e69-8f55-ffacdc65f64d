<template>
  <div>
    <!-- <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item
        v-for="(item, index) in breadList"
        :key="index"
        :to="{ path: item.path }"
        >{{ item.meta.title }}</el-breadcrumb-item
      >
    </el-breadcrumb> -->

    <div class="mainTitle">
      <!-- <div
        style="
          width: 5px;
          height: 19px;
          background: #526ade;
          margin-right: 8px;
          border-radius: 5px;
        "
      ></div> -->
      <div v-for="(item, index) in breadList" :key="index">
        {{ item.meta.title }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      breadList: [], // 路由集合
    }
  },
  watch: {
    $route() {
      this.getBreadcrumb()
    },
  },
  methods: {
    isHome(route) {
      return route.name === 'Home'
    },
    getBreadcrumb() {
      let matched = this.$route.matched
      //如果不是首页
      //   if (!this.isHome(matched[0])) {
      //     matched = [{ path: "/Home", meta: { title: "首页" } }].concat(matched);
      //   }
      this.breadList = matched
    },
  },
  created() {
    this.getBreadcrumb()
  },
}
</script>

<style lang="scss" scoped>
.mainTitle {
  font-size: 20px;
  color: #000;
  font-weight: 700;
  // border-left: 3px solid pink;
  display: flex;
  align-items: center;

  // margin-bottom: 8px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e6e6e6;
}
// .mainTitle:before {
//   // display: inline-block;
//   // float: left;
//   width: 4px;
//   height: 20px;
//   background-color: #526ade;
//   // margin: 0 10px 2px;
//   // vertical-align: middle;
//   content: '';
// }
</style>
