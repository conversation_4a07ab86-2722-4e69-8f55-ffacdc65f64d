export class Point {
  constructor(x, y) {
    this.x = x
    this.y = y
  }
  log() {
    console.log(`(${this.x}, ${this.y})`)
  }
}
export class Circle {
  constructor(c, r) {
    this.center = c
    this.radius = r
  }
  log() {
    console.log(`center: ${this.center.log()}; radius: ${this.center};`)
  }
}
// 计算线与杆塔图元的切点，A点坐标 B点坐标 圆的半径
export function getPoints(A, B, distance) {
  const m = (B.y - A.y) / (B.x - A.x) // 直线 AB 的斜率
  if (m === 0) {
    // 斜率为 0 时进行特殊处理
    return [
      new Point(A.x, A.y + distance),
      new Point(B.x, B.y + distance),
      new Point(A.x, A.y - distance),
      new Point(B.x, B.y - distance)
    ]
  }
  const M = -1 / m // 直线 A1A3 和 A2A4 的斜率
  const b13 = A.y - M * A.x // 直线 A1A3 的 b 值
  const b24 = B.y - M * B.x // 直线 A2A4 的 b 值
  const xOffset = Math.sqrt((distance * distance) / (M * M + 1)) // 线段(A1A, A3A, A2B, A4B)在 X 轴上的距离差
  // 可得
  const x1 = A.x - xOffset
  const x3 = A.x + xOffset
  const y1 = M * x1 + b13
  const y3 = M * x3 + b13
  // 同理可得
  const x2 = B.x - xOffset
  const x4 = B.x + xOffset
  const y2 = M * x2 + b24
  const y4 = M * x4 + b24
  // 返回
  return [
    new Point(x1, y1),
    new Point(x2, y2),
    new Point(x3, y3),
    new Point(x4, y4)
  ]
}
// 计算线与杆塔图元的交点
// 计算线段 p1p2 和 circle 的交点
export function getInterect(circle, p1, p2) {
  // 直线 p1p2 的公式 (y = mx + c)
  const m = (p2.y - p1.y) / (p2.x - p1.x)
  const c = p1.y - m * p1.x
  // rename
  const p = circle.center.x
  const q = circle.center.y
  const r = circle.radius
  // coefficients
  const A = m * m + 1
  const B = 2 * (m * c - m * q - p)
  const C = q * q - r * r + p * p - 2 * c * q + c * c
  // results
  const offset = Math.sqrt(B * B - 4 * A * C)
  const x1 = (-B + offset) / (2 * A)
  const y1 = m * x1 + c
  const x2 = (-B - offset) / (2 * A)
  const y2 = m * x2 + c
  const maxX = Math.max(p1.x, p2.x)
  const minX = Math.min(p1.x, p2.x)
  const maxY = Math.max(p1.y, p2.y)
  const minY = Math.min(p1.y, p2.y)
  if (x1 >= minX && x1 <= maxX && y1 >= minY && y1 <= maxY) {
    return new Point(x1, y1)
  } else {
    return new Point(x2, y2)
  }
}

// 根据与正北方向的夹角 移动距离 推算移动后的坐标 用于拉线绘制
export function getEndpoint(start, distance, angle) {
  start.x = Math.abs(start.x)
  start.y = Math.abs(start.y)
  /* special case */
  const ang = angle % 360
  if (ang === 0) {
    return new Point(start.x, start.y + distance)
  } else if (ang === 180) {
    return new Point(start.x, start.y - distance)
  } else if (ang === 90) {
    return new Point(start.x + distance, start.y)
  } else if (ang === 270) {
    return new Point(start.x - distance, start.y)
  }
  // y = mx + b
  const m = 1 / Math.tan(Math.PI / 180 * ang)
  const b = start.y - m * start.x
  // co-efficient
  const A = m * m + 1
  const B = 2 * m * (b - start.y) - 2 * start.x
  const C = start.x * start.x + (b - start.y) * (b - start.y) - distance * distance
  const offset = Math.sqrt(B * B - 4 * A * C)
  // possible results
  const endX1 = (-B + offset) / (2 * A)
  const endX2 = (-B - offset) / (2 * A)
  const endY1 = m * endX1 + b
  const endY2 = m * endX2 + b
  if (ang < 180) {
    if (endX1 > start.x) { return new Point(endX1, endY1) } else { return new Point(endX2, endY2) }
  } else {
    if (endX1 < start.x) { return new Point(endX1, endY1) } else { return new Point(endX2, endY2) }
  }
}
// 微调地图与正北方向的夹角
export function getBearing(angle) {
  if(angle < 20) {

  } else if(angle >= 20 && angle < 30) {
      
  } else if(angle >= 30 && angle < 40) {
    return angle = angle - 5 
  } 
}