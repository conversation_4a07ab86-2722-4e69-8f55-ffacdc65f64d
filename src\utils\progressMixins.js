import {
    uadateProstate,
    gethandleObtain
  } from "@/api/api"
export const progressMixins ={
    data(){
        return{
            gridData: [
                {
                  id: "1",
                  name: "断面数据生成",
                  taskid: "1",
                  pro: 0,
                  isSuccess: false,
                },
                {
                  id: "2",
                  name: "图模数据生成",
                  taskid: "2",
                  pro: 0,
                  isSuccess: false,
                },
                {
                  id: "3",
                  name: "接收CIM数据",
                  taskid: "3",
                  pro: 0,
                  isSuccess: false,
                },
                {
                  id: "4",
                  name: "接收SVG数据",
                  taskid: "4",
                  pro: 0,
                  isSuccess: false,
                },
                {
                  id: "5",
                  name: "架空线路图模解析",
                  taskid: "5",
                  pro: 0,
                  isSuccess: false,
                },
                {
                  id: "6",
                  name: "电缆线路图模解析",
                  taskid: "6",
                  pro: 0,
                  isSuccess: false,
                },
                {
                  id: "7",
                  name: "配电站房图模解析",
                  taskid: "7",
                  pro: 0,
                  isSuccess: false,
                },
                {
                  id: "8",
                  name: "生成zip",
                  taskid: "8",
                  pro: 0,
                  isSuccess: false,
                },
              ], //获取现状图
              timer: null,
              isProgressExecuting: false,
              isComplete: true, //判断接口请求是否完成
              isErr: false, //判断是否正常返回
              tipMessage:{
                type:'',
                message:''
              }
              
        }
    },
    watch: {
        obtainDialog (val) {
          console.log(val, "watch")
          if (!val) {
            console.log("清空进度条")
            this.gridData.forEach((item) => {
              item.pro = 0
              item.isSuccess = false
            })
          }
        },
      },
    methods:{
        increaseProgress () {
            if (!this.isProgressExecuting) {
              this.isProgressExecuting = true
              this.executeProgress()
            }
          },
          async executeProgress () {
            for (let i = 0; i < this.gridData.length; i++) {
              const task = this.gridData[i]
              await this.increaseTaskProgress(task)
            }
      
            // 在所有任务完成后触发事件
      
            setTimeout(() => {
              this.triggerEvent()
            }, 200)
            // 清除定时器
            clearInterval(this.timer)
            this.timer = null
            this.isProgressExecuting = false
          },
          increaseTaskProgress (task) {
            return new Promise((resolve) => {
              this.interval = setInterval(() => {
                const increment = Math.floor(Math.random() * 30) + 1
                task.pro = Math.min(task.pro + increment, 100)
                if (task.pro === 100) {
                  task.isSuccess = true
                  clearInterval(this.interval)
                  resolve() // 当任务完成时解析Promise
                }
              }, 200) // 每秒增加一次进度
            })
          },
          //进度条加载完毕后触发
          triggerEvent () {
            
              this.obtainDialog = false
              console.log("所有进度条走完", this.obtainDialog);
              this.gridData.forEach((item) => {
                item.pro = 0
                item.isSuccess = false
              })
              if(this.tipMessage.type=="success"){
                uadateProstate(this.taskID).then((res) => {
                  this.getList()
                  console.log(res, "更新状态")
                  this.obtainDialog = false
                })
              }
                this.$message({
                  message: this.tipMessage.message,
                  type: this.tipMessage.type,
                })

      
              console.log(this.obtainDialog, "dldldl")
              clearInterval(this.timer)
            
          },
          handleObtain () {
            this.isComplete = false
            this.timer = setInterval(this.increaseProgress, 100)
            this.obtainDialog = true
            gethandleObtain(this.taskID)
              .then((res) => {
                console.log(res.data.result.data.length,'lengthhhhhhhh')
                if (res.data.result.data.length !== 0) {
                    this.tipMessage= {
                        type:'success',
                        message:"获取现状成功！"
                    }
                } else {
                    this.tipMessage={
                        type:'warning',
                        message:"获取现状失败！"
                    }
                }
              })
              .catch(() => {
                this.tipMessage={
                    type:'warning',
                    message:"获取现状失败！"
                }
              })
          
          },
    }
}