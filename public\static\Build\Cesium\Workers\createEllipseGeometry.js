define(["./Cartographic-3309dd0d","./when-b60132fc","./EllipseGeometry-56e329a6","./Cartesian2-47311507","./Check-7b2a090c","./Math-119be1a3","./arrayFill-4513d7ad","./buildModuleUrl-4e1b81e7","./PrimitiveType-a54dc62f","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./EllipseGeometryLibrary-96261ee5","./GeometryAttribute-3a88ba31","./FeatureDetection-c3b71206","./GeometryAttributes-252e9929","./GeometryInstance-68aae013","./GeometryOffsetAttribute-fbeb6f1a","./GeometryPipeline-9d1ef0b6","./AttributeCompression-90851096","./EncodedCartesian3-f1396b05","./IndexDatatype-8a5eead4","./IntersectionTests-7386ffbf","./Plane-7ae8294c","./VertexFormat-6446fca0"],(function(e,t,a,r,i,n,o,c,l,s,d,b,f,m,p,y,u,G,C,E,h,A,_,D,F,I,P){"use strict";return function(i,n){return t.defined(n)&&(i=a.EllipseGeometry.unpack(i,n)),i._center=e.Cartesian3.clone(i._center),i._ellipsoid=r.Ellipsoid.clone(i._ellipsoid),a.EllipseGeometry.createGeometry(i)}}));
