<template>
  <div class="index flex-c h100">
    <!-- 标题 -->
     <div style="height:100vh;width:100vw;background:#fff;  position: absolute;left:0;top:0;z-index:99" v-show="!showPage"></div>
    <div class="main-header">需求任务接收</div>
    <div class="" style="width: 100%; height: 1px; border-bottom: 1px solid #eee"></div>
    <!-- 表单 -->
    <div class="query-form-box">
      <el-form ref="form" :model="form" :inline="true" label-width="80px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="年度:" prop="pcnd">
              <el-date-picker v-model="form.pcnd" type="year" format="yyyy" value-format="yyyy" placeholder="选择年">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6" class="midSearchArea">
            <el-form-item label="地市公司:" prop="dsgs">
              <el-select v-model="form.dsgs" clearable placeholder="请选择" @change="CityPoint">
                <el-option v-for="item in dsgsOptions" :key="item.cityid" :label="item.cityname" :value="item.cityid">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="县公司:" prop="xgs">
              <el-select v-model="form.xgs" clearable placeholder="请选择">
                <el-option v-for="item in xgsOptions" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="lastSearchArea">
            <el-form-item label="工程名称:" prop="projectName">
              <el-input v-model="form.projectName" clearable placeholder="请输入工程名称"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="状态:" prop="projState">
              <el-select v-model="form.projState" clearable placeholder="请选择">
                <el-option label="待处理" value="01">待处理</el-option>
                <el-option label="处理中" value="02">处理中</el-option>
                <el-option label="已提交" value="05" v-show="userType == '3'">已提交</el-option>
                <el-option label="已提交" value="06" v-show="userType == '2'">已提交</el-option>
                <el-option label="评审通过" value="04">评审通过</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" class="midSearchArea">
            <el-form-item label="项目编码:">
              <el-input v-model="form.code" clearable placeholder="请输入项目编码"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" class="lastSearchArea">
            <el-button class="blue-btn" @click="query()">查询</el-button>
            <el-button @click="clearForm()">重置</el-button>

          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="driver"></div>
    <div class="actionAreaPage">
      <el-upload class="uploadAction" action="test" accept=".xlsx,.xls" :before-upload="beforeDesignUpload" :show-file-list="false" :http-request="uploadFilesProject">
        <div><el-button>
            <div class="buttonArea"><img src="@/assets/img/批量导入.svg" alt="" style="margin-right:3px">
              <div> 批量导入</div>
            </div>
          </el-button>
        </div>
      </el-upload>

      <!-- <el-button @click="downLoadTemplates" style="margin-left:12px">
        <div class="buttonArea"><img src="@/assets/img/下载.svg" alt="" style="margin-right:3px">
          <div> 模板下载</div>
        </div>
      </el-button> -->

      <el-button @click="removeListPorj" style="margin-left:12px">
        <div class="buttonArea"><img src="@/assets/img/回退.svg" alt="" style="margin-right:3px">
          <div> 批量删除</div>
        </div>
      </el-button>
    </div>
    <!-- 项目列表 -->
    <div class="tablesArea">
      <el-table ref="table" :data="tableData" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }" :height="tableHeight" highlight-current-row @selection-change="handleSelectionChange" style="width: 98%; margin: 0px 16px 0 16px" :row-key="getRowKeys">
        <el-table-column type="selection" :reserve-selection="true" width="55" >
        </el-table-column>
        <el-table-column type="index" :index="indexMethod" label="序号" align="center" :resizable="false" width="60">
        </el-table-column>
        <el-table-column prop="pcnd" label="年度" align="left" width="100">
        </el-table-column>
        <el-table-column prop="projectName" label="工程名称" align="left">
        </el-table-column>
        <el-table-column prop="cityName" label="地市公司" align="left" width="230px">
        </el-table-column>
        <el-table-column prop="countyName" label="县公司" align="left" width="230px">
        </el-table-column>
        <el-table-column prop="gwbzdw" label="编制单位" align="left" width="100px">
        </el-table-column>
        <el-table-column prop="voltageLevel" label="电压等级" align="left" width="100px">
        </el-table-column>
        <el-table-column label="适用深度规范" align="left" width="140px">
          <template slot-scope="scope">
            <span v-if="scope.row.projectType == '01'">架空</span>
            <span v-if="scope.row.projectType == '02'">电缆</span>
            <span v-if="scope.row.projectType == '03'">配变</span>
          </template>
        </el-table-column>
        <el-table-column prop="projState" label="任务状态" align="left" width="100px">
          <template slot-scope="scope">
            <span v-if="(scope.row.projState == '01' || scope.row.projState == '')" style="color: skyblue">待处理</span>
            <span v-else-if="scope.row.projState == '02'" style="color: orangered">处理中</span>
            <span v-else-if="scope.row.projState == '06' && userType == '3'" style="color: royalblue">已提交（地市公司评审）</span>
            <span v-else-if="scope.row.projState == '06' && userType == '2'" style="color: royalblue">已提交（地市公司评审）</span>
            <span v-else-if="scope.row.projState == '06' && userType == '1'" style="color: royalblue">已提交（地市公司评审）</span>
            <span v-else-if="scope.row.projState == '05' && userType == '3'" style="color: royalblue">已提交(县公司评审)</span>
            <span v-else-if="scope.row.projState == '04'" style="color: green">评审通过</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="left" width="250px">
          <template slot-scope="scope">
            <span class="directiveHandle">
              <div :class="!['', '01', '02'].includes(scope.row.projState) || userType == '1'
                ? 'el-buttonDisabled'
                : 'el-buttonStyle'
                ">
                <span @click="handleEdit(scope.row)"> 编辑 </span>
              </div>
              <span class="el-buttonDriver">|</span>
              <div :class="scope.row.projState !== '02' || userType == '1'
                ? 'el-buttonDisabled'
                : 'el-buttonStyle'
                ">
                <span @click="handleSubt(scope.row)"> 提交 </span>
              </div>
              <span class="el-buttonDriver">|</span>
              <div :class="scope.row.projState !== '02' || userType == '1'
                ? 'el-buttonDisabled'
                : 'el-buttonStyle'
                ">
                <span @click="handleReturn(scope.row)"> 退回 </span>
              </div>
              <span class="el-buttonDriver">|</span>
              <el-dropdown>
                <el-button type="text">
                  <i class="el-icon-more directiveicon" style="font-size: 14px"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="删除" class="el-buttonStyle">
                    <span @click="removeProj(scope.row)"> 删除 </span>
                  </el-dropdown-item>
                  <el-dropdown-item command="获取现状" :class="scope.row.projState !== '01'
                    ? 'el-buttonDisabled'
                    : 'el-buttonStyle'
                    ">
                    <span @click="handleCofirm(scope.row)"> 获取现状 </span>
                  </el-dropdown-item>
                  <el-dropdown-item command="查看馈线" :class="scope.row.projState !== '02'
                    ? 'el-buttonDisabled'
                    : 'el-buttonStyle'
                    ">
                    <span @click="handleFeeder(scope.row)"> 查看馈线 </span>
                  </el-dropdown-item>
                  <el-dropdown-item command="设计软件" :class="!['', '01', '02'].includes(scope.row.projState)
                    ? 'el-buttonDisabled'
                    : 'el-buttonStyle'
                    ">
                    <span @click="handleEamine(scope.row)"> 设计软件 </span>
                  </el-dropdown-item>
                  <el-dropdown-item command="成果分析" :class="!['', '01', '02'].includes(scope.row.projState)
                    ? 'el-buttonDisabled'
                    : 'el-buttonStyle'
                    ">
                    <span @click="handleResults(scope.row)"> 成果分析 </span>
                  </el-dropdown-item>
                  <el-dropdown-item command="问题清单" class="el-buttonStyle">
                    <span @click="handleProblem(scope.row)"> 问题清单 </span>
                  </el-dropdown-item>
                  <!-- <el-dropdown-item command="需求说明" class="el-buttonStyle">
                    <span @click="handleRequirements(scope.row)">
                      需求说明
                    </span>
                  </el-dropdown-item> -->
                  <el-dropdown-item command="查看详情" class="el-buttonStyle">
                    <span @click="handleDetails(scope.row)"> 查看详情 </span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination style="margin: 10px" background :current-page="form.page" :page-sizes="pageSize" :page-size="form.perPage" layout="total, sizes, prev, pager, next" @current-change="handleCurrentChange" @size-change="handleSizeChange"
      :total="total">
    </el-pagination>
    <!-- 查看问题清单 -->
    <el-dialog title="查看问题清单" :visible.sync="problemDialog" width="60%">
      <el-table :data="ProblemData" border>
        <el-table-column type="index" width="60" align="center" label="序号">
        </el-table-column>
        <el-table-column property="sblx" label="设备类型" width="120" align="center"></el-table-column>
        <el-table-column property="sbmc" label="设备名称" align="center"></el-table-column>
        <el-table-column property="sbPmsid" label="设备PMSID" align="center"></el-table-column>
        <el-table-column property="wtLx" label="设备问题类型" align="center"></el-table-column>
        <el-table-column property="wtStore" label="设备详情数据" align="center"></el-table-column>
        <el-table-column property="wtkid" label="设备库数据" align="center"></el-table-column>
        <el-table-column property="wttbsj" label="问题提报时间" align="center"></el-table-column>
      </el-table>
      <el-pagination style="margin: 10px" background :current-page="formProblem.pageSize" :page-sizes="ProblemPageSize" :page-size="formProblem.pageIndex" layout="total, sizes, prev, pager, next" @current-change="ProblemhandleCurrentChange"
        @size-change="ProblemhandleSizeChange" :total="ProblemTotal">
      </el-pagination>
    </el-dialog>
    <!-- 编辑数据 -->
    <el-dialog title="编辑数据" :visible.sync="editDialog" top="5vh" width="70%" center>
      <div class="editArea" style="height: 70vh; overflow-y: auto">
        <el-card class="box-card">
          <el-descriptions title="基础信息:"> </el-descriptions>
          <el-form ref="basicInfo" :model="formEdit" :inline="true" :rules="modelRules">
            <!-- <el-form ref="basicInfo" :model="formEdit" :inline="true"> -->
            <el-form-item label="批次年度:" prop="pcnd" label-width="120px">
              <el-date-picker v-model="formEdit.pcnd" format="yyyy" value-format="yyyy" type="year" placeholder="选择年">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="项目名称:" prop="projectName" label-width="120px">
              <el-input v-model="formEdit.projectName" clearable></el-input>
            </el-form-item>
            <el-form-item label="所属公司:" prop="cityName" label-width="120px">
              <el-select v-model="formEdit.cityName" clearable placeholder="请选择">
                <el-option v-for="item in cityName" :key="item.system_value" :label="item.system_name" :value="item.system_value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="供电所:" prop="powerSubstationName" label-width="120px">
              <el-input v-model="formEdit.powerSubstationName" clearable></el-input>
            </el-form-item>
            <el-form-item label="编制单位:" prop="gwbzdw" label-width="120px">
              <el-input v-model="formEdit.gwbzdw" clearable></el-input>
            </el-form-item>
            <el-form-item label="电压等级:" prop="voltageLevel" label-width="120px">
              <el-select v-model="formEdit.voltageLevel" clearable placeholder="请选择">
                <el-option v-for="item in voltageLevel" :key="item.system_value" :label="item.system_name" :value="item.system_value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </el-card>
        <el-card class="box-card">
          <el-descriptions title="详情信息:"> </el-descriptions>
          <el-form ref="detailsInfo" :model="formEdit" :inline="true" :rules="modeldetailsRules">
            <!-- <el-form ref="detailsInfo" :model="formEdit" :inline="true" > -->
            <el-form-item label="计划年度:" prop="gwjhnd" label-width="120px">
              <el-select v-model="formEdit.gwjhnd" clearable placeholder="请选择">
                <el-option v-for="item in gwjhnd" :key="item.system_value" :label="item.system_name" :value="item.system_value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="编制时间:" prop="gwbzsj" label-width="120px">
              <!-- <el-input v-model="formEdit.gwbzsj" clearable></el-input> -->
              <el-date-picker v-model="formEdit.gwbzsj" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择时间">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="设计单位名称:" prop="designInstituteName" label-width="120px">
              <el-input v-model="formEdit.designInstituteName" clearable></el-input>
            </el-form-item>
            <el-form-item label="设计人员名称:" prop="designerName" label-width="120px">
              <el-input v-model="formEdit.designerName" clearable></el-input>
            </el-form-item>
            <el-form-item label="项目性质:" prop="gwxmxz" label-width="120px">
              <el-select v-model="formEdit.gwxmxz" clearable placeholder="请选择">
                <el-option v-for="item in gwxmxz" :key="item.system_value" :label="item.system_name" :value="item.system_value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="专业类别:" prop="gwzylb" label-width="120px">
              <el-select v-model="formEdit.gwzylb" clearable placeholder="请选择">
                <el-option v-for="item in gwzylb" :key="item.system_value" :label="item.system_name" :value="item.system_value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="项目级别:" prop="gwxmjb" label-width="120px">
              <el-select v-model="formEdit.gwxmjb" clearable placeholder="请选择">
                <el-option v-for="item in gwxmjb" :key="item.system_value" :label="item.system_name" :value="item.system_value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="改造原因:" prop="gwgzyy" label-width="120px">
              <el-select v-model="formEdit.gwgzyy" clearable placeholder="请选择">
                <el-option v-for="item in gwgzyy" :key="item.system_value" :label="item.system_name" :value="item.system_value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="建设改造目的:" prop="gwgzmd" label-width="120px">
              <el-select v-model="formEdit.gwgzmd" clearable placeholder="请选择">
                <el-option v-for="item in gwgzmd" :key="item.system_value" :label="item.system_name" :value="item.system_value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="资产所属单位:" prop="gwzcssdw" label-width="120px">
              <el-input v-model="formEdit.gwzcssdw" clearable></el-input>
            </el-form-item>
            <el-form-item label="区域属性:" prop="gwqyxz" label-width="120px">
              <el-select v-model="formEdit.gwqyxz" clearable placeholder="请选择">
                <el-option v-for="item in gwqyxz" :key="item.system_value" :label="item.system_name" :value="item.system_value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="项目类别:" prop="gwxmlb" label-width="120px">
              <el-select v-model="formEdit.gwxmlb" clearable placeholder="请选择">
                <el-option v-for="item in gwxmlb" :key="item.system_value" :label="item.system_name" :value="item.system_value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="设备类型:" prop="jcxxsblx" label-width="120px">
              <el-select v-model="formEdit.jcxxsblx" clearable placeholder="请选择">
                <el-option v-for="item in jcxxsblx" :key="item.system_value" :label="item.system_name" :value="item.system_value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="设备名称:" prop="sbmc" label-width="120px">
              <el-input v-model="formEdit.sbmc" clearable></el-input>
            </el-form-item>
            <el-form-item label="深度规范:" prop="projectType" label-width="120px">
              <el-select v-model="formEdit.projectType" clearable placeholder="请选择">
                <el-option v-for="item in projectType" :key="item.system_value" :label="item.system_name" :value="item.system_value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="项目大类:" prop="xmdl" label-width="120px">
              <el-select v-model="formEdit.xmdl" clearable placeholder="请选择">
                <el-option v-for="item in xmdl" :key="item.system_value" :label="item.system_name" :value="item.system_value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="项目小类:" prop="xmxl" label-width="120px">
              <el-select v-model="formEdit.xmxl" clearable placeholder="请选择">
                <el-option v-for="item in xmxl" :key="item.system_value" :label="item.system_name" :value="item.system_value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="需求类别:" prop="xqlb" label-width="120px">
              <el-select v-model="formEdit.xqlb" clearable placeholder="请选择">
                <el-option v-for="item in xqlb" :key="item.system_value" :label="item.system_name" :value="item.system_value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="重要程度:" prop="xmzycd" label-width="120px">
              <el-select v-model="formEdit.xmzycd" clearable placeholder="请选择">
                <el-option v-for="item in xmzycd" :key="item.system_value" :label="item.system_name" :value="item.system_value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="资金来源:" prop="xmzjly" label-width="120px">
              <el-select v-model="formEdit.xmzjly" clearable placeholder="请选择">
                <el-option v-for="item in xmzjly" :key="item.system_value" :label="item.system_name" :value="item.system_value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </el-card>
        <el-card class="box-card">
          <el-descriptions title="基本信息:"> </el-descriptions>
          <el-form ref="detailsInfo" :model="formEdit" :inline="true" :rules="modeldetailsRules">
            <!-- <el-form ref="detailsInfo" :model="formEdit" :inline="true" > -->
            <el-form-item label="调整系数:" prop="xqtzxs" label-width="120px">
              <el-input v-model="formEdit.xqtzxs" clearable></el-input>
            </el-form-item>
            <el-form-item label="建设规模:" prop="xqjsgm" label-width="120px">
              <el-input v-model="formEdit.xqjsgm" clearable></el-input>
            </el-form-item>
            <!-- 这几个暂时屏蔽-->
            <!--            <el-form-item-->
            <!--              label="规格型号"-->
            <!--              prop="xqggxh"-->
            <!--              label-width="120px"-->
            <!--              -->
            <!--            >-->
            <!--              <el-input v-model="formEdit.xqggxh" clearable ></el-input>-->
            <!--            </el-form-item>-->
            <!--            <el-form-item-->
            <!--              label="单位"-->
            <!--              prop="xqdw"-->
            <!--              label-width="120px"-->
            <!--              -->
            <!--            >-->
            <!--              <el-input v-model="formEdit.xqdw" clearable ></el-input>-->
            <!--            </el-form-item>-->
            <!--            <el-form-item-->
            <!--              label="数量"-->
            <!--              prop="xqsl"-->
            <!--              label-width="120px"-->
            <!--              -->
            <!--            >-->
            <!--              <el-input v-model="formEdit.xqsl" clearable ></el-input>-->
            <!--            </el-form-item>-->
            <el-form-item label="参考造价区间:" prop="xqzjqj" label-width="120px">
              <el-input v-model="formEdit.xqzjqj" clearable></el-input>
            </el-form-item>
            <el-form-item label="中位数:" prop="xqzjs" label-width="120px">
              <el-input v-model="formEdit.xqzjs" clearable></el-input>
            </el-form-item>
            <el-form-item label="总投资:" prop="xqztz" label-width="120px">
              <el-input v-model="formEdit.xqztz" clearable></el-input>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialog = false">取 消</el-button>
        <el-button class="blue-btn" @click="editDialogQuery">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 需求说明 -->
    <el-dialog title="需求说明书上传" :visible.sync="requirementsDialog" width="30%" center>
      <el-upload class="upload-demo" :data="getformData()" accept=".zip" :before-upload="beforeAvatarUpload" :on-error="handleError" :file-list="addProjectInfo.file" :http-request="importFileXqsms">
        <button class="uploadFiles">
          <i class="el-icon-upload2">点击上传</i>
        </button>
        <div slot="tip" class="el-upload__tip">只能上传.zip文件</div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="requirementsDialog = false">取 消</el-button>
        <el-button class="blue-btn" @click="handleSuccess">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 现状数据 -->
    <el-dialog title="获取现状数据" :visible.sync="obtainDialog" width="30%" left :close-on-click-modal="false">
      <el-table :data="gridData" :header-cell-style="{ background: '#F7F8FA' }">
        <el-table-column type="index" width="60" align="left" label="序号">
        </el-table-column>
        <el-table-column property="name" label="步骤" align="left"></el-table-column>
        <el-table-column width="100" align="left" property="isSuccess" label="是否成功">
          <template slot-scope="scope">
            {{ scope.row.isSuccess ? "成功" : "加载中" }}
          </template>
        </el-table-column>
        <el-table-column property="" label="进度" align="left" width="150px">
          <template slot-scope="scope">
            <div class="progresswrapper">
              <!-- <div class="pro" :style="{width:scope.row.pro+'%'}">{{scope.row.pro}}%</div> -->
              <el-progress :text-inside="true" :stroke-width="15" :percentage="scope.row.pro" text-color="#fff" :color="scope.row.isSuccess ? '#16A065' : '#7286E8'"></el-progress>
              <!-- #526ADE -->
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!-- 详情数据 -->
    <el-dialog title="查看详情数据" :visible.sync="detailsDialog" width="65%" center>
      <div class="descriptions-borerStyle">
        <el-descriptions title="基本信息:" :labelStyle="{
          whiteSpace: 'nowrap',
          display: 'flex',
          alignItems: 'center',
        }">
          <el-descriptions-item label="批次年度">{{
            this.Details.pcnd
          }}</el-descriptions-item>
          <el-descriptions-item label="项目名称">{{
            this.Details.projectName
          }}</el-descriptions-item>
          <el-descriptions-item label="计划年度">{{
            this.Details.gwjhnd
          }}</el-descriptions-item>
          <el-descriptions-item label="编制时间">{{
            this.Details.gwbzsj
          }}</el-descriptions-item>
          <el-descriptions-item label="所属公司">{{
            this.Details.cityName
          }}</el-descriptions-item>
          <el-descriptions-item label="供电所">{{
            this.Details.powerSubstationName
          }}</el-descriptions-item>
          <el-descriptions-item label="设计单位名称">{{
            this.Details.designInstituteName
          }}</el-descriptions-item>
          <el-descriptions-item label="设计人员名称">{{
            this.Details.designerName
          }}</el-descriptions-item>
          <el-descriptions-item label="项目性质">
            <span v-if="this.Details.gwxmxz == '01'">配网</span>
            <span v-if="this.Details.gwxmxz == '02'">农网</span>
            <span v-if="this.Details.gwxmxz == '03'">租赁</span>
          </el-descriptions-item>
          <el-descriptions-item label="项目级别">
            <span v-if="this.Details.gwxmjb == '01'">省级项目</span>
            <span v-if="this.Details.gwxmjb == '02'">市级项目</span>
            <span v-if="this.Details.gwxmjb == '03'">县级项目</span>
          </el-descriptions-item>
          <el-descriptions-item label="改造原因">
            <span v-if="this.Details.gwgzyy == '01'">设备老化</span>
            <span v-if="this.Details.gwgzyy == '02'">落实安措(安全防护)</span>
            <span v-if="this.Details.gwgzyy == '03'">网架结构不合理</span>
            <span v-if="this.Details.gwgzyy == '04'">供电能力不足</span>
            <span v-if="this.Details.gwgzyy == '05'">设备故障</span>
            <span v-if="this.Details.gwgzyy == '06'">设备质量缺陷</span>
            <span v-if="this.Details.gwgzyy == '07'">配合市政建设</span>
            <span v-if="this.Details.gwgzyy == '08'">落实国家政策</span>
            <span v-if="this.Details.gwgzyy == '09'">其他</span>
            <span v-if="this.Details.gwgzyy == '10'">电能质量问题</span>
          </el-descriptions-item>
          <el-descriptions-item label="建设改造目的">
            <span v-if="this.Details.gwgzmd == '01'">提升电网安全稳定水平</span>
            <span v-if="this.Details.gwgzmd == '02'">提升设备运行可靠性</span>
            <span v-if="this.Details.gwgzmd == '03'">提升电网输送能力</span>
            <span v-if="this.Details.gwgzmd == '04'">提升电网经济运行水平</span>
            <span v-if="this.Details.gwgzmd == '05'">提升电网智能化水平</span>
            <span v-if="this.Details.gwgzmd == '06'">提升电网环保水平</span>
            <span v-if="this.Details.gwgzmd == '07'">其他</span>
          </el-descriptions-item>
          <el-descriptions-item label="资产所属单位">{{
            this.Details.gwzcssdw
          }}</el-descriptions-item>
          <el-descriptions-item label="区域属性">
            <span v-if="this.Details.gwqyxz == '01'">A+</span>
            <span v-if="this.Details.gwqyxz == '02'">A</span>
            <span v-if="this.Details.gwqyxz == '03'">B</span>
            <span v-if="this.Details.gwqyxz == '04'">C</span>
            <span v-if="this.Details.gwqyxz == '05'">D</span>
            <span v-if="this.Details.gwqyxz == '06'">E</span>
          </el-descriptions-item>
          <el-descriptions-item label="项目类别">
            <span v-if="this.Details.gwxmlb == '01'">迎峰度夏项目</span>
            <span v-if="this.Details.gwxmlb == '02'">迎峰度冬项目</span>
            <span v-if="this.Details.gwxmlb == '03'">阶段性保障供电项目</span>
            <span v-if="this.Details.gwxmlb == '04'">"五大"体系建设项目</span>
            <span v-if="this.Details.gwxmlb == '05'">"灾后恢复项目"</span>
            <span v-if="this.Details.gwxmlb == '06'">"其他"</span>
          </el-descriptions-item>
          <el-descriptions-item label="电压等级">{{
            this.Details.voltageLevel
          }}</el-descriptions-item>
          <el-descriptions-item label="设备类型">
            <span v-if="this.Details.jcxxsblx == '01'">线路</span>
            <span v-if="this.Details.jcxxsblx == '02'">配变</span>
          </el-descriptions-item>
          <el-descriptions-item label="设备名称">{{
            this.Details.sbmc
          }}</el-descriptions-item>
          <el-descriptions-item label="深度规范">
            <span v-if="this.Details.projectType == '01'">架空</span>
            <span v-if="this.Details.projectType == '02'">电缆</span>
            <span v-if="this.Details.projectType == '03'">配变</span>
          </el-descriptions-item>
          <el-descriptions-item label="项目大类">
            <span v-if="this.Details.xmdl == '1'">线路新建</span>
            <span v-if="this.Details.xmdl == '2'">线路改造</span>
            <span v-if="this.Details.xmdl == '3'">台区新建</span>
            <span v-if="this.Details.xmdl == '4'">台区改造</span>
          </el-descriptions-item>
          <el-descriptions-item label="项目小类">
            <span v-if="this.Details.xmxl == '1'">配变增容布点</span>
            <span v-if="this.Details.xmxl == '2'">老旧或缺陷设备改造</span>
            <span v-if="this.Details.xmxl == '3'">架空线路绝缘化改造</span>
            <span v-if="this.Details.xmxl == '4'">网架优化完善</span>
            <span v-if="this.Details.xmxl == '5'">供电区域优化</span>
            <span v-if="this.Details.xmxl == '6'">配电自动化改造</span>
            <span v-if="this.Details.xmxl == '7'">业扩接入电网配套改造</span>
            <span v-if="this.Details.xmxl == '8'">其他人工输入</span>
          </el-descriptions-item>
          <el-descriptions-item label="需求类别">
            <span v-if="this.Details.xqlb == '1'">大修技改需求</span>
            <span v-if="this.Details.xqlb == '2'">基建需求</span>
          </el-descriptions-item>
          <el-descriptions-item label="重要程度">
            <span v-if="this.Details.xmzycd == '1'">重大</span>
            <span v-if="this.Details.xmzycd == '2'">重要</span>
            <span v-if="this.Details.xmzycd == '3'">一般</span>
          </el-descriptions-item>
          <el-descriptions-item label="资金来源">
            <span v-if="this.Details.xmzjly == '1'">中央资本金</span>
            <span v-if="this.Details.xmzjly == '2'">专项建设基金</span>
            <span v-if="this.Details.xmzjly == '3'">中电财贷款</span>
            <span v-if="this.Details.xmzjly == '4'">普通贷款</span>
            <span v-if="this.Details.xmzjly == '5'">自有资金</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <!-- 馈线ID -->
    <el-dialog title="馈线ID查看" width="95%" top="5vh" v-if="feederDialog" :visible.sync="feederDialog">
      <div style="height: 500px; overflow-y: auto" v-loading="fullscreenLoading" element-loading-text="拼命加载中">
        <!-- <FeederDialog :ProCode="cuttentProCode" v-if="flag"></FeederDialog> -->
        <json-viewer class="jsonview" :value="feederData" :expand-depth="jsonformat.expandDepth" :copyable="jsonformat.copyable">
        </json-viewer>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="feederDialog = false">取 消</el-button>
        <el-button class="blue-btn" @click="feederDialogQuery">保 存</el-button>
      </span>
      <!-- <div style="height: 76vh">
        <FeederDialog :ProCode="cuttentProCode" v-if="flag"></FeederDialog>
      </div> -->
    </el-dialog>
    <!-- 成果分析 -->
    <el-dialog title="选择解析内容" width="25%" style="height: 100vh" :visible.sync="resultDialog">
      <div style="height: 100%">
        <ResultsDialog :ProCode="cuttentresult" v-if="flag"></ResultsDialog>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSettlement,
  getCity,
  getCounty,
  getXQEditPull,
  EditSave,
  DetailsView,
  resquest,
  ProblemView,
  handleReturn,
  handleSubt,
  getURLSJ,
  getuserInfo,
  uadateProstate,
  feeederObtainCurrent,
  importFileXqsms,
  handleSubtCS,
  handleReturnCS,
  importFileProjectInfo,
  downLoadTemplate,removeProject
} from "@/api/api"
import FeederDialog from "@/views/projectAgent/components/tree"
import ResultsDialog from "@/views/projectAgent/components/table"
import { progressMixins } from "@/utils/progressMixins.js"
export default {
  components: {
    FeederDialog,
    ResultsDialog,
  },
  mixins: [progressMixins],
  data() {
    return {
      getRowKeys(row){
        return row.taskID
      },
      removeList:[],
      flag: true,
      showPage: false,
      url: resquest,
      form: {
        //表单参数
        id: "",
        projState: '',
        stageType: "0",
        projectName: "",
        pcnd: '',
        BATCH: "",
        dsgs: "",
        xgs: "",
        xmName: "",
        tgzt: "",
        csmc: "",
        dwmc: "",
        page: 1,
        perPage: 10,
        code: '',
        source: ''
      },
      xgsOptions: [], //县公司下拉数据
      dsgsOptions: [], //城市下拉
      jsdwOptions: [], //批次名次下拉数据
      pageSize: [10, 20, 50, 100], //分页页数
      ProblemPageSize: [10, 20, 50, 100], //问题查看分页
      modelRules: {
        pcnd: [
          { required: true, message: "请选择批次年度", trigger: "blur" },
        ],
        projectName: [
          { required: true, message: "请填写项目名称", trigger: "blur" },
        ],
        cityName: [
          { required: true, message: "请选择所属公司", trigger: "blur" },
        ],
        powerSubstationName: [
          { required: true, message: "请填写供电所名称", trigger: "blur" },
        ],
        gwbzdw: [
          { required: true, message: "请填写编制单位", trigger: "blur" },
        ],
        voltageLevel: [
          { required: true, message: "请选择电压等级", trigger: "blur" },
        ],
      },
      modeldetailsRules: {
        gwjhnd: [
          { required: true, message: "请选择计划年度", trigger: "blur" },
        ],
        gwbzsj: [
          { required: true, message: "请输入编制时间", trigger: "blur" },
        ],
        designInstituteName: [
          { required: true, message: "请输入设计单位", trigger: "blur" },
        ],
        designerName: [
          { required: true, message: "请填写设计人员名称", trigger: "blur" },
        ],
        gwxmxz: [
          { required: true, message: "请选择项目性质", trigger: "blur" },
        ],
        gwzylb: [
          { required: true, message: "请选择专业类别", trigger: "blur" },
        ],
        gwxmjb: [
          { required: true, message: "请选择项目级别", trigger: "blur" },
        ],
        gwgzyy: [
          { required: true, message: "请选择改造原因", trigger: "blur" },
        ],
        gwgzmd: [
          { required: true, message: "请选择建设改造目的", trigger: "blur" },
        ],
        gwqyxz: [
          { required: true, message: "请选择区域属性", trigger: "blur" },
        ],
        gwxmlb: [
          { required: true, message: "请选择项目类别", trigger: "blur" },
        ],
        jcxxsblx: [
          { required: true, message: "请选择设备类型", trigger: "blur" },
        ],
        sbmc: [{ required: true, message: "请输入设备名称", trigger: "blur" }],
        projectType: [
          { required: true, message: "请选择深度规范", trigger: "blur" },
        ],
        xmdl: [
          { required: true, message: "请选择项目大类", trigger: "blur" },
        ],
        xqlb: [
          { required: true, message: "请选择需求类别", trigger: "blur" },
        ],
        xmzycd: [
          { required: true, message: "请选择重要程度", trigger: "blur" },
        ],
        xmzjly: [
          { required: true, message: "请选择资金来源", trigger: "blur" },
        ],
        xqtzxs: [
          { required: true, message: "请输入调整系数", trigger: "blur" },
        ],
        xqjsgm: [
          { required: true, message: "请输入建设规模", trigger: "blur" },
        ],
        xqzjqj: [
          { required: true, message: "请输入参考造价区间", trigger: "blur" },
        ],
        xqzjs: [
          { required: true, message: "请输入中位数", trigger: "blur" },
        ],
        xqztz: [
          { required: true, message: "请输入总投资", trigger: "blur" },
        ],
        xqggxh: [
          { required: true, message: "请输入规格型号", trigger: "blur" },
        ],
        xqdw: [
          { required: true, message: "请输入单位", trigger: "blur" },
        ],
        xqsl: [
          { required: true, message: "请输入数量", trigger: "blur" },
        ],
      },
      q: {
        page: 1,
        limit: 5,
      },
      total: 0, //总共页数
      ProblemTotal: 0, //问题查看总共页数
      tableData: [],
      tableHeight: 0,
      formEdit: {
        //编辑
        taskID: "", //主键id
        pcnd: new Date().getFullYear() + '',
        batch: "",
        projectName: "",
        powerSubstationName: "",
        cityName: "",
        gwbzdw: "",
        voltageLevel: "",
        gwXqglbh: "",
        gwjhnd: "",
        gwbzsj: "",
        designInstituteName: "",
        designerName: "",
        gwxmxz: "",
        gwzylb: "",
        gwxmjb: "",
        gwgzyy: "",
        gwgzmd: "",
        gwzcssdw: "",
        gwqyxz: "",
        gwxmlb: "",
        jcxxsblx: "",
        sbmc: "",
        projectType: "",
        xmdl: "",
        xmxl: "",
        xqlb: "",
        xmzycd: "",
        xmzjly: "",
        xqtzxs: '',
        xqjsgm: '',
        xqzjqj: '',
        xqzjs: '',
        xqztz: '',
        xqggxh: '',
        xqdw: '',
        xqsl: ''
      },
      //编辑下拉集合
      cityName: [], //城市名称下拉
      gwgzmd: [], //建设改造地
      gwgzyy: [], //改造原因
      gwjhnd: [], //年度计划
      gwqyxz: [], //区域属性
      gwxmjb: [], //项目级别
      gwxmlb: [], //项目类别
      gwxmxz: [], //项目性质
      gwzylb: [], //专业类别
      jcxxsblx: [], //设备类型
      projectType: [], //深度规范
      voltageLevel: [], //电压等级
      xmdl: [], //项目大类
      xmxl: [], //项目小类
      xmzjly: [], //资金来源
      xmzycd: [], //重要程度
      xqlb: [], //需求类别
      formProblem: {
        //问题查看参数
        taskid: "",
        pageSize: 1,
        pageIndex: 5,
      },
      ProblemData: [], //问题查看表格
      cuttentProCode: "", //馈线ID
      cuttentresult: "", //成果分析
      problemDialog: false, //问题清单

      editDialog: false, //编辑
      requirementsDialog: false, //需求说明
      obtainDialog: false, //现状数据
      detailsDialog: false, //详情
      feederDialog: false, //馈线ID
      resultDialog: false, //成果分析
      addProjectInfo: {
        file: [], // 文件上传
        id: "",
      },
      Details: {
        pcnd: "",
        projectName: "",
        powerSubstationName: "",
        cityName: "",
        gwbzdw: "",
        voltageLevel: "",
        gwXqglbh: "",
        gwjhnd: "",
        gwbzsj: "",
        designInstituteName: "",
        designerName: "",
        gwxmxz: "",
        gwzylb: "",
        gwxmjb: "",
        gwgzyy: "",
        gwgzmd: "",
        gwzcssdw: "",
        gwqyxz: "",
        gwxmlb: "",
        jcxxsblx: "",
        sbmc: "",
        projectType: "",
        xmdl: "",
        xmxl: "",
        xqlb: "",
        xmzycd: "",
        xmzjly: "",
      },
      userId: "",
      // timer: null,
      // isProgressExecuting: false,
      // isComplete: true, //判断接口请求是否完成
      // isErr: false, //判断是否正常返回
      taskID: "",
      interval: null,
      feederData: [],
      jsonformat: {
        expandDepth: 2,
        copyable: true,
      },
      userType: '',
      tempUrl: ""
    }
  },
  watch: {
    // obtainDialog (val) {
    //   console.log(val, "watch")
    //   if (!val) {
    //     console.log("清空进度条")
    //     this.gridData.forEach((item) => {
    //       item.pro = 0
    //       item.isSuccess = false
    //     })
    //   }
    // },
  },
  mounted() {
    this.setTablesHeight()
    const token = sessionStorage.getItem("bhneToken")
    const pageType = sessionStorage.getItem('bhnePageType')
    getuserInfo(token).then((res) => {
      if (Object.keys(res.data.result).length == 0) {
        this.$message.error("获取用户信息失败，请重新进入页面")
      }
       let menuTypes = res.data.result.zmenu.split(',')

      if (menuTypes.includes(pageType)) {
        console.log("允许进入");
        this.showPage=true
      }else{
          console.log("无权限")
         this.$message.warning("无权限")
         this.showPage=false
      }
      this.form.csmc = res.data.result.USERID
      this.form.dwmc = res.data.result.DWMC
      this.userId = res.data.result.USERID
      this.userType = res.data.result.rank
      this.getList()
    })
    this.showPage=true
    const that = this
    window.onresize = function () {
      that.setTablesHeight()
    }
  },
  methods: {
    removeListPorj(){
      if(this.removeList.length <= 0)return this.$message.warning("请选中数据")
      let newArr = []
      for (let j in this.removeList) {
          newArr.push(this.removeList[j].taskID)
        }
      removeProject({taskid:newArr.join(',')}).then((res) => {
            console.log("删除成功",res);
            if(res.data.result=='success'){
              this.$message.success("删除成功")
              this.getList()
                  this.removeList.forEach(row=>{
                    this.$refs.table.toggleRowSelection(row, false);
                  })
                  this.removeList = []
                 }
          })
    },
    creatChunk(file, chunkSize) {
      const result = []

      for (let i = 0; i < file.size; i += chunkSize) {

        result.push(file.slice(i, i + chunkSize))

      }
      return result
    },

    beforeDesignUpload(file) {
      let fileName = file.name
      let pos = fileName.lastIndexOf('.')
      let lastName = fileName.substring(pos, fileName.length)
      const ext = lastName.toLowerCase()
      // if (ext !== '.doc' && ext !== '.docx' && ext != '.xls' && ext != '.xlsx' && ext != '.txt' && ext !=
      //   '.bmp' && ext != '.word') {
      //   this.$message.error('文件必须为.doc')
      //   return
      // }
    },
    async downLoadTemplates() {
      let res = await downLoadTemplate()
      const blob = new Blob([res.data])
      let link = document.createElement('a')
      link.download = "模板.xlsx"
      link.style.display = "none"
      link.href = URL.createObjectURL(blob)
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)
    },
    // 批量导入模板
    uploadFilesProject(files) {
      const formData = new FormData()
      formData.append('file', files.file)
      formData.append('design', this.form.csmc)
      // formData.append('design', "7dd4112b9020646de0530402a7c0b8dd")
      importFileProjectInfo(formData).then(res => {
        this.$message.success('导入成功')
        console.log(res)
      })
    },
    // 需求说明书
    importFileXqsms(files) {
      let tempUrl = ''
      const loading = this.$loading({
        lock: true,
        text: '上传中',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)'
      })
      const chunkArr = this.creatChunk(files.file, 10 * 1024 * 1024)
      let chunkIndex = 0
      const uploadNextChunk = () => {
        if (chunkIndex >= chunkArr.length) {
          console.log('All chunks uploaded successfully')
          loading.close()
          return
        }
        const file = chunkArr[chunkIndex]

        const formData1 = new FormData()
        formData1.append('id', this.addProjectInfo.id,)
        formData1.append('file', file)
        formData1.append('chunkNumber', chunkIndex)
        formData1.append('totalChunks', chunkArr.length)
        formData1.append('fileName', files.file.name)
        formData1.append('tempDirectory', tempUrl)
        console.log(`Uploading chunk ${chunkIndex} of ${chunkArr.length}`)
        importFileXqsms(formData1).then(res => {
          console.log(res, chunkIndex, "测试返回结果")

          if (chunkIndex === 0 && res) {
            tempUrl = res.data.result
            console.log('Temp URL received:', tempUrl)
          }
          if (['上传成功', 'success'].includes(res.data.result)) {
            this.$message.success("上传成功")
            loading.close()
          }
          chunkIndex++
          uploadNextChunk()
        }).catch(error => {
          console.error('Error uploading chunk:', error)
          this.$message.warning("上传失败，请重试")
          loading.close()
        })
      }
      uploadNextChunk()
    },
    // increaseProgress () {
    //   if (!this.isProgressExecuting) {
    //     this.isProgressExecuting = true
    //     this.executeProgress()
    //   }
    // },
    // async executeProgress () {
    //   for (let i = 0; i < this.gridData.length; i++) {
    //     const task = this.gridData[i]
    //     await this.increaseTaskProgress(task)
    //   }

    //   // 在所有任务完成后触发事件

    //   setTimeout(() => {
    //     this.triggerEvent()
    //   }, 500)
    //   // 清除定时器
    //   clearInterval(this.timer)
    //   this.timer = null
    //   this.isProgressExecuting = false
    // },
    // increaseTaskProgress (task) {
    //   return new Promise((resolve) => {
    //     if (this.isComplete) {
    //       this.interval = setInterval(() => {
    //         const increment = Math.floor(Math.random() * 30) + 1
    //         task.pro = Math.min(task.pro + increment, 100)

    //         if (task.pro === 100) {
    //           task.isSuccess = true
    //           clearInterval(this.interval)
    //           resolve() // 当任务完成时解析Promise
    //         }
    //       }, 50) // 每秒增加一次进度
    //     } else {
    //       this.interval = setInterval(() => {
    //         const increment = Math.floor(Math.random() * 30) + 1
    //         task.pro = Math.min(task.pro + increment, 100)
    //         if (task.pro === 100) {
    //           task.isSuccess = true
    //           clearInterval(this.interval)
    //           resolve() // 当任务完成时解析Promise
    //         }
    //       }, 190) // 每秒增加一次进度
    //     }
    //   })
    // },
    // //进度条加载完毕后触发
    // triggerEvent () {
    //   if (this.isComplete) {
    //     this.obtainDialog = false
    //     this.gridData.forEach((item) => {
    //       item.pro = 0
    //       item.isSuccess = false
    //     })
    //     if (this.isErr) {
    //       this.$message({
    //         message: "获取成功",
    //         type: "success",
    //       })

    //     } else {
    //       this.$message({
    //         message: "获取失败",
    //         type: "warning",
    //       })

    //     }

    //     console.log(this.obtainDialog, "dldldl")
    //   }
    // },
    removeProj(row){
      console.log(row);

      this.$confirm("确定要删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: "warning",
      })
        .then(() => {
          removeProject({ taskid:row.taskID}).then((res) => {
            console.log("删除成功",res);
            if(res.data.result=='success'){
              this.$message.success("删除成功")
              this.getList()
            }
          })
        })
        .catch(() => {

        })
    },
    handleCofirm(row) {
      this.taskID = row.taskID
      if (row.lineID === '') return this.$message.warning("未获取到有效数据")
      this.$confirm("现状数据只能获取一次,是否确定?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: "warning",
      })
        .then(() => {
          this.handleObtain()
        })
        .catch(() => {

        })
    },
    // table获取现状数据
    // handleObtain () {
    //   this.isComplete = false
    //   this.timer = setInterval(this.increaseProgress, 100)
    //   this.obtainDialog = true
    //   this.gethandleObtain(this.taskID)
    //     .then((res) => {
    //       if (res.data.status == "200") {
    //         this.isComplete = true
    //         this.isErr = true
    //         uadateProstate(this.taskID).then((res) => {
    //           console.log(res, "更新状态")
    //           this.getList()
    //         })
    //         // coordinateTransformation(this.taskID).then((res) => {
    //         //   console.log("坐标转换", res)
    //         // })
    //       } else {
    //         this.isComplete = true
    //         this.obtainDialog = false
    //         this.isErr = false
    //       }
    //     })
    //     .catch(() => {
    //       this.isComplete = true
    //       this.obtainDialog = false
    //       this.isErr = false
    //     })
    //   // this.getList();
    //   //     uadateProstate(this.taskID).then((res) => {
    //   //       console.log(res, "更新状态");
    //   //     });
    // },
    setTablesHeight() {
      this.$nextTick(() => {
        const tablesAreaHeight = document.getElementsByClassName('driver')[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        this.tableHeight = windowsAreaHeight - tablesAreaHeight - 120
      })
    },
    // 获取列表
    getList() {
      getSettlement(this.form)
        .then((res) => {
          this.tableData = res.data.data
          this.total = res.data.total
        })
        .catch(() => { })
      // 获取城市下拉
      if (this.dsgsOptions.length === 0) {
        const params = {
          optId: this.form.dwmc,
        }
        getCity(params)
          .then((res) => {
            this.dsgsOptions = res.data.result
          })
          .catch(() => { })
      }
    },
    // 城市点击获取县下拉
    CityPoint(val) {
      const params = {
        optId: val,
        dwmc: this.form.dwmc,
      }
      getCounty(params)
        .then((res) => {
          this.xgsOptions = res.data.result
        })
        .catch(() => { })
    },
    // table列表序号索引
    indexMethod(index) {
      return (this.form.page - 1) * this.form.perPage + index + 1
    },
    // 查询
    query() {

      this.getList()
    },
    // 重置
    clearForm() {
      this.form = {
        //表单参数
        id: "",

        stageType: "0",
        projectName: "",
        pcnd: "",
        BATCH: "",
        dsgs: "",
        xgs: "",
        xmName: "",
        tgzt: "09",
        csmc: this.form.csmc,
        dwmc: this.form.dwmc,
        page: 1,
        perPage: 10,
        source: ''
      }
      this.getList()
    },
    // 复选框
    handleSelectionChange(row) {
      console.log(row);
      this.removeList=row

    },
    handleCurrentChange(row) {
      this.form.page = row
      this.getList()
    },
    handleSizeChange(row) {
      this.form.perPage = row
      this.getList()
    },

    // 提交
    handleSubt(row) {
      this.$confirm("是否确定提交?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: "warning",
      })
        .then(() => {
          let params = { taskid: row.taskID, rank: this.userType }
          handleSubtCS(params)
            .then((res) => {
              if (res.data.message == "success") {
                this.$message({
                  message: "提交成功",
                  type: "success",
                })
              } else {
                this.$message.error("提交失败!")
              }
              this.getList()
            })
            .catch(() => { })
        })
        .catch(() => {

        })
    },
    // 启动设计软件
    handleEamine(row) {
      console.log(row)
      // const routeUrl = this.$router.resolve({
      //   path: "/onlineDesign",
      //   query: { taskID: row.taskID },
      // });
      // this.$router.push({
      //   path: "/onlineDesign",
      //   query: { taskID: row.taskID, routerType: '/projectAgent/RequirementProject' },
      // })
      getURLSJ(row.taskID)
        .then((res) => {
          if (res.data.message == "success") {
            window.location = encodeURI(
              "BhneSJRJ://taskID=" +
              row.taskID +
              "&userID=" +
              this.userId +
              "&token=" +
              "11" +
              "&stageType=0"
            )
          } else {
            this.$message.error("启动失败!")
          }
          console.log(res)
        })
        .catch(() => { })
    },
    // 退回
    handleReturn(row) {
      this.$confirm("是否确定退回?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: "warning",
      })
        .then(() => {
          let params = {
            rank: this.userType,
            taskid: row.taskID
          }
          handleReturnCS(params)
            .then((res) => {
              if (res.data.message == "success") {
                this.$message({
                  message: "退回成功",
                  type: "success",
                })
              } else {
                this.$message.error("退回失败!")
              }
              this.getList()
            })
            .catch(() => { })
        })
        .catch(() => {

        })

    },
    // table 问题清单按钮
    handleProblem(row) {
      this.formProblem.taskid = row.taskID
      ProblemView(this.formProblem)
        .then((res) => {
          this.ProblemData = res.data.result
          this.ProblemTotal = res.data.result.length
        })
        .catch(() => { })
      this.problemDialog = true
    },
    // 问题查看
    ProblemhandleSizeChange(row) {
      this.formProblem.pageIndex = row
      ProblemView(this.formProblem)
        .then((res) => {
          this.ProblemData = res.data.result
          this.ProblemTotal = res.data.result.length
        })
        .catch(() => { })
    },
    // 问题查看
    ProblemhandleCurrentChange(row) {
      this.formProblem.pageSize = row
      ProblemView(this.formProblem)
        .then((res) => {
          this.ProblemData = res.data.result
          this.ProblemTotal = res.data.result.length
        })
        .catch(() => { })
    },
    // table 编辑按钮
    handleEdit(row) {
      this.formEdit.taskID = row.taskID
      this.formEdit = row
      this.editDialog = true
      getXQEditPull()
        .then((res) => {
          this.cityName = res.data.result.cityName //所属公司
          this.gwgzmd = res.data.result.gwgzmd //建设改造地
          this.gwgzyy = res.data.result.gwgzyy
          this.gwjhnd = res.data.result.gwjhnd
          this.gwqyxz = res.data.result.gwqyxz
          this.gwxmjb = res.data.result.gwxmjb
          this.gwxmlb = res.data.result.gwxmlb
          this.gwxmxz = res.data.result.gwxmxz
          this.gwzylb = res.data.result.gwzylb
          this.jcxxsblx = res.data.result.jcxxsblx
          this.projectType = res.data.result.projectType
          this.voltageLevel = res.data.result.voltageLevel
          this.xmdl = res.data.result.xmdl
          this.xmxl = res.data.result.xmxl
          this.xmzjly = res.data.result.xmzjly
          this.xmzycd = res.data.result.xmzycd
          this.xqlb = res.data.result.xqlb
        })
        .catch(() => { })
    },
    // 编辑弹框确认按钮
    editDialogQuery() {
      console.log(this.formEdit, '编辑数据')
      //  EditSave(this.formEdit)
      //         .then((res) => {
      //           console.log(res,'返回结果');
      //           if (res.data.message === "success") {
      //             this.$message({
      //               message: "修改成功",
      //               type: "success",
      //             })
      //           } else {
      //             this.$message.error("修改失败!")
      //           }
      //         })
      //         .catch(() => { })
      //       this.editDialog = false
      this.$refs["basicInfo"].validate((valid) => {
        if (valid) {
          this.$refs["detailsInfo"].validate((valid) => {
            if (valid) {
              EditSave(this.formEdit)
                .then((res) => {
                  if (res.data.message === "success") {
                    this.$message({
                      message: "修改成功",
                      type: "success",
                    })
                  } else {
                    this.$message.error("修改失败!")
                  }
                })
                .catch(() => { })
              this.editDialog = false
            } else {
              this.$message.error("请填写必填项!")
              return false
            }
          })
        } else {
          return false
        }
      })
    },
    // table 需求说明
    handleRequirements(row) {
      this.addProjectInfo.id = row.taskID
      this.requirementsDialog = true
    },
    // 需求说明上传弹框确认按钮
    requirementsDialogQuery() {
      this.requirementsDialog = false
    },

    // table 详情
    handleDetails(row) {
      this.detailsDialog = true
      DetailsView(row.taskID)
        .then((res) => {
          console.log(res.data.result)
          const result = res.data.result
          this.Details.pcnd = result.pcnd
          this.Details.pcnd = result.pcnd
          this.Details.projectName = result.projectName
          this.Details.gwjhnd = result.gwjhnd
          this.Details.gwbzsj = result.gwbzsj
          this.Details.cityName = result.cityName
          this.Details.powerSubstationName = result.powerSubstationName
          this.Details.designInstituteName = result.designInstituteName
          this.Details.designerName = result.designerName
          this.Details.gwxmxz = result.gwxmxz
          this.Details.gwxmjb = result.gwxmjb
          this.Details.gwgzyy = result.gwgzyy
          this.Details.gwgzmd = result.gwgzmd
          this.Details.gwzcssdw = result.gwzcssdw
          this.Details.gwqyxz = result.gwqyxz
          this.Details.gwxmlb = result.gwxmlb
          this.Details.jcxxsblx = result.jcxxsblx
          this.Details.sbmc = result.sbmc
          this.Details.voltageLevel = result.voltageLevel
          this.Details.projectType = result.projectType
          this.Details.xmdl = result.xmdl
          this.Details.xmxl = result.xmxl
          this.Details.xqlb = result.xqlb
          this.Details.xmzycd = result.xmzycd
          this.Details.xmzjly = result.xmzjly
        })
        .catch(() => { })
    },
    // table 现状数据
    // handleObtain (row) {
    //   this.obtainDialog = true
    //   uadateProstate(row.taskID).then((res) => {
    //     if (res.status === 200) {
    //       this.getList()
    //     }
    //   })
    // },
    // 馈线ID
    handleFeeder(row) {
      this.cuttentProCode = row.taskID
      this.feederDialog = true
      this.fullscreenLoading = true
      feeederObtainCurrent(row.taskID).then((res) => {
        console.log(res, " res")
        this.feederData = res.data.result.data
        this.fullscreenLoading = false
      })
    },
    // 馈线保存按钮
    feederDialogQuery() {
      this.feederDialog = false
    },
    // 成果分析
    handleResults(row) {
      this.cuttentresult = row.taskID
      this.resultDialog = true
      this.flag = false
      this.$nextTick(() => {
        this.flag = true
      })
    },
    // 上传传参
    getformData() {
      return {
        id: this.addProjectInfo.id,
      }
    },
    //上传成功
    handleSuccess() {
      this.$message.success("上传成功")
      this.requirementsDialog = false
    },
    // 上传失败
    handleError() {
      this.$message.error("上传失败")
    },
    // 上传前判断
    beforeAvatarUpload(file) {
      let fileName = file.name
      let pos = fileName.lastIndexOf(".")
      let lastName = fileName.substring(pos, fileName.length)
      if (lastName.toLowerCase() !== ".zip") {
        this.$message.error("文件必须为.zip")
        return
      }
    },
  },
}
</script>

<style >
.box-card {
  margin-bottom: 10px;
}

.progresswrapper {
  width: 100%;
  height: 20px;
  color: white;
  margin-left: 3%;
  background: #ffffff;
  position: relative;
}

.pro {
  width: 90%;
  height: 100%;
  background: #00b83f;
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 40px 40px;
  animation: progressbar 2s linear infinite;
}

@keyframes progressbar {
  0% {
    background-position: 40px 0;
  }

  100% {
    background-position: 0 0;
  }
}

.actionAreaPage {
  padding: 0 16px 12px 16px;
  display: flex;

}

.buttonArea {
  height: 18px;
  display: flex;
  line-height: 18px;
}
.el-dialog__body {
  padding: 24px !important;
}
.el-dialog__footer {
  padding: 16px 24px !important;
}
</style>
