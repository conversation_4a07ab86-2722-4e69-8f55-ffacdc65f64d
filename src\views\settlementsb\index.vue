<template>
  <div class="index flex-c h100">
      <div style="height:100vh;width:100vw;background:#fff;  position: absolute;left:0;top:0;z-index:99" v-show="!showPage"></div>
    <!-- 标题 -->
    <div class="main-header">结算申报</div>
    <div class="" style="width: 100%; height: 1px; border-bottom: 1px solid #eee"></div>
    <!-- 表单 -->
    <div class="query-form-box">
      <el-form ref="form" :model="form" :inline="true" label-width="80px">
        <el-row>
          <el-col :span="5">
            <el-form-item label="年度:" prop="pcnd">
              <el-date-picker v-model="form.pcnd" type="year" format="yyyy" value-format="yyyy" placeholder="选择日期"
                @change="NDPoint">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="批次名次:" prop="batch">
              <el-select v-model="form.batch" clearable placeholder="请选择">
                <el-option v-for="item in batchOptions" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="地市公司:" prop="dsgs">
              <el-select v-model="form.dsgs" clearable placeholder="请选择" @change="CityPoint">
                <el-option v-for="item in dsgsOptions" :key="item.cityid" :label="item.cityname" :value="item.cityid">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="lastSearchArea">
            <el-form-item label="县公司:" prop="xgs">
              <el-select v-model="form.xgs" clearable placeholder="请选择">
                <el-option v-for="item in xgsOptions" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="项目名称:" prop="xmName">
              <el-input v-model="form.xmName" clearable placeholder="请输入项目名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="工程名称:" prop="projectName">
              <el-input v-model="form.projectName" clearable placeholder="请输入工程名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="工程编码:" prop="projectName">
              <el-input v-model="form.code" clearable placeholder="请输入工程编码"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="lastSearchArea">
            <el-button class="blue-btn" @click="query()">查询</el-button>
            <el-button @click="clearForm()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="driver"></div>
    <!-- 项目列表 -->
    <div class="tablesArea">
      <el-table :data="tableData" :height="tableHeight" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
        highlight-current-row @selection-change="handleSelectionChange" style="width: 98%; margin: 0px 16px 0 16px">
        <el-table-column type="index" :index="indexMethod" label="序号" align="center" :resizable="false" width="60">
        </el-table-column>
        <el-table-column prop="pcnd" label="年度" align="left">
        </el-table-column>
        <el-table-column prop="batchname" label="项目批次" align="left" width="120">
        </el-table-column>
        <el-table-column prop="projectName" label="工程名称" align="left" width="400px">
        </el-table-column>
        <el-table-column prop="code" label="工程编码" align="left" min-width="120">
        </el-table-column>
        <el-table-column prop="xmmc" label="项目名称" align="left" width="400px">
        </el-table-column>
        <el-table-column prop="cityName" label="地市公司" align="left" width="130px">
        </el-table-column>
        <el-table-column prop="countyName" label="县公司" align="left" width="130px">
        </el-table-column>
        <el-table-column prop="projState" label="任务状态" align="left" min-width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.tgzt == ''" style="color: green">待编辑</span>
            <!--县的显示-->
            <span v-if="scope.row.tgzt == '2'" style="color: green">已提交</span>
            <!-- <span v-if="scope.row.tgzt == '2' && userType == '3'" style="color: green">已提交</span> -->
            <span v-if="scope.row.tgzt == '3' && userType == '3'" style="color: #526ade">评审通过</span>
            <span v-if="scope.row.tgzt == '4' && userType == '3'" style="color: #526ade">市公司待评审</span>
            <span v-if="scope.row.tgzt == '5' && userType == '3'" style="color: #526ade">市公司评审通过</span>
            <span v-if="scope.row.tgzt == '6' && userType == '3'" style="color: #526ade">待报审</span>
            <span v-if="scope.row.tgzt == '7' && userType == '3'" style="color: #526ade">待审核</span>
            <span v-if="scope.row.tgzt == '1' && userType == '3'" style="color: #526ade">待审计</span>
            <!--市的显示-->
            <!-- <span v-if="scope.row.tgzt == '3' && userType == '2'" style="color: #526ade">省公司评审通过</span> -->
            <span v-if="scope.row.tgzt == '4' && userType == '2'" style="color: #526ade">待审核</span>
            <span v-if="scope.row.tgzt == '6' && userType == '2'" style="color: #526ade">待报审</span>
            <span v-if="scope.row.tgzt == '1' && userType == '2'" style="color: #526ade">待审计</span>
            <span v-if="scope.row.tgzt == '3' && userType == '2'" style="color: #526ade">评审通过</span>
            <!--省的显示-->
            <span v-if="scope.row.tgzt == '3' && userType == '1'" style="color: #526ade">评审通过</span>
            <span v-if="scope.row.tgzt == '5' && userType == '1'" style="color: green">已提交</span>
             <span v-if="scope.row.tgzt == '1' && userType == '1'" style="color: #526ade">待审计</span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="address" label="编辑" align="left">
          <template slot-scope="scope">
            <el-button
              size="mini"
              class="el-buttonStyle"
              @click="handleEdit(scope.row)"
              >编辑
            </el-button>
          </template>
        </el-table-column> -->
        <el-table-column label="操作" align="left" fixed="right" width="240px">
          <template slot-scope="scope">
            <span class="directiveHandle">
              <div :class="userType == '1' ||scope.row.tgzt != ''? 'el-buttonDisabled' : 'el-buttonStyle'
                ">
                <span @click="handleEdit(scope.row)"> 编辑 </span>
              </div>
              <span class="el-buttonDriver">|</span>
              <div :class="scope.row.tgzt == '' || userType == '1' ? 'el-buttonDisabled' : 'el-buttonStyle'
                ">
                <span @click="handleaddress(scope.row)"> 报审 </span>
              </div>
              <span class="el-buttonDriver">|</span>
              <div :class="scope.row.tgzt == '' || scope.row.tgzt == '6' || userType == '1' ? 'el-buttonDisabled' : 'el-buttonStyle'
                ">
                <span @click="settlemenetAudit(scope.row)"> 审核 </span>
              </div>
              <span class="el-buttonDriver">|</span>
              <el-dropdown>
                <el-button type="text">
                  <i class="el-icon-more directiveicon" style="font-size: 14px"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="审计结算"
                    :class="scope.row.tgzt == '' || scope.row.tgzt == '6' || scope.row.tgzt == '7' || userType == '1' ? 'el-buttonDisabled' : 'el-buttonStyle'">
                    <span @click="handleEamine(scope.row)"> 审计 </span>
                  </el-dropdown-item>
                  <!-- <el-dropdown-item command="提交" class="el-buttonStyle">
                    <span @click="handleSubt(scope.row)"> 提交 </span>
                  </el-dropdown-item> -->
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination style="margin: 10px" background :current-page="form.page" :page-sizes="pageSize"
      :page-size="form.perPage" layout="total, sizes, prev, pager, next" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" :total="total">
    </el-pagination>
    <!-- 编辑数据 -->
    <el-dialog title="编辑数据" width="20%" :visible.sync="dialogProblem">
      <el-form ref="formEdit" :model="formEdit" :inline="true" :rules="rulesEdit">
        <el-form-item label="年度:" prop="pcnd" label-width="90px">
          <el-date-picker v-model="formEdit.pcnd" type="year" format="yyyy" value-format="yyyy" placeholder="选择日期"
            @change="NDPointEdit">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="批次名次:" prop="batch" label-width="90px">
          <el-select v-model="formEdit.batch" placeholder="请选择" @change="changePcData">
            <el-option v-for="item in batchOptionsEdit" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogProblem = false">取 消</el-button>
        <el-button class="blue-btn" @click="dialogQuery">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 报审 -->
    <el-dialog title="报审" width="95%" top="4vh" style="height: 100vh" :visible.sync="dialogSB" v-if="dialogSB">
      <div style="height: 100%">
        <Declaration :ProCode="cuttentProCode" v-if="flag" @cloaseDialogVis="changeDialog"></Declaration>
      </div>
    </el-dialog>

    <!-- 审核 -->
    <el-dialog title="审核" width="95%" top="4vh" style="height: 100vh" :visible.sync="auditSh" v-if="auditSh">
      <div style="height: 100%">
        <YjAudit @cloaseDialogauditSh="closeSH" v-if="auditShPro" :ProCode="cuttentProCode">
        </YjAudit>
      </div>
    </el-dialog>
     <!-- 审计 -->
     <el-dialog title="审计" width="95%" top="4vh" style="height: 100vh" :visible.sync="dialogSbPro">
      <div style="height: 100%">
        <Dialog :ProCode="cuttentProCode" v-if="flagSbPro" @cloaseDialogVis="closeSbPro" @sumbitDataSH="handleSubt">
        </Dialog>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSb,
  getCity,
  getCounty,
  getXQEditPull,
  getAnnex,
  getGSInformation,
  getJSInformation,
  getFBCost,
  BtnSubmint,
  getPCSel,
  EditSave,
  getuserInfo,
  jssbtj,
  jssbth
} from '@/api/api'
import Declaration from '@/views/settlementsb/components/index'
import Dialog from '@/views/settlementsh/components/index'
import YjAudit from '@/views/settlementsb/components/yjAudit'
export default {
  components: {
    Declaration,
    Dialog,
    YjAudit
  },
  data () {
    return {
      showPage:false,
      flag: false,
      tableHeight: 0,
      flagSbPro: false,
      dialogSbPro: false,
      auditSh: false,
      auditShPro: false,
      userType: '', // 1省 2市 3县
      form: { //表单参数
        id: '',
        projState: '',
        stageType: 5,
        projectName: '',
        rank: '',
        pcnd:  '',
        batch: '',
        dsgs: '',
        xgs: '',
        xmName: '',
        tgzt: '',
        csmc: '',
        dwmc: '',
        page: 1,
        perPage: 10,
        code: '',
      },
      formEdit: {
        batch: '',
        batchname: '',
        pcnd:  '',
        taskID: ''
      },
      cuttentProCode: '',
      isSb: '',
      batchOptionsEdit: [],
      xgsOptions: [], //县公司下拉数据
      dsgsOptions: [], //城市下拉
      batchOptions: [], //批次名次下拉数据
      pageSize: [5, 10, 20, 50, 100], //分页页数
      q: {
        page: 1,
        limit: 5
      },
      total: 0, //总共页数
      tableData: [],
      dialogProblem: false, //编辑
      dialogSB: false, //申报
      tableDatas: [],
      // 附件
      formAnnex: {
        code: '',
        pageSize: 5,
        pageIndex: 1
      },
      tableDataAnnex: [],
      formJY1: { //概算信息
        hj: '',
        jzgcf: '',
        azgcf: '',
        sbgzf: '',
        qtfy: '',
        jbfy: '',
        sjf: '',
        jlf: '',
      },
      formJY2: { //概算信息
        sgjsf: '',
        bstjf: '',
        bsazf: '',
        bstf: '',
        jbybf: '',
        bsjgsbf: '',
        bsjgclf: '',
        jhshrq: '',
        sendDate: '',
      },
      formJY3: { //概算信息
        zyfbgcl: '',
        sszyfbfy: '',
        sdzybfy: '',
        sdzyfbfyzk: '',
        lwfbzgq: '',
        sslwbfy: '',
        sdlwfbfy: '',
        sdsgf: '',
        fbzb: ''
      },
      shData: {},
      rulesEdit:{
        pcnd: [{
          required: true,
          message: '请选择批次年度',
          trigger: 'blur'
        }],
        batch: [{
          required: true,
          message: '请输入批次名称',
          trigger: 'blur'
        }],
      }

    }
  },
  mounted () {

    this.NDPoint()
    this.setTablesHeight()
    const token = sessionStorage.getItem('bhneToken')
     const pageType = sessionStorage.getItem('bhnePageType')
    getuserInfo(token).then((res) => {
         if(Object.keys(res.data.result).length==0){
        this.$message.warning("获取用户信息失败，请重新进入页面")
      }
       let menuTypes = res.data.result.zmenu.split(',')

      if (menuTypes.includes(pageType)) {
        console.log("允许进入");
        this.showPage=true
      }else{
          console.log("无权限")
         this.$message.warning("无权限")
         this.showPage=false
      }
      this.form.csmc = res.data.result.USERID
      this.userType =   res.data.result.rank
      this.form.dwmc = res.data.result.DWMC
      this.form.rank = res.data.result.rank


      this.getList()
    })
    //  this.form.csmc = "123456"
    //   this.userType =  "3"
    //   this.form.dwmc = "3740321"
    //   this.form.rank = "3"
    const that = this
    window.onresize = function () {
      that.setTablesHeight()
    }

  },
  methods: {
    setTablesHeight () {
      this.$nextTick(() => {
        const tablesAreaHeight = document.getElementsByClassName('driver')[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        this.tableHeight = windowsAreaHeight - tablesAreaHeight - 75
      })
    },
    // 查询列表
    getList () {
      console.log(this.form)

      getSb(this.form)
        .then((res) => {
          console.log(res)
          this.tableData = res.data.data
          this.total = res.data.total
        })
        .catch(() => { })
      // 获取城市下拉
      if (this.dsgsOptions.length === 0) {
        const params = {
          optId: this.form.dwmc,
        }
        if (this.dsgsOptions.length === 0) {
          getCity(params)
            .then((res) => {
              this.dsgsOptions = res.data.result
            })
            .catch(() => { })
        }
      }
    },
    changePcData (val) {
      for (let j in this.batchOptionsEdit) {
        if (val === this.batchOptionsEdit[j].id) {
          this.formEdit.batchname = this.batchOptionsEdit[j].name
        }
      }
    },
    changeDialog (val) {
      // 提交后调用更改状态接口
      this.dialogSB = val
      this.flag = val
      console.log('12313', this.cuttentProCode)

      let params = {
        taskid: this.cuttentProCode.taskID,
        rank: this.userType,
        tgzt: this.cuttentProCode.tgzt,
      }
      jssbtj(params).then((tjres) => {
        this.getList()
        console.log('编辑提交', tjres)
        // this.$message({
        //   message: '修改成功!',
        //   type: 'success'
        // })
      })

    },
    closeSbPro (val) {
      this.dialogSbPro = val
      this.flagSbPro = val
      this.getList()
    },
    closeSH (val) {
      let params = {
        taskid: this.shData.taskID,
        rank: this.userType,
        tgzt: this.shData.tgzt,
      }
      jssbtj(params).then((tjres) => {
        this.getList()
        console.log('编辑提交', tjres)
        // this.$message({
        //   message: '修改成功!',
        //   type: 'success'
        // })
      })
      this.auditSh = val
      console.log(this.auditSh)


    },
    // 城市点击获取县下拉
    CityPoint (val) {
      const params = {
        optId: val,
        dwmc: this.form.dwmc,
      }
      console.log(val)
      getCounty(params)
        .then((res) => {
          console.log(res)
          this.xgsOptions = res.data.result

        })
        .catch(() => { })
    },
    NDPointEdit () {
      // 获取批次名词
      const param = {
        optId: this.formEdit.pcnd,
      }
      getPCSel(param)
        .then((res) => {
          this.batchOptionsEdit = res.data.result
        })
        .catch(() => { })
    },
    NDPoint () {
      // 获取批次名词
      const param = {
        optId: this.form.pcnd,
      }
      getPCSel(param)
        .then((res) => {
          console.log(res)
          this.batchOptions = res.data.result
        })
        .catch(() => { })
    },
    // table列表序号索引
    indexMethod (index) {
      return (this.form.page - 1) * this.form.perPage + index + 1
    },
    // 查询
    query () {
      this.getList()
    },
    // 重置
    clearForm () {
      this.form = {
        id: '',
        projState: '',
        stageType: 5,
        projectName: '',
        pcnd: '',
        batch: '',
        dsgs: '',
        xgs: '',
        xmName: '',
        tgzt: '1',
        csmc: this.form.csmc,
        dwmc: this.form.dwmc,
        page: 1,
        perPage: 10,
        code: ''
      }
      this.getList()
    },

    // 复选框
    handleSelectionChange () {

    },
    handleCurrentChange (val) {
      this.form.page = val
      this.getList()
    },
    handleSizeChange (val) {
      this.form.perPage = val
      this.getList()
    },
    // 编辑按钮
    handleEdit (row) {
      this.formEdit.taskID = row.taskID
      this.formEdit.batch = row.batch
      this.formEdit.batchname = row.batchname
      this.formEdit.pcnd = row.pcnd
      this.dialogProblem = true
      // 获取批次名词
      const param = {
        optId: row.pcnd,
      }
      getPCSel(param)
        .then((res) => {
          this.batchOptionsEdit = res.data.result
        })

    },
    // 结算审核
    settlemenetAudit (row) {
      console.log(row, 'dkldkdk')
      this.auditSh = true
      this.auditShPro = false
      this.$nextTick(() => {
        this.auditShPro = true
      })
      this.shData = row
      this.cuttentProCode = row
    },
    // 审计
    handleEamine (row) {
      this.cuttentProCode = row
      this.dialogSbPro = true
      this.flagSbPro = false
      this.isSb = 'shck'
      this.$nextTick(() => {
        this.flagSbPro = true
      })
    },
    // 提交
    async handleSubt (row) {

      this.formAnnex.code = row.code // 附件
      try {
        // 并行执行多个异步请求
        const [gsRes, jsRes, fbRes, annexRes] = await Promise.all([
          getGSInformation(row.code),
          getJSInformation(row.code),
          getFBCost(row.code),
          getAnnex(this.formAnnex)
        ])

        // 处理每个请求的结果
        this.formJY1 = gsRes.data.result
        this.formJY2 = jsRes.data.result
        this.formJY3 = fbRes.data.result
        this.tableDataAnnex = annexRes.data.result

        // 检查附件是否已上传
        // const flag = this.tableDataAnnex.every(item => !!item.name);
        // if (!flag) {
        //   this.$message.warning('请上传附件!');
        //   return;
        // }

        // 检查必填项是否已填写
        // if (
        //   !this.formJY1.jzgcf || !this.formJY1.hj || !this.formJY1.azgcf || !this.formJY1.qtfy ||
        //   !this.formJY1.jbfy || !this.formJY1.sjf || !this.formJY1.jlf || !this.formJY2.bsjgsbf ||
        //   !this.formJY2.bsjgclf || !this.formJY3.zyfbgcl || !this.formJY3.sszyfbfy || !this.formJY3.sdzybfy ||
        //   !this.formJY3.sdzybffyzk || !this.formJY3.lwfbzgq || !this.formJY3.sslwbfy || !this.formJY3.sdlwfbfy ||
        //   !this.formJY3.sdsgf || !this.formJY3.fbzb || !this.formJY2.sgjsf || !this.formJY2.bstf || !this.formJY2.bstjf ||
        //   !this.formJY2.bsazf || !this.formJY2.jbybf || !this.formJY2.jhshrq || !this.formJY2.sbgzf || !this.formJY2.sendDate
        // ) {
        //   this.$message.error('必填项数据不能为空!')
        //   return
        // }

        // 所有检查通过后，调用提交方法
        let params = {
          taskid: row.taskID,
          rank: this.userType,
          tgzt: row.tgzt,
        }
        const submitRes = await jssbtj(params)

        if (submitRes.data.message === 'success') {
          this.$message.success('提交成功!')
          this.dialogSbPro = false
          this.getList()
        } else {
          this.$message.error('提交失败!')
        }
      } catch (error) {
        this.$message.error('请求失败，请重试!')
      }
    },
    // 编辑保存
    dialogQuery () {
      console.log(this.$refs['formEdit']);

      this.$refs['formEdit'].validate(async (result) => {
        if (result) {
          EditSave(this.formEdit)
        .then((res) => {
          if (res.data.message === 'success') {
            let params = {
              taskid: this.formEdit.taskID,
              rank: this.userType,
              tgzt: '',
            }
            jssbtj(params).then((tjres) => {
              console.log('编辑提交', tjres)
              this.$message({
                message: '修改成功!',
                type: 'success'
              })
              this.getList()
            })

          } else {
            this.$message.error('修改失败!')
          }
        })
        .catch(() => { })
      this.dialogProblem = false
        }else{
          this.$message.warning('请填写必填项!')
        }
      })


    },
    SBdialogQuery () {
      console.log(this.cuttentProCode.code)
      this.dialogSB = false
    },
    // 申报
    handleaddress (row) {
      console.log(row.batchname, "batchnamenmane")
      if (row.batchname === '') {
        this.$message.warning('请先编辑批次信息')
      } else {
        this.cuttentProCode = row
        this.dialogSB = true
        this.isSb = '申报'
        this.$nextTick(() => {
          this.flag = true
        })
      }
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 24px !important;
}
::v-deep .el-dialog__footer {
  padding: 16px 24px !important;
}
</style>
