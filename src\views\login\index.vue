<template>
  <div class="login">
    <div class="background">
      <!-- <img :src="imgSrc" width="100%" height="100%" alt="" /> -->
    </div>
    <div class="loginTop">
      <div class="loginLogo">
        <!-- <img :src="logoSrc" alt="" /> -->
      </div>
      <div class="loginTitle">
        <!-- <img :src="titleSrc" alt="" /> -->
      </div>
    </div>
    <div class="loginBottom">
      <el-form ref="form" :model="form" :rules="rules" class="demo-ruleForm">
        <el-form-item prop="user" label="用户名">
          <el-input v-model="form.user" placeholder="账号" type="text">
          </el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.pwenc" placeholder="密码" type="password">
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="login">登录</el-button>
          <!-- <el-button @click="resetForm">重置</el-button> -->
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { login } from '@/api/api'
export default {
  name: 'login',
  data() {
    return {
      // imgSrc: require('../../assets/img/背景.png'),
      // logoSrc: require('../../assets/img/logo.png'),
      // titleSrc: require('../../assets/img/登录标题.png'),
      form: {
        pwenc: '',
        user: '',
      },
      rules: {
        user: [
          {
            required: true,
            message: '请输入您的用户名/手机号',
            trigger: 'blur',
          },
        ],
        pwenc: [
          {
            required: true,
            message: '请输入密码',
            trigger: 'blur',
          },
        ],
      },
    }
  },
  methods: {
    login() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          login(this.form)
            .then((res) => {
              if (res.data.result) {
                if (res.data.result.DWMC == '0') {
                  alert('登陆失败，该用户未匹配所属供电公司')
                  return
                }
                if (res.data.status == '500') {
                  alert('登陆失败，请查看用户名、密码是否正确')
                  return
                }
                sessionStorage.setItem('sdszhyj_username', this.form.user)
                sessionStorage.setItem('sdszhyj_dwmc', res.data.result.DWMC)
                sessionStorage.setItem('sdszhyj_dwname', res.data.result.DWNAME)
                sessionStorage.setItem('sdszhyj_csmc', res.data.result.CSMC)
                sessionStorage.setItem('sdszhyj_userid', res.data.result.USERID)
                if (
                  '3710100' == res.data.result.DWMC ||
                  '37101' == res.data.result.DWMC
                ) {
                  sessionStorage.setItem('userType', '0')
                } else {
                  sessionStorage.setItem('userType', '1')
                }
                sessionStorage.setItem(
                  'sdszhyj_rolename',
                  res.data.result.RNAME
                )
                sessionStorage.setItem('sdszhyj_user', res.data.result.NAME)
                var menu = res.data.result.menu
                sessionStorage.setItem('sdszhyj_sy', menu)
                sessionStorage.setItem('sdszhyj_menu', res.data.result.zmenu)
                sessionStorage.setItem('sdszhyj_rank', res.data.result.rank)
                sessionStorage.setItem('sdszhyj_starttime', Date.now())
                if (menu.indexOf('首页') >= 0) {
                  this.$router.push('../index/index.vue')
                } else {
                  this.$router.push('../projecthz/index.vue')
                }
              }
            })
            .catch(() => {})
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm() {
      this.form = {
        password: '',
        username: '',
      }
    },
  },
}
</script>
<style lang="scss">
.login {
  .background {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: -1;
  }

  .loginTop {
    margin-top: 3%;
    position: absolute;
    left: 50%;
    top: 0;
    transform: translate(-50%, 0);

    .loginLogo {
      display: flex;
      justify-content: center;
      margin-bottom: 4%;
    }

    // .loginTitle {
    //   display: flex;
    //   justify-content: center
    // }
  }

  .loginBottom {
    width: 450px;
    // height: 300px;
    // background-color: pink;
    position: absolute;
    left: 50%;
    bottom: 20%;
    transform: translate(-50%, -30%);
  }

  .el-textarea__inner,
  .el-input__inner {
    background: transparent;
    border-color: #30366a;
    color: #000;
  }

  .el-button {
    width: 100%;
    background-color: #1b235f;
    opacity: 0.7;
    border-color: #30366a;
    color: #fff;
  }
}
</style>
