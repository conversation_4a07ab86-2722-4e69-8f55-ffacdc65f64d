var map // 地图全局变量
var movePointDOM // 移动选择框得全局变量
var drawPolygon // 绘制的矩形
var darwGssPoly // 绘格式刷
var movePointDomDis //
var movePointDomlnglat //
var movePointMarker
var movePointMarkerLineDis
var movePointMarkerDis
var movePointShowAutoLine // 显示自动选型型号
var movePointShowMark // 显示编号
var movePointShowGt // 显示杆头
var movePointShowLineSpec // 线型号
var geometryUtil // 样式
var geolocation // 第一次定位服务
var geo // 连续定位服务
export default {
    map,
    movePointDOM,
    darwGssPoly,
    movePointDomDis,
    movePointMarker,
    movePointMarkerLineDis,
    movePointMarkerDis,
    geometryUtil,
    geolocation,
    geo,
    drawPolygon,
    movePointDomlnglat,
    movePointShowAutoLine,
    movePointShowMark,
    movePointShowGt,
    movePointShowLineSpec
}
export function getUrlValue(kValue) {
    var url = window.location.href // 当前页面的地址
    // var url = '10.193.2.11s9:29303/?data=ca7a72e880ef4a178b842f0cBc4b0775&type=cs'
    var reg = /([^?&=]+)=([^?&=]+)/g
    var obj = {}
    url.replace(reg, function() {
        obj[arguments[1]] = arguments[2]
    })
    for (var keyName in obj) {
        if (keyName === kValue) {
            return obj[keyName]
        }
    }
}
