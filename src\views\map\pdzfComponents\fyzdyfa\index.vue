<template>
  <div class="fyzdyfa">
    <el-form :model="ruleForm" ref="ruleForm" label-width="100px" class="demo-ruleForm" :style="{margin: '20px'}">
      <el-form-item label="方案类别" prop="region">
        <el-select v-model="ruleForm.falb" placeholder="请选择方案类别">
          <el-option v-for="item in selectOptions" :label="item.code" :value="item.code"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="方案编号" prop="region">
        <el-select v-model="ruleForm.fabh" placeholder="请选择方案编号">
          <el-option v-for="item in selectOptions" :label="item.name" :value="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="自定义方案" prop="region">
        <el-select v-model="ruleForm.zdyfa" placeholder="请选择自定义方案">
          <el-option v-for="item in selectZdyOptions" :label="item.customschemename" :value="item.customschemename"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="region">
        <el-select v-model="ruleForm.state" placeholder="请选择状态">
          <el-option label="新建" value="新建"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { apipost } from '@/utils/mapRequest'
  export default {
    components: {},
    props: {
      // 传过来得工程id
      pointId: {
        default: '',
        type: String
      },
    },
    data() {
      return {
        ruleForm: {
          falb: '', // 方案类别
          fabh: '', // 方案编号
          zdyfa: '', // 自定义方案
          zfmc: '', // 站房名称
          state: '新建', // 状态
        },
        selectOptions: [],
        selectZdyOptions: []
      };
    },
    methods: {
      getSelect() {
         let paramsQ={pointid:this.pointId}
        apipost(`/mapModel/querySchema`,paramsQ).then((res)=>{
          this.selectOptions = res.result
          this.ruleForm.falb = res.result[0].code
          this.ruleForm.fabh = res.result[0].name
          this.getZdyFaTable(res.result[0].code)
        })
      },
      getZdyFaTable(val) {
        let params={schema:val}
        apipost(`/mapModel/queryTempSchema`,params).then((res)=>{
          this.selectZdyOptions = res.result
          this.ruleForm.zdyfa = res.result[0].customschemename
        })
      }
    },
    mounted() {
      this.getSelect()
    },
  }

</script>

<style lang="scss" scoped>
  .dqycsj-footer{
    position: relative;
    display: flex;
    justify-content: end;
    margin: 12px 0 12px 0;
    .submitBtn{
      width: 80px;
      background: #526ADE;
      border-radius: 20px;
      display: block;
      color: white;
      text-align: center;
      height: 30px;
      line-height: 30px;
    }
  }
  .editInput{
    display: flex;
    ::v-deep{
      .el-form-item__content{
        margin-left: 0 !important;
        width: 205px !important;
      }
    }
  }
</style>

