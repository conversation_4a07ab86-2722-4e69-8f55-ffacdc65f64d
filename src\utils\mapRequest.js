import axios from 'axios'
import { Toast } from 'vant'
import router from '@/router/index'
import { sm2, sm3 } from "sm-crypto"
const service = axios.create({
  baseURL:process.env.VUE_APP_API_publicUrl, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 80000 // request timeout
})
// request interceptor
service.interceptors.request.use(function (config) {
  // 在发送请求之前做些什么
  // const token = JSON.parse(sessionStorage.getItem('userInfo'))
  // if (token) {
  //   // config.headers['token'] = token.token
  //   config.headers['token'] = '22d09b8f562d4d809cbc5baa68f19cc2-cf3f4ab8-eedd-478d-b73d-d46dfe15b334'
  // }
  const browserFingerprint = sessionStorage.getItem("browserFingerprint")
  // getTime接口返回的安全数据
  let safetyData = sessionStorage.getItem("wbSafetyData")
  if (safetyData) {
    safetyData = JSON.parse(safetyData)
    // 请求头中添加浏览器指纹ID属性
    config.headers.zwId =
      "04" + sm2.doEncrypt(browserFingerprint, safetyData.flagfour)
    // 请求头中添加防重放属性
    config.headers.antiReplayId =
      "04" +
      sm2.doEncrypt(
        `${new Date().getTime() + safetyData.diffeValue}${sm3(process.env.VUE_APP_API_webUrl +
          config.url + (config.data ? JSON.stringify(config.data) : "")
        )}-FGF-${safetyData.flagthr}`,
        safetyData.flagfour
      )
      if(process.env.VUE_APP_API_isHasprofessionalType=='1'){
        config.headers.professionalType = "distribution" //网关标识
      }
    // config.headers.whiteReq = "iyjdlfw" //白名單
    // 入参加密
    config.headers.accessTicket = sessionStorage.getItem('PWYJYBJ-BHXN')
    // config.headers.whiteReq = "iyjdlfw" //白名單
    // 入参加密  上传文件和获取ticket接口不需要加密

      config.data &&
      (config.data = {
        param:
          "04" +
          sm2.doEncrypt(JSON.stringify(config.data), safetyData.flagfour),
      })
    
  }
  config.data && (config.data = JSON.stringify(config.data))
  return config
}, function (error) {
  console.log(error) // for debug
  return Promise.reject(error)
})
// response interceptor
// service.interceptors.response.use(
//   response => {
//     const res = response.data
//     // if the custom code is not 20000, it is judged as an error.
//     if (res.code === 1401) {
//       // Toast.success('登录超时,请重新登录')
//       Toast.success({
//         message: '登录信息过期,请重新登录',
//         position: 'bottom',
//         duration: 900
//       })
//       setTimeout(() => {
//         router.push({
//           path: '/user/login'
//         })
//         sessionStorage.removeItem('userInfo')
//         sessionStorage.removeItem('proList')
//         sessionStorage.removeItem('mainListLen')
//       }, 1000)
//       return res
//     } else {
//       return res
//     }
//   },
//   error => {
//     console.log('登录错误信息' + error) // for debug
//     // Toast.error({
//     //   message: error.message,
//     //   type: 'error',
//     //   duration: 5 * 1000
//     // })
//     return Promise.reject(error)
//   }
// )
service.interceptors.response.use(
  (response) => {
    // getTime接口返回的安全数据 
    const safetyData = JSON.parse(sessionStorage.getItem("wbSafetyData"))
    response.data &&
      (response.data =
        safetyData && !response.config.noDoDecrypt
          ? sm2.doDecrypt(
            response.data.slice(2, response.data.length),
            safetyData.flagfive
          ) // 返参解密
          : response.data)
    // let parseData = typeof response.data === "string" && JSON.parse(response.data)

    // return { ...response, data: parseData || response.data }
    console.warn(response,'返回');
    return typeof response.data === "string"
      ? JSON.parse(response.data)
      : response.data
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 导出常用函数

/**
 * @param {string} url
 * @param {object} data
 * @param {object} params
 */
export function apipost (url, data = {}) {
  const config = {
    method: 'post',
    url: url,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  }
  if (data) config.data = data
  return service(config)
}
export function apipostGetImg (url, data = {}, params = {}) {
  return service({
    method: 'post',
    url,
    data,
    params,
    responseType: 'blob',
    noDoDecrypt: true,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
  })
}
export function postFrom (url, data = {}, params = {}) {
  const formData = new FormData()
  Object.keys(data).forEach((key, index) => {
    if (key === 'voiceFiles') {
      console.log(data)
      for (const j in data.imgFiles) {
        formData.append(`voiceFiles[${j}]`, data[key][j])
      }
    } else if (key === 'imgFiles') {
      for (const j in data.imgFiles) {
        formData.append(`imgFiles[${j}]`, data[key][j])
      }
    } else if (key === 'listArr') {
      for (const j in data[key]) {
        formData.append(`listArr[${j}]`, data[key][j])
      }
    } else {
      formData.append(key, data[key])
    }
  })
  data = formData
  return service({
    method: 'post',
    url,
    data,
    params
  })
}

/**
 * @param {string} url
 * @param {object} params
 */
export function apiget (url, params = {}) {
  return service({
    method: 'get',
    url,
    params
  })
}

/**
 * @param {string} url
 * @param {object} data
 * @param {object} params
 */
export function put (url, data = {}, params = {}) {
  return service({
    method: 'put',
    url,
    params,
    data
  })
}

/**
 * @param {string} url
 * @param {object} params
 */
export function _delete (url, params = {}) {
  return service({
    method: 'delete',
    url,
    params
  })
}
export default service
