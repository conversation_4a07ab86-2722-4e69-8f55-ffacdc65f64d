/*
 * @Description: 描述
 * @version:
 * @Author: fengcan<PERSON><PERSON>@gmail.com
 * @Date: 2023-08-25 15:42:40
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-08-28 17:00:21
 * @FilePath: \safeHttpDemoV2\src\http.js
 */
import axios from "axios"
import { sm2, sm3 } from "sm-crypto"
/**
 * axios 实例
 * */
const axiosInstance = axios.create({
  timeout: 1000 * 60,
  baseURL: ""
})
/**
 * @description: 请求拦截器
 * @param {*}
 * @return {*}
 */
axiosInstance.interceptors.request.use(
  (config) => {
    // 浏览器指纹ID
    const browserFingerprint = sessionStorage.getItem("browserFingerprint")
    // getTime接口返回的安全数据
    let safetyData = sessionStorage.getItem("bhxnsafetyData")
    if (safetyData) {
      safetyData = JSON.parse(safetyData)
      // 请求头中添加浏览器指纹ID属性
      config.headers.zwId =
        "04" + sm2.doEncrypt(browserFingerprint, safetyData.flagfour)
      // 请求头中添加防重放属性
      config.headers.antiReplayId =
        "04" +
        sm2.doEncrypt(
          `${new Date().getTime() + safetyData.diffeValue}${sm3(window.wgParameter.webUrl +
            config.url.split('tygztsp')[1] + (config.data ? JSON.stringify(config.data) : "")
          )}-FGF-${safetyData.flagthr}`,
          safetyData.flagfour
        )
      config.headers.professionalType = "distribution" //网关标识
      // config.headers.whiteReq = "iyjdlfw" //白名單
      // 入参加密
      config.data &&
        (config.data = {
          param:
            "04" +
            sm2.doEncrypt(JSON.stringify(config.data), safetyData.flagfour),
        })
    }
    config.headers['Content-Type'] = 'application/json;charset=UTF-8'

    config.data && (config.data = JSON.stringify(config.data))
    // console.log(config)
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

/**
 * @description: 响应拦截器
 * @param {*}
 * @return {*}
 */
axiosInstance.interceptors.response.use(
  (response) => {

    // getTime接口返回的安全数据 
    const safetyData = JSON.parse(sessionStorage.getItem("bhxnsafetyData"))
      response.data &&
      console.log("getTime返回");
      (response.data =
        safetyData && !response.config.noDoDecrypt
          ? sm2.doDecrypt(
            response.data.slice(2, response.data.length),
            safetyData.flagfive
          ) // 返参解密
          : response.data)
      return typeof response.data === "string"
        ? JSON.parse(response.data)
        : response.data
 
   
    // response.data &&
    // (response.data =
    //   safetyData && !response.config.noDoDecrypt
    //     ? sm2.doDecrypt(
    //       response.data.slice(2, response.data.length),
    //       safetyData.flagfive
    //     ) // 返参解密
    //     : response.data)
    
    // let parseData = typeof response.data === "string" && JSON.parse(response.data)
    // console.log(response.data, '返回参数')
    // return { ...response, data: parseData || response.data }

  },
  (error) => {
    return Promise.reject(error)
  }
)

export default axiosInstance
