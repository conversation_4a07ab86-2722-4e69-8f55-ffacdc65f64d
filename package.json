{"name": "bhxn", "version": "0.1.0", "private": true, "scripts": {"te": "vue-cli-service serve --mode te", "serve": "vue-cli-service serve", "dev": "vue-cli-service serve --mode dev", "pro": "vue-cli-service serve --mode pro", "build": "vue-cli-service build", "build:dev": "vue-cli-service build --mode dev", "build:pro": "vue-cli-service build --mode pro", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "vue-cli-service lint"}, "dependencies": {"@babel/core": "^7.21.8", "@babel/preset-env": "^7.21.5", "@vue-office/excel": "^1.6.0", "@vue/composition-api": "^1.7.2", "axios": "^0.21.4", "core-js": "^3.6.5", "echarts": "^5.4.2", "element-ui": "^2.15.6", "file-saver": "^2.0.5", "fingerprintjs2": "^2.1.4", "html2canvas": "^1.4.1", "jquery": "^3.5.0", "js-audio-recorder": "^1.0.7", "js-cookie": "^3.0.1", "js-web-screen-shot": "^1.9.9-rc.9", "jspdf": "^2.5.1", "luckyexcel": "^1.0.1", "node-sass": "^8.0.0", "qs": "^6.10.1", "sm-crypto": "^0.3.13", "umy-ui": "^1.1.6", "vant": "^2.12.48", "vue": "^2.6.11", "vue-axios": "^3.3.6", "vue-demi": "^0.13.11", "vue-json-viewer": "^2.2.22", "vue-pdf": "^4.3.0", "vue-router": "^3.2.0", "vuex": "^3.6.2", "vxe-table": "^3.6.11", "xe-utils": "^3.5.7", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "^4.5.13", "@vue/cli-plugin-vuex": "^4.5.13", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.6", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "file-loader": "^6.2.0", "sass": "^1.62.1", "sass-loader": "^7.3.1", "style-loader": "^3.3.0", "svg-sprite-loader": "^4.1.3", "svgo": "1.2.2", "vue-cli-plugin-element": "~1.0.1", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"no-unused-vars": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}