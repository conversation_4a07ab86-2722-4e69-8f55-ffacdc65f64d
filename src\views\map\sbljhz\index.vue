<template>
  <div>
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div v-if="showBackAdds" class="maps-zhedNav" @click="backCurrentDom">
            <img class="mapites-backImg" :src="require('@/assets/'+'map/settingImg/backAdds.png')" alt="">
          </div>
          <span />
          设备连接
          <div v-if="!showBackAdds" class="maps-zhedNav" @click="showEveryItemSet">
            <img v-show="!isFoldArea" class="mapites-zhed" :src="require('@/assets/'+'map/settingImg/zhedie.png')" alt="">
            <img v-show="isFoldArea" class="mapites-zhed" :src="require('@/assets/'+'map/settingImg/zkzhedie.png')" alt="">
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img class="settingImg" :src="require('@/assets/'+'map/settingImg/useSetting.png')" alt="">
          <p>启用</p>
        </div>
      </div>
      <van-row v-show="isFoldArea" class="pro-addForm">
        <div class="map-showNav">基本信息</div>
        <van-row>
          <van-field
            v-model="cableLine.mark"
            label="编号"
            placeholder="请输入电缆拐点编号"
          />
        </van-row>
        <div class="map-showNav">选型信息</div>
        <van-row>
          <!--电缆线路通道类别-->
          <van-field
            readonly
            clickable
            :value="cableLine.type"
            label="通道类别"
            placeholder="请选择通道类别"
            @click="settingObj.cableLine.type = true"
          />
          <van-popup v-model="settingObj.cableLine.type" round position="bottom">
            <van-picker
              show-toolbar
              title="通道类别"
              value-key="moduletypename"
              :columns="tdlaying"
              @confirm="onConfirmDlxlSel(0, '', $event)"
              @cancel="settingObj.cableLine.type = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--电压等级-->
          <van-field
            readonly
            clickable
            :value="cableLine.voltage"
            label="电压等级"
            placeholder="电压等级"
            @click="settingObj.cableLine.voltage = true"
          />
          <van-popup v-model="settingObj.cableLine.voltage" round position="bottom">
            <van-picker
              show-toolbar
              title="电压等级"
              value-key="key"
              :columns="voltage"
              @confirm="onConfirmDlxlSel(6, '', $event)"
              @cancel="settingObj.cableLine.listArr.voltage = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <van-field
            readonly
            clickable
            :value="cableLine.plmval"
            label="破路面类型"
            placeholder="破路面类型"
            @click="settingObj.cableLine.plmval = true"
          />
          <van-popup v-model="settingObj.cableLine.plmval" round position="bottom">
            <van-picker
              show-toolbar
              title="破路面类型"
              value-key="key"
              :columns="plmOptions"
              @confirm="onConfirmDlxlSel(9, '', $event)"
              @cancel="settingObj.cableLine.listArr.plmval = false"
            />
          </van-popup>
        </van-row>
        <!--电缆线路电缆设置-->
        <van-row>
          <div class="pro-addTitle">
            电缆设置
            <van-icon name="add-o" size="14" :style="{marginLeft: '0.8rem',top: '0.7rem'}" @click="addCableItem">
              添加
            </van-icon>
          </div>
          <div :style="{width: '100%', overflowX: 'scroll'}">
            <table
              border="1"
              cellspacing="0"
              class="map-table"
              cellpadding="0"
              align="center"
            >
              <tr>
                <th :style="{width: '5rem'}">线路名称</th>
                <th :style="{width: '12rem'}">电缆型号</th>
                <th :style="{width: '14rem'}">通道型号</th>
                <th :style="{width: '12rem'}">终端头型号</th>
                <th :style="{width: '12rem'}">中间头型号</th>
                <th :style="{width: '5rem'}">状态</th>
                <th :style="{width: '5rem'}">终端头</th>
                <th :style="{width: '5rem'}">中间头</th>
                <th :style="{width: '5rem'}">编辑</th>
              </tr>
              <tr v-for="(item,index) in cableLine.listArr">
                <!--电缆设置-->
                <td>
                  <van-field
                    v-model="item.lineName"
                    placeholder="请输入电路名称"
                  />
                </td>
                <!--电缆型号-->
                <td>
                  <van-field
                    readonly
                    clickable
                    :value="item.lineModel"
                    class="map-dlxh"
                    placeholder="电缆型号"
                    @click="settingObj.cableLine.listArr[index].cableLineModel = true"
                  />
                  <van-popup v-model="settingObj.cableLine.listArr[index].cableLineModel" round position="bottom">
                    <van-picker
                      show-toolbar
                      title="电缆型号"
                      value-key="name"
                      :columns="cableLineModel"
                      @confirm="onConfirmDlxlSel(2, index, $event)"
                      @cancel="settingObj.cableLine.listArr[index].cableLineModel = false"
                    />
                  </van-popup>
                </td>
                <td>
                  <van-field
                    readonly
                    clickable
                    :value="item.tdTypeSpec"
                    class="map-dlxh"
                    placeholder="通道型号"
                    @click="settingObj.cableLine.listArr[index].tdType = true"
                  />
                  <van-popup v-model="settingObj.cableLine.listArr[index].tdType" round position="bottom">
                    <van-picker
                      show-toolbar
                      title="通道型号"
                      value-key="moduleName"
                      :columns="cableLineTdModel"
                      @confirm="onConfirmDlxlSel(1, index, $event)"
                      @cancel="settingObj.cableLine.listArr[index].tdType = false"
                    />
                  </van-popup>
                </td>
                <!--终端头型号-->
                <td>
                  <van-field
                    readonly
                    clickable
                    class="map-dlxh"
                    :value="item.zdtModel"
                    placeholder="终端头型号"
                    @click="settingObj.cableLine.listArr[index].zdtModel = true"
                  />
                  <van-popup v-model="settingObj.cableLine.listArr[index].zdtModel" round position="bottom">
                    <van-picker
                      show-toolbar
                      title="终端头型号"
                      value-key="moduleName"
                      :columns="zdtList"
                      @confirm="onConfirmDlxlSel(7, index, $event)"
                      @cancel="settingObj.cableLine.listArr[index].zdtModel = false"
                    />
                  </van-popup>
                </td>
                <!--中间头型号-->
                <td>
                  <van-field
                    readonly
                    clickable
                    class="map-dlxh"
                    :value="item.zjtModel"
                    placeholder="中间头型号"
                    @click="settingObj.cableLine.listArr[index].zjtModel = true"
                  />
                  <van-popup v-model="settingObj.cableLine.listArr[index].zjtModel" round position="bottom">
                    <van-picker
                      show-toolbar
                      title="中间头型号"
                      value-key="moduleName"
                      :columns="zjtList"
                      @confirm="onConfirmDlxlSel(8, index, $event)"
                      @cancel="settingObj.cableLine.listArr[index].zjtModel = false"
                    />
                  </van-popup>
                </td>
                <!--电缆状态-->
                <td>
                  <van-field
                    readonly
                    clickable
                    class="map-dlxh"
                    :value="item.lineState"
                    placeholder="状态"
                    @click="settingObj.cableLine.listArr[index].cableLineState = true"
                  />
                  <van-popup v-model="settingObj.cableLine.listArr[index].cableLineState" round position="bottom">
                    <van-picker
                      show-toolbar
                      title="状态"
                      value-key="key"
                      :columns="cableLineState"
                      @confirm="onConfirmDlxlSel(3, index, $event)"
                      @cancel="settingObj.cableLine.listArr[index].cableLineState = false"
                    />
                  </van-popup>
                </td>
                <!--终端头-->
                <td :style="{paddingLeft: '1.8rem'}">
                  <van-checkbox v-model="item.terminal" />
                </td>
                <!--中间头-->
                <td :style="{textAlign: 'center'}">
                  <van-field
                    v-model="item.middle"
                    placeholder="中间头"
                  />
                </td>
                <td :style="{textAlign: 'center', color: 'red'}">
                  <span @click="removeMainLine(2, index)">删除</span>
                </td>
              </tr>
            </table>
          </div>
        </van-row>
      </van-row>
    </div>
  </div>
</template>

<script>
import { apiget,apipost } from '@/utils/mapRequest'
import { Toast } from 'vant'

export default {
  components: {},
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false
    },
    // 编辑的id
    editData: {
      type: Object,
      defaults: () => {
      }
    },
    // 通道型号
    tdxhList: {
      type: Array,
      defaults: () => []
    },
    // 通道类别
    tdlbList: {
      type: Array,
      defaults: () => []
    },
    // 电缆线路
    dlxlList: {
      type: Array,
      defaults: () => []
    },
    // 中间头型号
    zjtTypeList: {
      type: Array,
      defaults: () => []
    },
    // 终端头型号
    zdtTypeList: {
      type: Array,
      defaults: () => []
    },
    remodeState: {
      type: Boolean,
      defaults: false
    }
  },
  data() {
    return {
      isFoldArea: false,
      tdlaying: [], // 通道类别
      voltage: [
        {
          key: '10kV',
          value: '10kV'
        },
        {
          key: '0.4kV',
          value: '0.4kV'
        }
      ], // 电压等级
      plmOptions: [
        {
          key: '混凝土路面(150mm以下)',
          value: '混凝土路面(150mm以下)'
        },
        {
          key: '混凝土路面(250mm以下)',
          value: '混凝土路面(250mm以下)'
        },
        {
          key: '花岗石路面',
          value: '花岗石路面'
        },
        {
          key: '彩砖路面',
          value: '彩砖路面'
        },
        {
          key: '绿化带',
          value: '绿化带'
        },
        {
          key: '沥青路面',
          value: '沥青路面'
        }
      ],
      cableLineModel: [], // 电缆线路电缆信号 从后台读
      cableLineTdModel: [], // 土建路径型号的选项
      zdtList: [], // 终端头型号
      zjtList: [], // 中间头型号
      cableLineState: [], // 电缆线路状态
      lineState: [
        {
          key: '新建',
          value: '新建'
        },
        {
          key: '原有',
          value: '原有'
        },
        {
          key: '拆除',
          value: '拆除'
        }
      ],
      settingObj: {
        // 电缆线路
        cableLine: {
          type: false, // 通道类别
          backLine: false, // 通道型号
          voltage: false, // 电压等级
          plmval: false, // 破路面类型
          listArr: [
            {
              cableLineModel: false, // 电缆型号
              cableLineState: false, // 电缆状态
              tdType: false, // 通道型号
              zdtModel: false, // 中间头下拉是否使用
              zjtModel: false // 中间头是否使用
            }
          ]
        }
      },
      // 电缆线路
      cableLine: {
        type: '排管', // 通道类别
        isStart: '',
        voltage: '10kV', // 电压等级
        plmval: '混凝土路面(150mm以下)', // 破路面的值
        imgList: [], // 文件列表
        mark: '', // 编号
        message: '', // 备注信息
        audioList: [], // 语音列表
        listArr: [
          {
            lineName: '线路1', // 线路名称
            lineModel: '', // 型号
            tdTypeSpec: '', // 通道型号
            tdType: '', // 通道型号id
            lineModelId: '', // 型号
            lineState: '新建', // 状态
            terminal: true, // 终端头
            zdtModel: '', // 终端头型号
            zdtModelId: '', // 终端头型号
            zjtModel: '', // 中间头型号
            zjtModelId: '', // 终端头型号
            lineLength: 0,
            middle: '250' // 中间头
          }
        ]
      }
    }
  },
  watch: {
    remodeState: {
      handler(newVal) {
        if (newVal) {
          // 改后不显示拆除的数据
          this.cableLineState = this.lineState.slice(0, 2)
          for (const j in this.cableLine.listArr) {
            this.cableLine.listArr[j].lineState = '新建'
          }
        } else {
          // 改前不显示新建的数据
          this.cableLineState = this.lineState.slice(1)
          for (const j in this.cableLine.listArr) {
            this.cableLine.listArr[j].lineState = '原有'
          }
        }
      },
      deep: true,
      immediate: true
    },
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal
      },
      deep: true,
      immediate: true
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal
        if (data.moduleType === 'DLXLHZ') {
          this.cableLine.listArr = []
          for (const j in data.listArr) {
            const obj = {
              lineName: data.listArr[j].lineName, // 线路名称
              lineModel: data.listArr[j].lineModel, // 型号
              lineModelId: data.listArr[j].moduleId, // 型号
              lineState: data.listArr[j].state, // 状态
              tdTypeSpec: data.listArr[j].tdTypeSpec, // 通道型号
              tdType: data.listArr[j].tdType, // 通道型号
              terminal: data.listArr[j].terminal === '1', // 终端头
              lineLength: data.listArr[j].lineLength,
              middle: data.listArr[j].middle, // 中间头
              zdtModel: data.listArr[j].zdtModelSpec, // 终端头型号
              zdtModelId: data.listArr[j].zdtModelId, // 终端头型号
              zjtModel: data.listArr[j].zjtModelSpec, // 中间头型号
              zjtModelId: data.listArr[j].zjtModelId // 终端头型号
            }
            const isShow_one = {
              cableLineModel: false, // 电缆型号
              tdType: false,
              cableLineState: false, // 电缆状态
              zdtModel: false, // 中间头下拉是否使用
              zjtModel: false // 中间头是否使用
            }
            this.settingObj.cableLine.listArr.push(isShow_one)
            this.cableLine.listArr.push(obj)
          }
          this.getAwaitTowerOrLineType(33, '', 3, '', '', 'DLTD', '', '', '', data.category)
          // 查询电缆线路电缆头
          this.getAwaitTowerOrLineType(22, '', 4, '', 'DLZJT', '', '', data.voltage)
          this.getAwaitTowerOrLineType(23, '', 4, '', 'DLZDT', '', '', data.voltage)
          this.getAwaitTowerOrLineType(24, 4, 1, '', '', '', '', data.voltage)
          this.cableLine.isStart = data.isStart
          this.cableLine.type = data.category
          this.cableLine.modelId = data.lineTypeiD
          this.cableLine.model = data.lineType
          this.cableLine.voltage = data.voltage
          this.cableLine.plmval = data.plmType
          this.cableLine.mark = data.mark
          this.cableLine.message = data.note
        }
      },
      deep: true
    },
    // 通道型号
    tdxhList: {
      handler(newVal) {
        this.cableLineTdModel = newVal
        this.cableLine.listArr[0].tdTypeSpec = newVal[0].moduleName
        this.cableLine.listArr[0].tdType = newVal[0].moduleID
      },
      deep: true
    },
    // 电缆线路
    dlxlList: {
      handler(newVal) {
        this.cableLineModel = newVal
        this.cableLine.listArr[0].lineModel = newVal[0].name
        this.cableLine.listArr[0].lineModelId = newVal[0].id
      },
      deep: true
    },
    // 通道类别
    tdlbList: {
      handler(newVal) {
        this.cableLine.aisleType = newVal[0].moduletypename
        this.tdlaying = newVal
      },
      deep: true
    },
    // 中间头型号
    zjtTypeList: {
      handler(newVal) {
        this.cableLine.listArr[0].zjtModel = newVal[0].moduleName
        this.cableLine.listArr[0].zjtModelId = newVal[0].moduleID
        this.zjtList = newVal
      },
      deep: true
    },
    // 终端头型号
    zdtTypeList: {
      handler(newVal) {
        this.cableLine.listArr[0].zdtModel = newVal[0].moduleName
        this.cableLine.listArr[0].zdtModelId = newVal[0].moduleID
        this.zdtList = newVal
      },
      deep: true
    }
  },
  mounted() {

  },
  methods: {
    /**
       * 提交数据
       */
    submitData() {
      const parma = {
        type: 21,
        param: this.cableLine,
        visParam: this.settingObj.cableLine
      }
      this.$emit('submitChildData', parma)
    },
    backCurrentDom() {
      this.$emit('backCurrentDom')
      // 变电站
      this.cableLine.valtage = '35kV'
      this.cableLine.state = '新建'
      this.cableLine.name = '编号1'
      this.cableLine.plmType = '混凝土路面(150mm以下)'
      const stateText = this.remodeState ? '新建' : '原有'
      this.cableLine.listArr = [
        {
          lineName: '线路1', // 线路名称
          lineType: this.cableLineModel[0].name, // 型号
          lineModelId: this.cableLineModel[0].id, // 型号
          lineState: stateText, // 状态
          terminal: true, // 终端头
          zdtModel: this.zjtList[0].moduleName, // 终端头型号
          zdtModelId: this.zjtList[0].moduleID, // 终端头型号
          zjtModel: this.zdtList[0].moduleName, // 中间头型号
          zjtModelId: this.zdtList[0].moduleID, // 终端头型号
          lineLength: 0,
          middle: '250' // 中间头
        }
      ]
      this.settingObj.cableLine.state = false
      this.settingObj.cableLine.valtage = false
      this.settingObj.cableLine.listArr = [
        {
          lineState: false, // 线路状态
          tdType: false, // 通道型号
          lineType: false, // 线路状态
          lineModel: false, // 线路型号
          zjtModel: false, // 线路型号
          zdtModel: false // 线路型号
        }
      ]
      // 变电站通道型号
      this.cableLineTdModel = this.tdxhList
      this.cableLine.listArr[0].tdTypeSpec = this.tdxhList[0].moduleName
      this.cableLine.listArr[0].tdType = this.tdxhList[0].moduleID
      // 变电站电缆线路
      this.cableLineModel = this.dlxlList
      this.cableLine.listArr[0].lineModel = this.dlxlList[0].name
      this.cableLine.listArr[0].lineModelId = this.dlxlList[0].id
      // 变电站通道类别
      this.cableLine.aisleType = this.tdlbList[0].moduletypename
      this.tdlaying = this.tdlbList
      // 变电站中间头型号
      this.cableLine.listArr[0].zjtModel = this.zjtTypeList[0].moduleName
      this.cableLine.listArr[0].zjtModelId = this.zjtTypeList[0].moduleID
      this.zjtList = this.zjtTypeList
      // 变电站终端头型号
      this.cableLine.listArr[0].zdtModel = this.zdtTypeList[0].moduleName
      this.cableLine.listArr[0].zdtModelId = this.zdtTypeList[0].moduleID
      this.zdtList = this.zdtTypeList
    },
    showEveryItemSet() {
      this.isFoldArea = !this.isFoldArea
    },
    /**
       * 电缆线路
       */
    onConfirmDlxlSel(type, index, item) {
      const val = item.value
      switch (type) {
        case 0:
          this.cableLine.type = item.moduletypename
          this.getTowerOrLineType(21, '', 4, '', item.moduletypekey, '', '', '')
          this.settingObj.cableLine.type = false
          break
        case 1:
          this.cableLine.listArr[index].tdTypeSpec = item.moduleName
          this.cableLine.listArr[index].tdType = item.moduleID
          this.settingObj.cableLine.listArr[index].tdType = false
          break
        case 2:
          this.settingObj.cableLine.listArr[index].cableLineModel = false
          this.cableLine.listArr[index].lineModel = item.name
          this.cableLine.listArr[index].lineLength = item.lineLength
          this.cableLine.listArr[index].lineModelId = item.id
          break
        case 3:
          this.settingObj.cableLine.listArr[index].cableLineState = false
          this.cableLine.listArr[index].lineState = val
          break
        case 5:
          this.settingObj.cableLine.cableLineHeadModel = false
          this.cableLine.cableLineHeadModel = val
          break
        case 6:
          this.settingObj.cableLine.voltage = false
          this.cableLine.voltage = val
          // 电缆中间头
          this.getTowerOrLineType(22, '', 4, '', 'DLZJT', '', '', val)
          // 电缆终端头
          this.getTowerOrLineType(23, '', 4, '', 'DLZDT', '', '', val)
          // 电缆线路电缆型号
          this.getTowerOrLineType(24, 4, 1, '', '', '', '', val)
          break
        case 7:
          this.settingObj.cableLine.listArr[index].zdtModel = false
          this.cableLine.listArr[index].zdtModel = item.moduleName
          this.cableLine.listArr[index].zdtModelId = item.moduleID
          break
        case 8:
          this.settingObj.cableLine.listArr[index].zjtModel = false
          this.cableLine.listArr[index].zjtModel = item.moduleName
          this.cableLine.listArr[index].zjtModelId = item.moduleID
          break
        case 9:
          this.settingObj.cableLine.plmval = false
          this.cableLine.plmval = val
          break
      }
    },
    getTowerOrLineType(settype, type, moduleType, materialsTypeKey, moduleTypeKey, parentKey, moduleCode, voltage, moduleName) {
      const that = this
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: 'aaf86d47-14b7-4da6-9938-398e476a2a75' // 版本号暂时写死 版本号是后台给的
      }
      apipost('/moduleSelection/selectModuleData', param).then(function(res) {
        if (res.code === 1001) {
          switch (settype) {
            case 21:
              // 土建路径通道型号
              that.cableLineTdModel = res.data
              if (res.data.length === 0) {
                that.cableLine.listArr[0].tdTypeSpec = res.data[0].moduleName
                that.cableLine.listArr[0].tdType = res.data[0].moduleID
              } else {
                that.cableLine.listArr[0].tdTypeSpec = ''
                that.cableLine.listArr[0].tdType = ''
              }
              break
            case 22:
              // 土建路径电缆中间头
              that.zjtList = res.data
              if (res.data.length !== 0) {
                that.cableLine.listArr[0].zjtModel = res.data[0].moduleName
                that.cableLine.listArr[0].zjtModelId = res.data[0].moduleID
              }
              break
            case 23:
              // 土建路径电缆终端头
              that.zdtList = res.data
              if (res.data.length !== 0) {
                that.cableLine.listArr[0].zdtModel = res.data[0].moduleName
                that.cableLine.listArr[0].zdtModelId = res.data[0].moduleID
              }
              break
            case 24:
              if (voltage === '10kV') {
                // 土建路径电缆型号
                that.cableLineModel = res.data.tenDl
                if (res.data.tenDl.length !== 0) {
                  that.cableLine.listArr[0].lineType = res.data.tenDl[0].name
                  that.cableLine.listArr[0].lineModelId = res.data.tenDl[0].id
                }
              } else {
                // 土建路径电缆型号
                that.cableLineModel = res.data.lowDl
                if (res.data.lowDl.length !== 0) {
                  that.cableLine.listArr[0].lineType = res.data.lowDl[0].name
                  that.cableLine.listArr[0].lineModelId = res.data.lowDl[0].id
                }
              }
              break
          }
        }
      })
    },
    async getAwaitTowerOrLineType(settype, type, moduleType, materialsTypeKey, moduleTypeKey, parentKey, moduleCode, voltage, moduleName, selectVal, selectLevelTwoVal) {
      const that = this
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: 'aaf86d47-14b7-4da6-9938-398e476a2a75' // 版本号暂时写死 版本号是后台给的
      }
      await apipost('/moduleSelection/selectModuleData', param).then(function(res) {
        if (res.code === 1001) {
          switch (settype) {
            case 22:
              // 土建路径电缆中间头
              that.zjtList = res.data
              break
            case 23:
              // 土建路径电缆终端头
              that.zdtList = res.data
              break
            case 24:
              // 土建路径电缆型号
              if (voltage === '10kV') {
                that.cableLineModel = res.data.tenDl
              } else {
                that.cableLineModel = res.data.lowDl
              }
              break
            case 33:
              // 查询电缆线路通道类别
              that.tdlaying = res.data
              let tdlbTypes
              for (const j in res.data) {
                if (selectVal === res.data[j].moduletypename) {
                  tdlbTypes = res.data[j].moduletypekey
                }
              }
              // 查询电缆线路通道型号
              that.getAwaitTowerOrLineType(34, '', 4, '', tdlbTypes, '', '', '')
              break
            case 34:
              // 查询电缆线路通道型号
              that.cableLineTdModel = res.data
              break
          }
        }
      })
    },
    addCableItem() {
      const stateText = this.remodeState ? '新建' : '原有'
      const item_two = {
        lineName: '线路1', // 线路名称
        lineType: this.cableLineModel[0].name, // 型号
        lineModelId: this.cableLineModel[0].id, // 型号
        lineState: stateText, // 状态
        terminal: true, // 终端头
        zdtModel: this.zjtList[0].moduleName, // 终端头型号
        zdtModelId: this.zjtList[0].moduleID, // 终端头型号
        zjtModel: this.zdtList[0].moduleName, // 中间头型号
        zjtModelId: this.zdtList[0].moduleID, // 终端头型号
        lineLength: 0,
        middle: '250' // 中间头
      }
      const isshow_two = {
        zdtModel: false, // 中间头下拉是否使用
        zjtModel: false, // 中间头是否使用
        tdType: false, // 通道型号
        cableLineModel: false, // 电缆型号
        cableLineState: false // 电缆状态
      }
      this.cableLine.listArr.push(item_two)
      this.settingObj.cableLine.listArr.push(isshow_two)
    },
    removeMainLine(type, index) {
      if (this.cableLine.listArr.length !== 1) {
        this.settingObj.cableLine.listArr.splice(index, 1)
        this.cableLine.listArr.splice(index, 1)
      } else {
        Toast.fail('最少有一条线路!')
      }
    }
  }
}

</script>

<style lang="sass" scoped>
</style>

