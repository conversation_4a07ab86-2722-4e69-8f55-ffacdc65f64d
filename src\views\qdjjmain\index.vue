<template>
  <el-container class="home-container">
    <!--头部  -->
    <!-- <el-header>
      <div>
        <span> 配电网工程造价编制</span>
      </div>
    </el-header> -->
    <!-- 页面主体区  嵌套容器  包裹 Aside与Main -->
    <el-container>
      <!-- 左侧 -->
      <el-aside width="200px">
        <el-menu
          @select="changeMenu"
          default-active="/projectinfo"
          class="el-menu-vertical-demo"
          background-color="#ffffff"
          text-color="#545c64"
          active-text-color="#ffd04b"
        >
          <el-menu-item index="/qdjj/projectinfo">
            <i class="el-icon-location"></i>
            <span>工程数据配置</span>
          </el-menu-item>

          <el-menu-item index="/qdjj/jsinfo">
            <i class="el-icon-setting"></i>
            <span>技经数据配置</span>
          </el-menu-item>
          <el-menu-item index="/qdjj/depz">
            <i class="el-icon-setting"></i>
            <span slot="title">定额数据配置</span>
          </el-menu-item>
          <!-- <el-menu-item index="3" >
       <i class="el-icon-document"></i>
       <span slot="title">组合件</span>
     </el-menu-item> -->

          <el-menu-item index="/qdjj/jzinfo">
            <i class="el-icon-setting"></i>
            <span slot="title">建筑工程定额套用</span>
          </el-menu-item>
          <el-menu-item index="/qdjj/azinfo">
            <i class="el-icon-setting"></i>
            <span slot="title">安装工程定额套用</span>
          </el-menu-item>
          <el-menu-item index="/qdjj/ccgc">
            <i class="el-icon-setting"></i>
            <span slot="title">拆除工程定额套用</span>
          </el-menu-item>
          <el-menu-item index="/qdjj/qtfy">
            <i class="el-icon-setting"></i>
            <span slot="title">其他费用定额套用</span>
          </el-menu-item>
          <el-menu-item index="/qdjj/jrg">
            <i class="el-icon-setting"></i>
            <span slot="title">计日工</span>
          </el-menu-item>
          <el-menu-item index="/qdjj/cgsc">
            <i class="el-icon-setting"></i>
            <span slot="title">成果计算</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      <!-- 主体 -->
      <el-main>
        <router-view
          :style="{ overflow: 'scroll', height: '100vh' }"
        ></router-view>
      </el-main>
    </el-container>
  </el-container>
</template>
<script>
import azinfo from './azgc/index.vue'
import ccgc from './ccgc/index.vue'
import cgsv from './cgsv/index.vue'
import jrg from './jrg/index.vue'
import jsinfo from './jsinfo/index.vue'
import jzinfo from './jzgc/index.vue'
import projectinfo from './project/index.vue'
import qtinfo from './qtfy/index.vue'
export default {
  name: 'Billing',
  components: {
    azinfo,
    ccgc,
    cgsv,
    jrg,
    jsinfo,
    jzinfo,
  },
  return() {},
  mounted() {
    this.getParams()
    console.log(this.$route.query, 'ddlldl')
    const that = this
    // 获取当前屏幕的高度
    window.onresize = () => {
      return (() => {
        window.screenHeight = document.body.clientHeight - 90
        that.screenHeight = window.screenHeight
      })()
    }
  },
  //    var projectid=localStorage.getItem('projectId'),
  methods: {
    getParams() {
      // 从本地获取主页面的项目id
      var routerParams = localStorage.getItem('projectId')
      if (routerParams == null) {
        routerParams = this.$route.query.projectId
      }
    },
    changeMenu(val) {
      console.log(val)
      this.$router.push({
        path: val + '?projectId=' + this.$route.query.projectId,
      })
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep {
  .el-menu-item.is-active {
    background: #f0f4ff !important;
    color: #526ade !important;
  }
  .el-menu-item:focus,
  .el-menu-item:hover {
    background: #f0f4ff !important;
    color: #526ade !important;
  }
}
.home-container {
  height: 100%;
  width: 100%;
}

.el-header {
  background-color: #51b4f7;
  display: flex;
  justify-content: space-between;
  padding-left: 0px;
  align-items: center;
  color: #fff;
  font-size: 20px;
  height: 70px;
}

.el-aside {
  background-color: #ffffff;
}

.el-main {
  background-color: #eaedf1;
}
</style>
