<template>
  <div class="index flex-c h100">
    <!-- 标题 -->
    <div class="main-header">施设项目</div>
    <div
      class=""
      style="width: 100%; height: 1px; border-bottom: 1px solid #eee"
    ></div>
    <!-- 表单 -->
    <div class="query-form-box">
      <el-form ref="form" :model="form" :inline="true" label-width="80px">
        <el-row>
          <el-col :span="5">
            <el-form-item label="年度:" prop="PCND">
              <el-date-picker
                v-model="form.pcnd"
                format="yyyy"
                value-format="yyyy"
                type="year"
                placeholder="选择年"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="地市公司:" prop="dsgs">
              <el-select
                v-model="form.dsgs"
                clearable
                placeholder="请选择"
                @change="CityPoint"
              >
                <el-option
                  v-for="item in dsgsOptions"
                  :key="item.cityid"
                  :label="item.cityname"
                  :value="item.cityid"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="县公司:" prop="xgs">
              <el-select v-model="form.xgs" clearable placeholder="请选择">
                <el-option
                  v-for="item in xgsOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="lastSearchArea">
            <el-form-item label="工程名称:" prop="projectName">
              <el-input
                v-model="form.projectName"
                clearable
                placeholder="请输入工程名称"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="状态:" prop="projState">
              <el-select
                v-model="form.projState"
                clearable
                placeholder="请选择"
              >
                <el-option label="待处理" value="01">待处理</el-option>
                <el-option label="处理中" value="02">处理中</el-option>
                <el-option label="已提交" value="03">已提交</el-option>
                <el-option label="评审通过" value="04">评审通过</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="19" class="lastSearchArea">
            <el-button class="blue-btn" @click="query()" icon="el-icon-search"
              >查询</el-button
            >
            <el-button @click="clearForm()" icon="el-icon-refresh"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="driver"></div>
    <!-- 项目列表 -->
    <div class="tablesArea">
      <el-table
        :data="tableData"
        :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
        :height="tableHeight"
        highlight-current-row
        @selection-change="handleSelectionChange"
        style="width: 98%; margin: 0px 16px 0 16px"
      >
        <el-table-column
          type="index"
          label="序号"
          align="center"
          :index="indexMethod"
          :resizable="false"
          width="60"
        >
        </el-table-column>
        <el-table-column prop="pcnd" label="年度" align="center">
        </el-table-column>
        <el-table-column
          prop="projectName"
          label="工程名称"
          align="center"
          width="400px"
        >
        </el-table-column>
        <el-table-column
          prop="batchname"
          label="项目批次"
          align="center"
          min-width="150px"
        >
        </el-table-column>
        <el-table-column
          prop="xmmc"
          label="项目名称"
          align="center"
          width="400px"
        >
        </el-table-column>
        <el-table-column
          prop="code"
          label="项目管理编码"
          align="center"
          width="150px"
        >
        </el-table-column>
        <el-table-column
          prop="cityName"
          label="地市公司"
          align="center"
          width="130px"
        >
        </el-table-column>
        <el-table-column
          prop="countyName"
          label="县公司"
          align="center"
          width="130px"
        >
        </el-table-column>
        <el-table-column
          prop="voltageLevel"
          label="电压等级"
          align="center"
          width="100px"
        >
        </el-table-column>
        <el-table-column label="适用深度规范" align="center" width="140px">
          <template slot-scope="scope">
            <span v-if="scope.row.projectType == '01'">架空</span>
            <span v-if="scope.row.projectType == '02'">电缆</span>
            <span v-if="scope.row.projectType == '03'">配变</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="projState"
          label="任务状态"
          align="center"
          width="100px"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.projState == '01'" style="color: skyblue"
              >待处理</span
            >
            <span
              v-else-if="scope.row.projState == '02'"
              style="color: orangered"
              >处理中</span
            >
            <span
              v-else-if="scope.row.projState == '03'"
              style="color: royalblue"
              >已提交</span
            >
            <span v-else-if="scope.row.projState == '04'" style="color: green"
              >评审通过</span
            >
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          fixed="right"
          align="center"
          width="250px"
        >
          <template slot-scope="scope">
            <span class="directiveHandle">
           <div class="el-buttonStyle">
              <span @click="handleEdit(scope.row)">
                编辑
              </span>
            </div>
            <span class="el-buttonDriver">|</span>
           <div class="el-buttonStyle">
              <span @click="handleSubt(scope.row)">
                提交
              </span>
            </div>
            <span class="el-buttonDriver">|</span>
            <div class="el-buttonStyle">
              <span @click="handleReturn(scope.row)">
                退回
              </span>
            </div>
            <span class="el-buttonDriver">|</span>
            <el-dropdown>
                <el-button type="text">
                  <i class="el-icon-more directiveicon" style="font-size: 14px"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="设计成果"
                    class="el-buttonStyle">
                    <span @click="handleRequirements(scope.row)">
                      设计成果
                    </span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="获取现状"
                    :class="scope.row.projState !== '01' ? 'el-buttonDisabled' : 'el-buttonStyle'">
                    <span @click="handleObtain(scope.row)">
                      获取现状
                    </span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="在线设计"
                    :class="scope.row.projState !== '02' ? 'el-buttonDisabled' : 'el-buttonStyle'">
                    <span @click="handleFeeder(scope.row)">
                      查看馈线
                    </span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="在线设计"
                    :class="scope.row.projState !== '02' ? 'el-buttonDisabled' : 'el-buttonStyle'">
                    <span @click="handleEamine(scope.row)">
                      在线设计
                    </span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="成果分析"
                    :class="scope.row.projState !== '02' ? 'el-buttonDisabled' : 'el-buttonStyle'">
                    <span @click="handleResults(scope.row)">
                      成果分析
                    </span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
          </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      style="margin: 10px"
      background
      :current-page="form.page"
      :page-sizes="pageSize"
      :page-size="form.perPage"
      layout="total, sizes, prev, pager, next"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      :total="total"
    >
    </el-pagination>
    <!-- 编辑数据 -->
    <el-dialog
      title="编辑基础信息数据"
      :visible.sync="editDialog"
      width="23%"
      center
    >
      <el-form ref="form" :model="form" :inline="true">
        <el-form-item
          label="批次年度:"
          prop="pcnd"
          label-width="120px"
          style="margin-bottom: 0"
        >
          <el-date-picker
            v-model="formEdit.pcnd"
            format="yyyy"
            value-format="yyyy"
            type="year"
            placeholder="选择年"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialog = false">取 消</el-button>
        <el-button class="blue-btn" @click="editDialogQuery">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 设计成果导入 -->
    <el-dialog
      title="设计成果导入"
      :visible.sync="requirementsDialog"
      width="30%"
      center
    >
      <el-upload
        class="upload-demo"
        :data="getformData()"
        :action="url + '/tDtfProject/importXqSms'"
        accept=".zip"
        :before-upload="beforeAvatarUpload"
        :on-error="handleError"
        :file-list="addProjectInfo.file"
      >
        <button class="uploadFiles">
          <i class="el-icon-upload2">点击上传</i>
        </button>
        <div slot="tip" class="el-upload__tip">只能上传.zip文件</div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="requirementsDialog = false">取 消</el-button>
        <el-button class="blue-btn" @click="handleSuccess">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 现状数据 -->
    <el-dialog
      title="获取现状数据"
      :visible.sync="obtainDialog"
      width="30%"
      center
    >
      <el-table
        :data="gridData"
        :header-cell-style="{ background: 'rgb(160 167 176 / 45%)' }"
        border
      >
        <el-table-column type="index" width="60" align="center" label="序号">
        </el-table-column>
        <el-table-column
          property="name"
          label="步骤"
          width="180"
          align="center"
        ></el-table-column>
        <el-table-column property="" label="进度" align="center">
          <div class="progresswrapper">
            <div class="pro">100%</div>
          </div>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!-- 馈线ID -->
    <el-dialog
      title="馈线ID查看"
      width="95%"
      style="height: 100vh"
      v-if="feederDialog"
      :visible.sync="feederDialog"
    >
      <div style="height: 100%">
        <FeederDialog :ProCode="cuttentProCode" v-if="flag"></FeederDialog>
      </div>
    </el-dialog>
    <!-- 成果分析 -->
    <el-dialog
      title="选择解析内容"
      width="25%"
      style="height: 100vh"
      :visible.sync="resultDialog"
    >
      <div style="height: 100%">
        <ResultsDialog :ProCode="cuttentresult" v-if="flag"></ResultsDialog>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSb,
  getCity,
  getCounty,
  getXQEditPull,
  EditSave,
  DetailsView,
  resquest,
  ProblemView,
  handleReturn,
  handleSubt,
  getURLSJ,
  getuserInfo,
  getSettlement
} from "@/api/api"
import FeederDialog from "@/views/projectAgent/SJBGcomonents/tree"
import ResultsDialog from "@/views/projectAgent/SJBGcomonents/table"
export default {
  components: {
    FeederDialog,
    ResultsDialog,
  },
  data () {
    return {
      flag: true,
      url: resquest,
      form: {
        //表单参数
        id: "",
        projState: "",
        stageType: "4",
        projectName: "",
        pcnd: new Date().getFullYear() + '',
        BATCH: "",
        dsgs: "",
        xgs: "",
        xmName: "",
        tgzt: "09",
        csmc: "",
        dwmc: "",
        page: 1,
        perPage: 10,
        code: ""
      },
      xgsOptions: [], //县公司下拉数据
      dsgsOptions: [], //城市下拉
      jsdwOptions: [], //批次名次下拉数据
      pageSize: [10, 20, 50, 100], //分页页数
      q: {
        page: 1,
        limit: 5,
      },
      total: 0, //总共页数
      tableHeight: 0,
      tableData: [],
      formEdit: {
        //编辑
        tasklD: "", //主键id
        pcnd: "",
      },
      cuttentProCode: "", //馈线ID
      cuttentresult: "", //成果分析
      gridData: [
        {
          id: "1",
          name: "断面数据生成",
          taskid: "1",
        },
        {
          id: "2",
          name: "图模数据生成",
          taskid: "2",
        },
        {
          id: "3",
          name: "接收CIM数据",
          taskid: "3",
        },
        {
          id: "4",
          name: "接收SVG数据",
          taskid: "4",
        },
        {
          id: "5",
          name: "架空线路图模解析",
          taskid: "5",
        },
        {
          id: "6",
          name: "电缆线路图模解析",
          taskid: "6",
        },
        {
          id: "7",
          name: "配电站房图模解析",
          taskid: "7",
        },
        {
          id: "8",
          name: "生成zip",
          taskid: "8",
        },
      ], //获取现状图
      editDialog: false, //编辑
      obtainDialog: false, //现状数据
      addProjectInfo: {
        file: [], // 文件上传
        id: "",
      },
      requirementsDialog: false, //需求说明
      feederDialog: false, //馈线ID
      resultDialog: false, //成果分析
      userId: "",
    }
  },
  mounted () {
    this.setTablesHeight()
    const token = sessionStorage.getItem("bhneToken")
    getuserInfo(token).then((res) => {
      this.form.csmc = res.data.result.USERID
      this.form.dwmc = res.data.result.DWMC
      this.userId = res.data.result.USERID
      this.getList()
    })
    const that = this
    window.onresize = function () {
      that.setTablesHeight()
    }
  },
  methods: {
    setTablesHeight () {
      this.$nextTick(() => {
        const tablesAreaHeight = document.getElementsByClassName('driver')[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        this.tableHeight = windowsAreaHeight - tablesAreaHeight - 80
      })
    },
    // 获取列表
    getList () {
      getSettlement(this.form)
        .then((res) => {
          console.log(res)
          this.tableData = res.data.data
          this.total = res.data.total
        })
        .catch(() => { })
      if (this.dsgsOptions.length === 0) {
        // 获取城市下拉
        const params = {
          optId: this.form.dwmc,
        }
        getCity(params)
          .then((res) => {
            this.dsgsOptions = res.data.result
          })
          .catch(() => { })
      }
    },
    // 城市点击获取县下拉
    CityPoint (val) {
      const params = {
        optId: val,
        dwmc: this.form.dwmc,
      }
      getCounty(params)
        .then((res) => {
          this.xgsOptions = res.data.result
        })
        .catch(() => { })
    },
    // table列表序号索引
    indexMethod (index) {
      return (this.form.page - 1) * 10 + index + 1
    },
    // 查询
    query () {
      this.getList()
    },
    // 重置
    clearForm () {
      this.form = {
        //表单参数
        id: "",
        projState: "",
        stageType: "4",
        projectName: "",
        pcnd: "",
        BATCH: "",
        dsgs: "",
        xgs: "",
        xmName: "",
        tgzt: "09",
        csmc: this.form.csmc,
        dwmc: this.form.dwmc,
        page: 1,
        perPage: 10,
      }
      this.getList()
    },
    // 复选框
    handleSelectionChange () { },
    handleCurrentChange (row) {
      this.form.page = row
      this.getList()
    },
    handleSizeChange (row) {
      this.form.perPage = row
      this.getList()
    },

    // 提交
    handleSubt (row) {
      this.$confirm("是否确定提交?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: "warning",
      })
        .then(() => {
          handleSubt(row.taskID)
            .then((res) => {
              if (res.data.message == "success") {
                this.$message({
                  message: "提交成功",
                  type: "success",
                })
              } else {
                this.$message.error("提交失败!")
              }
              this.getList()
            })
            .catch(() => { })
        })
        .catch(() => {

        })
    },
    // 启动设计软件
    handleEamine (row) {
      // getURLSJ(row.taskID)
      //   .then((res) => {
      //     if (res.data.message == "success") {
      //       window.location = encodeURI(
      //         "BhneSJRJ://taskID=" +
      //           row.taskID +
      //           "&userID=" +
      //           this.userId +
      //           "&token=" +
      //           "11" +
      //           "&stageType=0"
      //       );
      //     } else {
      //       this.$message.error("启动失败!");
      //     }
      //     console.log(res);
      //   })
      //   .catch(() => {});
      // console.log(row)
      // const routeUrl = this.$router.resolve({
      //   path: "/onlineDesign",
      //   query: { taskID: row.taskID },
      // });
      this.$router.push({
        path: "/onlineDesign",
        query: { taskID: row.taskID, routerType: '/projectAgent/SJBGProject'  },
      })
    },
    // 退回
    handleReturn (row) {
      this.$confirm("是否确定退回?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: "warning",
      })
        .then(() => {
          handleReturn(row.taskID)
            .then((res) => {
              if (res.data.message == "success") {
                this.$message({
                  message: "退回成功",
                  type: "success",
                })
              } else {
                this.$message.error("退回失败!")
              }
              this.getList()
            })
            .catch(() => { })
        })
        .catch(() => {

        })

    },
    // table 编辑按钮
    handleEdit (row) {
      this.formEdit.taskID = row.taskID
      this.formEdit = row
      this.editDialog = true
      getXQEditPull()
        .then((res) => {
          this.pcnd = res.data.result.pcnd
        })
        .catch(() => { })
    },
    // 编辑弹框确认按钮
    editDialogQuery () {
      EditSave(this.formEdit)
        .then((res) => {
          if (res.data.message === "success") {
            this.$message({
              message: "修改成功",
              type: "success",
            })
          } else {
            this.$message.error("修改失败!")
          }
        })
        .catch(() => { })
      this.editDialog = false
    },
    // table 现状数据
    handleObtain () {
      this.obtainDialog = true
    },
    // table 需求说明
    handleRequirements (row) {
      this.addProjectInfo.id = row.taskID
      this.requirementsDialog = true
    },
    // 需求说明上传弹框确认按钮
    requirementsDialogQuery () {
      this.requirementsDialog = false
    },
    // 馈线ID
    handleFeeder (row) {
      this.cuttentProCode = row.taskID
      this.feederDialog = true
      this.flag = false
      this.$nextTick(() => {
        this.flag = true
      })
    },
    // 馈线保存按钮
    feederDialogQuery () {
      this.feederDialog = false
    },
    // 成果分析
    handleResults (row) {
      this.cuttentresult = row.taskID
      this.resultDialog = true
      this.flag = false
      this.$nextTick(() => {
        this.flag = true
      })
    },
    // 上传传参
    getformData () {
      return {
        id: this.addProjectInfo.id,
      }
    },
    //上传成功
    handleSuccess () {
      this.$message.success("上传成功")
      this.requirementsDialog = false
    },
    // 上传失败
    handleError () {
      this.$message.error("上传失败")
    },
    // 上传前判断
    beforeAvatarUpload (file) {
      let fileName = file.name
      let pos = fileName.lastIndexOf(".")
      let lastName = fileName.substring(pos, fileName.length)
      if (lastName.toLowerCase() !== ".zip") {
        this.$message.error("文件必须为.zip")
        return
      }
    },
  },
};
</script>

<style>
.el-dialog__body {
  padding: 24px !important;
}
.el-dialog__footer {
  padding: 16px 24px !important;
}
</style>
