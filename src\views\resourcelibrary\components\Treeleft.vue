<template>
  <div class="">
    <el-tree
      :data="treeDataList"
      :props="treetypeProps"
      node-key="id"
      highlight-current
      v-on:node-click="handleNodeClick"
    >
    </el-tree>
  </div>
</template>

<script>
import {
  getMaterialTypeList,
  getModuleTypeList,
  getDrawingTypeList,
} from '@/views/basicParameter/data-static/data.js'
export default {
  name: '',
  data () {
    return {
      treeDataList: [],
      materialtypeList: getMaterialTypeList(),
      moduletypeList: getModuleTypeList(),
      drawingtypeList: getDrawingTypeList(),
      treetypeProps: {
        children: 'children',
        label: 'text',
        id: 'key',
      },
    }
  },
  mounted () {
    this.getTreeData()
  },
  methods: {
    handleNodeClick (data, node) {
      if (node.childNodes.length != 0) return
      if (this.$attrs.tabKey === 'material') {
        this.$emit('treeclick', data.MaterialsTypeKey, this.$attrs.tabKey)
      }
      if (this.$attrs.tabKey === 'module') {
        this.$emit('treeclick', data.ModuleTypeKey, this.$attrs.tabKey)
      }
      if (this.$attrs.tabKey === 'drawing') {
        this.$emit('treeclick', data.DrawingTypeKey, this.$attrs.tabKey)
        console.log(data.DrawingTypeKey)
      }
    },
    getTreeData () {
      if (this.$attrs.tabKey === 'material') {
        this.treeDataList = this.materialtypeList
      }
      if (this.$attrs.tabKey === 'module') {
        this.treeDataList = this.moduletypeList
      }
      if (this.$attrs.tabKey === 'drawing') {
        this.treeDataList = this.drawingtypeList
      }
    },
  },
}
</script>

<style scoped lang="scss"></style>
