// 表格高度占满
export const tableHeightMixin = {
  data() {
    return {
      tableHeight: 0,
      timer: 0,
    }
  },
  mounted() {
    this.setTableHeight()
    console.log('tableHeight',this.tableHeight)
    window.addEventListener('resize', this.onResize)
  },
  beforeDestroy() {
    this.timer && clearTimeout(this.timer)
    window.removeEventListener('resize', this.onResize)
  },
  methods: {
    // 设置表格高度
    setTableHeight() {
      this.$nextTick(() => {
        let rect = this.$refs.tablecontent.getBoundingClientRect()
        console.log('rect', rect)
        this.tableHeight = rect.height
      })
    },
    onResize() {
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.setTableHeight()
      }, 300)
    },
  },
}
