define(["exports","./arrayFill-4513d7ad","./buildModuleUrl-4e1b81e7","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./when-b60132fc","./GeometryAttribute-3a88ba31","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./PrimitiveType-a54dc62f","./VertexFormat-6446fca0"],(function(e,t,a,n,r,i,o,m,u,s,y,p){"use strict";var x=new n.Cartesian3;function f(e){var t=(e=o.defaultValue(e,o.defaultValue.EMPTY_OBJECT)).minimum,a=e.maximum,r=o.defaultValue(e.vertexFormat,p.VertexFormat.DEFAULT);this._minimum=n.Cartesian3.clone(t),this._maximum=n.Cartesian3.clone(a),this._vertexFormat=r,this._offsetAttribute=e.offsetAttribute,this._workerName="createBoxGeometry"}f.fromDimensions=function(e){var t=(e=o.defaultValue(e,o.defaultValue.EMPTY_OBJECT)).dimensions,a=n.Cartesian3.multiplyByScalar(t,.5,new n.Cartesian3);return new f({minimum:n.Cartesian3.negate(a,new n.Cartesian3),maximum:a,vertexFormat:e.vertexFormat,offsetAttribute:e.offsetAttribute})},f.fromAxisAlignedBoundingBox=function(e){return new f({minimum:e.minimum,maximum:e.maximum})},f.packedLength=2*n.Cartesian3.packedLength+p.VertexFormat.packedLength+1,f.pack=function(e,t,a){return a=o.defaultValue(a,0),n.Cartesian3.pack(e._minimum,t,a),n.Cartesian3.pack(e._maximum,t,a+n.Cartesian3.packedLength),p.VertexFormat.pack(e._vertexFormat,t,a+2*n.Cartesian3.packedLength),t[a+2*n.Cartesian3.packedLength+p.VertexFormat.packedLength]=o.defaultValue(e._offsetAttribute,-1),t};var c,l=new n.Cartesian3,A=new n.Cartesian3,d=new p.VertexFormat,b={minimum:l,maximum:A,vertexFormat:d,offsetAttribute:void 0};f.unpack=function(e,t,a){t=o.defaultValue(t,0);var r=n.Cartesian3.unpack(e,t,l),i=n.Cartesian3.unpack(e,t+n.Cartesian3.packedLength,A),m=p.VertexFormat.unpack(e,t+2*n.Cartesian3.packedLength,d),u=e[t+2*n.Cartesian3.packedLength+p.VertexFormat.packedLength];return o.defined(a)?(a._minimum=n.Cartesian3.clone(r,a._minimum),a._maximum=n.Cartesian3.clone(i,a._maximum),a._vertexFormat=p.VertexFormat.clone(m,a._vertexFormat),a._offsetAttribute=-1===u?void 0:u,a):(b.offsetAttribute=-1===u?void 0:u,new f(b))},f.createGeometry=function(e){var r=e._minimum,p=e._maximum,f=e._vertexFormat;if(!n.Cartesian3.equals(r,p)){var c,l,A=new u.GeometryAttributes;if(f.position&&(f.st||f.normal||f.tangent||f.bitangent)){if(f.position&&((l=new Float64Array(72))[0]=r.x,l[1]=r.y,l[2]=p.z,l[3]=p.x,l[4]=r.y,l[5]=p.z,l[6]=p.x,l[7]=p.y,l[8]=p.z,l[9]=r.x,l[10]=p.y,l[11]=p.z,l[12]=r.x,l[13]=r.y,l[14]=r.z,l[15]=p.x,l[16]=r.y,l[17]=r.z,l[18]=p.x,l[19]=p.y,l[20]=r.z,l[21]=r.x,l[22]=p.y,l[23]=r.z,l[24]=p.x,l[25]=r.y,l[26]=r.z,l[27]=p.x,l[28]=p.y,l[29]=r.z,l[30]=p.x,l[31]=p.y,l[32]=p.z,l[33]=p.x,l[34]=r.y,l[35]=p.z,l[36]=r.x,l[37]=r.y,l[38]=r.z,l[39]=r.x,l[40]=p.y,l[41]=r.z,l[42]=r.x,l[43]=p.y,l[44]=p.z,l[45]=r.x,l[46]=r.y,l[47]=p.z,l[48]=r.x,l[49]=p.y,l[50]=r.z,l[51]=p.x,l[52]=p.y,l[53]=r.z,l[54]=p.x,l[55]=p.y,l[56]=p.z,l[57]=r.x,l[58]=p.y,l[59]=p.z,l[60]=r.x,l[61]=r.y,l[62]=r.z,l[63]=p.x,l[64]=r.y,l[65]=r.z,l[66]=p.x,l[67]=r.y,l[68]=p.z,l[69]=r.x,l[70]=r.y,l[71]=p.z,A.position=new m.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:l})),f.normal){var d=new Float32Array(72);d[0]=0,d[1]=0,d[2]=1,d[3]=0,d[4]=0,d[5]=1,d[6]=0,d[7]=0,d[8]=1,d[9]=0,d[10]=0,d[11]=1,d[12]=0,d[13]=0,d[14]=-1,d[15]=0,d[16]=0,d[17]=-1,d[18]=0,d[19]=0,d[20]=-1,d[21]=0,d[22]=0,d[23]=-1,d[24]=1,d[25]=0,d[26]=0,d[27]=1,d[28]=0,d[29]=0,d[30]=1,d[31]=0,d[32]=0,d[33]=1,d[34]=0,d[35]=0,d[36]=-1,d[37]=0,d[38]=0,d[39]=-1,d[40]=0,d[41]=0,d[42]=-1,d[43]=0,d[44]=0,d[45]=-1,d[46]=0,d[47]=0,d[48]=0,d[49]=1,d[50]=0,d[51]=0,d[52]=1,d[53]=0,d[54]=0,d[55]=1,d[56]=0,d[57]=0,d[58]=1,d[59]=0,d[60]=0,d[61]=-1,d[62]=0,d[63]=0,d[64]=-1,d[65]=0,d[66]=0,d[67]=-1,d[68]=0,d[69]=0,d[70]=-1,d[71]=0,A.normal=new m.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:d})}if(f.st){var b=new Float32Array(72),C=0;b[C++]=0,b[C++]=0,b[C++]=-1,b[C++]=1,b[C++]=0,b[C++]=-1,b[C++]=1,b[C++]=1,b[C++]=-1,b[C++]=0,b[C++]=1,b[C++]=-1,b[C++]=1,b[C++]=0,b[C++]=-1,b[C++]=0,b[C++]=0,b[C++]=-1,b[C++]=0,b[C++]=1,b[C++]=-1,b[C++]=1,b[C++]=1,b[C++]=-1,b[C++]=0,b[C++]=0,b[C++]=0,b[C++]=1,b[C++]=0,b[C++]=0,b[C++]=1,b[C++]=1,b[C++]=0,b[C++]=0,b[C++]=1,b[C++]=0,b[C++]=1,b[C++]=0,b[C++]=0,b[C++]=0,b[C++]=0,b[C++]=0,b[C++]=0,b[C++]=1,b[C++]=0,b[C++]=1,b[C++]=1,b[C++]=0,b[C++]=1,b[C++]=0,b[C++]=1,b[C++]=0,b[C++]=0,b[C++]=1,b[C++]=0,b[C++]=1,b[C++]=1,b[C++]=1,b[C++]=1,b[C++]=1,b[C++]=0,b[C++]=0,b[C++]=1,b[C++]=1,b[C++]=0,b[C++]=1,b[C++]=1,b[C++]=1,b[C++]=1,b[C++]=0,b[C++]=1,b[C++]=1,A.st=new m.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:b})}if(f.tangent){var v=new Float32Array(72);v[0]=1,v[1]=0,v[2]=0,v[3]=1,v[4]=0,v[5]=0,v[6]=1,v[7]=0,v[8]=0,v[9]=1,v[10]=0,v[11]=0,v[12]=-1,v[13]=0,v[14]=0,v[15]=-1,v[16]=0,v[17]=0,v[18]=-1,v[19]=0,v[20]=0,v[21]=-1,v[22]=0,v[23]=0,v[24]=0,v[25]=1,v[26]=0,v[27]=0,v[28]=1,v[29]=0,v[30]=0,v[31]=1,v[32]=0,v[33]=0,v[34]=1,v[35]=0,v[36]=0,v[37]=-1,v[38]=0,v[39]=0,v[40]=-1,v[41]=0,v[42]=0,v[43]=-1,v[44]=0,v[45]=0,v[46]=-1,v[47]=0,v[48]=-1,v[49]=0,v[50]=0,v[51]=-1,v[52]=0,v[53]=0,v[54]=-1,v[55]=0,v[56]=0,v[57]=-1,v[58]=0,v[59]=0,v[60]=1,v[61]=0,v[62]=0,v[63]=1,v[64]=0,v[65]=0,v[66]=1,v[67]=0,v[68]=0,v[69]=1,v[70]=0,v[71]=0,A.tangent=new m.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:v})}if(f.bitangent){var F=new Float32Array(72);F[0]=0,F[1]=1,F[2]=0,F[3]=0,F[4]=1,F[5]=0,F[6]=0,F[7]=1,F[8]=0,F[9]=0,F[10]=1,F[11]=0,F[12]=0,F[13]=1,F[14]=0,F[15]=0,F[16]=1,F[17]=0,F[18]=0,F[19]=1,F[20]=0,F[21]=0,F[22]=1,F[23]=0,F[24]=0,F[25]=0,F[26]=1,F[27]=0,F[28]=0,F[29]=1,F[30]=0,F[31]=0,F[32]=1,F[33]=0,F[34]=0,F[35]=1,F[36]=0,F[37]=0,F[38]=1,F[39]=0,F[40]=0,F[41]=1,F[42]=0,F[43]=0,F[44]=1,F[45]=0,F[46]=0,F[47]=1,F[48]=0,F[49]=0,F[50]=1,F[51]=0,F[52]=0,F[53]=1,F[54]=0,F[55]=0,F[56]=1,F[57]=0,F[58]=0,F[59]=1,F[60]=0,F[61]=0,F[62]=1,F[63]=0,F[64]=0,F[65]=1,F[66]=0,F[67]=0,F[68]=1,F[69]=0,F[70]=0,F[71]=1,A.bitangent=new m.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:F})}(c=new Uint16Array(36))[0]=0,c[1]=1,c[2]=2,c[3]=0,c[4]=2,c[5]=3,c[6]=6,c[7]=5,c[8]=4,c[9]=7,c[10]=6,c[11]=4,c[12]=8,c[13]=9,c[14]=10,c[15]=8,c[16]=10,c[17]=11,c[18]=14,c[19]=13,c[20]=12,c[21]=15,c[22]=14,c[23]=12,c[24]=18,c[25]=17,c[26]=16,c[27]=19,c[28]=18,c[29]=16,c[30]=20,c[31]=21,c[32]=22,c[33]=20,c[34]=22,c[35]=23}else(l=new Float64Array(24))[0]=r.x,l[1]=r.y,l[2]=r.z,l[3]=p.x,l[4]=r.y,l[5]=r.z,l[6]=p.x,l[7]=p.y,l[8]=r.z,l[9]=r.x,l[10]=p.y,l[11]=r.z,l[12]=r.x,l[13]=r.y,l[14]=p.z,l[15]=p.x,l[16]=r.y,l[17]=p.z,l[18]=p.x,l[19]=p.y,l[20]=p.z,l[21]=r.x,l[22]=p.y,l[23]=p.z,A.position=new m.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:l}),(c=new Uint16Array(36))[0]=4,c[1]=5,c[2]=6,c[3]=4,c[4]=6,c[5]=7,c[6]=1,c[7]=0,c[8]=3,c[9]=1,c[10]=3,c[11]=2,c[12]=1,c[13]=6,c[14]=5,c[15]=1,c[16]=2,c[17]=6,c[18]=2,c[19]=3,c[20]=7,c[21]=2,c[22]=7,c[23]=6,c[24]=3,c[25]=0,c[26]=4,c[27]=3,c[28]=4,c[29]=7,c[30]=0,c[31]=1,c[32]=5,c[33]=0,c[34]=5,c[35]=4;var z=n.Cartesian3.subtract(p,r,x),w=.5*n.Cartesian3.magnitude(z);if(o.defined(e._offsetAttribute)){var g=l.length,_=new Uint8Array(g/3),h=e._offsetAttribute===s.GeometryOffsetAttribute.NONE?0:1;t.arrayFill(_,h),A.applyOffset=new m.GeometryAttribute({componentDatatype:i.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:_})}return new m.Geometry({attributes:A,indices:c,primitiveType:y.PrimitiveType.TRIANGLES,boundingSphere:new a.BoundingSphere(n.Cartesian3.ZERO,w),offsetAttribute:e._offsetAttribute})}},f.getUnitBox=function(){return o.defined(c)||(c=f.createGeometry(f.fromDimensions({dimensions:new n.Cartesian3(1,1,1),vertexFormat:p.VertexFormat.POSITION_ONLY}))),c},e.BoxGeometry=f}));
