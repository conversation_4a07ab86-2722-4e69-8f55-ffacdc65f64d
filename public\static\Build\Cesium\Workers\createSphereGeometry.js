define(["./when-b60132fc","./Cartographic-3309dd0d","./Check-7b2a090c","./EllipsoidGeometry-be29e04c","./VertexFormat-6446fca0","./Math-119be1a3","./arrayFill-4513d7ad","./buildModuleUrl-4e1b81e7","./Cartesian2-47311507","./PrimitiveType-a54dc62f","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-3a88ba31","./FeatureDetection-c3b71206","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4"],(function(e,t,r,i,a,o,n,s,d,c,l,m,u,p,y,G,f,b,k,v){"use strict";function x(r){var a=e.defaultValue(r.radius,1),o={radii:new t.Cartesian3(a,a,a),stackPartitions:r.stackPartitions,slicePartitions:r.slicePartitions,vertexFormat:r.vertexFormat};this._ellipsoidGeometry=new i.EllipsoidGeometry(o),this._workerName="createSphereGeometry"}x.packedLength=i.EllipsoidGeometry.packedLength,x.pack=function(e,t,r){return i.EllipsoidGeometry.pack(e._ellipsoidGeometry,t,r)};var F=new i.EllipsoidGeometry,P={radius:void 0,radii:new t.Cartesian3,vertexFormat:new a.VertexFormat,stackPartitions:void 0,slicePartitions:void 0};return x.unpack=function(r,o,n){var s=i.EllipsoidGeometry.unpack(r,o,F);return P.vertexFormat=a.VertexFormat.clone(s._vertexFormat,P.vertexFormat),P.stackPartitions=s._stackPartitions,P.slicePartitions=s._slicePartitions,e.defined(n)?(t.Cartesian3.clone(s._radii,P.radii),n._ellipsoidGeometry=new i.EllipsoidGeometry(P),n):(P.radius=s._radii.x,new x(P))},x.createGeometry=function(e){return i.EllipsoidGeometry.createGeometry(e._ellipsoidGeometry)},function(t,r){return e.defined(r)&&(t=x.unpack(t,r)),x.createGeometry(t)}}));
