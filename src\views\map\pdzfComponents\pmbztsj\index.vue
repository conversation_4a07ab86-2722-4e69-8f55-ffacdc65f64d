<template>
  <div class="pmbzts">
    <div v-if="pllx === 0" class="pmbzts-img">
      <div v-for="(item, index) in dpbzimgUrl" :key="index + '_1'" class="imgShow" :style="{backgroundImage: `url(${item.imgUrl})`}">
        <div class="zhanwei"></div>
      </div>
    </div>
    <div v-if="pllx === 1" class="pmbzts-img">
      <div v-for="(item, index) in spbzimgUrl" :key="index + '_1'" class="twoLine" :style="{backgroundImage: `url(${item.imgUrl})`, marginTop: item.type === 2 ? '20px': ''}">
        <div class="zhanwei_bot" v-if="item.type === 1"></div>
        <div class="zhanwei_top" v-if="item.type === 2"></div>
      </div>
    </div>
    <div v-if="pllx === 2" class="pmbzts-img">
      <div v-for="(item, index) in spbzimgUrl" :key="index + '_1'" class="thrLine" :style="{backgroundImage: `url(${item.imgUrl})`}">
        <div class="zhanwei_bot" v-if="item.type === 1"></div>
        <div class="zhanwei_top" v-if="item.type === 2"></div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    components: {},
    props: {
      pllx: {
        default: -1,
        type: Number
      }
    },
    data() {
      return {
        dpbzimgUrl: [
          {
            imgUrl: require('@/assets/setting/test-g1.png')
          },
          {
            imgUrl: require('@/assets/setting/test-g2.png')
          },
          {
            imgUrl: require('@/assets/setting/test-g3.png')
          },
          {
            imgUrl: require('@/assets/setting/test-g4.png')
          },
          {
            imgUrl: require('@/assets/setting/test-g5.png')
          },
          {
            imgUrl: require('@/assets/setting/test-g6.png')
          },
          {
            imgUrl: require('@/assets/setting/test-g7.png')
          },
          {
            imgUrl: require('@/assets/setting/test-g8.png')
          }
        ], // 单排布置
        spbzimgUrl: [
          {
            imgUrl: require('@/assets/setting/test-g1.png'),
            type: 1
          },
          {
            imgUrl: require('@/assets/setting/test-g2.png'),
            type: 1
          },
          {
            imgUrl: require('@/assets/setting/test-g3.png'),
            type: 1
          },
          {
            imgUrl: require('@/assets/setting/test-g4.png'),
            type: 1
          },
          {
            imgUrl: require('@/assets/setting/test-g8.png'),
            type: 2
          },
          {
            imgUrl: require('@/assets/setting/test-g7.png'),
            type: 2
          },
          {
            imgUrl: require('@/assets/setting/test-g6.png'),
            type: 2
          },
          {
            imgUrl: require('@/assets/setting/test-g5.png'),
            type: 2
          }
        ] // 双排
      };
    },
    methods: {},
    mounted() {
      console.log('配电类型', this.pllx)
    },
  }

</script>

<style lang="scss" scoped>
  .pmbzts-img{
    padding: 20px;
    background: #000000;
    display: flex;
    flex-flow: row wrap;
    align-content: flex-start;
    justify-content: center;
    .imgShow{
      flex: 1;
      height: 200px;
      position: relative;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      border: 1px solid #ffffff;
      .zhanwei{
        width: 100%;
        height: 5px;
        position: absolute;
        bottom: -6px;
        border: 1px solid #fff;
      }
    }
    .twoLine{
      flex: 0 0 20%;
      height: 200px;
      position: relative;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      border: 1px solid #ffffff;
      .zhanwei_bot{
        position: absolute;
        bottom: -10px;
        width: 100%;
        height: 5px;
        border: 1px solid #fff;
      }
      .zhanwei_top{
        position: absolute;
        top: -10px;
        width: 100%;
        height: 5px;
        border: 1px solid #fff;
      }
    }
    .thrLine{
      flex: 0 0 20%;
      height: 200px;
      position: relative;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      border: 1px solid #ffffff;
      .zhanwei_bot{
        position: absolute;
        width: 100%;
        height: 5px;
        border: 1px solid #fff;
      }
      .zhanwei_top{
        position: absolute;
        width: 100%;
        height: 5px;
        bottom: 0px;
        border: 1px solid #fff;
      }
    }
  }
</style>

