<template>
  <div>
    <div v-show="selectCheck.objVis.showFacilities" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div class="pro-leftTitle">
            <span>附属设施</span>
            <div class="maps-zhedNav" @click="showEveryItemSet(9)">
              <img
                v-show="!selectCheck.showEveryItems.showFacilities"
                class="mapites-zhed"
                :src="require('@/assets/' + 'map/settingImg/zhedie.png')"
                alt=""
              />
              <img
                v-show="selectCheck.showEveryItems.showFacilities"
                class="mapites-zhed"
                :src="require('@/assets/' + 'map/settingImg/zkzhedie.png')"
                alt=""
              />
            </div>
          </div>
        </div>
        <div class="pro-rigTitle" @click="subMitItems(11)">
          <img
            class="settingImg"
            :src="require('@/assets/' + 'map/settingImg/useSetting.png')"
            alt=""
          />
          <p>启用</p>
        </div>
      </div>
      <div
        v-show="selectCheck.showEveryItems.showFacilities"
        class="pro-addForm"
      >
        <div class="map-showNav">基本信息</div>
        <van-row>
          <van-field
            v-model="addParam.facilities.mark"
            label="编号"
            placeholder="请输入附属设施编号"
          />
        </van-row>
        <div class="map-showNav">选型信息</div>
        <van-row>
          <van-col :span="12">
            <!--附属设施状态-->
            <van-field
              readonly
              clickable
              :value="addParam.facilities.state"
              label="状态"
              placeholder="请选择状态"
              @click="settingObj.facilities.state = true"
            />
            <van-popup v-model="settingObj.facilities.state" position="bottom">
              <van-picker
                show-toolbar
                title="状态"
                value-key="key"
                :columns="settingObj.mainLineState"
                @confirm="onConfirmFssbSel(0, $event)"
                @cancel="settingObj.facilities.state = false"
              />
            </van-popup>
          </van-col>
          <van-col :span="12">
            <!--附属设施类别-->
            <van-field
              readonly
              clickable
              :value="addParam.facilities.type"
              label="附属设施类别"
              placeholder="请选择附属设施类别"
              @click="settingObj.facilities.type = true"
            />
            <van-popup v-model="settingObj.facilities.type" position="bottom">
              <van-picker
                show-toolbar
                title="附属设施类别"
                value-key="key"
                :columns="settingObj.facilitiesType"
                @confirm="onConfirmFssbSel(1, $event)"
                @cancel="settingObj.facilities.type = false"
              />
            </van-popup>
          </van-col>
        </van-row>
        <!--附属设施型号-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="addParam.facilities.model"
            label="设施型号"
            placeholder="请选择设施型号"
            @click="settingObj.facilities.model = true"
          />
          <van-popup v-model="settingObj.facilities.model" position="bottom">
            <van-picker
              show-toolbar
              title="设施型号"
              value-key="name"
              :columns="settingObj.facilitiesModel"
              @confirm="onConfirmFssbSel(2, $event)"
              @cancel="settingObj.facilities.model = false"
            />
          </van-popup>
        </van-row>
        <div v-if="addParam.isShowNav" class="map-showNav">其他信息</div>
        <van-row v-if="addParam.isShowNav">
          <!--经度-->
          <van-field v-model="addParam.lngtitude" label="经度" disabled />
        </van-row>
        <van-row v-if="addParam.isShowNav">
          <!--纬度-->
          <van-field v-model="addParam.lattitude" label="纬度" disabled />
        </van-row>
        <van-row v-if="addParam.isShowNav">
          <!--高程-->
          <van-field v-model="addParam.highNum" label="高程" disabled />
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      settingObj: {
        // 附属设施
        facilities: {
          state: false, // 状态
          type: false, // 附属设施类别
          model: false // 设施型号
        },
    },
      facilitiesType: [
        {
          key: '卡盘',
          value: '卡盘',
          type: 'KP'
        },
        {
          key: '拉盘',
          value: '拉盘',
          type: 'LP'
        },
        {
          key: '底盘',
          value: '底盘',
          type: 'DP'
        }
      ],
      // 附属设施
      facilities: {
        state: '', // 状态
        type: '', // 附属设施类别
        model: '', // 设施型号
        modelId: '', // 设施型号
        imgList: [], // 文件列表
        message: '', // 备注信息
        audioList:
      }
  },
  mounted() {

  },
  methods: {
    /**
     * 附属设施
     */
    onConfirmFssbSel(type, item) {
      const val = item.value
      switch (type) {
        case 0:
          this.addParam.facilities.state = val
          this.settingObj.facilities.state = false
          break
        case 1:
          this.addParam.facilities.type = val
          this.getTowerOrLineType(12, 'FSSS', item.type, '')
          this.settingObj.facilities.type = false
          break
        case 2:
          this.addParam.facilities.model = item.spec
          this.addParam.facilities.modelId = item.materialsprojectid
          this.settingObj.facilities.model = false
          break
      }
    },
  }
  }
}

</script>

<style lang="sass" scoped>
</style>

