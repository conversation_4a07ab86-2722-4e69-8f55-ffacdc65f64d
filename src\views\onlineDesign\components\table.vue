<template>
  <el-table
    :data="data"
    stripe
    style="width: 100%"
    v-loading="loading"
    max-height="500"
    height="500px"
    @selection-change="handleSelectionChange"
  >
    <!--选择-->
    <el-table-column v-if="hasSelection" type="selection" width="55" />
    <!--序号-->
    <el-table-column v-if="hasIndex" type="index" label="序号" align="center" width="60" />
    <!--数据源-->
    <template v-for="(column, index) in columns">
      <!-- 表头存在type类型 -->
      <el-table-column
        v-if="
          column.type && (column.type == 'selection' || column.type == 'index')
        "
        :type="column.type"
      />

      <!-- 表头是数据或操作内容 -->
      <el-table-column v-else :label="column.label" :property="column.porp">
      </el-table-column>
    </template>
  </el-table>
</template>

<script>
export default {
  name: "DialogTable",
  props: {
    loading: {
      type: Boolean,
      default: () => false,
    },
    // 是否可以选择
    hasSelection: {
      type: Boolean,
      default: () => false,
    },
    // 是否有序列项
    hasIndex: {
      type: Boolean,
      default: () => false,
    },
    // 这是相应的字段展示
    columns: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    handleSelectionChange(val) {
      const selectionArr = [];
      val.forEach((item) => {
        selectionArr.push(item);
      });
      this.$emit("commitSelection", selectionArr);
    },
  },
};
</script>

<style></style>
