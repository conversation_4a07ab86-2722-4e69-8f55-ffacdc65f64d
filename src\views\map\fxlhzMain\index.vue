<template>
  <div>
    <!--分支线路-->
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div v-if="showBackAdds" class="maps-zhedNav" @click="backCurrentDom">
            <img
              class="mapites-backImg"
              :src="require('@/assets/' + 'map/settingImg/backAdds.png')"
              alt=""
            />
          </div>
          <span />
          分支线路
          <div
            v-if="!showBackAdds"
            class="maps-zhedNav"
            @click="showEveryItemSet"
          >
            <img
              v-show="!isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zhedie.png')"
              alt=""
            />
            <img
              v-show="isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zkzhedie.png')"
              alt=""
            />
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img
            class="settingImg"
            :src="require('@/assets/' + 'map/settingImg/useSetting.png')"
            alt=""
          />
          <p>启用</p>
        </div>
      </div>
      <div v-show="isFoldArea" class="pro-addForm">
        <div class="map-showNav">基本信息</div>
        <van-row>
          <van-field
            :disabled="inputIsDisabled"
            v-model="branchLine.mark"
            label="编号"
            placeholder="请输入杆塔编号"
          />
        </van-row>
        <div class="map-showNav">选型信息</div>
        <van-row>
          <van-field
            readonly
            clickable
            :disabled="inputIsDisabled"
            :value="branchLine.voltage"
            label="电压等级"
            placeholder="选择电压等级"
            @click="settingObj.branchLine.voltageVis = true"
          />
          <van-popup
            v-model="settingObj.branchLine.voltageVis"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="电压等级"
              value-key="key"
              :columns="allVoltage"
              @confirm="onConfirBranchSelc(0, '', $event)"
              @cancel="settingObj.branchLine.voltageVis = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--主线路回路数-->
          <van-field
            readonly
            clickable
            :disabled="inputIsDisabled"
            :value="branchLine.backLine"
            label="回路数"
            placeholder="选择回路数"
            @click="settingObj.branchLine.backLineVis = true"
          />
          <van-popup
            v-model="settingObj.branchLine.backLineVis"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="回路数"
              value-key="key"
              :columns="backLine"
              @confirm="onConfirBranchSelc(1, '', $event)"
              @cancel="settingObj.branchLine.backLineVis = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--杆塔状态-->
          <van-field
            readonly
            clickable
            :disabled="inputIsDisabled"
            :value="branchLine.state"
            label="杆塔状态"
            placeholder="选择状态"
            @click="settingObj.branchLine.stateVis = true"
          />
          <van-popup
            v-model="settingObj.branchLine.stateVis"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="杆塔状态"
              value-key="key"
              :columns="mainLineState"
              @confirm="onConfirBranchSelc(2, '', $event)"
              @cancel="settingObj.branchLine.stateVis = false"
            />
          </van-popup>
        </van-row>
        <!--        <van-row>-->
        <!--          <van-field-->
        <!--            readonly-->
        <!--            clickable-->
        <!--            :disabled="inputIsDisabled"-->
        <!--            :value="branchLine.branch"-->
        <!--            label="分支类型"-->
        <!--            placeholder="选择分支类型"-->
        <!--            @click="settingObj.branchLine.branchVis = true"-->
        <!--          />-->
        <!--          <van-popup v-model="settingObj.branchLine.branchVis" round position="bottom">-->
        <!--            <van-picker-->
        <!--              show-toolbar-->
        <!--              title="分支类型"-->
        <!--              value-key="key"-->
        <!--              :columns="branch"-->
        <!--              @confirm="onConfirBranchSelc(3, '', $event)"-->
        <!--              @cancel="settingObj.branchLine.branchVis = false"-->
        <!--            />-->
        <!--          </van-popup>-->
        <!--        </van-row>-->
        <!-- 分支线路杆塔类型-->
        <van-row>
          <van-field
            readonly
            clickable
            :disabled="inputIsDisabled"
            :value="branchLine.towerType"
            label="杆塔类型"
            placeholder="选择杆塔类型"
            @click="settingObj.branchLine.towerTypeVis = true"
          />
          <van-popup
            v-model="settingObj.branchLine.towerTypeVis"
            round
            position="bottom"
          >
            <van-picker
              title="杆塔类型"
              show-toolbar
              value-key="key"
              :columns="towerType"
              @confirm="onConfirBranchSelc(4, '', $event)"
              @cancel="settingObj.branchLine.towerTypeVis = false"
            />
          </van-popup>
        </van-row>
        <!--主线路杆塔设备类型-->
        <van-row v-if="settingObj.branchLine.isShowtowerMoudle">
          <van-field
            readonly
            clickable
            :disabled="inputIsDisabled"
            :value="branchLine.towerSbModel"
            label="设备类型"
            placeholder="选择设备类型"
            @click="settingObj.branchLine.towerSbModel = true"
          />
          <van-popup
            v-model="settingObj.branchLine.towerSbModel"
            round
            position="bottom"
          >
            <van-picker
              title="设备类型"
              show-toolbar
              value-key="key"
              :columns="towerSbModel"
              @confirm="onConfirBranchSelc(11, '', $event)"
              @cancel="settingObj.branchLine.towerSbModel = false"
            />
          </van-popup>
        </van-row>
        <van-row v-if="settingObj.branchLine.isShowtowerGGG">
          <van-field
            readonly
            clickable
            :disabled="inputIsDisabled"
            :value="branchLine.gggHeight"
            label="杆高"
            placeholder="选择杆高"
            @click="settingObj.branchLine.towerGGGVis = true"
          />
          <van-popup
            v-model="settingObj.branchLine.towerGGGVis"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="杆高"
              value-key="key"
              :columns="towerGGGGtHeight"
              @confirm="onConfirBranchSelc(8, '', $event)"
              @cancel="settingObj.branchLine.towerGGGVis = false"
            />
          </van-popup>
        </van-row>
        <van-row v-if="settingObj.branchLine.isShowtowerGGG">
          <van-field
            readonly
            clickable
            :disabled="inputIsDisabled"
            :value="branchLine.gggSj"
            label="稍经"
            placeholder="选择稍经"
            @click="settingObj.branchLine.towerSjVis = true"
          />
          <van-popup
            v-model="settingObj.branchLine.towerSjVis"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="稍经"
              value-key="key"
              :columns="towerSJHeight"
              @confirm="onConfirBranchSelc(9, '', $event)"
              @cancel="settingObj.branchLine.towerSjVis = false"
            />
          </van-popup>
        </van-row>
        <van-row v-if="settingObj.branchLine.isShowtowerZJT">
          <van-field
            readonly
            clickable
            :disabled="inputIsDisabled"
            :value="branchLine.zjtTg"
            label="塔高"
            placeholder="选择塔高"
            @click="settingObj.branchLine.towerZJTVis = true"
          />
          <van-popup
            v-model="settingObj.branchLine.towerZJTVis"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="杆高"
              value-key="key"
              :columns="towerGGGGtHeight"
              @confirm="onConfirBranchSelc(10, '', $event)"
              @cancel="settingObj.branchLine.towerGGGVis = false"
            />
          </van-popup>
        </van-row>
        <!--分支线路杆塔型号-->
        <van-row v-if="settingObj.branchLine.isShowtowerMoudle">
          <van-field
            readonly
            clickable
            :disabled="inputIsDisabled"
            :value="branchLine.towerModel"
            label="杆塔型号"
            placeholder="选择杆塔型号"
            @click="settingObj.branchLine.towerModelVis = true"
          />
          <van-popup
            v-model="settingObj.branchLine.towerModelVis"
            round
            position="bottom"
          >
            <van-picker
              title="杆塔型号"
              show-toolbar
              value-key="name"
              :columns="branchtowerModel"
              @confirm="onConfirBranchSelc(5, '', $event)"
              @cancel="settingObj.branchLine.towerModelVis = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <div :style="{ width: '100%', overflowX: 'scroll' }">
            <!--分支线路-->
            <table
              border="1"
              class="map-tables"
              cellspacing="0"
              cellpadding="0"
              align="center"
            >
              <tr>
                <th :style="{ width: '7rem' }">线路名称</th>
                <th :style="{ width: '7rem' }">状态</th>
                <th :style="{ width: '14rem' }">导线型号</th>
                <th>操作</th>
              </tr>
              <tr v-for="(item, index) in branchLine.listArr">
                <td>
                  <van-field
                    v-model="item.lineName"
                    placeholder="请输入线路名称"
                  />
                </td>
                <td>
                  <van-field
                    readonly
                    clickable
                    class="pickerSelect"
                    :value="item.lineState"
                    placeholder="线路状态"
                    @click="
                      settingObj.branchLine.listArr[index].lineStateVis = true
                    "
                  />
                  <van-popup
                    v-model="settingObj.branchLine.listArr[index].lineStateVis"
                    round
                    position="bottom"
                  >
                    <van-picker
                      title="线路状态"
                      show-toolbar
                      value-key="key"
                      :columns="cableLineState"
                      @confirm="onConfirBranchSelc(6, index, $event)"
                      @cancel="
                        settingObj.branchLine.listArr[
                          index
                        ].lineStateVis = false
                      "
                    />
                  </van-popup>
                </td>
                <td>
                  <van-field
                    readonly
                    clickable
                    class="pickerSelect"
                    :value="item.lineModel"
                    placeholder="选导线型号"
                    @click="
                      settingObj.branchLine.listArr[index].lineModelVis = true
                    "
                  />
                  <van-popup
                    v-model="settingObj.branchLine.listArr[index].lineModelVis"
                    round
                    position="bottom"
                  >
                    <van-picker
                      title="导线型号"
                      show-toolbar
                      value-key="name"
                      :columns="branchlineType"
                      @confirm="onConfirBranchSelc(7, index, $event)"
                      @cancel="
                        settingObj.branchLine.listArr[
                          index
                        ].lineModelVis = false
                      "
                    />
                  </van-popup>
                </td>
                <td :style="{ width: '4rem', textAlign: 'center' }">
                  <span :style="{ color: 'red' }" @click="removeMainLine(index)"
                    >删除</span
                  >
                </td>
              </tr>
            </table>
          </div>
        </van-row>
        <!--基本信息 2022.7.27新增-->
        <div v-if="isShowNav" class="map-showNav">其他信息</div>
        <van-row v-if="isShowNav">
          <!--经度-->
          <van-field v-model="lngtitude" label="经度" disabled />
        </van-row>
        <van-row v-if="isShowNav">
          <!--纬度-->
          <van-field v-model="lattitude" label="纬度" disabled />
        </van-row>
        <van-row v-if="isShowNav">
          <!--高程-->
          <van-field v-model="highNum" label="高程" disabled />
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { apiget ,apipost} from "@/utils/mapRequest";
import { Toast } from "vant";
export default {
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false,
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的id
    editData: {
      type: Object,
      defaults: () => {},
    },
    // 杆塔型号默认值
    gtList: {
      type: Array,
      defaults: () => [],
    },
    // 导线型号默认值
    dxList: {
      type: Array,
      defaults: () => [],
    },
    remodeState: {
      type: Boolean,
      defaults: false,
    },
  },
  watch: {
    // 监听改前改后状态
    remodeState: {
      handler(newVal) {
        if (newVal) {
          // 改后不显示拆除的数据
          this.cableLineState = this.lineState.slice(0, 2);
          this.mainLineState = this.gtState.slice(0, 3);
          this.branchLine.state = "新建";
          for (const j in this.branchLine.listArr) {
            this.branchLine.listArr[j].lineState = "新建";
          }
        } else {
          // 改前不显示新建的数据
          this.cableLineState = this.lineState.slice(1);
          this.mainLineState = this.gtState.slice(1);
          this.branchLine.state = "原有";
          for (const j in this.branchLine.listArr) {
            this.branchLine.listArr[j].lineState = "原有";
          }
        }
      },
      deep: true,
      immediate: true,
    },
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal;
      },
      deep: true,
      immediate: true,
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal;
        if (newVal.moduleType === "FXLHZ") {
          const valatege = data.voltage === "10kV" ? "10kV" : "0.4kV";
          // 查询分支线路杆塔类型
          this.getAwaitTowerOrLineType(0, 1, 1, "", "", "", "", valatege);
          // 查询导线型号
          this.getAwaitTowerOrLineType(1, 2, 1, "", "", "", "", valatege);
          this.settingObj.branchLine.listArr = [];
          this.branchLine.listArr = [];
          this.branchLine.imgList = [];
          this.branchLine.audioList = [];
          this.branchLine.voltage = data.voltage; // 电压
          this.branchLine.branch = data.gtType; // 分支类型
          this.branchLine.backLine = data.loopNum; // 回路数
          this.branchLine.state = data.state; // 状态
          this.branchLine.towerModelId = data.moduleId;
          this.branchLine.towerType = data.legendTypeKey;
          this.branchLine.towerModel = data.pointModule;
          this.branchLine.mark = data.mark; // 编号
          this.branchLine.towerSbModel = data.equipType; // 设备类型
          this.branchLine.message = data.note;
          for (const k in data.imgList) {
            const objs = {
              url: data.imgList[k].path,
              isImage: true,
              isSaveReport: data.imgList[k].isSaveReport,
            };
            this.branchLine.imgList.push(objs);
          }
          for (const s in data.voiceList) {
            const objs = {
              content: data.voiceList[s].path,
            };
            this.branchLine.audioList.push(objs);
          }
          for (const j in data.listArr) {
            const obj = {
              voltage: data.listArr[j].voltage,
              lineName: data.listArr[j].lineName, // 线路名称
              lineState: data.listArr[j].state, // 线路状态
              lineModel: data.listArr[j].lineModel, // 导线型号
              lineModelId: data.listArr[j].moduleId, // 导线id
            };
            const isShow_one = {
              lineStateVis: false, // 主线路状态
              lineModelVis: false, // 主线路导线型号
            };
            this.settingObj.branchLine.listArr.push(isShow_one);
            this.branchLine.listArr.push(obj);
          }
        }
      },
      deep: true,
    },
    gtList: {
      handler(newVal) {
        // 这块取父页面传过来的数据 因为存在多个模块请求一样的数据 避免模块间重复请求接口获取数据
        this.branchtowerModel = newVal;
        this.branchLine.towerModel = newVal[0].name;
        this.branchLine.towerModelId = newVal[0].id;
      },
      deep: true,
    },
    dxList: {
      handler(newVal) {
        // 这块取父页面传过来的数据 因为存在多个模块请求一样的数据 避免模块间重复请求接口获取数据
        this.branchlineType = newVal;
        this.branchLine.listArr[0].lineModel = newVal[0].name;
        this.branchLine.listArr[0].lineModelId = newVal[0].id;
      },
      deep: true,
    },
    // 修改分线路下得线得标识
    isEditLine: {
      handler(newVal) {
        this.inputIsDisabled = newVal;
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      branchtowerModel: [], // 杆塔型号
      branchlineType: [], // 导线型号
      cableLineState: [], // 电缆线路状态
      lineState: [
        {
          key: "新建",
          value: "新建",
        },
        {
          key: "原有",
          value: "原有",
        },
        {
          key: "拆除",
          value: "拆除",
        },
      ],
      isFoldArea: false,
      lngtitude: "",
      lattitude: "",
      inputIsDisabled: false,
      highNum: "",
      gtState: [
        {
          key: "新建",
          value: "新建",
        },
        {
          key: "原有",
          value: "原有",
        },
        {
          key: "改造",
          value: "改造",
        },
        {
          key: "拆除",
          value: "拆除",
        },
      ],
      mainLineState: [], // 电缆线路状态
      towerType: [
        {
          key: "水泥杆",
          value: "水泥杆",
          type: "SNG",
        },
        {
          key: "水泥双杆",
          value: "水泥双杆",
          type: "SNSG",
        },
        {
          key: "钢管杆",
          value: "钢管杆",
          type: "GGG",
        },
        {
          key: "窄基塔",
          value: "窄基塔",
          type: "ZJT",
        },
        {
          key: "低压水泥杆",
          value: "低压水泥杆",
          type: "DYSNG",
        },
      ], // 杆塔类型
      branch: [
        {
          key: "T接",
          value: "T接",
        },
        {
          key: "十字接",
          value: "十字接",
        },
      ], // 分支类型
      allVoltage: [
        {
          key: "10kV",
          value: "10kV",
        },
        {
          key: "380V",
          value: "380V",
        },
        {
          key: "220V",
          value: "220V",
        },
      ], // 电压等级
      backLine: [
        {
          key: "单回",
          value: "单回",
        },
        {
          key: "双回",
          value: "双回",
        },
        {
          key: "三回",
          value: "三回",
        },
        {
          key: "四回",
          value: "四回",
        },
      ], // 回路
      towerGGGGtHeight: [
        {
          key: "10",
          value: "10",
        },
        {
          key: "13",
          value: "13",
        },
        {
          key: "16",
          value: "16",
        },
      ], // 钢管杆杆高
      towerSJHeight: [
        {
          key: "310",
          value: "310",
        },
        {
          key: "350",
          value: "350",
        },
        {
          key: "390",
          value: "390",
        },
      ], // 钢管杆稍经
      towerSbModel: [
        {
          key: "不带杆上设备",
          value: "不带杆上设备",
        },
        {
          key: "带单侧隔离刀闸",
          value: "带单侧隔离刀闸",
        },
        {
          key: "带单侧隔离刀闸、断路器",
          value: "带单侧隔离刀闸、断路器",
        },
        {
          key: "带单侧隔离刀闸、断路器、关口计量",
          value: "带单侧隔离刀闸、断路器、关口计量",
        },
      ], // 杆塔设备类型
      settingObj: {
        // 分支线路
        branchLine: {
          voltageVis: false, // 电压等级
          backLineVis: false, // 回路数
          stateVis: false, // 状态
          branchVis: false, // 分支状态
          towerTypeVis: false, // 杆塔类型
          towerModelVis: false, // 杆塔型号
          towerGGGVis: false, // 钢管杆高度
          towerSjVis: false, // 钢管杆稍经
          towerZJTVis: false, // 窄基塔塔高
          towerSbModel: false, // 设备类型
          isShowtowerGGG: false, // 是否展示主杆塔钢管杆的杆高和稍经
          isShowtowerZJT: false, // 是否展示主杆塔窄基塔的塔高
          isShowtowerMoudle: true, // 是否展示主杆塔窄基塔的塔高
          listArr: [
            {
              lineStateVis: false, // 主线路状态
              lineModelVis: false, // 主线路导线型号
            },
          ],
        },
      },
      // 分支线路
      branchLine: {
        voltage: "10kV", // 电压等级
        backLine: "单回", // 回路数
        state: "新建", // 杆塔状态
        branch: "T接", // 分支类型
        towerType: "水泥杆", // 杆塔类型
        towerModel: "", // 杆塔型号
        towerModelId: "", // 杆塔型号id
        towerSbModel: "不带杆上设备", // 设备类型
        imgList: [], // 文件列表
        message: "", // 备注信息
        audioList: [], // 语音列表
        gggHeight: "",
        gggSj: "",
        zjtTg: "",
        listArr: [
          {
            voltage: "",
            lineName: "线路1", // 线路名称
            lineState: "新建", // 线路状态
            lineModel: "", // 导线型号
            lineModelId: "", // 导线id
            projectId: this.$route.query.childProjectId, // 工程id
          },
        ],
      },
    };
  },
  mounted() {},
  methods: {
    removeMainLine(index) {
      if (this.branchLine.listArr.length !== 1) {
        this.branchLine.listArr.splice(index, 1);
      } else {
        Toast.fail("最少有一条线路!");
      }
      switch (this.branchLine.listArr.length) {
        case 1:
          this.branchLine.backLine = "单回";
          break;
        case 2:
          this.branchLine.backLine = "双回";
          break;
        case 3:
          this.branchLine.backLine = "三回";
          break;
        case 4:
          this.branchLine.backLine = "四回";
          break;
      }
    },
    showEveryItemSet() {
      this.isFoldArea = !this.isFoldArea;
    },
    /**
     * 获取语音数据
     */
    getAudioData(data) {
      this.branchLine.audioList = data.aduioList;
    },
    /**
     * 提交数据
     */
    submitData() {
      const parma = {
        type: 1,
        param: this.branchLine,
        visParam: this.settingObj.branchLine,
      };
      this.$emit("submitChildData", parma);
    },
    backCurrentDom() {
      this.$emit("backCurrentDom");
      this.branchtowerModel = this.gtList;
      this.branchLine.towerModel = this.gtList[0].name;
      this.branchLine.towerModelId = this.gtList[0].id;
      this.branchlineType = this.dxList;
      // 分支线路的选型值
      this.branchLine.voltage = "10kV";
      this.branchLine.backLine = "单回";
      const stateText = this.remodeState ? "新建" : "原有";
      this.branchLine.state = stateText;
      this.branchLine.branch = "T接";
      this.branchLine.towerType = "水泥杆";
      this.branchLine.towerSbModel = "不带杆上设备";
      this.branchLine.imgList = [];
      this.branchLine.message = "";
      this.branchLine.audioList = [];
      this.branchLine.listArr = [
        {
          lineName: "线路1", // 线路名称
          lineState: stateText, // 线路状态
          lineModel: this.branchlineType[0].name, // 导线型号
          lineModelId: this.branchlineType[0].id, // 导线id
        },
      ];
      this.settingObj.branchLine.voltageVis = false;
      this.settingObj.branchLine.backLineVis = false;
      this.settingObj.branchLine.stateVis = false;
      this.settingObj.branchLine.branchVis = false;
      this.settingObj.branchLine.towerTypeVis = false;
      this.settingObj.branchLine.towerModelVis = false;
      this.settingObj.branchLine.listArr = [
        {
          lineStateVis: false, // 主线路状态
          lineModelVis: false, // 主线路导线型号
        },
      ];
    },
    /**
     * 获取上传图片数据
     */
    getImgData(data) {
      this.branchLine.imgList = data.imgList;
    },
    /**
     * 获取备注信息
     * @param data
     */
    getMsgData(data) {
      this.branchLine.message = data.message;
    },
    /**
     * 分支线路
     */
    onConfirBranchSelc(type, index, item) {
      const val = item.value;
      switch (type) {
        case 0:
          this.branchLine.voltage = val;
          // 220v和330v都按照0.4kv去查
          const valltege = val === "10kV" ? "10kV" : "0.4kV";
          this.getFirstTowerOrLineType(0, 1, 1, "", "", "", "", valltege);
          this.getFirstTowerOrLineType(1, 2, 1, "", "", "", "", valltege);
          // // 查询电缆线路类型
          this.settingObj.branchLine.voltageVis = false;
          break;
        case 1:
          this.branchLine.backLine = val;
          let length = 0;
          if (val === "单回") {
            length = 1;
          } else if (val === "双回") {
            length = 2;
          } else if (val === "三回") {
            length = 3;
          } else {
            length = 4;
          }
          this.branchLine.listArr = [];
          this.settingObj.branchLine.listArr = [];
          console.log("length", this.branchlineType);
          const stateText = this.remodeState ? "新建" : "原有";
          for (var i = 0; i < length; i++) {
            const item_one = {
              lineName: "线路" + (Number(i) + 1), // 线路名称
              lineState: stateText, // 线路状态
              lineModel: this.branchlineType[0].name, // 导线型号
              lineModelId: this.branchlineType[0].id, // 导线id
              voltage: this.branchLine.voltage,
              projectId: this.$route.query.childProjectId, // 工程id
            };
            const isShow_one = {
              lineStateVis: false, // 主线路状态
              lineModelVis: false, // 主线路导线型号
            };
            this.branchLine.listArr.push(item_one);
            this.settingObj.branchLine.listArr.push(isShow_one);
          }
          this.settingObj.branchLine.backLineVis = false;
          break;
        case 2:
          this.branchLine.state = val;
          this.settingObj.branchLine.stateVis = false;
          break;
        case 3:
          this.branchLine.branch = val;
          this.settingObj.branchLine.branchVis = false;
          break;
        case 4:
          this.branchLine.towerType = val;
          this.branchLine.towerTypeId = item.type;
          // 查询杆塔类型
          const vallteges =
            this.branchLine.voltage === "10kV" ? "10kV" : "0.4kV";
          this.getTowerOrLineType(2, 1, 1, "", "", "", "", vallteges);
          this.getTowerOrLineType(3, 2, 1, "", "", "", "", vallteges);
          // 查询电缆线路类型
          this.settingObj.branchLine.towerTypeVis = false;
          switch (val) {
            // 钢管杆展示杆高和稍经 窄基塔显示塔高 其余类型不展示
            case "钢管杆":
              this.settingObj.branchLine.isShowtowerGGG = true;
              this.settingObj.branchLine.isShowtowerZJT = false;
              this.settingObj.branchLine.isShowtowerMoudle = false;
              this.branchLine.towerModel = "";
              this.branchLine.towerModelId = "";
              this.branchLine.towerSbModel = "";
              break;
            case "窄基塔":
              this.settingObj.branchLine.isShowtowerGGG = false;
              this.settingObj.branchLine.isShowtowerZJT = true;
              this.settingObj.branchLine.isShowtowerMoudle = false;
              this.branchLine.towerModel = "";
              this.branchLine.towerModelId = "";
              this.branchLine.towerSbModel = "";
              break;
            case "水泥杆":
            case "水泥双杆":
            case "低压水泥杆":
              this.settingObj.branchLine.isShowtowerGGG = false;
              this.settingObj.branchLine.isShowtowerZJT = false;
              this.settingObj.branchLine.isShowtowerMoudle = true;
              this.branchLine.towerModel = this.branchtowerModel[0].name;
              this.branchLine.towerModelId = this.branchtowerModel[0].id;
              this.branchLine.gggHeight = "";
              this.branchLine.zjtTg = "";
              this.branchLine.zjtTg = "";
              this.branchLine.towerSbModel =
                this.settingObj.towerSbModel[0].key;
              break;
          }
          break;
        case 5:
          this.branchLine.towerModel = item.name;
          this.branchLine.towerModelId = item.id;
          this.settingObj.branchLine.towerModelVis = false;
          break;
        case 6:
          this.branchLine.listArr[index].lineState = val;
          this.settingObj.branchLine.listArr[index].lineStateVis = false;
          break;
        case 7:
          this.branchLine.listArr[index].lineModel = item.name;
          this.branchLine.listArr[index].lineModelId = item.id;
          this.settingObj.branchLine.listArr[index].lineModelVis = false;
          break;
        case 8:
          this.branchLine.gggHeight = val;
          this.settingObj.branchLine.towerGGGVis = false;
          break;
        case 9:
          this.branchLine.gggSj = val;
          this.settingObj.branchLine.towerSjVis = false;
          break;
        case 10:
          this.branchLine.zjtTg = val;
          this.settingObj.branchLine.towerZJTVis = false;
          break;
        case 11:
          this.branchLine.towerSbModel = val;
          this.settingObj.branchLine.towerSbModel = false;
          break;
      }
    },
    /**
     * 第一次初始化得时候查询各种型号赋默认值
     * 这里是为了减少修改方法这么处理了
     * @params settype 请求的具体某个模块的类型
     * @params type 请求的来自于基础参数设置当中的四类的类型 1: 水泥杆 2: 导线 3: 拉线 4: 电缆
     * @params moduleType 请求的不来自于基础参数当中的数据类型 打点类型（1：地图页查询用户参数设置、打点时选择水泥杆、导线、拉线、电缆
     2：绘制光缆3：柱上变压器方案类别、柱上设备类型、通道类别、通道类别、电缆井类型4：下户线型号、户表型号、柱上变压器方案、柱上设备型号、箱变方案类别、站房型号、环网箱方案类别、站房型号、环网室方案类别、开关站方案类别、配电室方案类别、通道信号、终端头型号、中间头型号、管材型号、电缆分支箱、电缆井型号）
     * @params materialsTypeKey 物料key
     * @params moduleTypeKey  模块key
     * @params parentKey 父类Key
     * @params moduleCode 模块code
     * @params moduleName 模块名称
     * @params voltage 请求的模块电压
     */
    getFirstTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          switch (settype) {
            case 0:
              // 分线路杆塔型号
              if (voltage === "10kV") {
                that.branchtowerModel = res.data.tenSNG;
                that.branchLine.towerModel = res.data.tenSNG[0].name;
                that.branchLine.towerModelId = res.data.tenSNG[0].id;
              } else {
                that.branchtowerModel = res.data.lowSNG;
                that.branchLine.towerModel = res.data.lowSNG[0].name;
                that.branchLine.towerModelId = res.data.lowSNG[0].id;
              }
              break;
            case 1:
              if (voltage === "10kV") {
                // 分线路导线型号
                that.branchlineType = res.data.tenDx;
                that.branchLine.listArr[0].lineModel = res.data.tenDx[0].name;
                that.branchLine.listArr[0].lineModelId = res.data.tenDx[0].id;
              } else {
                // 分线路导线型号
                that.branchlineType = res.data.lowDx;
                that.branchLine.listArr[0].lineModel = res.data.lowDx[0].name;
                that.branchLine.listArr[0].lineModelId = res.data.lowDx[0].id;
              }
              break;
          }
        }
      });
    },
    getAwaitTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName,
      selectVal,
      selectLevelTwoVal
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          switch (settype) {
            case 0:
              // 分线路杆塔型号
              if (voltage === "10kV") {
                that.branchtowerModel = res.data.tenSNG;
              } else {
                that.branchtowerModel = res.data.lowSNG;
              }
              break;
            case 1:
              // 分线路导线型号
              if (voltage === "10kV") {
                that.branchlineType = res.data.tenDx;
              } else {
                that.branchlineType = res.data.lowDx;
              }
              break;
          }
        }
      });
    },
  },
};
</script>

<style lang="sass" scoped>
</style>

