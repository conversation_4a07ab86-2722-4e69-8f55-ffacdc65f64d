<template>
  <div id="svgBox">
    <el-table
      id="svgTop"
      ref="tableData"
      :data="tableData1"
      style="width: 100%"
      :max-height="800"
      row-key="projectWorkloadId"
      :height="calcHeight"
      border
      stripe
      highlight-current-row
      :header-cell-style="{
        textAlign: 'center',
        background: '#dce0e2',
        color: '#fffff',
      }"
      default-expand-all
      :tree-props="{ children: 'workMenuList', hasChildren: 'hasChildren' }"
      @cell-click="selectionRow"
      @row-contextmenu="contextMenu"
    >
      <!-- <el-table-column align="center" width="140">
        <template slot="header" slot-scope="scope">
          <a v-for="(item,index) in maxLevel" :key="index" href="javascript:;" @click="handleExpand(item)">
            {{ item }}
          </a>&nbsp;
        </template>
      </el-table-column> -->
      <el-table-column prop="workSort" label="序号" align="center" width="80" />
      <el-table-column prop="workNumber" align="center" label="项目编码" />
      <el-table-column
        show-overflow-tooltip
        align="center"
        label="名称"
        width="230"
      >
        <template slot-scope="scope">
          <span
            v-show="
              scope.row.workMaterialsType === '清单' ||
              scope.row.workMaterialsType === '自定义清单'
            "
            style="
              color: white;
              background-color: #795da3;
              padding: 3px;
              border-radius: 5px;
              font-weight: 600;
            "
            >清</span
          >
          <span
            v-show="scope.row.workMaterialsType === '定额'"
            style="
              color: white;
              background-color: red;
              padding: 3px;
              border-radius: 5px;
              font-weight: 600;
            "
            >定</span
          >
          <span
            v-show="scope.row.workMaterialsType === '主材'"
            style="
              color: white;
              background-color: 51b4f7;
              padding: 3px;
              border-radius: 5px;
              font-weight: 600;
            "
            >主</span
          >
          <span
            v-show="scope.row.workMaterialsType === '设备'"
            style="
              color: white;
              background-color: #51b4f7;
              padding: 3px;
              border-radius: 5px;
              font-weight: 600;
            "
            >设</span
          >
          <span
            v-show="scope.row.workMaterialsType === '土方'"
            style="
              color: white;
              background-color: orange;
              padding: 3px;
              border-radius: 5px;
              font-weight: 600;
            "
            >土</span
          >
          <span
            v-show="scope.row.workMaterialsType === '注解'"
            style="
              color: white;
              background-color: #795da3;
              padding: 3px;
              border-radius: 5px;
              font-weight: 600;
            "
            >注</span
          >
          <span
            v-show="scope.row.workMaterialsType === '一笔性费用'"
            style="
              color: white;
              background-color: red;
              padding: 3px;
              border-radius: 5px;
              font-weight: 600;
            "
            >费</span
          >
          {{ scope.row.workName }}
        </template>
      </el-table-column>
      <el-table-column prop="workCode" align="center" label="项目代码" />
      <el-table-column
        prop="workKillProperty"
        align="center"
        label="专业属性"
      />
      <el-table-column
        v-if="isShowCell"
        prop="workSpecification"
        show-overflow-tooltip
        label="规格"
      />
      <el-table-column prop="workUnit" align="center" label="单位" />
      <el-table-column prop="workFormulaMode" label="计算试" />
      <el-table-column prop="workSize" align="center" label="数量" />
      <el-table-column
        v-if="isShowCell"
        prop="workPrice"
        align="center"
        label="单价(元)"
      />
      <el-table-column
        v-if="!isShowCell"
        prop="workPrice"
        width="180"
        align="center"
        label="预算不含税"
      />
      <el-table-column
        v-if="!isShowCell"
        prop="workUnitPrice"
        align="center"
        label="综合单价"
      />
      <el-table-column prop="workTotalPrice" align="center" label="合价(元)" />
      <!-- <el-table-column prop="workFeeTable" align="center" label="取费表" /> -->
      <!-- <el-table-column v-if="isShowCell" prop="workAssociated" align="center" label="关联级量" /> -->
      <!--              <el-table-column-->
      <!--                prop="name"-->
      <!--                label="备注"-->
      <!--              />-->
      <!-- <el-table-column fixed="right" width="100" label="操作">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            align="center"
            style="color: black;"
            @click="$emit('editClick',scope.row)"
          >编辑</el-button>
          <el-button
            type="text"
            size="small"
            align="center"
            style="color: red;"
            @click.native.prevent="$emit('deleteRow',scope.row)"
          >删除</el-button>
        </template>
      </el-table-column> -->
    </el-table>
    <!-- <div id="svgResize" />
    <div v-show="isShow" id="svgDown" style="border-top: 1px solid #b5b9a9; ">
      <slot id="svgBox" width="100%" />
    </div> -->
  </div>
</template>

<script>
import $ from 'jquery'
export default {
  name: 'TableTemplate',
  props: ['tableData1', 'maxLevel', 'isShowCell'],
  data() {
    return {
      calcHeight: 'calc(100vh - 24vh)',
      isShow: false,
    }
  },
  watch: {
    screenHeight(val) {
      // 为了避免频繁触发resize函数导致页面卡顿，使用定时器
      if (!this.timer) {
        // 一旦监听到的screenWidth值改变，就将其重新赋给data里的screenWidth
        this.screenHeight = val
        this.timer = true
        const that = this
        setTimeout(function () {
          // 打印screenWidth变化的值
          console.log(that.screenHeight)
          that.timer = false
        }, 400)
      }
    },
  },
  mounted() {
    this.dragControllerDiv()
    const that = this
    window.onresize = () => {
      return (() => {
        // 可以限制最小高度
        // if (document.body.clientHeight - 240 < 450) {
        //   return
        // }
        window.screenHeight = document.body.clientHeight - 110
        that.screenHeight = window.screenHeight
      })()
    }
  },
  methods: {
    dragControllerDiv: function () {
      $(document).ready(function () {
        var svgResize = document.getElementById('svgResize')
        var svgTop = document.getElementById('svgTop')
        var svgDown = document.getElementById('svgDown')
        var svgBox = document.getElementById('svgBox')
        svgResize.onmousedown = function (e) {
          var startY = e.clientY
          svgResize.top = svgResize.offsetTop
          document.onmousemove = function (e) {
            var endY = e.clientY
            var moveLen = svgResize.top + (endY - startY)
            var maxT = svgBox.clientHeight - svgResize.offsetHeight
            if (moveLen < 30) moveLen = 30
            if (moveLen > maxT - 30) moveLen = maxT - 30
            svgResize.style.top = moveLen
            svgTop.style.height = moveLen + 'px'
            svgDown.style.height = svgBox.clientHeight - moveLen - 5 + 'px'
          }
          document.onmouseup = function (evt) {
            document.onmousemove = null
            document.onmouseup = null
            svgResize.releaseCapture && svgResize.releaseCapture()
          }
          svgResize.setCapture && svgResize.setCapture()
          return false
        }
      })
    },
    /**
     * 右击某一行
     * @param row
     */
    contextMenu(row, column, event) {
      event.preventDefault()
      this.$emit('contextMenu', row, this.maxLevel, event)
    },

    /**
     * 展开事件
     * @param selectIndex
     */
    handleExpand(selectIndex) {
      this.toggleRowExpansionAll(this.tableData1, false)
      this.forArr(this.tableData1, selectIndex)
    },

    /**
     * 循环展开方法
     * @param arr
     * @param selectIndex
     */
    forArr(arr, selectIndex) {
      arr.forEach((i) => {
        if (parseInt(i.workMenuYs) < selectIndex) {
          // toggleRowExpansion(i, isExpand)用于多选表格，切换某一行的选中状态，如果使用了第二个参数，则是设置这一行选中与否（selected 为 true 则选中）
          this.$refs.tableData.toggleRowExpansion(i, true)
          if (i.workMenuList.length > 0) {
            this.forArr(i.workMenuList, selectIndex)
          }
        }
      })
    },

    /**
     * 全部展开收缩方法
     * @param data
     * @param isExpansion
     */
    toggleRowExpansionAll(data, isExpansion) {
      data.forEach((item) => {
        this.$refs.tableData.toggleRowExpansion(item, isExpansion)
        if (item.workMenuList !== undefined && item.workMenuList !== null) {
          this.toggleRowExpansionAll(item.workMenuList, isExpansion)
        }
      })
    },

    /**
     * 点击选中单元格信息
     * @param row
     */
    selectionRow(row) {
      if (
        row.workMaterialsType !== '节点' &&
        row.workMaterialsType !== '一笔性费用' &&
        row.workType !== '5'
      ) {
        this.calcHeight = 'calc(100vh - 50vh)'
        this.isShow = true
      } else {
        this.isShow = false
        this.calcHeight = 'calc(100vh - 26vh)'
      }

      this.isRight = false
      this.$emit('selectRow', row)
    },
  },
}
</script>

<style scoped>
#svgBox {
  width: 100%;
  position: relative;
  overflow: hidden;
  overflow-x: hidden;
  overflow-y: auto;
}

#svgTop {
  height: 100vh;
  width: 100%;
  float: left;
  overflow: auto;
}

#svgResize {
  position: relative;
  height: 5px;
  width: 100%;
  cursor: s-resize;
  float: left;
}

#svgDown {
  height: 55vh;
  width: 100%;
  float: left;
  overflow: hidden;
}
</style>
