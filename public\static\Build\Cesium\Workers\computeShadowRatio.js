define(["./Cartographic-3309dd0d","./Cartesian4-3ca25aab","./when-b60132fc","./Math-119be1a3","./createTaskProcessorWorker","./Check-7b2a090c"],(function(r,a,e,t,n,i){"use strict";var s=new a.Cartesian4(1,1/255,1/65025,1/160581375),o=new a.Cartesian4,f=1024;function u(e,n,i,u,c,h,C){var d=e.longitude,v=e.latitude,g=e.height;if(d=t.CesiumMath.toDegrees(d),v=t.CesiumMath.toDegrees(v),d<n[0]||d>n[2]||v<n[1]||v>n[3])return-1;for(var b=!1,l=0,p=.1*u,m=0;m<=i;m+=u){if(Math.abs(c+m-g)<p){b=!0;break}l++}if(!b)return-1;if(h.length<0)return-1;b=!1;for(var k=0;k<h.length;k+=2){var w=r.Cartesian3.fromDegrees(d,v,g),D=r.Cartesian3.fromDegrees(h[k+0],h[k+1],g);if(r.Cartesian3.distance(w,D)<p){b=!0;break}}if(!b)return-1;var M=n[2]-n[0],x=(g=n[3]-n[1],n[0]-.025*M),y=n[1]-.025*g;M+=.05*M,g+=.05*g;var I=parseInt((d-x)/M*f),P=parseInt((v-y)/g*f);I=I<1?1:I,P=P<1?1:P;var z=C[l],A=0;for(k=-1;k<2;k++)for(var B=-1;B<2;B++){var R=4*(f*(P+B)+(I+k));o.x=z[R],o.y=z[R+1],o.z=z[R+2],o.w=z[R+3],a.Cartesian4.divideByScalar(o,255,o),A=Math.max(A,a.Cartesian4.dot(o,s))}return A=A>.999?1:A}return n((function(a,e){for(var t=a.points,n=a.enuPoints,i=a.bounds,s=a.extend,o=a.spacing,f=a.bottom,c=a.pixelsArray,h=[],C=0,d=t.length;C<d;C++){var v=t[C],g=u(r.Cartographic.fromCartesian(v),i,s,o,f,n,c);h.push({position:r.Cartesian3.clone(v),shadowRatio:g})}return{resultData:h}}))}));
