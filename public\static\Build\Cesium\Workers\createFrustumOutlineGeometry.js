define(["./when-b60132fc","./buildModuleUrl-4e1b81e7","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./FrustumGeometry-9d84ae9b","./GeometryAttribute-3a88ba31","./GeometryAttributes-252e9929","./PrimitiveType-a54dc62f","./Cartesian2-47311507","./Math-119be1a3","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian4-3ca25aab","./Plane-7ae8294c","./VertexFormat-6446fca0","./FeatureDetection-c3b71206"],(function(e,t,r,a,n,i,u,o,c,s,p,m,d,h,f,g,_,k){"use strict";function l(t){var a,n,o=t.frustum,c=t.orientation,s=t.origin,p=e.defaultValue(t._drawNearPlane,!0);o instanceof i.PerspectiveFrustum?(a=0,n=i.PerspectiveFrustum.packedLength):o instanceof i.OrthographicFrustum&&(a=1,n=i.OrthographicFrustum.packedLength),this._frustumType=a,this._frustum=o.clone(),this._origin=r.Cartesian3.clone(s),this._orientation=u.Quaternion.clone(c),this._drawNearPlane=p,this._workerName="createFrustumOutlineGeometry",this.packedLength=2+n+r.Cartesian3.packedLength+u.Quaternion.packedLength}l.pack=function(t,a,n){n=e.defaultValue(n,0);var o=t._frustumType,c=t._frustum;return a[n++]=o,0===o?(i.PerspectiveFrustum.pack(c,a,n),n+=i.PerspectiveFrustum.packedLength):(i.OrthographicFrustum.pack(c,a,n),n+=i.OrthographicFrustum.packedLength),r.Cartesian3.pack(t._origin,a,n),n+=r.Cartesian3.packedLength,u.Quaternion.pack(t._orientation,a,n),a[n+=u.Quaternion.packedLength]=t._drawNearPlane?1:0,a};var y=new i.PerspectiveFrustum,v=new i.OrthographicFrustum,F=new u.Quaternion,b=new r.Cartesian3;return l.unpack=function(t,a,n){a=e.defaultValue(a,0);var o,c=t[a++];0===c?(o=i.PerspectiveFrustum.unpack(t,a,y),a+=i.PerspectiveFrustum.packedLength):(o=i.OrthographicFrustum.unpack(t,a,v),a+=i.OrthographicFrustum.packedLength);var s=r.Cartesian3.unpack(t,a,b);a+=r.Cartesian3.packedLength;var p=u.Quaternion.unpack(t,a,F),m=1===t[a+=u.Quaternion.packedLength];if(!e.defined(n))return new l({frustum:o,origin:s,orientation:p,_drawNearPlane:m});var d=c===n._frustumType?n._frustum:void 0;return n._frustum=o.clone(d),n._frustumType=c,n._origin=r.Cartesian3.clone(s,n._origin),n._orientation=u.Quaternion.clone(p,n._orientation),n._drawNearPlane=m,n},l.createGeometry=function(e){var r=e._frustumType,a=e._frustum,s=e._origin,p=e._orientation,m=e._drawNearPlane,d=new Float64Array(24);i.FrustumGeometry._computeNearFarPlanes(s,p,r,a,d);for(var h,f,g=new o.GeometryAttributes({position:new u.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:d})}),_=m?2:1,k=new Uint16Array(8*(_+1)),l=m?0:1;l<2;++l)f=4*l,k[h=m?8*l:0]=f,k[h+1]=f+1,k[h+2]=f+1,k[h+3]=f+2,k[h+4]=f+2,k[h+5]=f+3,k[h+6]=f+3,k[h+7]=f;for(l=0;l<2;++l)f=4*l,k[h=8*(_+l)]=f,k[h+1]=f+4,k[h+2]=f+1,k[h+3]=f+5,k[h+4]=f+2,k[h+5]=f+6,k[h+6]=f+3,k[h+7]=f+7;return new u.Geometry({attributes:g,indices:k,primitiveType:c.PrimitiveType.LINES,boundingSphere:t.BoundingSphere.fromVertices(d)})},function(t,r){return e.defined(r)&&(t=l.unpack(t,r)),l.createGeometry(t)}}));
