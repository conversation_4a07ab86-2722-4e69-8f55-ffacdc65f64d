import Vue from 'vue'
import { Aside, <PERSON><PERSON>, Col, Container, Form, FormItem, Header, Input, Main, MessageBox, Row, Message, Dropdown, DropdownItem, DropdownMenu, Menu, MenuItem, MenuItemGroup, Submenu, Table, TableColumn, DatePicker, Select, Option,Loading, Upload, Dialog, Link, Pagination, Descriptions, DescriptionsItem, Tooltip } from 'element-ui'

Vue.use(Button)
Vue.use(Form)
Vue.use(FormItem)
Vue.use(Input)
Vue.use(Header)
Vue.use(Aside)
Vue.use(Main)
Vue.use(Container)
Vue.use(Row)
Vue.use(Col)
Vue.use(Dropdown)
Vue.use(DropdownItem)
Vue.use(DropdownMenu)
Vue.use(Menu)
Vue.use(MenuItem)
Vue.use(MenuItemGroup)
Vue.use(Submenu)
Vue.use(Table)
Vue.use(TableColumn)
Vue.use(DatePicker)
Vue.use(Select)
Vue.use(Option)
Vue.use(Upload)
Vue.use(Dialog)
Vue.use(Link)
Vue.use(Pagination)
Vue.use(Descriptions)
Vue.use(DescriptionsItem)
Vue.use(Link)
Vue.use(Tooltip)
Vue.use(Loading);    // 使用服务方式的话，只安装Loading即可
Vue.use(Loading.directive);  //  指令方式(v-loading)的话这两行都得有
Vue.prototype.$confirm = MessageBox.confirm
Vue.prototype.$message = Message
