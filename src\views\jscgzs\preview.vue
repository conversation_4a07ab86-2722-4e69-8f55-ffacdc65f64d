<template>
  <el-dialog
    :title="'预览'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    lock-scroll
    append-to-body
    fullscreen
    width="60%"
    center
    @close="cancleForm()"
  >
    <div class="scrollBox" style="height: 87vh">
      <!-- <pdf
        v-for="item in numPages"
        :key="item"
        :src="pdfSrc"
        :page="item"
        ref="pdf"
      ></pdf> -->
      <iframe
        v-if="pdfUrl"
        class="prism-player"
        :src="pdfUrl"
        width="100%"
        height="100%"
      ></iframe>
    </div>
  </el-dialog>
</template>
<script>
import pdf from 'vue-pdf'
import { resquest } from '@/api/api'

export default {
  components: {
    pdf,
  },
  data() {
    return {
      visible: false,
      form: {},
      //   url: resquest,
      loading: false,
      numPages: null, // pdf 总页数
      pdfSrc: '',
      resquest: resquest,
      pdfUrl: '',
      wordDialog: {
        pdfSrc: '',
        loadPdf: false,
      },
    }
  },
  mounted() {},
  methods: {
    cancleForm() {
      // this.$refs['form'].resetFields()
      this.visible = false
      // this.wordDialog.pdfSrc = ''
      // this.loadPdf.pdfSrc = false
    },

    init(row) {
      this.visible = true // 打开弹窗
      // this.pdfSrc = resquest + '/' + row.url
      this.pdfSrc = row
      console.log(row)
      this.$nextTick(() => {
        // this.wordDialog.pdfSrc = ''
        // this.loadPdf.pdfSrc = false
        this.pdfUrl = ''
        // this.$refs['form'].resetFields() // 清空表单
        // 如果有id就是编辑查看，这里的请求用于做数据回填
      })
      this.getData()
      // this.getNumPages()
    },
    // 计算pdf页码总数
    getNumPages() {
      let loadingTask = pdf.createLoadingTask(this.pdfSrc)
      loadingTask.promise
        .then((pdf) => {
          this.numPages = pdf.numPages
        })
        .catch((err) => {
          console.error('pdf 加载失败', err)
        })
    },

    getData() {
      let _this = this
      _this
        .axios({
          method: 'get',
          url: this.pdfSrc,
          responseType: 'blob',
        })
        .then(function (res) {
          _this.$nextTick(() => {
            const blob = new Blob([res.data], {
              type: 'application/pdf',
            })
            // _this.wordDialog.pdfSrc = window.URL.createObjectURL(blob)
            // _this.wordDialog.loadPdf = true

            _this.pdfUrl = window.URL.createObjectURL(blob)
            // window.open(_this.wordDialog.pdfSrc)//新窗口打开，借用浏览器去打印
            // this.$refs.pdfRef.print();//这个打印调不起来，一直显示print方法找不到<br>});
          })
        })
        .catch(function (err) {
          console.log(err)
        })
    },
  },
}
</script>
<style lang="scss" scoped>
// .scrollBox {
//   height: 100%;
// }
</style>
