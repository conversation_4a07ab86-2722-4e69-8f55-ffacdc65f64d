<template>
  <div>
    <!--电缆头-->
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div v-if="showBackAdds" class="maps-zhedNav" @click="backCurrentDom">
            <img
              class="mapites-backImg"
              :src="require('@/assets/' + 'map/settingImg/backAdds.png')"
              alt=""
            />
          </div>
          <span />
          电缆头
          <div
            v-if="!showBackAdds"
            class="maps-zhedNav"
            @click="showEveryItemSet(14)"
          >
            <img
              v-show="!isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zhedie.png')"
              alt=""
            />
            <img
              v-show="isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zkzhedie.png')"
              alt=""
            />
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img
            class="settingImg"
            :src="require('@/assets/' + 'map/settingImg/useSetting.png')"
            alt=""
          />
          <p>启用</p>
        </div>
      </div>
      <div v-show="isFoldArea" class="pro-addForm">
        <div class="map-showNav">基本信息</div>
        <van-row>
          <van-field
            v-model="cableHead.mark"
            label="编号"
            placeholder="请输入电缆头编号"
          />
        </van-row>
        <div class="map-showNav">选型信息</div>
        <van-row>
          <!--电缆头类型-->
          <van-field
            readonly
            clickable
            :value="cableHead.type"
            label="电缆头类型"
            placeholder="请选择类型"
            @click="settingObj.cableHead.type = true"
          />
          <van-popup
            v-model="settingObj.cableHead.type"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="电缆头类型"
              value-key="key"
              :columns="cableHeadType"
              @confirm="onConfirmDltSel(0, $event)"
              @cancel="settingObj.cableHead.type = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--电缆头电压等级-->
          <van-field
            readonly
            clickable
            :value="cableHead.voltage"
            label="电压等级"
            placeholder="请选择电压等级"
            @click="settingObj.cableHead.voltage = true"
          />
          <van-popup
            v-model="settingObj.cableHead.voltage"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="电压等级"
              value-key="key"
              :columns="voltage"
              @confirm="onConfirmDltSel(2, $event)"
              @cancel="settingObj.cableHead.voltage = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <van-field
            readonly
            clickable
            :value="cableHead.state"
            label="电缆头状态"
            placeholder="请选择状态"
            @click="settingObj.cableHead.state = true"
          />
          <van-popup
            v-model="settingObj.cableHead.state"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="电缆头类型"
              value-key="key"
              :columns="cableLineState"
              @confirm="onConfirmDltSel(4, $event)"
              @cancel="settingObj.cableHead.state = false"
            />
          </van-popup>
        </van-row>
        <!--电缆头型号-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="cableHead.model"
            label="电缆头型号"
            placeholder="请选择电缆头型号"
            @click="settingObj.cableHead.model = true"
          />
          <van-popup
            v-model="settingObj.cableHead.model"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="电缆头型号"
              value-key="moduleName"
              :columns="cableDltModel"
              @confirm="onConfirmDltSel(3, $event)"
              @cancel="settingObj.cableHead.model = false"
            />
          </van-popup>
        </van-row>
        <div v-if="isShowNav" class="map-showNav">其他信息</div>
        <van-row v-if="isShowNav">
          <!--经度-->
          <van-field v-model="lngtitude" label="经度" disabled />
        </van-row>
        <van-row v-if="isShowNav">
          <!--纬度-->
          <van-field v-model="lattitude" label="纬度" disabled />
        </van-row>
        <van-row v-if="isShowNav">
          <!--高程-->
          <van-field v-model="highNum" label="高程" disabled />
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { apiget,apipost } from "@/utils/mapRequest";

export default {
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false,
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的id
    editData: {
      type: Object,
      defaults: () => {},
    },
    // 终端头型号
    zdtTypeList: {
      type: Array,
      defaults: () => [],
    },
    remodeState: {
      type: Boolean,
      defaults: false,
    },
  },
  watch: {
    // 监听改前改后状态
    remodeState: {
      handler(newVal) {
        if (newVal) {
          // 改后不显示拆除的数据
          this.cableLineState = this.lineState.slice(0, 2);
          this.cableHead.state = "新建";
        } else {
          // 改前不显示新建的数据
          this.cableLineState = this.lineState.slice(1);
          this.cableHead.state = "原有";
        }
      },
      deep: true,
      immediate: true,
    },
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal;
      },
      deep: true,
      immediate: true,
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal;
        if (data.moduleType === "DLTHZ") {
          this.cableHead.imgList = [];
          this.cableHead.audioList = [];
          // 查询电缆头型号
          this.getAwaitTowerOrLineType(
            "",
            4,
            "",
            "DLZDT",
            "",
            "",
            data.voltage
          );
          this.settingObj.visible = true;
          this.cableHead.voltage = data.voltage;
          this.cableHead.modelId = data.moduleId;
          this.cableHead.model = data.pointModule;
          this.cableHead.type = data.dltType;
          this.cableHead.state = data.state;
          this.cableHead.mark = data.mark; // 编号
          this.cableHead.message = data.note;
          for (const k in data.imgList) {
            const objs = {
              url: data.imgList[k].path,
              isImage: true,
              isSaveReport: data.imgList[k].isSaveReport,
            };
            this.cableHead.imgList.push(objs);
          }
          for (const s in data.voiceList) {
            const objs = {
              content: data.voiceList[s].path,
            };
            this.cableHead.audioList.push(objs);
          }
        }
      },
      deep: true,
    },
    // 终端头型号
    zdtTypeList: {
      handler(newVal) {
        this.cableHead.model = newVal[0].moduleName;
        this.cableHead.modelId = newVal[0].moduleID;
        this.cableDltModel = newVal;
      },
      deep: true,
    },
  },
  data() {
    return {
      isFoldArea: false,
      cableDltModel: [], // 电缆头型号 后台读
      lattitude: "",
      lngtitude: "",
      highNum: "",
      voltage: [
        {
          key: "10kV",
          value: "10kV",
        },
        {
          key: "0.4kV",
          value: "0.4kV",
        },
      ], // 电压等级
      cableHeadType: [
        {
          key: "终端头",
          value: "终端头",
          type: "ZDT",
        },
      ], // 电缆头类型
      cableLineState: [], // 电缆线路状态
      lineState: [
        {
          key: "新建",
          value: "新建",
        },
        {
          key: "原有",
          value: "原有",
        },
        {
          key: "拆除",
          value: "拆除",
        },
      ],
      settingObj: {
        // 电缆头
        cableHead: {
          type: false, // 电缆头类型
          state: false, // 电缆头状态
          voltage: false, // 电缆头电压等级
          model: false, // 电缆头型号
        },
      },
      // 电缆头
      cableHead: {
        type: "终端头", // 电缆头类型
        state: "新建", // 电缆头状态
        voltage: "10kV", // 电缆头电压等级
        model: "", // 电缆头型号
        modelId: "", // 电缆头型号
        mark: "", // 电缆头编号
        imgList: [], // 文件列表
        message: "", // 备注信息
        audioList: [], // 语音列表
      },
    };
  },
  mounted() {},
  methods: {
    /**
     * 电缆头
     */
    onConfirmDltSel(type, item) {
      const val = item.value;
      switch (type) {
        case 0:
          this.cableHead.type = val;
          this.settingObj.cableHead.type = false;
          if (val === "终端头") {
            this.settingObj.isDisabled = true;
          } else {
            this.settingObj.isDisabled = true;
          }
          break;
        case 2:
          this.cableHead.voltage = val;
          this.getTowerOrLineType("", 4, "", "DLZDT", "", "", val);
          this.settingObj.cableHead.voltage = false;
          break;
        case 3:
          this.cableHead.model = item.moduleName;
          this.cableHead.modelId = item.moduleID;
          this.settingObj.cableHead.model = false;
          break;
        case 4:
          this.cableHead.state = val;
          this.settingObj.cableHead.state = false;
          break;
      }
    },
    getTowerOrLineType(
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          // 电缆头型号
          that.cableDltModel = res.data;
          that.cableHead.model = res.data[0].moduleName;
          that.cableHead.modelId = res.data[0].moduleID;
        }
      });
    },
    /**
     * 提交数据
     */
    submitData() {
      const parma = {
        type: 13,
        param: this.cableHead,
        visParam: this.settingObj.cableHead,
      };
      this.$emit("submitChildData", parma);
    },
    async getAwaitTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName,
      selectVal,
      selectLevelTwoVal
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      await apipost("/moduleSelection/selectModuleData", param).then(function (
        res
      ) {
        if (res.code === 1001) {
          that.settingObj.cableDltModel = res.data;
        }
      });
    },
    backCurrentDom() {
      this.$emit("backCurrentDom");
      // 电缆头
      this.cableHead.type = "终端头";
      this.cableHead.voltage = "10kV";
      this.cableHead.number = "1";
      this.cableHead.spacing = "250";
      const stateText = this.remodeState ? "新建" : "原有";
      this.cableHead.state = stateText;
      this.cableHead.imgList = [];
      this.cableHead.message = "";
      this.cableHead.audioList = [];
      this.settingObj.cableHead.type = false;
      this.settingObj.cableHead.voltage = false;
      this.settingObj.cableHead.model = false;
      this.settingObj.cableHead.state = false;
      // 变电站终端头型号
      this.cableDltModel = this.zdtTypeList;
      this.cableHead.model = this.zdtTypeList[0].moduleName;
      this.cableHead.modelId = this.zdtTypeList[0].moduleID;
    },
    showEveryItemSet() {
      this.isFoldArea = !this.isFoldArea;
    },
    getMsgData(data) {
      this.cableHead.message = data.message;
    },
    /**
     * 获取语音数据
     */
    getAudioData(data) {
      this.cableHead.audioList = data.aduioList;
    },
    /**
     * 获取上传图片数据
     */
    getImgData(data) {
      this.cableHead.imgList = data.imgList;
    },
  },
};
</script>

<style lang="sass" scoped>
</style>

