import http from '../utils/http'
import qs from 'qs'
import axios from 'axios'
import axiosInstance from '@/utils/request.js'
import axiosInstanceJM from '@/utils/requestJM.js'

// import ip from '/public/ip.json'
/**
	*  @parms resquest 请求地址 例如：http://************:8088/request/...
	*  @param '/testIp'代表vue-cil中config，index.js中配置的代理
	*/
const resquest = process.env.VUE_APP_API_publicUrl
export const getTime_Http = (params) =>
	axiosInstanceJM.get(`${process.env.VUE_APP_API_publicUrlLocal}/oauth/getTime`, {
		params,
	})
	// 双加密新转增接口 start
	export const getTime_HttpSession = () =>axiosInstanceJM.post(`${process.env.VUE_APP_API_publicUrlLocal}/oauth/getTimmeForSession`)
	export const getTimeForPublickey = (params) =>	axiosInstanceJM.post(`${process.env.VUE_APP_API_publicUrlLocal}/oauth/getTimeForPublickey`, {
		params,
	})
	// 双加密新转增接口 end
export const getUserInfoTygzt = (params) =>
	axiosInstanceJM.post(`/workbench-isc/workbench-isc/user/getLoginUserInfoNew`, {
		params,
	})

export function getuserRole_Http (obj) {
	return axiosInstanceJM({
		url: `/secpmsedas/business-inspection-center/WMCenter/ZJ/user/getLoginUserInfoByUserId`,
		method: "post",
		data: obj,
	})
}

export function getTicket_Http (params) {
	return http.postnoJM(`${process.env.VUE_APP_API_publicUrlLocal}/oauth/getTicket`, params)
}
export function login (params) {
	
	return http.post(`${resquest}/tDtfUserRoles/getUser`, params)
}
export function generateMapJson (params) {
	return http.post(`${resquest}/mapModel/generateMapJson`, params)
}
export function pushMaterialData (params) {
	return http.post(`${resquest}/dataTransfer/materialData`, params)
}
export function gettoken () {
	return http.get(`${resquest}/spltkx/getToken`)
}
export function getlogin (params) {
	return http.get(`${resquest}/spltkx/login`, params)
}
export function getMenuChildren (params) {
	
	return http.post(`${resquest}/tDtfUserRoles/getUserid`, params)
}
// 结算成果展示
export function getJsData (params) {
	return http.post(`${resquest}/tDtfFileModule/queryFileModule`, params)
}
// 结算成果展示详情
export function getJscgDetail (params) {
	return http.post(`${resquest}/tDtfFileModule/queryFileModuleInfo`, params)
}
// 结算成果文件导入
export function jscgUpload (params) {
	return http.post(`${resquest}/tDtfFileModule/fileUpload`, params)
}
// 结算成果文件单条下载
export function jscgDownloadId (params) {
	
	return http.post(`${resquest}/tDtfFileModule/download`, params)
}
// 结算成果文件单条删除
export function jscgFileDel (params) {
	
	return http.post(`${resquest}/tDtfFileModule/deleteFile`, params)
}
// 结算成果文件全部下载
export function jscgDownloadAll (params) {
	
	return http.downloadPost(`${resquest}/tDtfFileModule/downloadAll`, params)
}

// 结算计划
export function getJsjh (params) {
	
	return http.post(`${resquest}/settlementPlan/selectSettlementPlan`, params)
}
// 结算计划修改
export function editJsjh (params) {
	
	return http.post(`${resquest}/settlementPlan/saveSettlementPlan`, params)
}
// 结算计划导出
export function downloadJsjh (params) {
	return http.post(`${resquest}/settlementPlan/download`, params)
}
// 结算统计
export function getJstj (params) {
	
	return http.post(`${resquest}/settlementStatistics/select`, params)
}
// 结算统计修改
export function editJstj (params) {
	
	return http.post(`${resquest}/settlementStatistics/save`, params)
}
// 获取建设单位
export function getJsdw (params) {
	
	return http.post(`${resquest}/settlementPlan/queryUnit`, params)
}


// 获取城市
export function getCity (params) {
	console.log(params, '获取城市')
	return http.post(`${resquest}/tDtfProject/selCityName`, params)
}

// 获取县
export function getCounty (params) {
	return http.post(`${resquest}/tDtfProject/selArea`, params)
}
// 获取项目代办---需求项目列表
export function getSettlement (params) {
	// 
	// return http.getObtain(`${resquest}/tDtfProject/queryProject`, params)
	return http.getObtain(`${resquest}/tDtfProject/queryDesign`, params)
}
// 初设评审下载方法
export function downFileCSPS (params, url) {
	return http.downloadPost(`${resquest}${url}`, params)
}
// 结算页面获取数据列表
export function getSettlementJS (params) {
	// 
	return http.getObtain(`${resquest}/tDtfProject/querySettlement`, params)
}
// 项目汇总 -- 获取项目汇总列表
export function getList (params) {
	return http.getObtain(`${resquest}/tXmhz/queryXmhzList`, params)
}

// // 结算审核、申报查询
export function getSb (params) {
	// 
	return http.getObtain(`${resquest}/tDtfProject/querySettlement`, params)
}

// 项目代办 --- 需求项目编辑获取下拉
export function getXQEditPull (params) {
	return http.post(`${resquest}/tDtfProject/queryBasicData`, params)
}

// 项目代办 --- 需求项目保存编辑
export function EditSave (params) {


	return http.post(`${resquest}/tDtfProject/save`, params)
}

// 项目代办 --- 需求项目问题查看
export function ProblemView (params) {
	// 
	return http.post(`${resquest}/tWtqd/`, params)
}

// 项目代办 --- 需求项目完整性分析查看
export function completeView (params) {
	// 
	console.log(params, 'paramsparams')
	return http.post(`${resquest}/tDtfProJxWzx/querytDtfProJxWzx`, params)
}

// 项目代办 --- 需求项目完整性分析查看
export function DetailsView (taskid) {
	console.log(taskid, "taskidtaskid")
	let params = { taskid: taskid }
	return http.post(`${resquest}/tDtfProject/getById`, params)
}

// 项目代办 --- 需求项目退回
export function handleReturn (taskid) {
	let params = { taskid: taskid }
	return http.post(`${resquest}/tDtfProject/thdb`, params)
}
export function handleReturnCS (params) {

	return http.post(`${resquest}/tDtfProject/designth`, params)
}

// 项目代办 --- 需求项目提交
export function handleSubt (taskid) {
	let params = { taskid: taskid }
	return http.post(`${resquest}/tDtfProject/tj`, params)
}
// 初设项目提交
export function handleSubtCS (params) {
	return http.post(`${resquest}/tDtfProject/designtj`, params)
}

// 项目代办 --- 需求项目获取馈线id
export function getKXId (params) {
	let paramss = { taskid: params }
	return http.post(`${resquest}/tDtfProject/treeKx/getById`, paramss)
}

// 评审 --- 需求项目保存馈线id
export function getPSKXId (params) {
	let paramss = { taskid: params }
	return http.post(`${resquest}/tDtfProject/tree`, paramss)
}


// 评审 --- 需求项目通过
export function handlePSSubt (params) {
	let paramss = { taskid: params }
	return http.post(`${resquest}/tDtfProject/tg`, paramss)
}

// 评审 --- 需求项目通过时传中台
export function handlePSSubtZT (params) {
	// 
	return http.post(`${resquest}/reportAllProcess/putNoticeToQgc`, params)
}

// 评审 --- 需求项目退回
export function handlePSReturn (params) {
	let paramss = { taskid: params }
	return http.post(`${resquest}/tDtfProject/th`, paramss)
}

// 评审 --- 需求项目编辑查询
export function getPSEdit (params) {
	let paramss = { taskid: params }
	return http.post(`${resquest}/tprojectAuditreView/getById`, paramss)
}

// 评审 --- 需求项目编辑保存
export function getPSSave (params) {

	// params = JSON.parse(JSON.stringify(params))
	console.log(params)
	return http.postFormData(`${resquest}/tprojectAuditreView/save`, params)
}

// 结算申报 -- 申报(退料,出库,移入,移除)
export function getSB (params) {
	// 
	return http.post(`${resquest}/tQd/queryQd`, params)
}

// 结算申报 -- 申报-删除
export function getDet (params) {
	let paramss = { taskid: params }
	return http.post(`${resquest}/tQd/sc`, paramss)
}

// 结算申报 -- 申报签证
export function getVisa (params) {
	// 
	return http.post(`${resquest}/tQz/`, params)
}

// 结算申报 -- 申报签证
export function getChange (params) {
	// 
	return http.post(`${resquest}/tSjbg/`, params)
}

// 结算申报 -- 申报签证-删除
export function VisaDet (params) {
	let paramss = { taskid: params }
	return http.post(`${resquest}/tQz/sc`, paramss)
}

// 结算申报 -- 申报设计变更-删除
export function ChangeDet (params) {
	let paramss = { taskid: params }
	return http.post(`${resquest}/tSjbg/sc`, paramss)
}

// 结算申报 -- 申报获取分包费用
export function getFBCost (params) {
	let param = { id: params }
	return http.post(`${resquest}/tFbfy/`, param)
}
// 结算申报 -- 申报分包费用保存
export function FBCostSave (params) {
	// 
	return http.post(`${resquest}/tFbfy/save`, params)
}
// 结算申报 -- 申报 获取附件
export function getAnnex (params) {
	// 
	return http.post(`${resquest}/tFile/`, params)
}

// 结算申报 -- 申报 获取删除
export function AnnexDet (params) {
	let paramss = { taskid: params }
	return http.post(`${resquest}/tFile/sc`, paramss)
}

// 结算申报 -- 申报 获取概算信息
export function getGSInformation (params) {
	let paramss = { taskid: params }
	return http.post(`${resquest}/tGais/getById`, paramss)
}

// 结算申报 -- 申报 保存概算信息
export function GSSave (params) {
	// 
	return http.post(`${resquest}/tGais/save`, params)
}

// 结算申报 -- 申报 获取结算信息
export function getJSInformation (params) {
	let paramss = { taskid: params }
	return http.post(`${resquest}/tProjectSettlement/getById`, paramss)
}

// 结算申报 -- 申报 提交
export function BtnSubmint (params) {
	let paramss = { taskid: params }
	return http.post(`${resquest}/tDtfProject/tj_js`, paramss)
}

// 结算审核 -- 审核 退回
export function SHReturn (params) {
	let paramss = { taskid: params }
	return http.post(`${resquest}/tDtfProject/jsth`, paramss)
}
// 结算审核 -- 审核 校验提交
export function SHCheck (params) {
	let paramss = { taskid: params }
	return http.post(`${resquest}/tDtfProject/tjyz`, paramss)
}

// 结算审核 -- 审核 通过
export function SHSubmint (params) {
	let paramss = { taskid: params }
	return http.post(`${resquest}/tDtfProject/jstj`, paramss)
}

// 结算审核 -- 审核 通过提交中台
export function SHSubmintZT (params) {
	
	return http.post(`${resquest}/dataTransfer/projectEndMsg`, params)
}
// 结算书导入
export function importSettlement (params) {
	return http.postFile(`${resquest}/tDtfProject/importSettlement`, params)
}
// 结算阶段提交
export function jssbtj (params) {
	return http.post(`${resquest}/tDtfProject/jssbtj`, params)
}
// 结算阶段退回
export function jssbth (params) {
	return http.post(`${resquest}/tDtfProject/jssbth`, params)
}

// 项目汇总 -- 获取项目汇总三率物料
export function getSLWL (params) {
	
	return http.post(`${resquest}/tDtfProject/selMaterial`, params)
}

// 项目汇总 -- 获取项目汇总三率设备
export function getSLSB (params) {
	
	return http.post(`${resquest}/tDtfProject/selMaterials`, params)
}

// 项目汇总 -- 获取项目汇总主设备
export function getZSB (params) {
	
	return http.post(`${resquest}/tZysb/`, params)
}

// 项目汇总 -- 获取项目汇总三率典设图元
export function getDSTY (params) {
	
	return http.post(`${resquest}/tDtfProject/selTY`, params)
}

// 项目汇总 -- 获取项目汇总三率典设图纸
export function getDSTZ (params) {
	
	return http.post(`${resquest}/tDtfProject/selTZ`, params)
}

// 投资规模 -- 获取列表
export function getTZGMList (params) {
	
	return http.post(`${resquest}/tGmtz/`, params)
}

// 投资规模 -- 获取列表
export function getTZGMList2 (params) {
	
	return http.post(`${resquest}/tGmtzP/`, params)
}

// 结算统计 -- 获取列表
export function getTJList (params) {
	// 
	return http.getObtain(`${resquest}/tJstj/queryTjstj`, params)
}

// 批次
export function getPCSel (params) {
	console.log(params, '批次')
	return http.post(`${resquest}/tDtfProject/selBatch`, params)
}

// 投资规模饼状图
export function getTZBING (params) {
	
	return http.post(`${resquest}/tXmhz/selXmlx`, params)
}

// 投资规模饼状图
export function getURLSJ (params) {
	// 
	let paramss = { taskid: params }
	return http.post(`${resquest}/tDtfProject/qdsjrj`, paramss)
}

// 馈线ID
export function getKXID (params) {
	let paramss = { taskid: params }
	return http.downloadPost(`${resquest}/tDtfProject/conversionPdf/`, paramss)
}
// 馈线ID
export function getKXID2 (params) {
	let paramss = { path: params }
	return http.postFile(`${resquest}/tDtfProject/imgDisplayDesign`, paramss)
}

// 角色管理
export function getRoleList (params) {
	return http.post(`${resquest}/t-dtf-roleinfo/list`)
}

// 查询角色
export function getuserInfo (params) {
	let nwparams = { user: params }
	return http.getObtain(`${resquest}/tDtfUserRoles/getUserid`, nwparams)
	// return http.post(`${resquest}/tDtfUserRoles/getUserid?user=` + '3244df0d794b442c91c869fdf7228a40')
}

// 角色管理删除
export function getRoleDel (params) {
	let nwparams = { taskid: params }
	return http.post(`${resquest}/t-dtf-roleinfo/del`, nwparams)
}

// 角色管理删除
export function uadateProstate (params) {
	console.log('params', params)
	let nwparams = { taskid: params }
	return http.post(`${resquest}/commitData/hq`, nwparams)
}

// 角色管理查询tree
export function getRoleTreeList (params) {
	return http.post(`${resquest}/t-dtf-permission/search`, params)
}

// 资料归集
export function getZLList (params) {
	
	return http.post(`${resquest}/tCggj/`, params)
}

// 勘察入库 --- 查看
export function getCK (params) {
	
	return http.post(`${resquest}/txckc/`, params)
}

// 勘察入库 --- 查看
export function submitDeclare (params) {
	console.log('params', params)
	let nwparams = { taskid: params }
	return http.post(`${resquest}/tDtfProject/tj_sbjs`, nwparams)
}
// 在线设计左侧深度菜单
export function leftMenu (params) {
	params = { id: params }
	console.log('params', params)
	return http.post(`${resquest}/TDtfSystemInfo/system-info`, params)
}
// 地图页接口改start
export function searchparms (params) {
	console.log('params', params)
	return http.get(`${resquest}/s-basic-parm/search/` + params)
}
// 地图页打点
export function addPoint (params) {
	console.log('params', params)
	return http.post(`${resquest}/t-dtf-app-point/addPoint`, params)
}
// 地图页接口改end

//在线设计头部导航
export function topMenu () {
	return http.post(`${resquest}/TDtfSystemInfo/design-menu`)
}
// 旧版预览设备材料清单的接口
export function selectExcelFile (params) {
	let nwparams = { filename: params }
	return http.post(`${resquest}/tdtfexcelfile/selectExcelFile`, nwparams)
}
// 新版预览设备材料清单的接口
export function wacthSbExcelFile (params) {
	let nwparams = { id: params }
	return http.postFile(`${resquest}/mapModel/checkOnlineFile`, nwparams)
}
// 在线设计获取主要设备材料清单
export function getMeterilsData (params) {
	let nwparams = { taskid: params }
	return http.post(`${resquest}/mapModel/generateBillMaterials`, nwparams)
}
// 在线设计主要设备材料清单导出材料清单
export function saveMeterilsData (params) {
	let nwparams = { taskid: params }
	return http.post(`${resquest}/mapModel/saveBillMaterials`, nwparams)
}
export function updateIsShow (params) {
	console.log(params, "更新树结构")
	let nwparams = { filename: params }
	return http.post(`${resquest}/tdtfexcelfile/updateIsShow`, nwparams)
}
export function deleteTreeNode (params) {
	console.log(params, "删除节点")
	return http.post(`${resquest}/tdtfexcelfile/updateIsShows?fileName=` + params)
}
// 获取现状数据
export function gethandleObtain (params) {
	let nwparams = { taskid: params }
	console.log(nwparams, "jjj")
	return http.getObtain(`${resquest}/spltkx/obtainCurrentSituationFeeeder`, nwparams)
}
export function coordinateTransformation (params) {
	let nwparams = { taskid: params }
	return http.post(`${resquest}/mapModel/coordinateTransformation`, nwparams)
}
// 合理性分析查看接口
export function rationalan (params) {
	
	console.log(params, "合理性分析查看")
	return http.getObtain(`${resquest}/tDtfProJxHg/queryWtq`, params)
}
// 标准化分析查看接口
export function standardization (params) {
	console.log(params, "标准化分析查看")
	return http.getObtain(`${resquest}/tDtfProJxBz/querytDtfProJxBz`, params)
}
export function feeederObtainCurrent (params) {
	console.log(params, "获取馈线信息")
	let nwparams = { taskid: params }
	return http.getObtain(`${resquest}/spltkx/obtainCurrentSituationFeeeder`, nwparams)
}
export function importFileJs (params) {
	return http.postFile(`${resquest}/tDtfProject/importJs`, params)
}
// 附件上传

export function fjImportFileJs (params) {
	return http.postFile(`${resquest}/tDtfProject/importfj`, params)
}
export function fjDownFileJs (params) {
	return http.downloadPost(`${resquest}/tDtfProject/downFile3`, params)
}

// 评审意见导入
export function importFilePsyjdr (params) {
	return http.postFile(`${resquest}/tDtfProject/imports`, params)
}
// 初设批复上传
export function importFileCspf (params) {
	return http.postFile(`${resquest}/tDtfProject/importpfwj`, params)
}
// 可研批复上传
export function importFileKypf (params) {
	return http.postFile(`${resquest}/tDtfProject/importkypfwj`, params)
}
// 可研阶段估算书上传
export function importFileKyGss (params) {
	return http.postFile(`${resquest}/tDtfProject/saveGss`, params)
}
//勘察报告，PDF签字盖章版上传
export function importFileKcbg (params) {
	return http.postFile(`${resquest}/tDtfProject/importFj1`, params)
}
// 勘察设计费结算计算说明上传
export function importFileKcbgJS (params) {
	return http.postFile(`${resquest}/tDtfProject/importFj2`, params)
}
export function importFileSjbg (params) {
	return http.postFile(`${resquest}/tDtfProject/importFj3`, params)
}
// 上传需求说明书
export function importFileXqsms (params) {
	return http.postFile(`${resquest}/tDtfProject/importXqSms`, params)
}
export function importFileXmmx (params) {
	return http.postFile(`${resquest}/tDtfProject/importFj4`, params)
}
export function importFileProjectInfo (params) {
	return http.postFile(`${resquest}/tDtfProject/importProjectInfo`, params)
}
export function downFileWl (params) {
	return http.downloadPost(`${resquest}/tDtfProject/downFilewl`, params)
}
// 下载
export function downFileXz1 (params) {
	return http.downloadPost(`${resquest}/tDtfProject/downFile/`, params)
}
export function downFileXz2 (params) {
	return http.downloadPost(`${resquest}/tDtfProject/downFile1/`, params)
}
export function downFileXz3 (params) {
	return http.downloadPost(`${resquest}/tDtfProject/downFile2/`, params)
}
export function saveShForm (params) {
	return http.post(`${resquest}/tProjectSettlement/save`, params)
}
// 申报阶段弹窗保存接口
export function saveSBForm (params) {
	return http.post(`${resquest}/tProjectSettlement/save`, params)
}
// 用户查询页面
export function queryUserList (params) {
	return http.post(`${resquest}/t-dtf-userinfo/list`, params)
}
// 用户接口
export function selectUserList (params) {
	return http.post(`${resquest}/t-dtf-roleinfo/list`, params)
}
export function editUserList (params) {
	return http.post(`${resquest}/t-dtf-userinfo/edit`, params)
}

export function addRoleinfoSave (params) {
	return http.post(`${resquest}/t-dtf-roleinfo/add`, params)
}
export function editRoleinfoSave (params) {
	return http.post(`${resquest}/t-dtf-roleinfo/edit`, params)
}
export function saveRole (params) {
	return http.post(`${resquest}/t-dtf-roleinfo/editPermission`, params)
}
export function operation (params) {
	return http.post(`${resquest}/dataOperation/operation`, params)
}
export function uploadAcheievement (params) {
	return http.postFile(`${resquest}/designAchievement/uploadAcheievement`, params)
}
export function downLoadTemplate (params) {
	return http.downloadPost(`${resquest}/tDtfProject/downXqKy`, params)
}
// 删除项目
export function removeProject (params) {
	return http.post(`${resquest}/tDtfProject/remove`, params)
}
// 上传非需求工程量
export function importOtherReqInfo (params) {
	return http.postFile(`${resquest}/tDtfProject/importOtherReqInfo`, params)
}
//上传需求工程量
export function importReqInfo (params) {
	return http.postFile(`${resquest}/tDtfProject/importReqInfo`, params)
}
//查看需求的单体工程量
export function getReqInfo (params) {
	return http.post(`${resquest}/tDtfProject/getReqInfo`, params)
}
//查看其他阶段单体工程量
export function getOtherReqInfo (params) {
	return http.post(`${resquest}/tDtfProject/getOtherReqInfo`, params)
}
//工程量模板下载
export function reqinfoDown (params) {
	return http.downloadPost(`${resquest}/tDtfProject/downFileGcl`, params)
}
// 上传成果文件
export function importDesign (params) {
	return http.postFile(`${resquest}/tDtfProject/importDesign`, params)
}
//删除成果文件
export function deleteFileTree (params) {
	return http.post(`${resquest}/tDtfProject/deleteFileTree`, params)
}

export {
	resquest
}
