<template>
  <div class="index flex-c h100">
     <div style="height:100vh;width:100vw;background:#fff;  position: absolute;left:0;top:0;z-index:99" v-show="!showPage"></div>
    <!-- 标题 -->
    <div class="main-header">管理页面</div>
    <div class="" style="width: 100%; height: 1px; border-bottom: 1px solid #eee"></div>
    <!-- 表单 -->
    <div class="query-form-box">
      <el-form ref="form" :model="form" :inline="true" label-width="80px">
        <el-row>
          <el-col :span="5">
            <el-form-item label="表名:" prop="pcnd">
              <el-input v-model="form.table" clearable placeholder="请输入表名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="操作方式:" prop="batch">
              <el-select v-model="form.type" clearable placeholder="请选择">
                <el-option v-for="item in batchOptions" :key="item.value" :label="item.name" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="条件:" prop="gcjd">
              <el-input v-model="filtersValue" clearable placeholder="请输入字段多字段用,号隔开" type="textarea" :rows="2"
                style="width: 340px;"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="lastSearchArea">
            <el-form-item label="返回字段:" prop="fields">
              <el-input v-model="form.fields" clearable placeholder="请输入需要返回的字段"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-show="isShowExpand" class="lastSearchArea">
            <el-button class="blue-btn" @click="query()">执行</el-button>
            <el-button @click="clearForm()">重置</el-button>
          </el-col>

        </el-row>
      </el-form>
    </div>
    <div class="driver" style="margin-top: 14px"></div>
    <div style="padding:0 16px">
      <el-button @click="checkedit('u')" type="parimary" plain size="small" icon="el-icon-edit">批量修改</el-button>
      <el-button @click="deleteData()" type="parimary" plain size="small" icon="el-icon-edit">批量删除</el-button>
      <el-button @click="checkedit('i')" type="parimary" plain size="small" icon="el-icon-edit">新增</el-button>
    </div>

    <div class="driver" style="margin-top: 14px"></div>
    <!-- 项目列表 -->
    <div class="tablesArea">
      <el-table :data="tableData" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }" :height="tableHeight"
        highlight-current-row @selection-change="handleSelectionChange" style="width: 98%; margin: 0px 16px 0 16px"
        v-if="refes">
        <el-table-column type="selection" align="center" :resizable="false" width="60px"></el-table-column>
        <el-table-column type="index" label="序号" align="center" :index="indexMethod" :resizable="false" width="100px">
        </el-table-column>
        <el-table-column align="left" v-for="(head, index) in tableHeader" :key="index" :prop="head" :label="head"
          min-width="140px">
        </el-table-column>
        <el-table-column label="操作" align="center" width="140px">
          <template slot-scope="scope">
            <span class="directiveHandle">
              <div :class="'el-buttonStyle'">
                <span @click="handleEdit(scope.row)"> 编辑 </span>
              </div>
              <span class="el-buttonDriver">|</span>

              <div :class="'el-buttonStyle'">
                <span @click="handleReturn(scope.row)"> 删除 </span>
              </div>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination style="margin: 10px" background :current-page="form.page" :page-sizes="pageSize"
      :page-size="form.perPage" layout="total, sizes, prev, pager, next" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" :total="total">
    </el-pagination>

    <!-- 编辑数据 -->
    <el-dialog title="编辑数据" :visible.sync="editDialog" width="44%" center>
      <div style="max-height:60vh;overflow-y: auto;"><el-form ref="formEdit" :model="formEdit" :inline="true">
          <el-form-item v-for="(head, index) in tableHeader" :key="index" :label="head + ':'" :prop="head"
            label-width="150px">
            <el-input v-model="formEdit[head]" clearable></el-input>
          </el-form-item>
        </el-form></div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialog = false">取 消</el-button>
        <el-button class="blue-btn" @click="editDialogQuery">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import {
  getCounty,
  resquest,
  handleReturnCS,
  getuserInfo,

  operation

} from "@/api/api"
import FeederDialog from "@/views/projectAgent/CScomonents/tree"
import ResultsDialog from "@/views/projectAgent/CScomonents/table"
import { progressMixins } from "@/utils/progressMixins.js"
export default {
  components: {
    FeederDialog,
    ResultsDialog,
  },
  mixins: [progressMixins],
  data () {
    return {
      refes: true,
      tableHeader: [''],
      checkEditList: [],
      addType: '',
      flag: true,
      isShowExpand: true,
      filtersValue: "",
      form: {
        //表单参数
        fields: "", //指定返回的字段
        type: "", //"select"
        table: "", //表名
        page: 1,
        perPage: 20,
        filters: [
          // {
          //   fieldName: "",//过滤属性
          //   compare: "",//过滤条件
          //   fieldValue: "",//过滤属性值
          // },
        ],
        orderBy: "name desc,id asc",
      },
      formFeeder: {
        name: "",
        ID: "",
      },
      jsonformat: {
        expandDepth: 2,
        copyable: true,
      },

      currentPagefeeder: 1,
      widthPro: 100,
      xgsOptions: [], //县公司下拉数据
      userId: "",
      fullscreenLoading: false,
      batchOptionsEdit: [],
      dsgsOptions: [
        {
          name: "初步设计",
          value: "cbsj",
        },
        {
          name: "需求编制",
          value: "xqbz",
        },
        {
          name: "结算阶段",
          value: "jsjd",
        },
        {
          name: "可研设计",
          value: "kysj",
        },
      ], //城市下拉
      batchOptions: [
        {
          name: "查询",
          value: "s",
        }
      ], //批次名次下拉数据
      pageSize: [10, 20, 50, 100], //分页页数
      total: 0, //总共页数
      tableData: [],
      formEdit: {

      },
      url: resquest,
      addProjectInfo: {
        file: [], // 文件上传
        id: "",
      },
      tableHeight: 0,
      cuttentProCode: "", //馈线ID
      cuttentresult: "", //成果分析
      editDialog: false, //编辑
      obtainDialog: false, //现状数据
      feederDialog: false, //馈线ID
      resultDialog: false, //成果分析
      requirementsDialog: false, //设计成果导入
      timer: null,
      isProgressExecuting: false,
      isComplete: true, //判断接口请求是否完成
      isErr: false, //判断是否正常返回
      taskID: "",
      interval: null,
      feederData: [],
      detailDate: new Date().getFullYear(),
      userType: "", // 1省 2市 3县
      showPage:false
    }
  },

  mounted () {
    this.setTablesHeight()
    const token = sessionStorage.getItem("bhneToken")
     const pageType = sessionStorage.getItem('bhnePageType')
    getuserInfo(token).then((res) => {
      if (Object.keys(res.data.result).length == 0) {
        this.$message.error("获取用户信息失败，请重新进入页面")
      }
        let menuTypes = res.data.result.zmenu.split(',')

      if (menuTypes.includes(pageType)) {
        console.log("允许进入");
        this.showPage=true
      }else{
          console.log("无权限")
         this.$message.warning("无权限")
         this.showPage=false
      }
      this.userType = res.data.result.rank
      this.userId = res.data.result.USERID
      this.getList()
    })
    const that = this
    window.onresize = function () {
      that.setTablesHeight()
    }
  },
  methods: {
    // 模板导入

    beforeDesignUpload (file) {
      let fileName = file.name
      let pos = fileName.lastIndexOf(".")
      let lastName = fileName.substring(pos, fileName.length)
      const ext = lastName.toLowerCase()
      // if (ext !== '.doc' && ext !== '.docx' && ext != '.xls' && ext != '.xlsx' && ext != '.txt' && ext !=
      //   '.bmp' && ext != '.word') {
      //   this.$message.error('文件必须为.doc')
      //   return
      // }
    },
    changeExpand () {
      this.isShowExpand = !this.isShowExpand
      this.setTablesHeight()
    },
    setTablesHeight () {
      this.$nextTick(() => {
        const tablesAreaHeight = document
          .getElementsByClassName("driver")[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        this.tableHeight = windowsAreaHeight - tablesAreaHeight - 150
      })
    },



    handleSizeChangefeeder (val) {
      console.log(`每页 ${val} 条`)
    },
    handleCurrentChangefeeder (val) {
      console.log(`当前页: ${val}`)
    },
    // 获取列表
    getList () {
      // t_dtf_project
      // task_id = e4d9244ae42a4040b7935434e663972e
      //  console.log(this.form,"form,");
      this.form.filters = []
      let regex = /^(\w+\s+[^ ]+\s+.+)(,\s*\w+\s+[^ ]+\s+.+)*$/
      // if (!regex.test(this.filtersValue))
      //   return this.$message.warning("请检查输入的条件")
      if (!this.filtersValue == '') {
        this.filtersValue.split(",").forEach((part) => {
          const match = part.match(/(\w+)\s+([^ ]+)\s+(.+)/)
          this.form.filters.push({
            fieldName: match[1].trim(), //过滤属性
            compare: match[2].trim(), //过滤条件
            fieldValue: match[3].trim(), //过滤属性值
          })
        })
      }

      operation(this.form).then(res => {
        console.log(res.data.result[0])
        if (res.data.result.length == 0) {
          this.tableHeader = ['']
        } else {
          this.tableHeader = Object.keys(res.data.result[0])
        }
        this.tableData = res.data.result
        this.total = res.data.count
        this.refes = true
       this.$message({
              type: 'success',
              message: '查询成功'
            })
      })
    },


    // table列表序号索引
    indexMethod (index) {
      return (this.form.page - 1) * this.form.perPage + index + 1
    },
    // 查询
    query () {
      this.refes = false
      this.getList()
    },
    // 删除
    deleteData () {
      let params = {
        //表单参数
        type: "d", //"删除"
        table: this.form.table, //表名
        filters: this.form.filters,
      }
      this.$confirm('确认删除查询的数据吗, 是否继续?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        operation(params).then(res => {
          console.log(res, "删除")
          if (res.data.message == "success") {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }

        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })

    },

    checkedit (type) {
      // if (this.checkEditList.length <= 0) return this.$message.warning("请选中数据")
      this.formEdit = {}
      this.addType = type

      this.editDialog = true
    },
    // 重置
    clearForm () {
      this.filtersValue = ""
      this.form = {
        //表单参数
        fields: "", //指定返回的字段
        type: "", //"select"
        table: "", //表名
        page: 1,
        perPage: 20,
        filters: [
          // {
          //   fieldName: "",//过滤属性
          //   compare: "",//过滤条件
          //   fieldValue: "",//过滤属性值
          // },
        ],

      }
      this.getList()
    },
    // 复选框
    handleSelectionChange (row) {
      console.log(row)
      this.checkEditList = row
    },
    handleCurrentChange (row) {
      this.form.page = row
      this.getList()
    },
    handleSizeChange (row) {
      this.form.perPage = row
      this.getList()
    },

    // 退回
    handleReturn (row) {
      this.$confirm("是否确定退回?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: "warning",
      }).then(() => {
        let params = {
          rank: this.userType,
          taskid: row.taskID,
        }

      })
    },
    // table 编辑按钮
    handleEdit (row) {
      console.log(row)
      this.formEdit = row
      // this.editDialog = true
    },
    // 编辑弹框确认按钮
    editDialogQuery () {

      let params = {
        //表单参数
        fields: [], //指定返回的字段
        type: this.addType, //"select"
        table: this.form.table, //表名

        filters: this.form.filters,

      }
      console.log(this.formEdit)

      Object.keys(this.formEdit).forEach((value, index) => {
        params.fields.push({ fieldName: value, fieldValue: Object.values(this.formEdit)[index] })
      })
      console.log(params)

      operation(params).then(res => {
        console.log(res, this.addType)
        if (res.data.message == 'success') {
          this.getList()
          this.formEdit = {}
           this.$message({
              type: 'success',
              message: '修改成功!'
            })
        }
      })
      this.editDialog = false
    },
  },
}
</script>

<style lang="scss" scoped>
.directiveHandle {
  justify-content: center;
}

.progresswrapper {
  width: 90%;
  height: 15px;
  border-radius: 10px;
  color: white;
  line-height: 15px;
  position: relative;
}

.pro {
  height: 100%;
  border-radius: 10px;
  background: #00b83f;
  background-image: linear-gradient(45deg,
      rgba(255, 255, 255, 0.15) 25%,
      transparent 25%,
      transparent 50%,
      rgba(255, 255, 255, 0.15) 50%,
      rgba(255, 255, 255, 0.15) 75%,
      transparent 75%,
      transparent);
  background-size: 40px 40px;
  animation: progressbar 2s linear infinite;
}

@keyframes progressbar {
  0% {
    background-position: 40px 0;
  }

  100% {
    background-position: 0 0;
  }
}

.expandArea {
  margin-left: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #606266;
  cursor: pointer;

  img {
    margin-left: 2px;
  }
}

.el-dialog__body {
  padding: 24px;
}
::v-deep .el-dialog__footer {
  padding: 16px 24px !important;
}
</style>
