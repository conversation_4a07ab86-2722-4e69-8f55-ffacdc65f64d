<template>
  <div class="app-container" style="width: 100%">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane lazy label="架空线路参数设置" name="1">
        <div class="">
          <div class="item-content">
            <div class="content-title">10kV</div>
          </div>
          <div class="item-content">
            <el-form
              ref="heightVoltageTowerAndLine"
              :label-position="labelPosition"
              label-width="120px"
            >
              <el-form-item label="常用水泥杆">
                <el-select
                  multiple
                  v-model="towerHeights"
                  class="width90"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in heightVoltageTowerAndLine.towerData"
                    :key="item.MaterialsProjectID"
                    :label="item.Spec"
                    :value="item.MaterialsProjectID"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="常用导线">
                <el-select
                  multiple
                  v-model="lineHeights"
                  class="width90"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in heightVoltageTowerAndLine.lineData"
                    :key="item.ModuleID"
                    :label="item.ModuleName"
                    :value="item.ModuleID"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>

          <div class="item-content">
            <div class="content-title">低压</div>
          </div>
          <div class="item-content">
            <el-form
              ref="lowVoltageTowerAndLine"
              :label-position="labelPosition"
              label-width="120px"
            >
              <el-form-item label="常用水泥杆">
                <el-select
                  multiple
                  v-model="towerLows"
                  class="width90"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in lowVoltageTowerAndLine.towerData"
                    :key="item.MaterialsProjectID"
                    :label="item.Spec"
                    :value="item.MaterialsProjectID"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="常用导线">
                <el-select
                  multiple
                  v-model="lineLows"
                  class="width90"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in lowVoltageTowerAndLine.lineData"
                    :key="item.ModuleID"
                    :label="item.ModuleName"
                    :value="item.ModuleID"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>

          <div class="item-content">
            <div class="content-title">其他参数</div>
          </div>
          <div class="item-content">
            <el-form
              :model="otherSetting"
              :inline="true"
              :label-position="labelPosition"
              label-width="150px"
            >
              <el-form-item label="耐张段长度(m)">
                <el-input-number
                  v-model="otherSetting.NaiZhangMaxLength"
                  :controls="false"
                  :min="0"
                ></el-input-number>
              </el-form-item>
              <el-form-item label="塔间最大档距(m)">
                <el-input-number
                  v-model="otherSetting.TaMaxDistance"
                  :controls="false"
                  :min="0"
                ></el-input-number>
              </el-form-item>
              <el-form-item label="塔间最小档距(m)">
                <el-input-number
                  v-model="otherSetting.TaMinDistance"
                  :controls="false"
                  :min="0"
                ></el-input-number>
              </el-form-item>
            </el-form>
          </div>

          <div class="item-content red margintop10">
            注：系统给出均为默认参数，如需调整，请进行更改
          </div>
          <!-- <div class="item-content text-center">
            <el-button class="minWidth100" @click="jkSave">保存 </el-button>
            <el-button class="minWidth100" type="primary" @click="jkSubmit"
              >提交
            </el-button>
          </div> -->
        </div>
      </el-tab-pane>
      <el-tab-pane lazy label="电缆线路参数设置" name="2">
        <div class="">
          <div class="item-content">
            <div class="content-title">10kV</div>
          </div>
          <div class="item-content">
            <el-form
              ref="cableBindData"
              :label-position="labelPosition"
              label-width="120px"
            >
              <el-form-item label="常用电缆">
                <el-select
                  multiple
                  v-model="cableHeights"
                  class="width90"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in cableBindData.heightVoltageCable"
                    :key="item.ModuleID"
                    :label="item.ModuleName"
                    :value="item.ModuleID"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>

          <div class="item-content">
            <div class="content-title">低压</div>
          </div>
          <div class="item-content">
            <el-form
              ref="cableBindData"
              :label-position="labelPosition"
              label-width="120px"
            >
              <el-form-item label="常用电缆">
                <el-select
                  multiple
                  v-model="cableLows"
                  class="width90"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in cableBindData.lowVoltageCable"
                    :key="item.ModuleID"
                    :label="item.ModuleName"
                    :value="item.ModuleID"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>

          <div class="item-content">
            <div class="content-title">电缆井参数</div>
          </div>
          <div class="item-content">
            <div>
              <el-button type="primary" @click="addRowWell">增加 </el-button>
              <el-button @click="delRowWell">删除 </el-button>
            </div>
            <el-table
              ref="multipleTable"
              :data="wellData"
              :row-class-name="rowIndex"
              class="widthFull"
            >
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column
                label="最小数量"
                width="80"
                align="center"
                prop="MinQuantity"
              >
                <template slot-scope="scope">
                  <el-input-number
                    :controls="false"
                    :min="0"
                    v-model="scope.row.MinQuantity"
                    class="widthFull"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column
                label="最大数量"
                width="80"
                align="center"
                prop="MaxQuantity"
              >
                <template slot-scope="scope">
                  <el-input-number
                    :controls="false"
                    :min="0"
                    v-model="scope.row.MaxQuantity"
                    class="widthFull"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="直线" align="center" prop="ZhiXianWell">
                <template slot-scope="scope">
                  <el-select
                    v-model="scope.row.ZhiXianWell"
                    class="widthFull"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in wellBindData.ZhiXianWell"
                      :key="item.ModuleID"
                      :label="item.ModuleName"
                      :value="item.ModuleID"
                    >
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="转角" align="center" prop="ZhuanJiaoWell">
                <template slot-scope="scope">
                  <el-select
                    v-model="scope.row.ZhuanJiaoWell"
                    class="widthFull"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in wellBindData.ZhuanJiaoWell"
                      :key="item.ModuleID"
                      :label="item.ModuleName"
                      :value="item.ModuleID"
                    >
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="三通" align="center" prop="SanTongWell">
                <template slot-scope="scope">
                  <el-select
                    v-model="scope.row.SanTongWell"
                    class="widthFull"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in wellBindData.SanTongWell"
                      :key="item.ModuleID"
                      :label="item.ModuleName"
                      :value="item.ModuleID"
                    >
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="四通" align="center" prop="SiTongWell">
                <template slot-scope="scope">
                  <el-select
                    v-model="scope.row.SiTongWell"
                    class="widthFull"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in wellBindData.SiTongWell"
                      :key="item.ModuleID"
                      :label="item.ModuleName"
                      :value="item.ModuleID"
                    >
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="item-content red margintop10">
            注：系统给出均为默认参数，如需调整，请进行更改
          </div>
          <!-- <div class="item-content text-center">
            <el-button class="minWidth100" @click="cableSave">保存 </el-button>
            <el-button class="minWidth100" type="primary" @click="cableSubmit"
              >提交
            </el-button>
          </div> -->
        </div>
      </el-tab-pane>
      <!-- <el-tab-pane lazy label="标注设置" name="3">
                标注设置
            </el-tab-pane>
            <el-tab-pane lazy label="绘图设置" name="4">
                绘图设置
            </el-tab-pane> -->
      <el-tab-pane label="图签设置" name="5">
        <div class="item-content">
          <el-form label-width="120px" :model="form">
            <el-form-item label="图签组">
              <el-select
                v-model="form.currentsign"
                placeholder="请选择"
                @change="signChange"
              >
                <el-option
                  v-for="item in signs"
                  :key="item.Id"
                  :value="item.Id"
                  :label="item.Name"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="item-content">
          <div class="content-title">预览</div>
        </div>
        <div class="item-content">
          <DwgCanvas
            v-if="activeName === '5'"
            ref="DwgDrawing"
            v-bind:canvasDataBind="CanvasData"
            v-bind:height="signSize.height"
            v-bind:width="signSize.width"
          />
        </div>
        <div class="item-content red margintop10">
          注：系统给出均为默认参数，如需调整，请进行更改
        </div>
        <!-- <div class="item-content text-center">
          <el-button class="minWidth100" @click="cableSave">保存 </el-button>
          <el-button class="minWidth100" type="primary" @click="cableSubmit"
            >提交
          </el-button>
        </div> -->
      </el-tab-pane>
      <el-tab-pane lazy label="杆塔明细表设置" name="6">
        <div class="">
          <div class="item-content">
            <el-transfer
              v-model="towerExcelBindData.value"
              :data="towerExcelBindData.data"
              :titles="['备用列表', '已选列表']"
            >
            </el-transfer>
          </div>
          <div class="item-content red margintop10">
            注：系统给出均为默认参数，如需调整，请进行更改
          </div>
          <!-- <div class="item-content text-center">
            <el-button class="minWidth100" @click="cableSave">保存 </el-button>
            <el-button class="minWidth100" type="primary" @click="cableSubmit"
              >提交
            </el-button>
          </div> -->
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import DwgCanvas from '@/components/ThreeLoad/index.vue'
import {
  getSignDrawingData,
  getHeightVoltageTowerAndLine,
  getLowVoltageTowerAndLine,
  getCableBindData,
  getWellBindData,
} from './data-static/data'
export default {
  name: 'BasicParametersSetting',
  components: { DwgCanvas },
  data() {
    return {
      labelPosition: 'right',
      heightVoltageTowerAndLine: getHeightVoltageTowerAndLine(),
      lowVoltageTowerAndLine: getLowVoltageTowerAndLine(),
      otherSetting: {
        NaiZhangMaxLength: '500',
        TaMaxDistance: '80',
        TaMinDistance: '0',
      },
      cableBindData: getCableBindData(),
      wellData: [
        //工井配置
        {
          MinQuantity: 1,
          MaxQuantity: 16,
          ZhiXianWell: '453',
          ZhuanJiaoWell: '468',
          SanTongWell: '473',
          SiTongWell: '486',
        },
        {
          MinQuantity: 17,
          MaxQuantity: 32,
          ZhiXianWell: '452',
          ZhuanJiaoWell: '454',
          SanTongWell: '456',
          SiTongWell: '458',
        },
      ],
      wellBindData: getWellBindData(),
      towerExcelBindData: {
        data: [
          { key: 0, label: `杆塔描述`, disabled: false },
          { key: 1, label: `杆塔基础`, disabled: false },
          { key: 2, label: `绝缘子与金具`, disabled: false },
          { key: 3, label: `杆塔配电设备/附件`, disabled: false },
          { key: 4, label: `档距描述(m)`, disabled: false },
          { key: 5, label: `导线描述`, disabled: false },
          { key: 6, label: `水平转角`, disabled: false },
          { key: 7, label: `接地装置`, disabled: false },
          { key: 8, label: `横担`, disabled: false },
          { key: 9, label: `拉线装置`, disabled: false },
          { key: 10, label: `交叉跨越(处)`, disabled: false },
          { key: 11, label: `备注`, disabled: false },
        ],
        value: [1, 2, 3, 4, 5, 6, 7, 8, 9],
      },
      activeName: '1',
      form: {
        currentsign: '标准图签',
      },
      signs: [
        { Id: '标准图签', Name: '标准图签' },
        { Id: '无文字', Name: '无文字' },
      ],
      signSize: {
        width: '615px !important',
        height: '268px !important',
      },
      rules: {},
      towerHeights: [], //高压杆塔
      lineHeights: [], //高压导线
      towerLows: [], //低压杆塔
      lineLows: [], //低压导线
      cableHeights: [], //高压电缆
      cableLows: ['222', '223'], //低压电缆
      CanvasData: {},
    }
  },
  mounted() {
    this.CanvasData = getSignDrawingData(this.form.currentsign)
  },
  methods: {
    rowIndex({ row, rowIndex }) {
      //增加索引
      row.index = rowIndex
    },
    addRowWell() {
      //添加工井配置行
      const row = {
        MinQuantity: 0,
        MaxQuantity: 0,
        ZhiXianWell: '',
        ZhuanJiaoWell: '',
        SanTongWell: '',
        SiTongWell: '',
      }
      this.wellData.push(row)
    },
    delRowWell() {
      //删除工井配置行
      if (this.$refs.multipleTable.selection.length == 0) return
      for (let i = 0; i < this.$refs.multipleTable.selection.length; i++) {
        for (let r = 0; r < this.wellData.length; r++) {
          if (
            this.$refs.multipleTable.selection[i].index ==
            this.wellData[r].index
          )
            this.wellData.splice(r, 1)
        }
      }
    },
    cableSubmit() {
      //电缆参数提交
      console.log('高压电缆：' + this.cableHeights)
      console.log('低压电缆：' + this.cableLows)
      console.log('工井配置：' + this.wellData)
    },
    cableSave() {},
    jkSubmit() {
      //架空参数提交
    },
    jkSave() {},
    signChange(val) {
      this.CanvasData = getSignDrawingData(val)
      this.$refs.DwgDrawing.Draw(this.CanvasData)
    },
    handleClick(tab, event) {},
  },
}
</script>

<style scoped lang="scss">
@import '../../assets/styles/variables.scss';
.app-container {
  height: 100vh;
  width: 100vw;
  background-color: #fff;
  box-sizing: border-box;
  padding: 10px;
}
.widthFull {
  width: 100%;
}
.width90 {
  width: 90%;
}
.minWidth100 {
  min-width: 100px;
}
.red {
  color: $red;
}
.margintop10 {
  margin-top: 10px;
}
.content-title {
  position: relative;
  padding-left: 10px;
  color: #526ade;
}
.content-title::after {
  content: '';
  position: absolute;
  left: 0;
  top: 50%; /* 将竖杠放置在元素的垂直中心位置 */
  transform: translateY(-50%);
  width: 5px; /* 竖杠的宽度 */
  height: 17px; /* 竖杠的高度 */
  background-color: #526ade; /* 竖杠的颜色 */
  border-radius: 5px; /* 添加弧度 */
  content: '';
}
::v-deep.widthFull .cell {
  font-size: 14px !important;
}
.text-center {
  display: flex;
  height: 70px;
  align-items: center;
  justify-content: center;
}
</style>
