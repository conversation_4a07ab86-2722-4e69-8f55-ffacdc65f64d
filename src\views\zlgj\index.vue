<template>
  <!-- 资料归集 -->
  <div class="index flex-c h100">
    <div class="main-header">资料归集</div>
    <div
      class=""
      style="width: 100%; height: 1px; background-color: #eee"
    ></div>
    <div class="query-form-box">
      <el-form
        ref="form"
        :model="form"
        :inline="true"
        @submit.native.prevent
        label-width="120px"
      >
        <el-row>
          <el-col :span="5">
            <el-form-item label="批次年度:" prop="pcnd">
              <el-date-picker
                v-model="form.pcnd"
                clearable
                type="year"
                format="yyyy"
                value-format="yyyy"
                placeholder="选择日期"
                @change="NDPoint"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="批次名称:" prop="batch">
              <el-select v-model="form.batch" clearable placeholder="请选择">
                <el-option
                  v-for="item in batchOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="地市公司:" prop="dsgs">
              <el-select
                v-model="form.dsgs"
                clearable
                placeholder="请选择"
                @change="CityPoint"
              >
                <el-option
                  v-for="item in dsgsOptions"
                  :key="item.cityid"
                  :label="item.cityname"
                  :value="item.cityid"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="lastSearchArea">
            <el-form-item label="县公司:" prop="xgs">
              <el-select v-model="form.xgs" clearable placeholder="请选择">
                <el-option
                  v-for="item in xgsOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="项目名称:" prop="xmName">
              <el-input
                v-model="form.xmName"
                clearable
                placeholder="请输入项目名称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="项目管理编码:" prop="xmName">
              <el-input
                v-model="form.code"
                clearable
                placeholder="请输入项目管理编码"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" class="lastSearchArea">
            <el-button class="blue-btn" @click="query()" icon="el-icon-search"
              >查询</el-button
            >
            <el-button @click="clearForm()" icon="el-icon-refresh"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="driver"></div>
    <div class="tablesArea">
      <el-table
        :data="tableData"
        :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
        :height="tableHeight"
        style="width: 98%; margin: 0px 16px 0 16px"
        highlight-current-row
      >
        <el-table-column
          type="index"
          :index="indexMethod"
          label="序号"
          align="center"
          :resizable="false"
          width="60"
        >
        </el-table-column>
        <el-table-column prop="pcnd" label="年度" align="center">
        </el-table-column>
        <el-table-column
          prop="batchname"
          label="项目批次"
          width=""
          align="center"
        >
        </el-table-column>
        <el-table-column prop="xmmc" label="项目名称" width="400px" align="center">
        </el-table-column>
        <el-table-column prop="code" label="项目管理编码" width="150px" align="center">
        </el-table-column>
        <el-table-column prop="cityName" label="地市公司" width="130px" align="center">
        </el-table-column>
        <el-table-column prop="countyName" label="县公司" width="130px" align="center">
        </el-table-column>
        <el-table-column prop="zlgj" label="操作" width="" align="center">
          <template slot-scope="scope">
            <span class="directiveHandle">
							 <div class="el-buttonStyle">
                <span @click="operation(scope.row)">
                  资料归集
                </span>
              </div>
						</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      style="margin: 10px"
      background
      :current-page="form.pageIndex"
      :page-sizes="pageSize"
      :page-size="form.pageSize"
      layout="total, sizes, prev, pager, next"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      :total="total"
    >
    </el-pagination>
    <el-dialog
      title="结算资料归集"
      :visible.sync="visible"
      lock-scroll
      width="85%"
      top="5vh"
      center
      custom-class="previewClass"
    >
      <button class="closeBox el-dialog__headerbtn" @click="visible = false">
        <i class="el-dialog__close el-icon el-icon-close"></i>
      </button>
      <div style="height: 75vh">
        <jscgzs :ProCode="cuttentProCode"></jscgzs>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getZLList,
  getCity,
  getCounty,
  getPCSel,
  getuserInfo
} from '@/api/api'
import jscgzs from '@/views/jscgzs/index'

export default {
  name: 'zlgj',
  components: {
    jscgzs
  },
  data () {
    return {
      tableHeight: 0,
      form: {
        batch: '',
        pcnd: new Date().getFullYear() + '',
        dsgs: '',
        xgs: '',
        xmName: '',
        stageType: "5",
        dwmc: '',
        code: '',
        pageIndex: 1,
        pageSize: 10,
      },
      pageSize: [10, 20, 50, 100],
      dsgsOptions: [],
      batchOptions: [],
      xgsOptions: [],
      total: 0,
      visible: false,
      cuttentProCode: '',
      tableData: []
    }
  },
  mounted () {
    this.NDPoint()
    this.setTablesHeight()
    const token = sessionStorage.getItem('bhneToken')
    getuserInfo(token).then((res) => {
      this.form.csmc = res.data.result.USERID
      this.form.dwmc = res.data.result.DWMC
      this.getList()
    })
    const that = this
    window.onresize = function () {
      that.setTablesHeight()
    }
  },
  methods: {
    setTablesHeight () {
      this.$nextTick(() => {
        const tablesAreaHeight = document.getElementsByClassName('driver')[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        this.tableHeight = windowsAreaHeight - tablesAreaHeight - 80
      })
    },
    getList () {
      getZLList(this.form)
        .then((res) => {
          this.tableData = res.data.result
          this.total = res.data.count
        })
        .catch(() => { })
      if (this.dsgsOptions.length === 0) {
        // 获取城市下拉
        const params = {
          optId: this.form.dwmc,
        }
        getCity(params)
          .then((res) => {
            this.dsgsOptions = res.data.result
          })
          .catch(() => { })
      }
    },
    // 城市点击获取县下拉
    CityPoint (val) {
      const params = {
        optId: this.form.dwmc,
        dwmc: val
      }
      getCounty(params)
        .then((res) => {
          this.xgsOptions = res.data.result
          this.xgsOptions.unshift({
            id: "100",
            name: "市公司项目",
          })
        })
        .catch(() => { })
    },
    NDPoint () {
      // 获取批次名词
      const param = {
        optId: this.form.pcnd,
      }
      getPCSel(param)
        .then((res) => {
          console.log(res)
          this.batchOptions = res.data.result
        })
        .catch(() => { })
    },
    // table列表序号索引
    indexMethod (index) {
      return (this.form.pageIndex - 1) * 10 + index + 1
    },
    // ↓在methods里面(窗体大小改变计算表格高度)
    // getHeight() {
    // 	this.tableHeight = (window.innerHeight - 320)
    // },
    handleCurrentChange (val) {
      this.form.pageIndex = val
      this.getList()
    },
    handleSizeChange (val) {
      this.form.pageSize = val
      this.getList()
    },
    // 查询
    query () {
      this.getList()
    },
    clearForm () {
      this.form = {
        batch: '',
        pcnd: '',
        dsgs: '',
        xgs: '',
        xmName: '',
        stageType: "5",
        dwmc: this.form.dwmc,
        code: '',
        pageIndex: 1,
        pageSize: 10,
      }
      this.getList()
    },
    // 斑马纹效果
    xxxTableRowClassName ({
      row,
      rowIndex
    }) {
      if (rowIndex % 2 == 0) {
        return ''
      } else {
        return 'statistics-warning-row'
      }
    },

    operation (row) {
      this.cuttentProCode = row.xmglbm
      this.visible = true
    }
  }
}
</script>
<style lang="scss">
.previewDialog {
  .el-dialog__header {
    display: none !important;
  }

  .dj-dialog-content {
    padding: 0;
    overflow: unset;
  }
}
::v-deep .el-dialog__body {
  padding: 24px !important;
}
::v-deep .el-dialog__footer {
  padding: 16px 24px !important;
}
</style>
