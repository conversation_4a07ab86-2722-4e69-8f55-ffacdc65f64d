<template>
  <div id="">
    <div id="">
      <el-row>
        <el-col :span="4">
          <div class="TreeBorder">
            <el-tree
              :data="data"
              :props="defaultProps"
              show-checkbox
              node-key="id"
              default-expand-all
              :render-content="renderContent"
              @check-change="handleCheckChange"
              @node-click="handleNodeCgxxClick"
            >
            </el-tree>
          </div>
        </el-col>
        <el-col :span="20">
          <div class="TreeBorder" v-loading="loading">
            <iframe :src="browseUrl" width="100%" height="100%"></iframe>
          </div>
        </el-col>
        <el-col :span="24">
          <div class="">
            <span
              slot="footer"
              class="dialog-footer"
              style="float: right; margin: 10px 10px 10px 0px"
            >
              <el-button class="blue-btn" @click="feederDialogQuery"
                >保 存</el-button
              >
            </span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { getKXId, SaveKXId, resquest, getKXID, getKXID2 } from '@/api/api'
import axios from 'axios'
export default {
  components: {},
  props: {
    ProCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      url: resquest,
      loading: true,
      data: [],
      defaultProps: {
        children: 'childNodes',
        label: 'text',
      },
      list: [],
      browseUrl: '',
    }
  },
  created() {
    this.getHeight()
    this.KXId()
    window.addEventListener('resize', this.getHeight)
  },
  methods: {
    KXId() {
      getKXId(this.ProCode)
        .then((res) => {
          console.log(res)
          this.data = res.data.result
        })
        .catch(() => {})
    },
    handleCheckChange(data) {
      if (data.expanded != true) {
        this.list.push(data.id)
      }
    },
    handleNodeCgxxClick(data) {
      getKXID2(data.imageUrl).then((res) => {
        let binaryData = []
        binaryData.push(res.data)
        const openUrl = window.URL.createObjectURL(new Blob(binaryData))
        this.browseUrl =
          window.wgParameter.pdfsUrls + '?file=' + encodeURIComponent(openUrl)
        this.loading = false
      })
    },
    feederDialogQuery() {
      if (this.list.length > 0) {
        this.list = Array.from(new Set(this.list))
        let kxid = this.list.join(',')
        axios
          .post(
            this.url +
              '/tDtfProject/saveSvg?id=' +
              this.ProCode +
              '&kxid=' +
              kxid
          )
          .then((res) => {
            if (res.data.message == 'success') {
              this.$message({
                message: '提交成功',
                type: 'success',
              })
            } else {
              this.$message.error('提交失败!')
            }
          })
      } else {
        this.$message.error('请勾选提交的项目')
      }
    },
    // ↓在methods里面(窗体大小改变计算表格高度)
    getHeight() {
      this.tableHeight = window.innerHeight - 320
    },
    renderContent: function (h, { node, data, store }) {
      return (
        <span>
          {' '}
          <i class={data.icon}> </i>
          <span style="padding-left: 4px;">{node.label}</span>{' '}
        </span>
      )
    },
  },
}
</script>

<style scoped="scoped">
.TreeBorder {
  border: 1px solid #cccccc;
  height: 100vh;
  border-radius: 5px;
  overflow: hidden;
}
</style>
