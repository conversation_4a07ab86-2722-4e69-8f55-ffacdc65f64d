<template>
  <div>
    <div class="setting project-addEveryArea">
      <div class="setting-body">
        <el-form ref="form" :model="proInfo" class="settingForm">
         <el-tabs v-model="active" type="border-card">
            <el-tab-pane name="工程参数设置" label="工程参数设置">
              <!--气象区设置-->
              <div class="prolist-seeting">
                <div class="pro-addTitle">
                  <span class="leftScrow"></span>
                  气象区设置
                </div>
                  <el-form-item label="工程所属气象区">
                    <el-select v-model="proInfo.addProParam.sceneVal" placeholder="请选择工程所属气象区" @change="changeSelectinfo(0,$event)">
                      <el-option v-for="item in proInfo.sceneOptions" :key="item.sname" :value="item.sname"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="冰的密度">
                    <el-input v-model="proInfo.addProParam.icedensity" placeholder="请输入冰的密度"></el-input>
                  </el-form-item>
              </div>
              <!--架空线路低压-->
              <div class="prolist-setting">
                <div class="pro-addTitle">
                  <span class="leftScrow"></span>
                  架空线路低压
                </div>
                <!--排列方式的表格是动态循环出来 给后台传值传数组格式，回显也是用数组格式-->
                <table class="proinfo-scaleTable" border="1" cellspacing="0" cellpadding="0" align="center">
                  <tr class="proinfo-scaleThead">
                    <th :style="{width: '6.5rem'}">工况</th>
                    <th>温度(℃)</th>
                    <th>风速(㎡/m)</th>
                    <th>冰厚/覆冰(mm)</th>
                  </tr>
                  <!--排列方式分类-->
                  <tr v-for="(item,index) in proInfo.overheadArr" :key="'tablse' + index">
                    <!-- 这里根据输入框是否是空字符串判断是否显示-->
                    <td v-show="item.column !== ''" :style="{width:'6rem'}">
                      <van-field
                        v-if="item.column !== null"
                        v-model="item.column"
                        readonly
                      />
                    </td>
                    <td>
                      <van-field
                        v-if="item.tem !== null"
                        v-model="item.tem"
                        type="number"
                      />
                    </td>
                    <td>
                      <van-field
                        v-if="item.windspeed !== null"
                        v-model="item.windspeed"
                        type="number"
                      />
                    </td>
                    <td>
                      <van-field
                        v-if="item.iceThickNess !== null"
                        v-model="item.iceThickNess"
                        type="number"
                      />
                    </td>
                  </tr>
                </table>
              </div>
              <!--海拔高度设置-->
              <div class="prolist-setting">
                <div class="pro-addTitle">
                  <span class="leftScrow"></span>
                  海拔高度设置
                </div>
                <el-form-item label="海拔高度">
                  <el-select v-model="proInfo.addProParam.highVal" placeholder="请选择海拔高度">
                    <el-option v-for="item in proInfo.showHighOptions" :key="item.sname" :value="item.sname"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="污秽等级">
                  <el-select v-model="proInfo.addProParam.filthVal" placeholder="请选择污秽等级">
                    <el-option v-for="item in proInfo.showFilthOptions" :key="item.sname" :value="item.sname"></el-option>
                  </el-select>
                </el-form-item>
              </div>
              <!--地形设置-->
              <div class="prolist-setting">
                <div class="pro-addTitle">
                  <span class="leftScrow"></span>
                  地形设置
                </div>
                <div class="pro-addForm">
                  <van-row>
                    <van-field
                      class="pro-addFieldLeng"
                      readonly
                      clickable
                      type="number"
                      :value="proInfo.addProParam.terrainVal"
                      label="地形选择"
                      placeholder="请选择地形"
                      @click="showCheckboxList(proInfo.addProParam.terrainVal,0)"
                    />
                  </van-row>
                </div>
              </div>
              <!--地质设置-->
              <div class="prolist-setting">
                <div class="pro-addTitle">
                  <span class="leftScrow"></span>
                  地质设置
                </div>
                <div class="pro-addForm">
                  <van-row>
                    <van-field
                      class="pro-addFieldLeng"
                      readonly
                      clickable
                      type="number"
                      :value="proInfo.addProParam.geologyVal"
                      label="地质设置"
                      placeholder="请选择地质值"
                      @click="showCheckboxList(proInfo.addProParam.geologyVal,1)"
                    />
                  </van-row>
                </div>
              </div>
              <!--架空线路其他参数-->
              <div class="prolist-setting">
                <div class="pro-addTitle">
                  <span class="leftScrow"></span>
                  其他参数
                </div>
                <div class="prolist-setting">
                  <!--运距设置(千米）-->
                  <div class="pro-addForm">
                    <van-row>
                      <van-field
                        class="pro-addFieldLeng"
                        readonly
                        clickable
                        :value="proInfo.addProParam.longDis"
                        label="运距设置(千米）"
                        type="number"
                        placeholder="运距设置(千米）"
                        @click="showCheckboxList(proInfo.addProParam.longDis,2)"
                      />
                    </van-row>
                  </div>
                  <!--供电区域-->
                  <el-form-item label="供电区域">
                    <el-select v-model="proInfo.addProParam.electArea" placeholder="请选择供电区域">
                      <el-option v-for="item in proInfo.showlectAreaOptions" :key="item.sname" :value="item.sname"></el-option>
                    </el-select>
                  </el-form-item>
                  <!--防雷等级-->
                  <el-form-item label="防雷等级">
                    <el-select v-model="proInfo.addProParam.rayLevel" placeholder="请选择防雷等级">
                      <el-option v-for="item in proInfo.showrayLevelOptions" :key="item.sname" :value="item.sname"></el-option>
                    </el-select>
                  </el-form-item>
                  <!--是否二次转运-->
                  <el-form-item label="是否二次转运">
                    <el-select v-model="proInfo.addProParam.isSecond" placeholder="请选择是否二次转运">
                      <el-option v-for="item in proInfo.showIsSecondOptions" :key="item.sname" :value="item.sname"></el-option>
                    </el-select>
                  </el-form-item>
                  <!--青苗赔偿-->
                  <div class="pro-addForm">
                    <van-row>
                      <van-field
                        class="pro-addFieldLeng"
                        readonly
                        clickable
                        :value="proInfo.addProParam.greenCrops"
                        label="青苗赔偿(亩)"
                        placeholder="请选择青苗赔偿"
                        @click="showCheckboxList(proInfo.addProParam.greenCrops,3)"
                      />
                    </van-row>
                  </div>
                  <!--树木赔偿-->
                  <div class="pro-addForm">
                    <van-row>
                      <van-field
                        class="pro-addFieldLeng"
                        readonly
                        clickable
                        type="number"
                        :value="proInfo.addProParam.treeCrops"
                        label="树木赔偿(株)"
                        placeholder="请选择树木赔偿"
                        @click="showCheckboxList(proInfo.addProParam.treeCrops,4)"
                      />
                    </van-row>
                  </div>
                  <!--带电作业-->
                  <!--是否二次转运-->
                  <el-form-item label="带电作业">
                    <el-select v-model="proInfo.addProParam.liveWork" placeholder="请选择带电作业">
                      <el-option v-for="item in proInfo.showIsLiveWorkOptions" :key="item.sname" :value="item.sname"></el-option>
                    </el-select>
                  </el-form-item>
                </div>
              </div>

            </el-tab-pane>
            <el-tab-pane name="计算参数设置" label="计算参数设置">
              <!--架空-->
              <div class="prolist-setting">
                <div class="pro-addTitle">
                  <span class="leftScrow"></span>
                  架空
                  <van-popover
                    v-model="showPopover"
                    trigger="click"
                    placement="bottom-start"
                  >
                    <div class="setting-tootip">
                      <div class="setting-tootipMain">
                        <div v-for="(item,index) in overHeadContent" :key="'project'+index" class="setting-tootipItem">
                          <div class="setting-title">{{ item.title }}</div>
                          <div class="setting-content">{{ item.content }}</div>
                        </div>
                      </div>
                    </div>
                    <template #reference>
                      <van-icon
                        slot="left-icon"
                        :style="{color: '#25B053'}"
                        class="iconfont"
                        class-prefix="icon"
                        size="16"
                        name="help"
                      />
                    </template>
                  </van-popover>
                </div>
                <el-form-item label="裕度(系数)">
                  <el-input v-model="proInfo.addProParam.overheadMargin" placeholder="请输入裕度(系数)"></el-input>
                </el-form-item>
                <el-form-item label="沿墙支架距离">
                  <el-input v-model="proInfo.addProParam.supportDis" placeholder="请输入沿墙支架距离"></el-input>
                </el-form-item>
                <el-form-item label="杆号牌">
                  <el-select v-model="proInfo.addProParam.polePlateRod" placeholder="选择杆号牌">
                    <el-option v-for="item in lineInfo.polePlateRodSelect" :key="item.sname" :value="item.sname"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="驱鸟器">
                  <el-select v-model="proInfo.addProParam.birdDevice" placeholder="选择驱鸟器">
                    <el-option v-for="item in lineInfo.birdDeviceSelect" :key="item.sname" :value="item.sname"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="驱鸟器间距">
                  <el-input v-model="proInfo.addProParam.birdDeviceDis" placeholder="请输入每n根杆一个,如2"></el-input>
                </el-form-item>
                <el-form-item label="驱鸟风车">
                  <el-select v-model="proInfo.addProParam.birdWindmill" placeholder="选择驱鸟风车">
                    <el-option v-for="item in lineInfo.birdWindmillSelect" :key="item.sname" :value="item.sname"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="驱鸟器风车间距">
                  <el-input v-model="proInfo.addProParam.birdWindmillDis" placeholder="请输入每n根杆一个,如2"></el-input>
                </el-form-item>
                <el-form-item label="防雷方案">
                  <el-select v-model="proInfo.addProParam.defineRay" placeholder="请选择防雷方案">
                    <el-option v-for="item in lineInfo.defineRaySelect" :key="item.sname" :value="item.sname"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="防雷间距">
                  <el-input v-model="proInfo.addProParam.defineRayDis" placeholder="请输入防雷间距"></el-input>
                </el-form-item>
                <el-form-item label="接地引线设置">
                  <el-select v-model="proInfo.addProParam.groundLead" placeholder="请选择接地引线设置">
                    <el-option v-for="item in lineInfo.groundLeadSelect" :key="item.sname" :value="item.sname"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="底卡盘配置">
                  <el-select v-model="proInfo.addProParam.lowChuck" placeholder="请选择底卡盘配置">
                    <el-option v-for="item in proInfo.showIsLiveWorkOptions" :key="item.sname" :value="item.sname"></el-option>
                  </el-select>
                </el-form-item>
              </div>
              <!--电缆余线-->
              <div class="prolist-setting">
                <!--排列方式的表格是动态循环出来 给后台传值传数组格式，回显也是用数组格式-->
                <div class="pro-addTitle">
                  <span class="leftScrow"></span>
                  电缆余线
                  <van-popover
                    v-model="showLine"
                    trigger="click"
                    placement="bottom-start"
                  >
                    <div class="setting-tootip">
                      <div class="setting-tootipMain">
                        电缆余线配置解释内容:通过设置裕度(系数)、中间头余线、上杆余线(不含杆高)、土建站房余线(米)、箱式站余线(米)、电缆井余线(米);物料输出时，电缆线路长短会依据用户配置的数据乘以以上述物料在电缆线路中出现的次数加上电缆档距之和进行输出(具体详细算法为系统配置)
                      </div>
                    </div>
                    <template #reference>
                      <van-icon
                        slot="left-icon"
                        :style="{color: '#25B053'}"
                        class="iconfont"
                        class-prefix="icon"
                        size="16"
                        name="help"
                      />
                    </template>
                  </van-popover>
                </div>
                <el-form-item label="裕度(系数)">
                  <el-input v-model="proInfo.addProParam.lineMargin" placeholder="请输入裕度(系数)"></el-input>
                </el-form-item>
                <el-form-item label="中间头余线(米)">
                  <el-input v-model="proInfo.addProParam.midResidueLine" placeholder="请输入中间头余线"></el-input>
                </el-form-item>
                <el-form-item label="上杆余线(米,不含杆高)">
                  <el-input v-model="proInfo.addProParam.topResidueLine" placeholder="请输入上杆余线"></el-input>
                </el-form-item>
                <el-form-item label="土建站房余线(米)">
                  <el-input v-model="proInfo.addProParam.civilLine" placeholder="请输入土建站房余线"></el-input>
                </el-form-item>
                <el-form-item label="箱式站余线(米)">
                  <el-input v-model="proInfo.addProParam.boxTypeLine" placeholder="请输入箱式站余线"></el-input>
                </el-form-item>
                <el-form-item label="电缆井余线(米)">
                  <el-input v-model="proInfo.addProParam.cablepitLine" placeholder="请输入电缆井余线(米)"></el-input>
                </el-form-item>
              </div>
              <!--其他电缆配置-->
              <div class="prolist-setting">
                <!--排列方式的表格是动态循环出来 给后台传值传数组格式，回显也是用数组格式-->
                <div class="pro-addTitle">
                  <span class="leftScrow"></span>
                  其他电缆配置
                  <van-popover
                    v-model="showOther"
                    trigger="click"
                    placement="bottom-start"
                  >
                    <div class="setting-tootip">
                      <div class="setting-tootipMain">
                        电缆墙间距、电缆挂钩间距、电缆管枕间距、警示牌间距、标志桩间距会影响各物料在电缆线路中的
                        数量,从而影响物料输出;您可以在此配置对应的电缆管枕、警示牌、标志桩、盖板的物料类型。(盖板的尺寸数据会依据用户所选盖板物料类型自动匹配并展示)
                      </div>
                    </div>
                    <template #reference>
                      <van-icon
                        slot="left-icon"
                        :style="{color: '#25B053'}"
                        class="iconfont"
                        class-prefix="icon"
                        size="16"
                        name="help"
                      />
                    </template>
                  </van-popover>
                </div>
                <el-form-item label="电缆墙间距(米)">
                  <el-input v-model="proInfo.addProParam.lineSupportDis" placeholder="请输入电缆墙间距(米)"></el-input>
                </el-form-item>
                <el-form-item label="电缆挂钩间距(米)">
                  <el-input v-model="proInfo.addProParam.lineHookDis" placeholder="请输入电缆挂钩间距(米)"></el-input>
                </el-form-item>
                <el-form-item label="电缆管枕(埋管)">
                  <el-input v-model="proInfo.addProParam.lineTube" placeholder="请选择电缆管枕(埋管)"></el-input>
                </el-form-item>
                <el-form-item label="电缆管枕间距(米/个)">
                  <el-input v-model="proInfo.addProParam.lineTubeDis" placeholder="请输入电缆管枕间距(米/个)"></el-input>
                </el-form-item>
                <el-form-item label="警示牌(电缆沟)">
                  <el-select v-model="proInfo.addProParam.linePlacard" placeholder="请选择警示牌(电缆沟)">
                    <el-option v-for="item in lineInfo.linePlacardSelect" :key="item.sname" :value="item.sname"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="警示牌间距(米/个)">
                  <el-input v-model="proInfo.addProParam.linePlacardDis" placeholder="请输入警示牌间距(米/个)"></el-input>
                </el-form-item>
                <el-form-item label="标志桩(全部类别通道)">
                  <el-select v-model="proInfo.addProParam.markPile" placeholder="请选择标志桩(全部类别通道)">
                    <el-option v-for="item in lineInfo.markPileSelect" :key="item.sname" :value="item.sname"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="标志桩间距(米/个)">
                  <el-input v-model="proInfo.addProParam.markPileDis" placeholder="请输入警示牌间距(米/个)"></el-input>
                </el-form-item>
                <el-form-item label="盖板(电缆沟)">
                  <el-select v-model="proInfo.addProParam.coverPlate" placeholder="请选择盖板(电缆沟)">
                    <el-option v-for="item in lineInfo.coverPlateSelect" :key="item.sname" :value="item.sname"></el-option>
                  </el-select>
                </el-form-item>
                <div class="pro-addForm">
                  <el-form-item label="盖板尺寸">
                    <div class="setting-input">
                      <div class="setting-inputLeft">
                        <label>
                          <input v-model="proInfo.addProParam.coverPlateLen" type="number" placeholder="长">
                        </label>
                      </div>
                      <div class="setting-inputMid">*</div>
                      <div class="setting-inputRig">
                        <label>
                          <input v-model="proInfo.addProParam.coverPlatewWidth" type="number" placeholder="宽">
                        </label>
                      </div>
                    </div>
                  </el-form-item>
                </div>
              </div>
            </el-tab-pane>
        </el-tabs>
        </el-form>
      </div>
      <!--地形值选择-->
      <el-dialog top="10vh" width="80vw" :append-to-body="true" title="地形值选择" :visible.sync="dxzSelectVis">
        <div class="setting-diaselect">
          <div class="setting-diaitems">
            <!--排列方式的表格是动态循环出来 给后台传值传数组格式，回显也是用数组格式-->
            <table
              class="proinfo-scaleTable"
              border="1"
              cellspacing="0"
              cellpadding="0"
              align="center"
              :style="{width: '100%'}"
            >
              <tr class="proinfo-scaleThead">
                <th v-for="item in proInfo.basicHeader" :key="item.key">{{ item.key }}</th>
              </tr>
              <!--排列方式分类-->
              <tr v-for="(item) in proInfo.basicSelectArr" :key="item.key + 'z'">
                <!-- 这里根据输入框是否是空字符串判断是否显示-->
                <td :style="{width:'7rem',color: '#000000'}">
                  {{ item.key }}
                </td>
                <!--默认类型的不允许删除，自己添加的允许删除 -->
                <td>
                  <van-field
                    v-model="item.value"
                    placeholder="0"
                    type="number"
                  />
                </td>
              </tr>
            </table>
          </div>
        </div>
        <div class="pro-setBtn">
          <button class="pro-confirm" @click="submitCheckbox">确认</button>
          <button class="pro-cancle" @click="showSettingDia(2)">取消</button>
        </div>
      </el-dialog>
      <!--青苗多选得页面-->
      <el-dialog top="10vh" width="80vw" :append-to-body="true" title="青苗赔偿" :visible.sync="qmpcSelectVis">
        <div class="setting-diaselect">
          <div class="setting-diaitems">
            <!--排列方式的表格是动态循环出来 给后台传值传数组格式，回显也是用数组格式-->
            <table
              class="proinfo-scaleTable"
              border="1"
              cellspacing="0"
              cellpadding="0"
              align="center"
              :style="{width: '100%'}"
            >
              <tr class="proinfo-scaleThead">
                <th :style="{width: '4rem'}">名称</th>
                <th>类型</th>
                <th>面积(亩)</th>
              </tr>
              <!--粮食作物-->
              <tr>
                <td>粮食作物</td>
                <td>小麦、玉米、水稻、大豆等</td>
                <td>
                  <van-field
                    v-model="proInfo.greenCropsSelect.wheat"
                    placeholder="请输入粮食作物"
                    type="number"
                  />
                </td>
              </tr>
              <!--粮食作物-->
              <tr>
                <td rowspan="9">经济作物</td>
                <td>油料</td>
                <td>
                  <van-field
                    v-model="proInfo.greenCropsSelect.oil"
                    placeholder="请输入油料"
                    type="number"
                  />
                </td>
              </tr>
              <tr>
                <td>大蒜</td>
                <td>
                  <van-field
                    v-model="proInfo.greenCropsSelect.garlic"
                    placeholder="请输入大蒜"
                    type="number"
                  />
                </td>
              </tr>
              <tr>
                <td>芦笋</td>
                <td>
                  <van-field
                    v-model="proInfo.greenCropsSelect.asparagus"
                    placeholder="请输入芦笋"
                    type="number"
                  />
                </td>
              </tr>
              <tr>
                <td>西瓜/甜瓜</td>
                <td>
                  <van-field
                    v-model="proInfo.greenCropsSelect.melon"
                    placeholder="请输入西瓜/甜瓜"
                    type="number"
                  />
                </td>
              </tr>
              <tr>
                <td>辣椒</td>
                <td>
                  <van-field
                    v-model="proInfo.greenCropsSelect.chili"
                    placeholder="请输入辣椒"
                    type="number"
                  />
                </td>
              </tr>
              <tr>
                <td>山药</td>
                <td>
                  <van-field
                    v-model="proInfo.greenCropsSelect.yam"
                    placeholder="请输入山药"
                    type="number"
                  />
                </td>
              </tr>
              <tr>
                <td>棉花</td>
                <td>
                  <van-field
                    v-model="proInfo.greenCropsSelect.cotton"
                    placeholder="请输入棉花"
                    type="number"
                  />
                </td>
              </tr>
              <tr>
                <td>中药材</td>
                <td>
                  <van-field
                    v-model="proInfo.greenCropsSelect.medicine"
                    placeholder="请输入中药材"
                    type="number"
                  />
                </td>
              </tr>
              <tr>
                <td>其他露地蔬菜</td>
                <td>
                  <van-field
                    v-model="proInfo.greenCropsSelect.vegetable"
                    placeholder="请输入其他露地蔬菜"
                    type="number"
                  />
                </td>
              </tr>
            </table>
          </div>
        </div>
        <div class="pro-setBtn">
          <button class="pro-confirm" @click="submitCheckbox">确认</button>
          <button class="pro-cancle" @click="showSettingDia(1)">取消</button>
        </div>
      </el-dialog>
      <!--树木赔偿得页面-->
      <el-dialog top="10vh" width="80vw" :append-to-body="true" title="树木赔偿" :visible.sync="smpcSelectVis">
        <div class="setting-diaselect">
          <div class="setting-diaitems">
            <!--排列方式的表格是动态循环出来 给后台传值传数组格式，回显也是用数组格式-->
            <table
              class="proinfo-scaleTable"
              border="1"
              cellspacing="0"
              cellpadding="0"
              align="center"
              :style="{width: '100%'}"
            >
              <tr class="proinfo-scaleThead">
                <th>名称</th>
                <th>类型</th>
                <th>数量</th>
              </tr>
              <!--乔木-->
              <tr>
                <td :rowspan="2">乔木</td>
                <td>用材林</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.garlic"
                    placeholder="请输入用材林"
                    type="digit"
                  />
                </td>
              </tr>
              <tr>
                <td>绿化树</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.greenTree"
                    placeholder="请输入绿化树"
                    type="digit"
                  />
                </td>
              </tr>
              <!--灌木-->
              <tr>
                <td :rowspan="2">灌木</td>
                <td>一年以内灌木</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.withinAyear"
                    placeholder="请输入一年以内灌木"
                    type="digit"
                  />
                </td>
              </tr>
              <tr>
                <td>一年以上灌木</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.oil"
                    placeholder="请输入一年以上灌木"
                    type="digit"
                  />
                </td>
              </tr>
              <!--苗圃-->
              <tr>
                <td :rowspan="2">苗圃</td>
                <td>原生</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.nativeNursery"
                    placeholder="请输入原生苗圃"
                    type="digit"
                  />
                </td>
              </tr>
              <tr>
                <td>定植</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.plantNursery"
                    placeholder="请输入定植苗圃"
                    type="digit"
                  />
                </td>
              </tr>
              <!--牡丹-->
              <tr>
                <td :rowspan="2">牡丹</td>
                <td>观赏牡丹</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.watchPeony"
                    placeholder="请输入观赏牡丹"
                    type="digit"
                  />
                </td>
              </tr>
              <tr>
                <td>油用牡丹</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.oilPeony"
                    placeholder="请输入油用牡丹"
                    type="digit"
                  />
                </td>
              </tr>
              <!--芍药-->
              <tr>
                <td :rowspan="2">芍药</td>
                <td>观赏芍药</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.watchShao"
                    placeholder="请输入观赏芍药"
                    type="digit"
                  />
                </td>
              </tr>
              <tr>
                <td>实生芍药</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.useShao"
                    placeholder="请输入使用芍药"
                    type="digit"
                  />
                </td>
              </tr>
              <!--草坪绿地-->
              <tr>
                <td>草坪绿地</td>
                <td>人工种植绿地或草坪</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.lawn"
                    placeholder="请输入草坪绿地"
                    type="digit"
                  />
                </td>
              </tr>
              <!--水果树-->
              <tr>
                <td :rowspan="5">水果树</td>
                <td>幼龄期</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.treeJuvenile"
                    placeholder="请输入水果树幼龄期"
                    type="digit"
                  />
                </td>
              </tr>
              <tr>
                <td>初果期</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.treeBegin"
                    placeholder="请输入水果树初果期"
                    type="digit"
                  />
                </td>
              </tr>
              <tr>
                <td>盛果期</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.treeGrand"
                    placeholder="请输入水果树盛果期"
                    type="digit"
                  />
                </td>
              </tr>
              <tr>
                <td>衰老期</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.treeOld"
                    placeholder="请输入水果树衰老期"
                    type="digit"
                  />
                </td>
              </tr>
              <tr>
                <td>葡萄(区分生长期)</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.treeGrapes"
                    placeholder="请输入葡萄(区分生长期)"
                    type="digit"
                  />
                </td>
              </tr>
              <!--经济林-->
              <tr>
                <td :rowspan="4">经济林</td>
                <td>幼龄期</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.groveJuvenile"
                    placeholder="请输入经济林幼龄期"
                    type="digit"
                  />
                </td>
              </tr>
              <tr>
                <td>初果期</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.groveBegin"
                    placeholder="请输入经济林初果期"
                    type="digit"
                  />
                </td>
              </tr>
              <tr>
                <td>盛果期</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.groveGrand"
                    placeholder="请输入经济林盛果期"
                    type="digit"
                  />
                </td>
              </tr>
              <tr>
                <td>衰老期</td>
                <td>
                  <van-field
                    v-model="proInfo.treeCropsSelect.groveOld"
                    placeholder="请输入经济林衰老期"
                    type="digit"
                  />
                </td>
              </tr>
            </table>
          </div>
        </div>
        <div class="pro-setBtn">
          <button class="pro-confirm" @click="submitCheckbox">确认</button>
          <button class="pro-cancle" @click="showSettingDia(0)">取消</button>
        </div>
      </el-dialog>
    </div>
    <div class="pro-setBtn">
      <button class="pro-confirm" @click="submitForm">确认</button>
      <button class="pro-cancle" @click="onClickLeft">取消</button>
    </div>
    <!--  遮罩层-->
    <van-overlay class="overDelay" :show="isShooLoading">
      <div class="wrapper" @click.stop>
        <van-loading :style="{ position: 'absolute' ,top: '50%', left: '45%'}" color="#0094ff" size="24px" vertical>
          加载中...
        </van-loading>
      </div>
    </van-overlay>
  </div>
</template>

<script>
import { apiget, apipost } from '@/utils/mapRequest'
import $ from 'jquery'
import Vue from 'vue'
import { Toast } from 'vant'
Vue.use(Toast)
export default {
  components: {},
  data() {
    return {
      isShooLoading: false,
      dxzSelectVis: false, // 地形值选择
      qmpcSelectVis: false, // 青苗赔偿
      smpcSelectVis: false, // 树木赔偿
      overHeadContent: [
        {
          title: '裕度: ',
          content: '输出物料信息时,导线长度会*裕度输出;'
        },
        {
          title: '沿墙支架距离: ',
          content: '物料输出时,支架数量依据沿墙线路/支架距离进行计算;'
        },
        {
          title: '杆号牌、柱上变台安全警示牌: ',
          content: '物料输出会依据用户配置的数据进行输出'
        },
        {
          title: '驱鸟器、驱鸟风车、防雷方案: ',
          content: '物料输出会依据用户选择配置的数据进行输出'
        },
        {
          title: '驱鸟器间距、驱鸟风车间距、防雷间距: ',
          content: '物料输出时对应的物料数量=架空线路长度/各物料间距进行输出'
        },
        {
          title: '水泥制品是否匹配: ',
          content: '用于决定杆塔是否配置卡盘、拉盘、物料输出时会依据用户的选择进行配置。'
        }
      ],
      proId: '',
      active: '工程参数设置',
      checked: true,
      showPopover: false, // 架空的tootip
      showLine: false, // 电缆余线的tootip
      showOther: false, // 其他配置的tootip
      isShowGreenCrops: false, // 显示青苗
      isShowCheckboxType: 0,
      // 基础信息得参数项
      proInfo: {
        // addItems: '', // 添加多选项
        showSceneVis: false, // 工程参数
        showHigh: false, // 海拔高度
        showFilth: false, // 污秽等级
        showelectArea: false, // 供电区域
        showrayLevel: false, // 防雷等级
        showIsSecond: false, // 二次转运
        showIsLiveWork: false, // 带电作业
        sceneOptions: [{ sname: 'A气象区' }, { sname: 'B气象区' }, { sname: 'C气象区' }, { sname: 'D1气象区' }, { sname: 'D2气象区' }, { sname: 'E1气象区' }, { sname: 'E2气象区' }, { sname: 'E3气象区' }],
        showHighOptions: [{ sname: '1000m及以下' }, { sname: '1000m至2000m' }, { sname: '2000m至3000m' }, { sname: '3000m至4000m' }, { sname: '4000m及以上' }],
        showlectAreaOptions: [{ sname: 'A+' }, { sname: 'A' }, { sname: 'B' }, { sname: 'C' }, { sname: 'D' }, { sname: 'E' }],
        showrayLevelOptions: [{ sname: 'B1' }, { sname: 'B2' }, { sname: 'C1' }, { sname: 'C2' }, { sname: 'D1' }, { sname: 'D2' }],
        showFilthOptions: [{ sname: 'a' }, { sname: 'b' }, { sname: 'c' }, { sname: 'd' }, { sname: 'e' }],
        showIsSecondOptions: [{ sname: '是' }, { sname: '否' }],
        showIsLiveWorkOptions: [{ sname: '是' }, { sname: '否' }],
        overheadArr: [
          {
            column: '最高温度',
            tem: 40,
            windspeed: null,
            iceThickNess: null
          },
          {
            column: '最低温度',
            tem: -10,
            windspeed: null,
            iceThickNess: null
          },
          {
            column: '平均气温',
            tem: 20,
            windspeed: null,
            iceThickNess: null
          },
          {
            column: '覆冰工况',
            tem: -5,
            windspeed: -10,
            iceThickNess: 0
          },
          {
            column: '覆冰厚度',
            tem: null,
            windspeed: null,
            iceThickNess: 5
          },
          {
            column: '覆冰密度',
            tem: null,
            windspeed: null,
            iceThickNess: 900
          },
          {
            column: '最大风速',
            tem: 10,
            windspeed: 35,
            iceThickNess: null
          },
          {
            column: '安装工况',
            tem: -5,
            windspeed: 10,
            iceThickNess: null
          },
          {
            column: '外过电压',
            tem: 15,
            windspeed: 15,
            iceThickNess: null
          },
          {
            column: '内过电压',
            tem: 0,
            windspeed: 18,
            iceThickNess: null
          }
        ], // 气象区工况的数组
        addProParam: {
          sceneVal: 'A气象区', // 气象值
          icedensity: '0.9x10³', // 密度
          highVal: '1000m及以下', // 海拔高度
          filthVal: 'd', // 污秽等级
          terrainVal: '丘陵:20;平原:80;', // 地形设置
          geologyVal: '松软土:20;普通土:80;', // 地质设置
          longDis: '人力运距:1;汽车运距:5;', // 运距设置
          electArea: 'A', // 供电区域
          rayLevel: 'B1', // 防雷等级
          isSecond: '否', // 是否二次运转
          greenCrops: '0', // 青苗赔偿
          treeCrops: '0', // 树木赔偿
          liveWork: '否', // 带电作业
          overheadMargin: '1.05', // 架空得裕度系数
          supportDis: '6', // 沿墙支架距离
          polePlateRod: '', // 杆号牌(杆用)
          birdDevice: '', // 驱鸟器
          birdDeviceDis: '2', // 驱鸟器间距
          birdWindmill: '', // 驱鸟器风车
          birdWindmillDis: '2', // 驱鸟器风车间距
          defineRay: '', // 防雷方案
          defineRayDis: '5', // 防雷间距
          groundLead: '', // 接地引线
          lowChuck: '否', // 底卡盘配置
          lineMargin: '1.02', // 电缆得裕度系数
          midResidueLine: '4', // 中间头余线
          topResidueLine: '0', // 上杆余线(米,不含杆高)
          civilLine: '1.5', // 土建站房余线(米)
          boxTypeLine: '3', // 箱式站余线(米)
          cablepitLine: '15', // 电缆井余线(米)
          lineSupportDis: '6', // 电缆墙间距(米)
          lineHookDis: '6', // 电缆挂钩间距(米)
          lineTube: '', // 电缆管枕(埋管)
          lineTubeDis: '50', // 电缆管枕间距(米/个)
          linePlacard: '', // 警示牌(电缆沟)
          linePlacardDis: '5', // 警示牌间距(米/个)
          markPile: '', // 标志桩(全部类别通道)
          markPileDis: '50', // 标志桩间距(米/个)
          coverPlate: '', // 盖板(电缆沟)
          coverPlateLen: '1000', // 盖板尺寸长
          coverPlatewWidth: '1000' // 盖板尺寸宽
        },
        // 这部分数据放后台数据库里维护 前端只接受
        basicSelectCheckVal: [], // 工况参数和低压复选框选中的值
        basicSelectArr: [], // 工况参数添加选项得页面统一用遍历生成
        basicHeader: [], // 工况参数设置多选得表头
        compareSceneVal: '', // 存放气象区的对比值 如果查询到这个值 切换气象区的下拉框设置默认值时候跳过
        // 以下均为mock数据，等真实数据来后读真实数据
        // 地形设置
        terrainSelect: [
          {
            key: '丘陵',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '平原',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '沙漠',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '草原',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '戈壁',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '盆地',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '高原',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '山地',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '湿地',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          }
        ],
        // 地质设置
        geologySelect: [
          {
            key: '松软土',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '普通土',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '坚土',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '砂砾坚土',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '软石',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '次坚土',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '坚石',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '特坚石',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          }
        ],
        // 运距设置
        longDisSelect: [
          {
            key: '人力运距',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '汽车运距',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '板车运距',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          },
          {
            key: '船舶运距',
            value: '',
            isDefault: 1 // 是否默认值，默认值不允许删除
          }
        ],
        // 青苗赔偿
        greenCropsSelect: {
          oil: '', // 油料
          garlic: '', // 大蒜
          wheat: '', // 小麦玉米等
          asparagus: '', // 芦笋
          melon: '', // 西瓜甜瓜
          chili: '', // 辣椒
          yam: '', // 山药
          cotton: '', // 棉花
          medicine: '', // 中药材
          vegetable: '' // 蔬菜
        },
        // 树木赔偿
        treeCropsSelect: {
          timber: '', // 用材林
          greenTree: '', // 绿化树
          withinAyear: '', // 一年以内
          outAyear: '', // 一年以外
          nativeNursery: '', // 原生苗圃
          plantNursery: '', // 种植苗圃
          watchPeony: '', // 观赏牡丹,
          oilPeony: '', // 油用牡丹,
          watchShao: '', // 观赏芍药,
          useShao: '', // 实用芍药,
          lawn: '', // 绿地
          treeJuvenile: '', // 水果树幼龄期
          treeBegin: '', // 水果树出果期
          treeGrand: '', // 水果树盛果期
          treeOld: '', // 水果树衰老期
          treeGrapes: '', // 水果树葡萄 treeJuvenile: '', // 水果树幼龄期
          groveJuvenile: '', // 经济林幼龄期
          groveBegin: '', // 经济林出果期
          groveGrand: '', // 经济林盛果期
          groveOld: '' // 经济林衰老期
        }

      },
      // 电缆线路的参数项
      lineInfo: {
        isShowpolePlateRod: false, //  杆号牌(杆用)
        isShowbirdDevice: false, //  驱鸟器
        isShowbirdWindmill: false, //  驱鸟器风车
        isShowdefineRay: false, //  防雷方案
        isShowgroundLead: false, //  接地引线设置
        islowChuck: false, //  电缆管枕
        islineTube: false, //  电缆管枕(埋管)
        islinePlacard: false, //  警示牌(电缆沟)
        ismarkPile: false, //  标志桩
        iscoverPlate: false, //  盖板(电缆沟)
        polePlateRodSelect: [], // 杆号牌(杆用)
        birdDeviceSelect: [], // 驱鸟器
        birdWindmillSelect: [], // 驱鸟风车
        defineRaySelect: [], // 防雷方案
        groundLeadSelect: [], // 接地引线
        lowChuckSelect: ['是', '否'], // 底卡盘配置
        lineTubeSelect: [], // 电缆管枕(埋管)
        linePlacardSelect: [], // 警示牌
        markPileSelect: [], // 标志桩
        coverPlateSelect: [] // 盖板(电缆沟)
      }
    }
  },
  mounted() {
    this.getGhpSelect()
    this.getQnqSelect()
    this.getQnfcSelect()
    this.getJdyxSelect()
    this.getDlglSelect()
    this.getDlgzSelect()
    this.getBzzSelect()
    this.getgbSelect()
    this.getFlfaSelect()
    this.getList(this.$route.query.childProjectId)
  },
  methods: {
    /**
       * 返回上一级
       */
    onClickLeft() {
      this.$router.push({
        path: '/index'
      })
    },
    /**
       * 获取列表
       * @param val 子项目id
       */
    getList(val) {
      this.isShooLoading = true
      const that = this
      apiget(`/s-basic-parm/search/${val}`).then(function(res) {
        if (res.code === 1001) {
          that.isShooLoading = false
          if (res.data !== null) {
            that.proInfo.addProParam.overheadMargin = res.data.ydxs // 架空-裕度(系数)
            that.proInfo.addProParam.supportDis = res.data.yqzjjl // 沿墙支架距离
            that.proInfo.addProParam.polePlateRod = res.data.ghp // 杆号牌(杆用)
            that.proInfo.addProParam.birdDevice = res.data.qnqxz // 驱鸟器选择
            that.proInfo.addProParam.birdDeviceDis = res.data.qnjjsz // 驱鸟间距设置
            that.proInfo.addProParam.birdWindmill = res.data.qnfcxz // 驱鸟风车选择
            that.proInfo.addProParam.birdWindmillDis = res.data.qnfcjjsz // 驱鸟风车间距设置
            that.proInfo.addProParam.defineRay = res.data.flfaxz // 防雷方案选择
            that.proInfo.addProParam.defineRayDis = res.data.fljjsz // 防雷间距设置
            that.proInfo.addProParam.groundLead = res.data.jdyxsz // 接地引线设置
            that.proInfo.addProParam.lowChuck = res.data.dkppz // 底卡盘配置
            that.proInfo.addProParam.lineMargin = res.data.dYdxs // 电缆余线-裕度(系数)
            that.proInfo.addProParam.midResidueLine = res.data.zjtyx // 中间头余线
            that.proInfo.addProParam.topResidueLine = res.data.sxgyx // 上下杆余线
            that.proInfo.addProParam.civilLine = res.data.tjzfyx // 土建站房余线
            that.proInfo.addProParam.boxTypeLine = res.data.xszyx // 箱式站余线
            that.proInfo.addProParam.cablepitLine = res.data.dljyx // 电缆井余线
            that.proInfo.addProParam.lineSupportDis = res.data.dlqjj // 电缆墙间距
            that.proInfo.addProParam.lineHookDis = res.data.dlggjj // 电缆挂钩间距
            that.proInfo.addProParam.lineTube = res.data.dlgzxz // 电缆管枕选择
            that.proInfo.addProParam.lineTubeDis = res.data.dlgzjjsz // 电缆管枕间距设置
            that.proInfo.addProParam.linePlacard = res.data.jspxz // 警示牌选择
            that.proInfo.addProParam.linePlacardDis = res.data.jspjjsz // 警示牌间距设置
            that.proInfo.addProParam.markPile = res.data.bzzxz // 标志桩选择
            that.proInfo.addProParam.markPileDis = res.data.bzzjjsz // 标志桩间距设置
            that.proInfo.addProParam.coverPlate = res.data.gbxz // 盖板选择
            that.proInfo.addProParam.coverPlateLen = res.data.gbccszc // 盖板尺寸设置(长
            that.proInfo.addProParam.coverPlatewWidth = res.data.gbccszk // 盖板尺寸设置(宽)
            that.proInfo.addProParam.sceneVal = res.data.gcssqxq // 工程所属气象区vueObj.proInfo.addProParam.icedensity = res.data.bdmd, // 冰的密度
            that.proInfo.compareSceneVal = res.data.gcssqxq // 存放气象区的对比值 如果查询到这个值 切换气象区的下拉框设置默认值时候跳过
            that.proInfo.overheadArr[0].tem = res.data.zgwd // 最高温度
            that.proInfo.overheadArr[1].tem = res.data.zdwd // 最低温度
            that.proInfo.overheadArr[2].tem = res.data.pjqw // 平均气温
            that.proInfo.overheadArr[3].tem = res.data.fbgkw // 覆冰工况(温度)
            that.proInfo.overheadArr[3].windspeed = res.data.fbgkf // 覆冰工况(风速)
            that.proInfo.overheadArr[3].iceThickNess = res.data.fbgkb // 覆冰工况(冰厚)
            that.proInfo.overheadArr[4].iceThickNess = res.data.fbhd // 覆冰厚度
            that.proInfo.overheadArr[5].iceThickNess = res.data.fbmd // 覆冰密度
            that.proInfo.overheadArr[6].tem = res.data.zdfsw // 最大风速(温度)
            that.proInfo.overheadArr[6].windspeed = res.data.zdfsf // 最大风速(风速)
            that.proInfo.overheadArr[7].tem = res.data.azgkgw // 安装工况(温度)
            that.proInfo.overheadArr[7].windspeed = res.data.azgkgf // 安装工况(风速)
            that.proInfo.overheadArr[8].tem = res.data.wgdyw // 外过电压(温度)
            that.proInfo.overheadArr[8].windspeed = res.data.wgdyf // 外过电压(风速)
            that.proInfo.overheadArr[9].tem = res.data.ngdyw // 内过电压(温度)
            that.proInfo.overheadArr[9].windspeed = res.data.ngdyf // 内过电压(风速)
            that.proInfo.addProParam.highVal = res.data.hbgd // 海拔高度
            that.proInfo.addProParam.filthVal = res.data.whdj // 污秽等级
            that.proInfo.addProParam.terrainVal = res.data.dxxz // 地形选择
            that.proInfo.addProParam.geologyVal = res.data.dzxz // 地质选择
            that.proInfo.addProParam.longDis = res.data.yjsz // 运距设置
            that.proInfo.addProParam.electArea = res.data.gdqy // 供电区域
            that.proInfo.addProParam.rayLevel = res.data.fldj // 防雷等级
            that.proInfo.addProParam.isSecond = res.data.sfeczy // 是否二次转运
            that.proInfo.addProParam.greenCrops = res.data.qmpc // 青苗赔偿
            that.proInfo.addProParam.treeCrops = res.data.smpc // 树木赔偿
            that.proInfo.addProParam.liveWork = res.data.ddzy // 带电作业
          }
        }
      })
    },
    clickTabs() {
    },
    showSettingDia(type) {
      switch (type) {
        case 0:
          this.smpcSelectVis = false
          break
        case 1:
          this.qmpcSelectVis = false
          break
        case 2:
          this.dxzSelectVis = false
          break
      }
    },
    changeSelectinfo(type, val) {
      switch (type) {
        case 0:
          this.proInfo.addProParam.sceneVal = val
          this.proInfo.showSceneVis = false
          if (val !== this.proInfo.compareSceneVal) {
            switch (val) {
              case 'A气象区':
                this.proInfo.overheadArr[0].tem = 40 // 最高温度
                this.proInfo.overheadArr[1].tem = -10 // 最低温度
                this.proInfo.overheadArr[2].tem = 20 // 平均气温
                this.proInfo.overheadArr[3].tem = -5 // 覆冰工况(温度)
                this.proInfo.overheadArr[3].windspeed = 10 // 覆冰工况(风速)
                this.proInfo.overheadArr[3].iceThickNess = 0 // 覆冰工况(冰厚)
                this.proInfo.overheadArr[4].iceThickNess = 5 // 覆冰厚度
                this.proInfo.overheadArr[5].iceThickNess = 900 // 覆冰密度
                this.proInfo.overheadArr[6].tem = 10 // 最大风速(温度)
                this.proInfo.overheadArr[6].windspeed = 35 // 最大风速(风速)
                this.proInfo.overheadArr[7].tem = -5 // 安装工况(温度)
                this.proInfo.overheadArr[7].windspeed = 10 // 安装工况(风速)
                this.proInfo.overheadArr[8].tem = 15 // 外过电压(温度)
                this.proInfo.overheadArr[8].windspeed = 15 // 外过电压(风速)
                this.proInfo.overheadArr[9].tem = 0 // 内过电压(温度)
                this.proInfo.overheadArr[9].windspeed = 18 // 内过电压(风速)
                break
              case 'B气象区':
                this.proInfo.overheadArr[0].tem = 40 // 最高温度
                this.proInfo.overheadArr[1].tem = -20 // 最低温度
                this.proInfo.overheadArr[2].tem = 10 // 平均气温
                this.proInfo.overheadArr[3].tem = -5 // 覆冰工况(温度)
                this.proInfo.overheadArr[3].windspeed = 10 // 覆冰工况(风速)
                this.proInfo.overheadArr[3].iceThickNess = 0 // 覆冰工况(冰厚)
                this.proInfo.overheadArr[4].iceThickNess = 10 // 覆冰厚度
                this.proInfo.overheadArr[5].iceThickNess = 900 // 覆冰密度
                this.proInfo.overheadArr[6].tem = -5 // 最大风速(温度)
                this.proInfo.overheadArr[6].windspeed = 25 // 最大风速(风速)
                this.proInfo.overheadArr[7].tem = -10 // 安装工况(温度)
                this.proInfo.overheadArr[7].windspeed = 10 // 安装工况(风速)
                this.proInfo.overheadArr[8].tem = 15 // 外过电压(温度)
                this.proInfo.overheadArr[8].windspeed = 10 // 外过电压(风速)
                this.proInfo.overheadArr[9].tem = 0 // 内过电压(温度)
                this.proInfo.overheadArr[9].windspeed = 15 // 内过电压(风速)
                break
              case 'C气象区':
                this.proInfo.overheadArr[0].tem = 40 // 最高温度
                this.proInfo.overheadArr[1].tem = -40 // 最低温度
                this.proInfo.overheadArr[2].tem = -5 // 平均气温
                this.proInfo.overheadArr[3].tem = -5 // 覆冰工况(温度)
                this.proInfo.overheadArr[3].windspeed = 10 // 覆冰工况(风速)
                this.proInfo.overheadArr[3].iceThickNess = 0 // 覆冰工况(冰厚)
                this.proInfo.overheadArr[4].iceThickNess = 10 // 覆冰厚度
                this.proInfo.overheadArr[5].iceThickNess = 900 // 覆冰密度
                this.proInfo.overheadArr[6].tem = -5 // 最大风速(温度)
                this.proInfo.overheadArr[6].windspeed = 30 // 最大风速(风速)
                this.proInfo.overheadArr[7].tem = -15 // 安装工况(温度)
                this.proInfo.overheadArr[7].windspeed = 10 // 安装工况(风速)
                this.proInfo.overheadArr[8].tem = 15 // 外过电压(温度)
                this.proInfo.overheadArr[8].windspeed = 10 // 外过电压(风速)
                this.proInfo.overheadArr[9].tem = 0 // 内过电压(温度)
                this.proInfo.overheadArr[9].windspeed = 15 // 内过电压(风速)
                break
              case 'D1气象区':
                this.proInfo.overheadArr[0].tem = 40 // 最高温度
                this.proInfo.overheadArr[1].tem = -5 // 最低温度
                this.proInfo.overheadArr[2].tem = 20 // 平均气温
                this.proInfo.overheadArr[3].tem = -5 // 覆冰工况(温度)
                this.proInfo.overheadArr[3].windspeed = 10 // 覆冰工况(风速)
                this.proInfo.overheadArr[3].iceThickNess = 0 // 覆冰工况(冰厚)
                this.proInfo.overheadArr[4].iceThickNess = 0 // 覆冰厚度
                this.proInfo.overheadArr[5].iceThickNess = 900 // 覆冰密度
                this.proInfo.overheadArr[6].tem = 10 // 最大风速(温度)
                this.proInfo.overheadArr[6].windspeed = 40 // 最大风速(风速)
                this.proInfo.overheadArr[7].tem = 0 // 安装工况(温度)
                this.proInfo.overheadArr[7].windspeed = 10 // 安装工况(风速)
                this.proInfo.overheadArr[8].tem = 15 // 外过电压(温度)
                this.proInfo.overheadArr[8].windspeed = 15 // 外过电压(风速)
                this.proInfo.overheadArr[9].tem = 0 // 内过电压(温度)
                this.proInfo.overheadArr[9].windspeed = 20 // 内过电压(风速)
                break
              case 'D2气象区':
                this.proInfo.overheadArr[0].tem = 40 // 最高温度
                this.proInfo.overheadArr[1].tem = -5 // 最低温度
                this.proInfo.overheadArr[2].tem = 20 // 平均气温
                this.proInfo.overheadArr[3].tem = -5 // 覆冰工况(温度)
                this.proInfo.overheadArr[3].windspeed = 10 // 覆冰工况(风速)
                this.proInfo.overheadArr[3].iceThickNess = 0 // 覆冰工况(冰厚)
                this.proInfo.overheadArr[4].iceThickNess = 0 // 覆冰厚度
                this.proInfo.overheadArr[5].iceThickNess = 900 // 覆冰密度
                this.proInfo.overheadArr[6].tem = 10 // 最大风速(温度)
                this.proInfo.overheadArr[6].windspeed = 45 // 最大风速(风速)
                this.proInfo.overheadArr[7].tem = 0 // 安装工况(温度)
                this.proInfo.overheadArr[7].windspeed = 10 // 安装工况(风速)
                this.proInfo.overheadArr[8].tem = 15 // 外过电压(温度)
                this.proInfo.overheadArr[8].windspeed = 15 // 外过电压(风速)
                this.proInfo.overheadArr[9].tem = 0 // 内过电压(温度)
                this.proInfo.overheadArr[9].windspeed = 23 // 内过电压(风速)
                break
              case 'E1气象区':
                this.proInfo.overheadArr[0].tem = 40 // 最高温度
                this.proInfo.overheadArr[1].tem = -20 // 最低温度
                this.proInfo.overheadArr[2].tem = 10 // 平均气温
                this.proInfo.overheadArr[3].tem = -5 // 覆冰工况(温度)
                this.proInfo.overheadArr[3].windspeed = 15 // 覆冰工况(风速)
                this.proInfo.overheadArr[3].iceThickNess = 0 // 覆冰工况(冰厚)
                this.proInfo.overheadArr[4].iceThickNess = 15 // 覆冰厚度
                this.proInfo.overheadArr[5].iceThickNess = 900 // 覆冰密度
                this.proInfo.overheadArr[6].tem = -5 // 最大风速(温度)
                this.proInfo.overheadArr[6].windspeed = 30 // 最大风速(风速)
                this.proInfo.overheadArr[7].tem = -10 // 安装工况(温度)
                this.proInfo.overheadArr[7].windspeed = 10 // 安装工况(风速)
                this.proInfo.overheadArr[8].tem = 15 // 外过电压(温度)
                this.proInfo.overheadArr[8].windspeed = 10 // 外过电压(风速)
                this.proInfo.overheadArr[9].tem = 0 // 内过电压(温度)
                this.proInfo.overheadArr[9].windspeed = 15 // 内过电压(风速)
                break
              case 'E2气象区':
                this.proInfo.overheadArr[0].tem = 40 // 最高温度
                this.proInfo.overheadArr[1].tem = -20 // 最低温度
                this.proInfo.overheadArr[2].tem = 10 // 平均气温
                this.proInfo.overheadArr[3].tem = -5 // 覆冰工况(温度)
                this.proInfo.overheadArr[3].windspeed = 15 // 覆冰工况(风速)
                this.proInfo.overheadArr[3].iceThickNess = 0 // 覆冰工况(冰厚)
                this.proInfo.overheadArr[4].iceThickNess = 20 // 覆冰厚度
                this.proInfo.overheadArr[5].iceThickNess = 900 // 覆冰密度
                this.proInfo.overheadArr[6].tem = -5 // 最大风速(温度)
                this.proInfo.overheadArr[6].windspeed = 30 // 最大风速(风速)
                this.proInfo.overheadArr[7].tem = -10 // 安装工况(温度)
                this.proInfo.overheadArr[7].windspeed = 10 // 安装工况(风速)
                this.proInfo.overheadArr[8].tem = 15 // 外过电压(温度)
                this.proInfo.overheadArr[8].windspeed = 10 // 外过电压(风速)
                this.proInfo.overheadArr[9].tem = 0 // 内过电压(温度)
                this.proInfo.overheadArr[9].windspeed = 15 // 内过电压(风速)
                break
              case 'E3气象区':
                this.proInfo.overheadArr[0].tem = 40 // 最高温度
                this.proInfo.overheadArr[1].tem = -20 // 最低温度
                this.proInfo.overheadArr[2].tem = 10 // 平均气温
                this.proInfo.overheadArr[3].tem = -5 // 覆冰工况(温度)
                this.proInfo.overheadArr[3].windspeed = 15 // 覆冰工况(风速)
                this.proInfo.overheadArr[3].iceThickNess = 0 // 覆冰工况(冰厚)
                this.proInfo.overheadArr[4].iceThickNess = 30 // 覆冰厚度
                this.proInfo.overheadArr[5].iceThickNess = 900 // 覆冰密度
                this.proInfo.overheadArr[6].tem = -5 // 最大风速(温度)
                this.proInfo.overheadArr[6].windspeed = 30 // 最大风速(风速)
                this.proInfo.overheadArr[7].tem = -10 // 安装工况(温度)
                this.proInfo.overheadArr[7].windspeed = 10 // 安装工况(风速)
                this.proInfo.overheadArr[8].tem = 15 // 外过电压(温度)
                this.proInfo.overheadArr[8].windspeed = 10 // 外过电压(风速)
                this.proInfo.overheadArr[9].tem = 0 // 内过电压(温度)
                this.proInfo.overheadArr[9].windspeed = 15 // 内过电压(风速)
                break
            }
          }
          break
        case 7:
          this.proInfo.addProParam.polePlateRod = val.sname
          this.lineInfo.isShowpolePlateRod = false
          break
        case 9:
          this.proInfo.addProParam.birdDevice = val.sname
          this.lineInfo.isShowbirdDevice = false
          break
        case 10:
          this.proInfo.addProParam.birdWindmill = val.sname
          this.lineInfo.isShowbirdWindmill = false
          break
        case 11:
          this.proInfo.addProParam.defineRay = val.sname
          this.lineInfo.isShowdefineRay = false
          break
        case 12:
          this.proInfo.addProParam.groundLead = val.sname
          this.lineInfo.isShowgroundLead = false
          break
        case 13:
          this.proInfo.addProParam.lowChuck = val.sname
          this.proInfo.showSceneVis = false
          break
        case 14:
          this.proInfo.addProParam.lineTube = val.sname
          this.lineInfo.islineTube = false
          break
        case 15:
          this.proInfo.addProParam.linePlacard = val.sname
          this.lineInfo.islinePlacard = false
          break
        case 16:
          this.proInfo.addProParam.markPile = val.sname
          this.lineInfo.ismarkPile = false
          break
        case 17:
          this.proInfo.addProParam.coverPlate = val.sname
          this.lineInfo.iscoverPlate = false
          break
      }
    },
    /**
       * 下拉框改变后值改变的问题
       * @param type 类型
       * @param type 0气象区 1海拔高度 2污秽等级 3供电区域 4防雷等级
       * @param type 5二次转运 6带电作业 7杆号牌(杆用)
       * @param type 9驱鸟器 10驱鸟风车 11防雷方案 12接地引线设置
       * @param type 13底卡盘配置 14电缆管枕 15警示牌(电缆沟) 16标志桩
       * @param type 17盖板(电缆沟)
       * @param val 选中值
       */
    onConfirmSelect(type, val) {
      switch (type) {
        case 0:
          this.proInfo.addProParam.sceneVal = val.sname
          this.proInfo.showSceneVis = false
          console.log(val, val.sname === 'B气象区')
          if (val !== this.proInfo.compareSceneVal) {
            switch (val.sname) {
              case 'A气象区':
                this.proInfo.overheadArr[0].tem = 40 // 最高温度
                this.proInfo.overheadArr[1].tem = -10 // 最低温度
                this.proInfo.overheadArr[2].tem = 20 // 平均气温
                this.proInfo.overheadArr[3].tem = -5 // 覆冰工况(温度)
                this.proInfo.overheadArr[3].windspeed = 10 // 覆冰工况(风速)
                this.proInfo.overheadArr[3].iceThickNess = 0 // 覆冰工况(冰厚)
                this.proInfo.overheadArr[4].iceThickNess = 5 // 覆冰厚度
                this.proInfo.overheadArr[5].iceThickNess = 900 // 覆冰密度
                this.proInfo.overheadArr[6].tem = 10 // 最大风速(温度)
                this.proInfo.overheadArr[6].windspeed = 35 // 最大风速(风速)
                this.proInfo.overheadArr[7].tem = -5 // 安装工况(温度)
                this.proInfo.overheadArr[7].windspeed = 10 // 安装工况(风速)
                this.proInfo.overheadArr[8].tem = 15 // 外过电压(温度)
                this.proInfo.overheadArr[8].windspeed = 15 // 外过电压(风速)
                this.proInfo.overheadArr[9].tem = 0 // 内过电压(温度)
                this.proInfo.overheadArr[9].windspeed = 18 // 内过电压(风速)
                break
              case 'B气象区':
                this.proInfo.overheadArr[0].tem = 40 // 最高温度
                this.proInfo.overheadArr[1].tem = -20 // 最低温度
                this.proInfo.overheadArr[2].tem = 10 // 平均气温
                this.proInfo.overheadArr[3].tem = -5 // 覆冰工况(温度)
                this.proInfo.overheadArr[3].windspeed = 10 // 覆冰工况(风速)
                this.proInfo.overheadArr[3].iceThickNess = 0 // 覆冰工况(冰厚)
                this.proInfo.overheadArr[4].iceThickNess = 10 // 覆冰厚度
                this.proInfo.overheadArr[5].iceThickNess = 900 // 覆冰密度
                this.proInfo.overheadArr[6].tem = -5 // 最大风速(温度)
                this.proInfo.overheadArr[6].windspeed = 25 // 最大风速(风速)
                this.proInfo.overheadArr[7].tem = -10 // 安装工况(温度)
                this.proInfo.overheadArr[7].windspeed = 10 // 安装工况(风速)
                this.proInfo.overheadArr[8].tem = 15 // 外过电压(温度)
                this.proInfo.overheadArr[8].windspeed = 10 // 外过电压(风速)
                this.proInfo.overheadArr[9].tem = 0 // 内过电压(温度)
                this.proInfo.overheadArr[9].windspeed = 15 // 内过电压(风速)
                break
              case 'C气象区':
                this.proInfo.overheadArr[0].tem = 40 // 最高温度
                this.proInfo.overheadArr[1].tem = -40 // 最低温度
                this.proInfo.overheadArr[2].tem = -5 // 平均气温
                this.proInfo.overheadArr[3].tem = -5 // 覆冰工况(温度)
                this.proInfo.overheadArr[3].windspeed = 10 // 覆冰工况(风速)
                this.proInfo.overheadArr[3].iceThickNess = 0 // 覆冰工况(冰厚)
                this.proInfo.overheadArr[4].iceThickNess = 10 // 覆冰厚度
                this.proInfo.overheadArr[5].iceThickNess = 900 // 覆冰密度
                this.proInfo.overheadArr[6].tem = -5 // 最大风速(温度)
                this.proInfo.overheadArr[6].windspeed = 30 // 最大风速(风速)
                this.proInfo.overheadArr[7].tem = -15 // 安装工况(温度)
                this.proInfo.overheadArr[7].windspeed = 10 // 安装工况(风速)
                this.proInfo.overheadArr[8].tem = 15 // 外过电压(温度)
                this.proInfo.overheadArr[8].windspeed = 10 // 外过电压(风速)
                this.proInfo.overheadArr[9].tem = 0 // 内过电压(温度)
                this.proInfo.overheadArr[9].windspeed = 15 // 内过电压(风速)
                break
              case 'D1气象区':
                this.proInfo.overheadArr[0].tem = 40 // 最高温度
                this.proInfo.overheadArr[1].tem = -5 // 最低温度
                this.proInfo.overheadArr[2].tem = 20 // 平均气温
                this.proInfo.overheadArr[3].tem = -5 // 覆冰工况(温度)
                this.proInfo.overheadArr[3].windspeed = 10 // 覆冰工况(风速)
                this.proInfo.overheadArr[3].iceThickNess = 0 // 覆冰工况(冰厚)
                this.proInfo.overheadArr[4].iceThickNess = 0 // 覆冰厚度
                this.proInfo.overheadArr[5].iceThickNess = 900 // 覆冰密度
                this.proInfo.overheadArr[6].tem = 10 // 最大风速(温度)
                this.proInfo.overheadArr[6].windspeed = 40 // 最大风速(风速)
                this.proInfo.overheadArr[7].tem = 0 // 安装工况(温度)
                this.proInfo.overheadArr[7].windspeed = 10 // 安装工况(风速)
                this.proInfo.overheadArr[8].tem = 15 // 外过电压(温度)
                this.proInfo.overheadArr[8].windspeed = 15 // 外过电压(风速)
                this.proInfo.overheadArr[9].tem = 0 // 内过电压(温度)
                this.proInfo.overheadArr[9].windspeed = 20 // 内过电压(风速)
                break
              case 'D2气象区':
                this.proInfo.overheadArr[0].tem = 40 // 最高温度
                this.proInfo.overheadArr[1].tem = -5 // 最低温度
                this.proInfo.overheadArr[2].tem = 20 // 平均气温
                this.proInfo.overheadArr[3].tem = -5 // 覆冰工况(温度)
                this.proInfo.overheadArr[3].windspeed = 10 // 覆冰工况(风速)
                this.proInfo.overheadArr[3].iceThickNess = 0 // 覆冰工况(冰厚)
                this.proInfo.overheadArr[4].iceThickNess = 0 // 覆冰厚度
                this.proInfo.overheadArr[5].iceThickNess = 900 // 覆冰密度
                this.proInfo.overheadArr[6].tem = 10 // 最大风速(温度)
                this.proInfo.overheadArr[6].windspeed = 45 // 最大风速(风速)
                this.proInfo.overheadArr[7].tem = 0 // 安装工况(温度)
                this.proInfo.overheadArr[7].windspeed = 10 // 安装工况(风速)
                this.proInfo.overheadArr[8].tem = 15 // 外过电压(温度)
                this.proInfo.overheadArr[8].windspeed = 15 // 外过电压(风速)
                this.proInfo.overheadArr[9].tem = 0 // 内过电压(温度)
                this.proInfo.overheadArr[9].windspeed = 23 // 内过电压(风速)
                break
              case 'E1气象区':
                this.proInfo.overheadArr[0].tem = 40 // 最高温度
                this.proInfo.overheadArr[1].tem = -20 // 最低温度
                this.proInfo.overheadArr[2].tem = 10 // 平均气温
                this.proInfo.overheadArr[3].tem = -5 // 覆冰工况(温度)
                this.proInfo.overheadArr[3].windspeed = 15 // 覆冰工况(风速)
                this.proInfo.overheadArr[3].iceThickNess = 0 // 覆冰工况(冰厚)
                this.proInfo.overheadArr[4].iceThickNess = 15 // 覆冰厚度
                this.proInfo.overheadArr[5].iceThickNess = 900 // 覆冰密度
                this.proInfo.overheadArr[6].tem = -5 // 最大风速(温度)
                this.proInfo.overheadArr[6].windspeed = 30 // 最大风速(风速)
                this.proInfo.overheadArr[7].tem = -10 // 安装工况(温度)
                this.proInfo.overheadArr[7].windspeed = 10 // 安装工况(风速)
                this.proInfo.overheadArr[8].tem = 15 // 外过电压(温度)
                this.proInfo.overheadArr[8].windspeed = 10 // 外过电压(风速)
                this.proInfo.overheadArr[9].tem = 0 // 内过电压(温度)
                this.proInfo.overheadArr[9].windspeed = 15 // 内过电压(风速)
                break
              case 'E2气象区':
                this.proInfo.overheadArr[0].tem = 40 // 最高温度
                this.proInfo.overheadArr[1].tem = -20 // 最低温度
                this.proInfo.overheadArr[2].tem = 10 // 平均气温
                this.proInfo.overheadArr[3].tem = -5 // 覆冰工况(温度)
                this.proInfo.overheadArr[3].windspeed = 15 // 覆冰工况(风速)
                this.proInfo.overheadArr[3].iceThickNess = 0 // 覆冰工况(冰厚)
                this.proInfo.overheadArr[4].iceThickNess = 20 // 覆冰厚度
                this.proInfo.overheadArr[5].iceThickNess = 900 // 覆冰密度
                this.proInfo.overheadArr[6].tem = -5 // 最大风速(温度)
                this.proInfo.overheadArr[6].windspeed = 30 // 最大风速(风速)
                this.proInfo.overheadArr[7].tem = -10 // 安装工况(温度)
                this.proInfo.overheadArr[7].windspeed = 10 // 安装工况(风速)
                this.proInfo.overheadArr[8].tem = 15 // 外过电压(温度)
                this.proInfo.overheadArr[8].windspeed = 10 // 外过电压(风速)
                this.proInfo.overheadArr[9].tem = 0 // 内过电压(温度)
                this.proInfo.overheadArr[9].windspeed = 15 // 内过电压(风速)
                break
              case 'E3气象区':
                this.proInfo.overheadArr[0].tem = 40 // 最高温度
                this.proInfo.overheadArr[1].tem = -20 // 最低温度
                this.proInfo.overheadArr[2].tem = 10 // 平均气温
                this.proInfo.overheadArr[3].tem = -5 // 覆冰工况(温度)
                this.proInfo.overheadArr[3].windspeed = 15 // 覆冰工况(风速)
                this.proInfo.overheadArr[3].iceThickNess = 0 // 覆冰工况(冰厚)
                this.proInfo.overheadArr[4].iceThickNess = 30 // 覆冰厚度
                this.proInfo.overheadArr[5].iceThickNess = 900 // 覆冰密度
                this.proInfo.overheadArr[6].tem = -5 // 最大风速(温度)
                this.proInfo.overheadArr[6].windspeed = 30 // 最大风速(风速)
                this.proInfo.overheadArr[7].tem = -10 // 安装工况(温度)
                this.proInfo.overheadArr[7].windspeed = 10 // 安装工况(风速)
                this.proInfo.overheadArr[8].tem = 15 // 外过电压(温度)
                this.proInfo.overheadArr[8].windspeed = 10 // 外过电压(风速)
                this.proInfo.overheadArr[9].tem = 0 // 内过电压(温度)
                this.proInfo.overheadArr[9].windspeed = 15 // 内过电压(风速)
                break
            }
          }
          break
        case 1:
          this.proInfo.addProParam.highVal = val.sname
          this.proInfo.showHigh = false
          break
        case 2:
          this.proInfo.addProParam.filthVal = val.sname
          this.proInfo.showFilth = false
          break
        case 3:
          this.proInfo.addProParam.electArea = val.sname
          this.proInfo.showelectArea = false
          break
        case 4:
          this.proInfo.addProParam.rayLevel = val.sname
          this.proInfo.showrayLevel = false
          break
        case 5:
          this.proInfo.addProParam.isSecond = val.sname
          this.proInfo.showIsSecond = false
          break
        case 6:
          this.proInfo.addProParam.liveWork = val.sname
          this.proInfo.showIsLiveWork = false
          break
        case 7:
          this.proInfo.addProParam.polePlateRod = val.sname
          this.lineInfo.isShowpolePlateRod = false
          break
        case 9:
          this.proInfo.addProParam.birdDevice = val.sname
          this.lineInfo.isShowbirdDevice = false
          break
        case 10:
          this.proInfo.addProParam.birdWindmill = val.sname
          this.lineInfo.isShowbirdWindmill = false
          break
        case 11:
          this.proInfo.addProParam.defineRay = val.sname
          this.lineInfo.isShowdefineRay = false
          break
        case 12:
          this.proInfo.addProParam.groundLead = val.sname
          this.lineInfo.isShowgroundLead = false
          break
        case 13:
          this.proInfo.addProParam.lowChuck = val.sname
          this.proInfo.showSceneVis = false
          break
        case 14:
          this.proInfo.addProParam.lineTube = val.sname
          this.lineInfo.islineTube = false
          break
        case 15:
          this.proInfo.addProParam.linePlacard = val.sname
          this.lineInfo.islinePlacard = false
          break
        case 16:
          this.proInfo.addProParam.markPile = val.sname
          this.lineInfo.ismarkPile = false
          break
        case 17:
          this.proInfo.addProParam.coverPlate = val.sname
          this.lineInfo.iscoverPlate = false
          break
      }
    },
    /**
       * 选择列表里面添加得
       */
    submitCheckbox() {
      debugger
      switch (this.isShowCheckboxType) {
        // eslint当中const作用域会出问题 需要包起来
        case 0: {
          const data_one = this.proInfo.terrainSelect
          let str = ''
          let dataTotal = 0
          // 便利数组 将输入框当中值不等于0的值显示出来
          for (const j in data_one) {
            if (data_one[j].value !== '') {
              str += data_one[j].key + ':' + data_one[j].value + ';'
              dataTotal += Number(data_one[j].value)
            }
          }
          if (dataTotal > 100) {
            return Toast.fail('比例值累加小于等于100,请调整')
          } else {
            this.proInfo.addProParam.terrainVal = str
            this.dxzSelectVis = false
          }
          break
        }
        case 1: {
          const data_two = this.proInfo.geologySelect
          let str_two = ''
          let dataTotalTwo = 0
          // 便利数组 将输入框当中值不等于0的值显示出来
          for (const j in data_two) {
            if (data_two[j].value !== '') {
              str_two += data_two[j].key + ':' + data_two[j].value + ';'
              dataTotalTwo += Number(data_two[j].value)
            }
          }
          if (dataTotalTwo > 100) {
            return Toast.fail('比例值累加小于等于100,请调整')
          } else {
            this.proInfo.addProParam.geologyVal = str_two
            this.dxzSelectVis = false
          }
          break
        }
        case 2: {
          const data_thr = this.proInfo.longDisSelect
          let str_thr = ''
          // 便利数组 将输入框当中值不等于0的值显示出来
          for (const j in data_thr) {
            if (data_thr[j].value !== '') {
              str_thr += data_thr[j].key + ':' + data_thr[j].value + ';'
            }
          }
          this.proInfo.addProParam.longDis = str_thr
          this.dxzSelectVis = false
          break
        }
        case 3: {
          const data_four = this.proInfo.greenCropsSelect
          // 青苗求和值
          let value_one = 0
          for (const key in data_four) {
            if (data_four[key] !== '') {
              value_one += Number(data_four[key])
            }
          }
          this.proInfo.addProParam.greenCrops = value_one
          this.qmpcSelectVis = false
          break
        }
        case 4: {
          const data_five = this.proInfo.treeCropsSelect
          // 树木求和值
          let value_two = 0
          for (const key in data_five) {
            if (data_five[key] !== '') {
              value_two += Number(data_five[key])
            }
          }
          this.proInfo.addProParam.treeCrops = value_two
          this.smpcSelectVis = false
          break
        }
      }
    },
    /**
       * 提交整个工程参数设置
       */
    submitForm() {
      this.isShooLoading = true
      const that = this
      const data = {
        'projId': this.$route.query.childProjectId,
        'ydxs': this.proInfo.addProParam.overheadMargin, // 架空-裕度(系数)
        'yqzjjl': this.proInfo.addProParam.supportDis, // 沿墙支架距离
        'ghp': this.proInfo.addProParam.polePlateRod, // 杆号牌(杆用)
        'qnqxz': this.proInfo.addProParam.birdDevice, // 驱鸟器选择
        'qnjjsz': this.proInfo.addProParam.birdDeviceDis, // 驱鸟间距设置
        'qnfcxz': this.proInfo.addProParam.birdWindmill, // 驱鸟风车选择
        'qnfcjjsz': this.proInfo.addProParam.birdWindmillDis, // 驱鸟风车间距设置
        'flfaxz': this.proInfo.addProParam.defineRay, // 防雷方案选择
        'fljjsz': this.proInfo.addProParam.defineRayDis, // 防雷间距设置
        'jdyxsz': this.proInfo.addProParam.groundLead, // 接地引线设置
        'dkppz': this.proInfo.addProParam.lowChuck, // 底卡盘配置
        'dYdxs': this.proInfo.addProParam.lineMargin, // 电缆余线-裕度(系数)
        'zjtyx': this.proInfo.addProParam.midResidueLine, // 中间头余线
        'sxgyx': this.proInfo.addProParam.topResidueLine, // 上下杆余线
        'tjzfyx': this.proInfo.addProParam.civilLine, // 土建站房余线
        'xszyx': this.proInfo.addProParam.boxTypeLine, // 箱式站余线
        'dljyx': this.proInfo.addProParam.cablepitLine, // 电缆井余线
        'dlqjj': this.proInfo.addProParam.lineSupportDis, // 电缆墙间距
        'dlggjj': this.proInfo.addProParam.lineHookDis, // 电缆挂钩间距
        'dlgzxz': this.proInfo.addProParam.lineTube, // 电缆管枕选择
        'dlgzjjsz': this.proInfo.addProParam.lineTubeDis, // 电缆管枕间距设置
        'jspxz': this.proInfo.addProParam.linePlacard, // 警示牌选择
        'jspjjsz': this.proInfo.addProParam.linePlacardDis, // 警示牌间距设置
        'bzzxz': this.proInfo.addProParam.markPile, // 标志桩选择
        'bzzjjsz': this.proInfo.addProParam.markPileDis, // 标志桩间距设置
        'gbxz': this.proInfo.addProParam.coverPlate, // 盖板选择
        'gbccszc': this.proInfo.addProParam.coverPlateLen, // 盖板尺寸设置(长
        'gbccszk': this.proInfo.addProParam.coverPlatewWidth, // 盖板尺寸设置(宽)
        'gcssqxq': this.proInfo.addProParam.sceneVal, // 工程所属气象区
        'bdmd': this.proInfo.addProParam.icedensity, // 冰的密度
        'zgwd': this.proInfo.overheadArr[0].tem, // 最高温度
        'zdwd': this.proInfo.overheadArr[1].tem, // 最低温度
        'pjqw': this.proInfo.overheadArr[2].tem, // 平均气温
        'fbgkw': this.proInfo.overheadArr[3].tem, // 覆冰工况(温度)
        'fbgkf': this.proInfo.overheadArr[3].windspeed, // 覆冰工况(风速)
        'fbgkb': this.proInfo.overheadArr[3].iceThickNess, // 覆冰工况(冰厚)
        'fbhd': this.proInfo.overheadArr[4].iceThickNess, // 覆冰厚度
        'fbmd': this.proInfo.overheadArr[5].iceThickNess, // 覆冰密度
        'zdfsw': this.proInfo.overheadArr[6].tem, // 最大风速(温度)
        'zdfsf': this.proInfo.overheadArr[6].windspeed, // 最大风速(风速)
        'azgkgw': this.proInfo.overheadArr[7].tem, // 安装工况(温度)
        'azgkgf': this.proInfo.overheadArr[7].windspeed, // 安装工况(风速)
        'wgdyw': this.proInfo.overheadArr[8].tem, // 外过电压(温度)
        'wgdyf': this.proInfo.overheadArr[8].windspeed, // 外过电压(风速)
        'ngdyw': this.proInfo.overheadArr[9].tem, // 内过电压(温度)
        'ngdyf': this.proInfo.overheadArr[9].windspeed, // 内过电压(风速)
        'hbgd': this.proInfo.addProParam.highVal, // 海拔高度
        'whdj': this.proInfo.addProParam.filthVal, // 污秽等级
        'dxxz': this.proInfo.addProParam.terrainVal, // 地形选择
        'dzxz': this.proInfo.addProParam.geologyVal, // 地质选择
        'yjsz': this.proInfo.addProParam.longDis, // 运距设置
        'gdqy': this.proInfo.addProParam.electArea, // 供电区域
        'fldj': this.proInfo.addProParam.rayLevel, // 防雷等级
        'sfeczy': this.proInfo.addProParam.isSecond, // 是否二次转运
        'qmpc': this.proInfo.addProParam.greenCrops, // 青苗赔偿
        'smpc': this.proInfo.addProParam.treeCrops, // 树木赔偿
        'ddzy': this.proInfo.addProParam.liveWork // 带电作业
      }
      for (const key in data) {
        if (data[key] === '') {
          this.isShooLoading = false
          return Toast.fail('请补充完整内容')
        }
      }
      apipost('/s-basic-parm/save', data).then(function(res) {
        if (res.code === 1001) {
          that.isShooLoading = false
          Toast.success('添加成功')
          that.$emit('saveSuccess','aaa')
          // that.$router.push({
          //   path: '/index'
          // })
        }
      })
    },
    /**
       *
       * @param type
       * type 0 地形选择
       * type 1 地质选择
       * type 2 运距设置(千米）
       * type 3 青苗赔偿(亩)
       * type 4 树木赔偿(株)
       */
    showCheckboxList(val, type) {
      this.proInfo.addItems = ''
      // 提交得时候根据type给对应得内容赋值
      this.isShowCheckboxType = type
      // 将字符串转成数组下放对象的格式
      const changeArr = val.split(';')
      const acceptArr = []
      for (const j in changeArr) {
        if (changeArr[j] !== '') {
          const obj = {}
          const arr = changeArr[j].split(':')
          obj.key = arr[0]
          obj.value = arr[1]
          acceptArr.push(obj)
        }
      }
      // 这里每次打开走一次接口读10kv和低压得数据 或者使用深拷贝第一次加载后在这拿来赋值新得变量用
      // tenkvSelect tenwireSelect是暂时定义得假数据
      switch (type) {
        case 0:
          this.proInfo.basicHeader = [
            { key: '地形' }, { key: '比例' }
          ]
          this.dxzSelectVis = true
          forEachSetDsat(acceptArr, this.proInfo.terrainSelect)
          console.log(this.proInfo.terrainSelect)
          this.proInfo.basicSelectArr = this.proInfo.terrainSelect
          break
        case 1:
          this.dxzSelectVis = true
          this.proInfo.basicHeader = [
            { key: '地质' }, { key: '比例' }
          ]
          forEachSetDsat(acceptArr, this.proInfo.geologySelect)
          this.proInfo.basicSelectArr = this.proInfo.geologySelect
          // 通过遍历回显复选框中得值
          break
        case 2:
          this.dxzSelectVis = true
          this.proInfo.basicHeader = [
            { key: '运距类型' }, { key: '运距长度' }
          ]
          forEachSetDsat(acceptArr, this.proInfo.longDisSelect)
          this.proInfo.basicSelectArr = this.proInfo.longDisSelect
          break
        case 3:
          this.qmpcSelectVis = true
          break
        case 4:
          this.smpcSelectVis = true
          break
      }

      function forEachSetDsat(newArr, oldArr) {
        for (const j in newArr) {
          for (const k in oldArr) {
            if (newArr[j].key === oldArr[k].key) {
              oldArr[k].value = newArr[j].value
            }
          }
        }
      }
    },
    /**
       * 查询杆号牌
       */
    getGhpSelect() {
      const that = this
      apiget(`/sys-select/search/${'yhg'}`).then(function(res) {
        for (const j in res.data) {
          res.data[j].checked = false
        }
        that.lineInfo.polePlateRodSelect = res.data
        that.proInfo.addProParam.polePlateRod = res.data[0].sname
      })
    },
    /**
       * 查询驱鸟器
       */
    getQnqSelect() {
      const that = this
      apiget(`/sys-select/search/${'qnq'}`).then(function(res) {
        for (const j in res.data) {
          res.data[j].checked = false
        }
        that.lineInfo.birdDeviceSelect = res.data
        that.proInfo.addProParam.birdDevice = res.data[0].sname
      })
    },
    /**
       * 查询驱鸟风车
       */
    getQnfcSelect() {
      const that = this
      apiget(`/sys-select/search/${'qnfc'}`).then(function(res) {
        for (const j in res.data) {
          res.data[j].checked = false
        }
        that.lineInfo.birdWindmillSelect = res.data
        that.proInfo.addProParam.birdWindmill = res.data[0].sname
      })
    },
    /**
       * 查询接地引线
       */
    getJdyxSelect() {
      const that = this
      apiget(`/sys-select/search/${'jdyx'}`).then(function(res) {
        for (const j in res.data) {
          res.data[j].checked = false
        }
        that.lineInfo.groundLeadSelect = res.data
        that.proInfo.addProParam.groundLead = res.data[0].sname
      })
    },
    /**
       * 查询电缆管枕
       */
    getDlglSelect() {
      const that = this
      apiget(`/sys-select/search/${'dlgl'}`).then(function(res) {
        for (const j in res.data) {
          res.data[j].checked = false
        }
        that.lineInfo.lineTubeSelect = res.data
        that.proInfo.addProParam.lineTube = res.data[0].sname
      })
    },
    /**
       * 查询警示牌
       */
    getDlgzSelect() {
      const that = this
      apiget(`/sys-select/search/${'jsp'}`).then(function(res) {
        for (const j in res.data) {
          res.data[j].checked = false
        }
        that.lineInfo.linePlacardSelect = res.data
        that.proInfo.addProParam.linePlacard = res.data[0].sname
      })
    },
    /**
       * 查询标志桩
       */
    getBzzSelect() {
      const that = this
      apiget(`/sys-select/search/${'bzz'}`).then(function(res) {
        for (const j in res.data) {
          res.data[j].checked = false
        }
        that.lineInfo.markPileSelect = res.data
        that.proInfo.addProParam.markPile = res.data[0].sname
      })
    },
    /**
       * 查询标志桩
       */
    getgbSelect() {
      const that = this
      apiget(`/sys-select/search/${'gb'}`).then(function(res) {
        for (const j in res.data) {
          res.data[j].checked = false
        }
        that.lineInfo.coverPlateSelect = res.data
        that.proInfo.addProParam.coverPlate = res.data[0].sname
      })
    },
    /**
       * 查询防雷方案
       */
    getFlfaSelect() {
      const that = this
      apiget(`/sys-select/search/${'flfa'}`).then(function(res) {
        for (const j in res.data) {
          res.data[j].checked = false
        }
        that.lineInfo.defineRaySelect = res.data
        that.proInfo.addProParam.defineRay = res.data[0].sname
      })
    },
    /**
       * 向电缆井参数表格当中添加项
       */
    addLineObj() {
      const obj = {
        minNum: 4,
        maxNum: 12,
        strightwell: '3.0*1.6*1.9砖混', // 直线井
        conrnerwell: '（6~10）*1.7*1.5砖砌盖板开启式', // 转角井
        mitsui: '6*1.7*1.5（砖砌）', // 三通井
        crosswell: '6*(1.7-1.7)*1.5(砖砌）', // 四通井
        checked: false
      }
      this.lineInfo.overLineArr.push(obj)
    },
    /**
       * 删除复选框得值
       */
    removeLine() {
      const data = this.lineInfo.overLineArr
      const isChecked = data.some(function(it) {
        return it.checked === true
      })
      if (isChecked) {
        for (const j in data) {
          if (data[j].checked) {
            data.splice(j, 1)
          }
        }
      } else {
        Toast({
          // duration: 0, // 持续展示
          message: '当前没有选中数据'
        })
      }
    }

  }
}

</script>

<style lang="scss" scoped>
  @import "./../../../styles/projectSetting.css";
  ::v-deep {
    .el-form-item__label{
      width: 120px !important;
    }
    .van-field__label{
      color: #646566;
      text-align: left;
      word-wrap: break-word;
      font-weight: 400;
      font-family: 宋体;
      color: #000 !important;
      font-weight: 600;
      margin-left: 10px;
      text-align: left !important;
    }
    .el-tabs__content {
      height: calc(100vh - 17rem);
      overflow-y: scroll;
    }
    .el-dialog__body{
      .setting-diaselect{
        max-height: 36rem;
        overflow-y: scroll;
      }
    }
  }
  .settingForm{
    .el-form-item{
      display: flex;
    }
  }
</style>

