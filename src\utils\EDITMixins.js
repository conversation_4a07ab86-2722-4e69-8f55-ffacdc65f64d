import {
  importFileProjectInfo,
  downLoadTemplate,
  removeProject,
  importOtherReqInfo,
  importReqInfo,
  getOtherReqInfo,
  getReqInfo,
  reqinfoDown,
} from '@/api/api'
import { excelFieldMappingOther,excelFieldMappingReq } from './gclDate'
export const EDITMixins = {
  data() {
    return {
      removeList: [],
      getRowKeys(row) {
        return row.taskID
      },
      ProjectVolumeDialog: false,
      excelFieldMappingOther:[],
      ProjectVolumeForm: {
  
      },
      excelFieldMappingReq:{}
    }
  },
  mounted() {
    this.excelFieldMappingOther=excelFieldMappingOther
    this.excelFieldMappingReq=excelFieldMappingReq
  },
  watch: {},
  methods: {
    ProjectVolumeView(data, type) {
      console.log(type, data)
      let params = { taskid: data.taskID }
      this.ProjectVolumeDialog = true
      if (type == 'fxq') {
        getOtherReqInfo(params).then((res) => {
          console.log(res)
          this.ProjectVolumeForm=res.data.result
        })
      }else{
        getReqInfo(params).then((res) => {
          console.log(res)
          this.ProjectVolumeForm=res.data.result
        })
      }
    },
    uploadFilesReqInfo(stage, file) {
      console.log(file)

      const formData1 = new FormData()

      formData1.append('file', file.file)
      if (stage != '需求编制') {
        formData1.append('stage', stage)
        // 上传非需求工程量
        importOtherReqInfo(formData1).then((res) => {
          console.log(res, '上传')
          if (res.data.result == 'success') {
            this.$message.success('上传成功')
          }
        })
      } else {
        // 上传需求工程量
        importReqInfo(formData1).then((res) => {
          console.log(res, '上传')
          if (res.data.result == 'success') {
            this.$message.success('上传成功')
          }
        })
      }
    },
    removeProj(row) {
      console.log(row)

      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'confirmBtn',
        cancelButtonClass: 'cancleBtn',
        type: 'warning',
      })
        .then(() => {
          removeProject({ taskid: row.taskID }).then((res) => {
            console.log('删除成功', res)
            if (res.data.result == 'success') {
              this.$message.success('删除成功')
              this.getList()
            }
          })
        })
        .catch(() => {})
    },
    handleSelectionChange(row) {
      console.log(row)
      this.removeList = row
    },
    uploadFilesProject(files) {
      let tempUrl = ''
      const loading = this.$loading({
        lock: true,
        text: '上传中',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)',
      })
      const chunkArr = this.creatChunk(files.file, 10 * 1024 * 1024)
      let chunkIndex = 0
      const uploadNextChunk = () => {
        if (chunkIndex >= chunkArr.length) {
          console.log('All chunks uploaded successfully')
          loading.close()
          return
        }
        const file = chunkArr[chunkIndex]

        const formData1 = new FormData()
        formData1.append('design', '7dd4112b9020646de0530402a7c0b8dd')
        formData1.append('file', file)
        formData1.append('chunkNumber', chunkIndex)
        formData1.append('totalChunks', chunkArr.length)
        formData1.append('fileName', files.file.name)
        formData1.append('tempDirectory', tempUrl)
        console.log(`Uploading chunk ${chunkIndex} of ${chunkArr.length}`)
        importFileProjectInfo(formData1)
          .then((res) => {
            console.log(res, chunkIndex, '测试返回结果')

            if (chunkIndex === 0 && res) {
              tempUrl = res.data.result
              console.log('Temp URL received:', tempUrl)
            }
            if (['上传成功', 'success'].includes(res.data.result)) {
              this.$message.success('上传成功')
              loading.close()
              this.getList()
            }
            chunkIndex++
            uploadNextChunk()
          })
          .catch((error) => {
            console.error('Error uploading chunk:', error)
            this.$message.warning('上传失败，请重试')
            loading.close()
          })
      }
      uploadNextChunk()

      // const formData = new FormData()
      // formData.append('file', files.file)
      // formData.append('design', this.form.csmc)
      // // formData.append('design', "7dd4112b9020646de0530402a7c0b8dd")
      // importFileProjectInfo(formData).then(res => {
      //   this.$message.success('导入成功')
      //   console.log(res)
      // })
    },
    beforeDesignUpload(file) {
      let fileName = file.name
      let pos = fileName.lastIndexOf('.')
      let lastName = fileName.substring(pos, fileName.length)
      const ext = lastName.toLowerCase()
    },
    async downLoadTemplates() {
      let res = await downLoadTemplate()
      const blob = new Blob([res.data])
      let link = document.createElement('a')
      link.download = '模板.xlsx'
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)
    },
    async downLoadTemplateReq(type) {
      let params = { key: type }
      let res = await reqinfoDown(params)
      const blob = new Blob([res.data])
      let link = document.createElement('a')
      link.download = '工程模板.xlsx'
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)
    },
    removeListPorj() {
      if (this.removeList.length <= 0)
        return this.$message.warning('请选中数据')
      let newArr = []
      for (let j in this.removeList) {
        newArr.push(this.removeList[j].taskID)
      }
      removeProject({ taskid: newArr.join(',') }).then((res) => {
        console.log('删除成功', res)
        if (res.data.result == 'success') {
          this.$message.success('删除成功')
          this.getList()
          this.removeList.forEach((row) => {
            this.$refs.table.toggleRowSelection(row, false)
          })
          this.removeList = []
        }
      })
    },
    creatChunk(file, chunkSize) {
      const result = []

      for (let i = 0; i < file.size; i += chunkSize) {
        result.push(file.slice(i, i + chunkSize))
      }
      return result
    },
  },
}
