import {
	getUrlValue
} from '../utils/global'
let routes = [{
	path: '/',
	meta: {
		title: '设计管理',
		icon: 'el-icon-goods'
	},
	redirect: '/projecthz'
},
//项目汇总
{
	path: '/projecthz',
	name: 'projecthz',
	meta: {
		title: '项目汇总'
	},
	component: () => import('@/views/projecthz/index.vue')
},
{
	path: '/403',
	name: '403',
	component: () => import('@/views/wsqPage/wsqPage.vue')
},
{
	path: '/index',
	name: 'index',
	component: () => import('@/views/index/index.vue')
},
// 结算申报
{
	path: '/settlementsb',
	name: 'settlementsb',
	meta: {
		title: '结算申报'
	},
	component: () => import('@/views/settlementsb/index.vue')
},
//结算审核
{
	path: '/settlementsh',
	name: 'settlementsh',
	meta: {
		title: '结算审核'
	},
	component: () => import('@/views/settlementsh/index.vue')
},
//结算指标
{
	path: '/settlementtj',
	name: 'settlementtj',
	meta: {
		title: '结算指标'
	},
	component: () => import('@/views/settlementtj/index.vue')
},
//投资规模
{
	path: '/InvestmentGM',
	name: 'InvestmentGM',
	meta: {
		title: '投资规模'
	},
	component: () => import('@/views/InvestmentGM/index.vue')
}, //项目评审 ---需求项目
{
	path: '/projectReview/RequirementProject',
	name: '/projectReview/RequirementProject',
	meta: {
		title: '需求项目',
		keepAlive:true,
	},
	component: () => import('@/views/projectReview/RequirementProject.vue')
}, //项目评审 ---可研项目
{
	path: '/projectReview/KYProject',
	name: '/projectReview/KYProject',
	meta: {
		title: '可研项目'
	},
	component: () => import('@/views/projectReview/KYProject.vue')
}, //项目评审 ---初设项目   完整性分析 合规性分西 标准化分析    标准化里面添加 标准物料应用统计管理  典型设计应用统计管理      标准设备应用统计管理
{
	path: '/projectReview/CSProject',
	name: '/projectReview/CSProject',
	meta: {
		title: '初设项目',
		keepAlive:true,
	},
	component: () => import('@/views/projectReview/CSProject.vue')
}, //项目评审 ---可研初设一体化项目
{
	path: '/projectReview/KYCSProject',
	name: '/projectReview/KYCSProject',
	meta: {
		title: '可研初设一体化项目'
	},
	component: () => import('@/views/projectReview/KYCSProject.vue')
}, //项目评审 ---施设项目
{
	path: '/projectReview/SSProject',
	name: '/projectReview/SSProject',
	meta: {
		title: '施设项目',
		keepAlive:true,
	},
	component: () => import('@/views/projectReview/SSProject.vue')
}, //项目评审 ---设计变更项目
{
	path: '/projectReview/SJBGProject',
	name: '/projectReview/SJBGProject',
	meta: {
		title: '设计变更项目'
	},
	component: () => import('@/views/projectReview/SJBGProject.vue')
}, //项目代办 ---需求项目
{
	path: '/projectAgent/RequirementProject',
	name: '/projectAgent/RequirementProject',
	meta: {
		title: '需求项目',
		keepAlive:true,
	},
	component: () => import('@/views/projectAgent/RequirementProject.vue')
}, //项目代办 ---可研项目
{
	path: '/projectAgent/KYProject',
	name: '/projectAgent/KYProject',
	meta: {
		title: '可研项目',
		keepAlive:true,
	},
	component: () => import('@/views/projectAgent/KYProject.vue')
}, //项目代办 ---初设项目
{
	path: '/projectAgent/CSProject',
	name: '/projectAgent/CSProject',

	meta: {
		title: '初设项目',
		keepAlive:true,

	},
	component: () => import('@/views/projectAgent/CSProject.vue')
}, //项目代办 ---可研初设一体化项目
{
	path: '/projectAgent/KYCSProject',
	name: '/projectAgent/KYCSProject',
	meta: {
		title: '可研初设一体化项目'
	},
	component: () => import('@/views/projectAgent/KYCSProject.vue')
}, //项目代办 ---施设项目
{
	path: '/projectAgent/SSProject',
	name: '/projectAgent/SSProject',
	meta: {
		title: '施设项目'
	},
	component: () => import('@/views/projectAgent/SSProject.vue')
}, //项目代办 ---设计变更项目
{
	path: '/projectAgent/SJBGProject',
	name: '/projectAgent/SJBGProject',
	meta: {
		title: '设计变更项目'
	},
	component: () => import('@/views/projectAgent/SJBGProject.vue')
},
// 结算资料归集
{
	path: '/jscgzs',
	name: 'jscgzs',
	meta: {
		title: '结算资料归集'
	},
	component: () => import('@/views/jscgzs/index.vue')
},
// 结算统计
{
	path: '/jstj',
	name: 'jstj',
	meta: {
		title: '结算统计'
	},
	component: () => import('@/views/jstj/index.vue')
},
// 结算计划
{
	path: '/jsjh',
	name: 'jsjh',
	meta: {
		title: '结算计划'
	},
	component: () => import('@/views/jsjh/index.vue')
},
// 结算审核
{
	path: '/zlgj',
	name: 'zlgj',
	meta: {
		title: '资料归集'
	},
	component: () => import('@/views/zlgj/index.vue')
},
// // 用户管理
{
	path: '/user',
	name: 'user',
	meta: {
		title: '用户管理'
	},
	component: () => import('@/views/user/index.vue')
},
//勘察入库----需求项目
{
	path: '/ProjectKC/XQProject',
	name: 'ProjectKC/XQProject',
	meta: {
		title: '需求项目'
	},
	component: () => import('@/views/ProjectKC/XQProject.vue')
},
//勘察入库----可研项目
{
	path: '/ProjectKC/KYProject',
	name: 'ProjectKC/KYProject',
	meta: {
		title: '可研项目'
	},
	component: () => import('@/views/ProjectKC/KYProject.vue')
},
//勘察入库----初设项目  三率指标
{
	path: '/ProjectKC/CSProject',
	name: 'ProjectKC/CSProject',
	meta: {
		title: '初设项目'
	},
	component: () => import('@/views/ProjectKC/CSProject.vue')
},
//角色管理
{
	path: '/roles',
	name: 'roles',
	meta: {
		title: '角色管理'
	},
	component: () => import('@/views/roles/index.vue')
},
// //角色管理
{
	path: '/maps',
	name: 'maps',
	meta: {
		title: '地图页'
	},
	component: () => import('@/views/map/index.vue')
},
// //角色管理
{
	path: '/changemaps',
	name: 'changemaps',
	meta: {
		title: '地图页'
	},
	component: () => import('@/views/map/sourceMap.vue')
},
//清单计价
{
	path: '/qdjj',
	name: 'qdjj',
	meta: {
		title: '清单计价'
	},
	children: [
		{
			path: '/qdjj/projectinfo', name: 'projectinfo',
			component: () => import('@/views/qdjjmain/project/index.vue')
		},
		{
			path: '/qdjj/jsinfo', name: 'jsinfo',
			component: () => import('@/views/qdjjmain/jsinfo/index.vue')
		},
		{
			path: '/qdjj/jzinfo', name: 'jzinfo',
			component: () => import('@/views/qdjjmain/jzgc/index.vue')
		},
		{
			path: '/qdjj/azinfo', name: 'azinfo',
			component: () => import('@/views/qdjjmain/azgc/index.vue')
		},
		{
			path: '/qdjj/ccgc', name: 'ccgc',
			component: () => import('@/views/qdjjmain/ccgc/index.vue')
		},
		{
			path: '/qdjj/qtfy', name: 'qtfy',
			component: () => import('@/views/qdjjmain/qtfy/index.vue')
		},
		{
			path: '/qdjj/jrg', name: 'jrg',
			component: () => import('@/views/qdjjmain/jrg/index.vue')
		}
		,
		{
			path: '/qdjj/cgsc', name: 'cgsc',
			component: () => import('@/views/qdjjmain/cgsv/index.vue')
		}
		,
		{
			path: '/qdjj/depz', name: 'depz',
			component: () => import('@/views/qdjjmain/depz/index.vue')
		}
	],
	component: () => import('@/views/qdjjmain/index.vue')
},
{
	path: "/basicParameter",
	name: "basicParameter",
	meta: {
		title: "基础参数配置",
	},
	component: () => import("@/views/basicParameter/basicParameter.vue"),
},
{
	path: "/resourcelibrary",
	name: "resourcelibrary",
	meta: {
		title: "图纸信息管理",
	},
	component: () => import("@/views/resourcelibrary/index.vue"),
},
{
	path: "/materialsAmod",
	name: "materialsAmod",
	meta: {
		title: "标准化信息管理",
	},
	component: () => import("@/views/resourcelibrary/materialsAmod.vue"),
},
{
	path: "/autoChangeType",
	name: "autoChangeType",
	meta: {
		title: "自动选型管理",
	},
	component: () => import("@/views/resourcelibrary/autoChangeType.vue"),
},

{
	path: "/onlineDesign",
	name: "/onlineDesign",
	meta: {
		title: "在线设计",
	},
	component: () => import("@/views/onlineDesign/onlineDesign.vue"),
},
{
	path: "/tagManage",
	name: "tagManage",
	meta: {
		title: "标注管理",
	},
	component: () => import("@/views/tagManage/index.vue"),
},
{
	path: "/maintainPage",
	name: "maintainPage",
	meta: {
		title: "运维页面",
	},
	component: () => import("@/views/maintainPage/index.vue"),
},
{
	path: "/taskReception/CSProject",
	name: "CSProjects",
	meta: {
		title: "初设任务接收页",
	},
	component: () => import("@/views/taskReception/CSProject.vue"),
},
{
	path: "/taskReception/KYProject",
	name: "KYProjects",
	meta: {
		title: "可研任务接收页",
	},
	component: () => import("@/views/taskReception/KYProject.vue"),
},
{
	path: "/taskReception/RequirementProject",
	name: "RequirementProjects",
	meta: {
		title: "需求任务接收页",
	},
	component: () => import("@/views/taskReception/RequirementProject.vue"),
},
{ //其他路径跳转到首页
	path: '*',
	redirect: {
		name: 'projecthz'
	}
}
]
// eslint-disable-next-line no-unused-vars
const routers = (
	function () {
		const routersFirst = routes[0]
		sessionStorage.setItem('bhneToken', getUrlValue('data'))
		sessionStorage.setItem('bhnePageType', getUrlValue('type'))
		console.log(getUrlValue('type'),'路由类型');
		if (getUrlValue('type') !== undefined) {
			switch (getUrlValue('type')) {
				case 'jstj':
					routersFirst.redirect = '/settlementtj'
					break
				case 'jssb':
					routersFirst.redirect = '/settlementsb'
					break
				case 'jssh':
					routersFirst.redirect = '/settlementsh'
					break
				case 'jsjh':
					routersFirst.redirect = '/jsjh'
					break
				case 'jstjs':
					routersFirst.redirect = '/jstj'
					break
				case 'zjfx':
					routersFirst.redirect = '/zlgj'
					break
				case 'xqdb':
					routersFirst.redirect = '/projectAgent/RequirementProject'
					break
				case 'kydb':
					routersFirst.redirect = '/projectAgent/KYProject'
					break
				case 'csdb':
					routersFirst.redirect = '/projectAgent/CSProject'
					break
				case 'ssdb':
					routersFirst.redirect = '/projectAgent/SSProject'
					break
				case 'kycsdb':
					routersFirst.redirect = '/projectAgent/KYCSProject'
					break
				case 'sjbgdb':
					routersFirst.redirect = '/projectAgent/SJBGProject'
					break
				case 'xqps':
					routersFirst.redirect = '/projectReview/RequirementProject'
					break
				case 'kyps':
					routersFirst.redirect = '/projectReview/KYProject'
					break
				case 'csps':
					routersFirst.redirect = '/projectReview/CSProject'
					break
				case 'ssps':
					routersFirst.redirect = '/projectReview/SSProject'
					break
				case 'kycsps':
					routersFirst.redirect = '/projectReview/KYCSProject'
					break
				case 'sjbgps':
					routersFirst.redirect = '/projectReview/SJBGProject'
					break
				case 'xq':
					routersFirst.redirect = '/projecthz'
					break
				case 'cs':
					routersFirst.redirect = '/projecthz'
					break
				case 'ky':
					routersFirst.redirect = '/projecthz'
					break
				case 'xqkc':
					routersFirst.redirect = '/ProjectKC/XQProject'
					break
				case 'kykc':
					routersFirst.redirect = '/ProjectKC/KYProject'
					break
				case 'cskc':
					routersFirst.redirect = '/ProjectKC/CSProject'
					break
				case 'roles':
					routersFirst.redirect = '/roles'
					break
				case 'user':
					routersFirst.redirect = '/user'
					break
				case 'ywym':
					routersFirst.redirect = '/maintainPage'
					break
				case 'csjs':
					routersFirst.redirect = '/taskReception/CSProject'
					break
				case 'kyjs':
					routersFirst.redirect = '/taskReception/KYProject'
					break
				case 'xqjs':
					routersFirst.redirect = '/taskReception/RequirementProject'
					break
			}
		}
	}
)()
export default routes
