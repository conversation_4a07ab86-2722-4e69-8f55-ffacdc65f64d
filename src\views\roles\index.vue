<template>
  <div class="index flex-c h100">
    <!-- 标题 -->
    <div style="height:100vh;width:100vw;background:#fff;  position: absolute;left:0;top:0;z-index:99" v-show="!showPage"></div>
    <div class="main-header">角色管理</div>
    <div class="driver"></div>
    <div class="cp-tableInfo">
      <div class="cp-btns">
        <div class="cp-handleBtn" @click="Add">
          <span class="cp-texts">
            <span style="margin-left: 5px">
              <i class="el-icon-folder-add"></i>
              新增
            </span>
          </span>
        </div>
      </div>
    </div>
    <div class="tablesArea">
      <el-table :data="tableData" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }" :height="tableHeight"
        highlight-current-row style="width: 98%; margin: 0px 16px 0 16px; margin-top: 10px">
        <el-table-column type="index" width="60" label="序号" align="center">
        </el-table-column>
        <el-table-column prop="rname" label="角色名称" align="center" min-width="150">
        </el-table-column>
        <el-table-column label="操作" align="center" width="280px">
          <template slot-scope="scope">
            <span class="directiveHandle">
              <div class="el-buttonStyle">
                <span @click="modify(scope.row)">
                  修改
                </span>
              </div>
              <span class="el-buttonDriver">|</span>
              <div class="el-buttonStyle">
                <span @click="Det(scope.row)">
                  删除
                </span>
              </div>
              <span class="el-buttonDriver">|</span>
              <div class="el-buttonStyle">
                <span @click="Permission(scope.row)">
                  权限配置
                </span>
              </div>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog title="角色信息" :visible.sync="dialogFormVisible" width="25%">
      <el-form :model="form" class="demo-ruleForm" :rules="rules">
        <el-form-item label="角色名称" prop="rname">
          <el-input v-model="formAdd.rname"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" class="blue-btn" @click="dialogBtn">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="角色信息" :visible.sync="dialogFormVisibleAdd" width="30%">
      <el-form :model="formAdd" class="demo-ruleForm" :rules="rules" label-width="100px" :style="{ display: 'flex' }">
        <el-form-item label="角色名称" prop="rname">
          <el-input v-model="formAdd.rname" placeholder="请输入角色名称"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisibleAdd = false">取 消</el-button>
        <el-button type="primary" class="blue-btn" @click="dialogBtnAdd">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="角色信息" class="showAll_dialog" :visible.sync="dialogPZ" width="30%">
      <div style="height: 60vh; overflow-y: auto">
        <el-tree :data="data" :props="defaultProps" style="max-height: 70vh !important" show-checkbox node-key="id"
          default-expand-all @check-change="handleCheckChange" :default-checked-keys="checkedId">
        </el-tree>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closePz">取 消</el-button>
        <el-button type="primary" class="blue-btn" @click="dialogPZBtn">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRoleList, getRoleDel, getRoleTreeList, resquest, selectUserList, addRoleinfoSave, editRoleinfoSave, saveRole,getuserInfo  } from '@/api/api'
import axios from 'axios'
export default {
  data () {
    return {
      url: resquest,
      tableHeight: 0,
      form: {
        id: '',
        rname: '',
      },
      formAdd: {
        id: '',
        rname: '',
      },
      xgsOptions: [],
      pageSize: [5, 10, 20, 50, 100], //分页页数
      total: 0, //总共页数
      tableData: [],
      dialogFormVisible: false,
      dialogFormVisibleAdd: false,
      dialogPZ: false,
      data: [],
      defaultProps: {
        children: 'children',
        label: 'text',
      },
      checkedId: [],
      listID: [],
      Id: '',
      rules: {
        rname: [{ required: true, message: '请输入角色信息', trigger: 'blur' }],
      },
      showPage:false
    }
  },
  mounted () {
    this.setTablesHeight()
     const token = sessionStorage.getItem('bhneToken')
     const pageType = sessionStorage.getItem('bhnePageType')
    getuserInfo(token).then((res) => {
         if(Object.keys(res.data.result).length==0){
        this.$message.warning("获取用户信息失败，请重新进入页面")
      }
       let menuTypes = res.data.result.zmenu.split(',')

      if (menuTypes.includes(pageType)) {
        console.log("允许进入");
        this.showPage=true
      }else{
          console.log("无权限")
         this.$message.warning("无权限")
         this.showPage=false
      }
      this.form.csmc = res.data.result.USERID
      this.userType =   res.data.result.rank
      this.form.dwmc = res.data.result.DWMC
      this.form.rank = res.data.result.rank


      this.getList()
    })
    const that = this
    window.onresize = function () {
      that.setTablesHeight()
    }
  },
  methods: {
    getList () {
      getRoleList()
        .then((res) => {
          this.tableData = res.data.rows
        })
        .catch(() => { })
    },
    // ↓在methods里面(窗体大小改变计算表格高度)
    setTablesHeight () {
      this.$nextTick(() => {
        const tablesAreaHeight = document
          .getElementsByClassName('driver')[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        this.tableHeight = windowsAreaHeight - tablesAreaHeight - 100
      })
    },
    // 修改
    modify (row) {
      console.log(row)
      this.dialogFormVisible = true
      this.formAdd.id = row.id
      this.formAdd.rname = row.rname
    },
    // 角色下拉点击
    roleNameId (val) {
      this.form.rname = val
      this.formAdd.rname = val
    },
    // 新增
    Add () {
      this.dialogFormVisibleAdd = true
      this.formAdd.id = ''
      this.formAdd.rname = ''
      selectUserList().then(res => {
        this.xgsOptions = res.data.rows
      })
      // axios.get(this.url + '/t-dtf-roleinfo/list').then((res) => {
      //   this.xgsOptions = res.data.rows
      // })
    },
    // 删除
    Det (row) {
      this.$confirm('此操作将永久删除, 是否确定?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'confirmBtn',
        cancelButtonClass: 'cancleBtn',
        type: 'warning',
      })
        .then(() => {
          getRoleDel(row.id)
            .then((res) => {
              console.log(res.data.message)
              if (res.data.message === '成功') {
                this.$message({
                  message: '删除成功!',
                  type: 'success',
                })
                this.getList()
              } else {
                this.$message.error('删除失败!')
              }
            })
            .catch(() => { })
        })
        .catch(() => { })
    },
    //新增保存按钮
    dialogBtnAdd () {
      const params = {
        rname: this.formAdd.rname,
      }
      console.log(params)
      addRoleinfoSave(params).then(res => {
        if (res.data.message === '成功') {
          this.$message({
            message: '新增成功!',
            type: 'success',
          })
          this.formAdd.rname = ''
          this.getList()
        } else {
          this.$message.error('新增失败!')
        }
      })
      // axios.post(this.url + '/t-dtf-roleinfo/add', params).then((res) => {

      // })
      this.dialogFormVisibleAdd = false
    },
    // 修改保存按钮
    dialogBtn () {
      const params = {
        id: this.formAdd.id,
        rname: this.formAdd.rname,
      }
      editRoleinfoSave(params).then(res => {
        if (res.data.message === '成功') {
          this.$message({
            message: '修改成功!',
            type: 'success',
          })
          this.getList()
        } else {
          this.$message.error('修改失败!')
        }
      })

      this.dialogFormVisible = false
    },
    closePz(){
      this.dialogPZ = false
      console.log(this.checkedId);
      this.checkedId=[]
    },
    // 保存权限
    dialogPZBtn () {
      if (this.checkedId.length > 0) {
        this.checkedId = Array.from(new Set(this.checkedId))
        const params = {
          roleid: this.Id,
          data: this.checkedId,
        }
        saveRole(params).then(res => {
          console.log(res)
          if (res.data.message === '成功') {
            this.$message({
              message: '保存成功!',
              type: 'success',
            })
            this.dialogPZ = false
            this.getList()
            this.checkedId=[]
          } else {
            this.$message.error('保存失败!')
          }
        })
        this.dialogPZ = true
      }
    },
    collectCheckedIds (data, isTopLevel = true) {
      data.forEach(item => {
        if (isTopLevel && item.children && item.children.length > 0) {
          // 检查所有子节点的 checked 值是否都为 true
          const allChildrenChecked = item.children.every(child => child.checked)
          if (allChildrenChecked) {
            this.checkedId.push(item.id)
          }
          // 递归处理子节点
          this.collectCheckedIds(item.children, false)
        } else {
          // 对于第二层及以下的数据，只要 checked 值为 true，就将该项的 id 加入 this.checkedId 数组
          if (item.checked) {
            this.checkedId.push(item.id)
          }
          // 递归处理子节点
          if (item.children && item.children.length > 0) {
            this.collectCheckedIds(item.children, false)
          }
        }
      })
    },
    // 权限配置
    Permission (row) {
      this.Id = row.id
      this.dialogPZ = true
      getRoleTreeList({ id: row.id })
        .then((res) => {
          this.data = res.data.data
          this.collectCheckedIds(res.data.data)
        })
        .catch(() => { })
    },
    handleCheckChange (data,checked) {
      if(checked){
        if(!this.checkedId.includes(data.id)){
          this.checkedId.push(data.id)
        }
      }else{
        const index =this.checkedId.indexOf(data.id)
        if(index>-1){
          this.checkedId.splice(index,1)
        }
      }
      console.log(this.checkedId)
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-form {
  .el-form-item {
    display: flex;

    .el-form-item__content {
      margin-left: 0 !important;
    }
  }

  .el-form-item--small .el-form-item__label {
    width: 110px !important;
  }
}

.cp-tableInfo {
  background: #ffffff;

  .cp-checkNav {
    width: 400px;
    background: #f5f6fa;
    padding-left: 32px;
    height: 36px;
  }

  .cp-btns {
    background: #ffffff;
    margin: 16px 0 16px 16px;
    display: flex;
    flex: 1;
    align-items: center;
    padding-right: 32px;
    justify-content: flex-start;
    margin-left: 12px;
    color: #7286e8;

    .cp-handleBtn {
      height: 26px;
      font-size: 12px;
      display: flex;
      color: #333333;
      background: #ffffff;
      border-radius: 5px;
      padding: 0 12px;
      align-items: center;
      cursor: pointer;
      border: 1px solid #abbdf7;
    }

    .cp-handleBtn:nth-child(2) {
      margin-left: 10px;
    }

    .cp-drawer {
      margin: 0 10px;
      color: #ccc;
    }

    .cp-handleUnBtn:hover {
      color: #7487e4;
      border-color: #7487e4;
    }

    .cp-handleUnBtn:active {
      color: #0c6b66;
      border-color: #0c6b66;
    }

    .cp-handleUnBtn:focus {
      color: #0c6b66;
      border-color: #0c6b66;
    }

    .cp-handleBtn {
      height: 26px;
      font-size: 12px;
      display: flex;
      color: #333333;
      background: #ffffff;
      border-radius: 5px;
      padding: 0 12px;
      align-items: center;
      cursor: pointer;
      border: 1px solid #abbdf7;

      .cp-icons {
        margin-right: 4px;

        .iconfont {
          font-size: 12px !important;
        }
      }

      .cp-texts {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        /*padding: 0 5px;*/
      }
    }

    .cp-handleUnBtn {
      height: 26px;
      font-size: 12px;
      display: flex;
      border-radius: 20px;
      border: 1px solid #7487e4;
      align-items: center;
      background-color: #fff;
      padding: 0 12px;
      cursor: pointer;
      color: #7487e4;

      .cp-icons {
        margin-right: 2px;

        .iconfont {
          font-size: 12px !important;
        }
      }
    }
  }
}
::v-deep .el-dialog__body {
  padding: 24px !important;
}
::v-deep .el-dialog__footer {
  padding: 16px 24px !important;
}
</style>
