<template>
  <div class="app-container">
    <div class="">
      <div class="item-content">
        <el-container style="height: 500px; border: 1px solid #eee">
          <el-aside width="200px" style="background-color: rgb(238, 241, 246)">
            <Treeleft v-on:treeclick="treeclick" />
          </el-aside>
          <el-container>
            <el-main>
              <el-table
                :data="tagData"
                v-loading="loading"
                height="300"
                max-height="500"
                align="center"
                class="widthFull"
              >
                <el-table-column label="是否显示" align="center" width="120">
                  <template slot-scope="scope">
                    <el-checkbox v-model="scope.row.IsShow"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column prop="TagName" align="center" label="标注项目">
                </el-table-column>
                <el-table-column label="字体样式" align="center">
                  <template slot-scope="scope">
                    <el-select
                      v-model="scope.row.FontStyle"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in fontStyleData"
                        :key="item.value"
                        :label="item.text"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="字体颜色" align="center">
                  <template slot-scope="scope">
                    <el-select
                      v-model="scope.row.FontColor"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in fontColorData"
                        :key="item.value"
                        :label="item.text"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="字体高度" align="center">
                  <template slot-scope="scope">
                    <el-input
                      v-model="scope.row.FontHeight"
                      placeholder="字体高度"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="角度" width="80" align="center">
                  <template slot-scope="scope">
                    <el-input
                      v-model="scope.row.Angle"
                      placeholder="角度"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="距离" width="80" align="center">
                  <template slot-scope="scope">
                    <el-input
                      v-model="scope.row.Distance"
                      placeholder="距离"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="AttachmentPoint" label="标注对齐方式" align="center">
                  <template slot-scope="scope">
                    <el-select
                      v-model="scope.row.AttachmentPoint"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in AttachmentPointData"
                        :key="item.value"
                        :label="item.text"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </template>
                </el-table-column>
              </el-table>
            </el-main>
          </el-container>
        </el-container>
      </div>
      <div class="item-content text-center">
        <el-button class="minWidth100">保存 </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import Treeleft from './components/Treeleft.vue'
export default {
  name: 'TagManage',
  components: { Treeleft },
  data() {
    return {
      name: '',
      form: {
        selectVersion: 1,
      },
      loading: true,
      tagData: [],
      fontColorData: [
        { value: 'ByBlock', text: '红色' },
        { value: '#FFFFFF', text: '白色' },
        { value: '#0000FF', text: '蓝色' },
        { value: '#FF00FF', text: '洋红色' },
        { value: '#FFFF00', text: '黄色' },
        { value: '#00FF00', text: '绿色' },
      ],
      fontStyleData: [
        { value: '宋体', text: '宋体' },
        { value: '仿宋', text: '仿宋' },
        { value: '微软雅黑', text: '微软雅黑' },
        { value: '新宋体', text: '新宋体' },
        { value: '楷体', text: '楷体' },
        { value: '黑体', text: '黑体' },
      ],
      AttachmentPointData: [
        { value: 'TopLeft', text: '左上' },
        { value: 'TopCenter', text: '中上' },
        { value: 'TopRight', text: '右上' },
        { value: 'MiddleLeft', text: '左中' },
        { value: 'MiddleCenter', text: '正中' },
        { value: 'MiddleRight', text: '右中' },
        { value: 'BottomLeft', text: '左下' },
        { value: 'BottomCenter', text: '中下' },
        { value: 'BottomRight', text: '右下' },
      ],
    }
  },
  mounted() {},
  created() {
    this.loading = false
  },
  methods: {
    treeclick(tags) {
      this.tagData = tags
    },
  },
}
</script>

<style scoped lang="scss">
.app-container {
  background-color: #fff;
}
.widthFull {
  width: 100%;
}
aside {
  padding: 8px;
}
.el-main {
  padding: 8px;
}
.content-title {
  position: relative;
  padding-left: 10px;
  color: #526ade;
}
.content-title::after {
  content: '';
  position: absolute;
  left: 0;
  top: 50%; /* 将竖杠放置在元素的垂直中心位置 */
  transform: translateY(-50%);
  width: 5px; /* 竖杠的宽度 */
  height: 17px; /* 竖杠的高度 */
  background-color: #526ade; /* 竖杠的颜色 */
  border-radius: 5px; /* 添加弧度 */
  content: '';
}
::v-deep.widthFull .cell {
  font-size: 14px !important;
}

.text-center {
  display: flex;
  height: 70px;
  align-items: center;
  justify-content: center;
}
</style>
