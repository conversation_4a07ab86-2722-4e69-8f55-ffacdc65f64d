/*
 * @Description: 描述
 * @version:
 * @Author: feng<PERSON><PERSON><PERSON>@gmail.com
 * @Date: 2023-08-25 15:42:40
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-11-04 16:12:49
 * @FilePath: \slSecondWeb\src\utils\request.js
 */

import axios from "axios"
import { sm2, sm3,sm4 } from "sm-crypto"
import { Message } from "element-ui"

function sortKeys(obj){
let keys= Object.keys(obj).sort()
let newObj={}
keys.forEach((key)=>{
  newObj[key]=obj[key]
})
return newObj
}
/**
 * axios 实例
 * */

const axiosInstance = axios.create({
  // timeout: 1000 * 60,
  baseURL: "",
})
/**
 * @description: 请求拦截器
 * @param {*}
 * @return {*}
 */
axiosInstance.interceptors.request.use(
  (config) => {
    // 浏览器指纹ID
    console.warn(config, '传参')
    const browserFingerprint = sessionStorage.getItem("browserFingerprint")
    // getTime接口返回的安全数据
    let safetyData = sessionStorage.getItem("bhxnsafetyData")
    // let safetyData = sessionStorage.getItem("wbSafetyData")
    if (safetyData) {
      safetyData = JSON.parse(safetyData)
      //1. 请求头中添加浏览器指纹ID属性

      config.headers.zwId =
        "04" + sm2.doEncrypt(browserFingerprint, safetyData.flagfour)
      if (process.env.VUE_APP_API_isHasprofessionalType == '1') {
        // 2.请求头中添加防重放属性
        config.headers.antiReplayId =
          "04" +
          sm2.doEncrypt(
            `${new Date().getTime() + safetyData.diffeValue}${sm3(process.env.VUE_APP_API_webUrl + config.url.split("tygztsp")[1] + (config.data ? JSON.stringify(config.data) : "")
            )}-FGF-${safetyData.flagthr}`,
            safetyData.flagfour
          )
          // if(config.headers['Content-Type']=='multipart/form-data'){
          //   config.headers.fdcheck='file'
          //   let fileData=config.data.get('file')
          //   let file={}
          //   file.file=fileData.name
          //   file.fdFileSize=fileData.size
          //   let data=sortKeys(file)
          //   let arr =[]
          //   for(let key in data){
          //     if(data.hasOwnProperty(key)){
          //       arr.push(`${key}=${data[key]}`)
          //     }
          //   }
          //   let url=process.env.VUE_APP_API_webUrl + config.url.split("tygztsp")[1]
          //   config.headers.antiReplayId =
          //   "04" +
          //   sm2.doEncrypt(
          //     `${new Date().getTime() + safetyData.diffeValue}${sm3( url+ (arr.length !=0?`{${arr.toString()}}`:'')
          //     )}-FGF-${safetyData.flagthr}`,
          //     safetyData.flagfour
          //   )
          //   return config
          // }
        // 3.网关标识
        config.headers.professionalType = "distribution" //网关标识
      }
      // 4.获取的票
      config.headers.accessTicket = sessionStorage.getItem("PWYJYBJ-BHXN")
      // 5。入参加密  上传文件和获取ticket接口不需要加密

      if (!config.url.includes("/oauth/getTicket") && !config.noDoDecrypt) {
        config.data &&
          (config.data = {
            param:
              "04" +
              sm2.doEncrypt(JSON.stringify(config.data), safetyData.flagfour),
          })
        config.data && (config.data = JSON.stringify(config.data))
      }
    }


    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

/**
 * @description: 响应拦截器
 * @param {*}
 * @return {*}
 */

axiosInstance.interceptors.response.use(
  (response) => {
    // getTime接口返回的安全数据
    const safetyData = JSON.parse(sessionStorage.getItem("bhxnsafetyData"))
    // const safetyData = JSON.parse(sessionStorage.getItem("wbSafetyData"))
    // 下载的接口不加密
    // let writeList=['downFile','conversionPdf']
    if ( response.config.url.includes('down')||response.config.url.includes('conversionPdf')) {
      return response
    }
    if (!response.config.url.includes("oauth/getTicket")) {
      // console.log(response.config.url);
      response.data &&
        (response.data =
          safetyData && !response.config.noDoDecrypt
            ? sm2.doDecrypt(
              response.data.slice(2, response.data.length),
              safetyData.flagfive
            ) // 返参解密
            : response.data)
    }
    let parseData =
      typeof response.data === "string" && JSON.parse(response.data)
    console.warn(parseData, '返回111')
    return { ...response, data: parseData || response.data }
  },
  (error) => {
    console.warn(error, '返回错误')
    // Message.error('服务器出错请稍后再试!')
    Message({
      message: '服务器出错请稍后再试!',
      offset: "20",
      type: "error"
    })

    return Promise.reject(error)
  }
)

export default axiosInstance
