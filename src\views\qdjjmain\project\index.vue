<template>
  <div>
    <div class="vala-cutLine">
      <div class="vala-cutLineLeft">
        <div class="vala-column" />
        <div class="vala-title">基础参数</div>
      </div>
      <div class="vala-cutLineRight">
        <el-button type="warning" class="inform-btn" @click="btn"
          >保存</el-button
        >
      </div>
    </div>
    <div class="inform-cardArea">
      <div class="inform-basics">
        <el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="inform-input-cont">
                <div class="el-input-prepend">工程名称</div>
                <el-input
                  v-model="parms.dataName"
                  placeholder="请输入内容"
                  class="inform-input1"
                />
              </div>
            </el-col>
            <el-col :span="8">
              <div class="inform-input-cont">
                <div class="el-input-prepend">特殊地区</div>
                <el-select
                  v-model="parms.dataSpecialArrea"
                  class="inform-input1"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in GInformationTS"
                    :key="item.key"
                    :label="item.value"
                    :value="item.key"
                  />
                </el-select>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="inform-input-cont">
                <div class="el-input-prepend">电压等级</div>
                <el-select
                  v-model="parms.dataVoltageGrade"
                  class="inform-input1"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in GInformationDY"
                    :key="item.key"
                    :label="item.value"
                    :value="item.key"
                  />
                </el-select>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="inform-input-cont">
                <div class="el-input-prepend">工程阶段</div>
                <el-input
                  v-model="parms.dataStage"
                  placeholder="请输入内容"
                  disabled=""
                  class="inform-input1"
                />
              </div>
            </el-col>
            <el-col :span="8">
              <div class="inform-input-cont">
                <div class="el-input-prepend">占地类型</div>
                <el-select
                  v-model="parms.dataHoldAreaType"
                  class="inform-input1"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in GInformationZD"
                    :key="item.key"
                    :label="item.value"
                    :value="item.key"
                  />
                </el-select>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="inform-input-cont">
                <div class="el-input-prepend">所属县(区)</div>
                <el-input
                  v-model="parms.dataTheirCounty"
                  placeholder="请输入内容"
                  class="inform-input1"
                /></div
            ></el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="inform-input-cont">
                <div class="el-input-prepend">所属乡(镇)</div>
                <el-input
                  v-model="parms.dataTheirVillage"
                  placeholder="请输入内容"
                  class="inform-input1"
                />
              </div>
            </el-col>
            <el-col :span="8">
              <div class="inform-input-cont">
                <div class="el-input-prepend">地区类型</div>
                <el-select
                  v-model="parms.dataAreaType"
                  class="inform-input1"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in GInformationDQ"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                  />
                </el-select>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="inform-input-cont">
                <div class="el-input-prepend">所属市</div>
                <el-input
                  v-model="parms.dataTheirCity"
                  placeholder="请输入内容"
                  class="inform-input1"
                />
              </div>
            </el-col>
          </el-row>
        </el-row>
      </div>
    </div>

    <!--分割线-->
    <div class="vala-cutLine">
      <div class="vala-cutLineLeft">
        <div class="vala-column" />
        <div class="vala-title">计算参数</div>
      </div>
    </div>
    <!--主要区域-->
    <div class="inform-cardArea">
      <div class="inform-basics">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">工程税率%</div>
              <el-input
                v-model="parms.dataTaxrate"
                oninput="value=value.replace(/[^0-9.]/g,'')"
                placeholder="请输入内容"
                class="inform-input1"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">本期台数(台)</div>
              <el-input
                v-model="parms.dataSetnumber"
                oninput="value=value.replace(/[^0-9.]/g,'')"
                placeholder="请输入内容"
                class="inform-input1"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">社会保险费费率%</div>
              <el-select
                v-model="parms.dataPremiumRate"
                filterable
                allow-create
                default-first-option
                clearable
                placeholder="请输入内容"
                class="inform-input1"
                @change="SelectS"
              >
                <el-option
                  v-for="item in GInformationFL"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">甲供主材配送距离</div>
              <el-input
                v-model="parms.dataMaterialsDistance"
                placeholder="请输入内容"
                class="inform-input1"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">甲供设备配送率%</div>
              <el-input
                v-model="parms.dataFacilityDistance"
                oninput="value=value.replace(/[^0-9.]/g,'')"
                placeholder="请输入内容"
                class="inform-input1"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">住房公积金费率%</div>
              <el-select
                v-model="parms.dataCpfRate"
                filterable
                allow-create
                default-first-option
                clearable
                placeholder="请输入内容"
                class="inform-input1"
                @change="SelectS"
              >
                <el-option
                  v-for="item in GInformationFL"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">工程费用小数位数</div>
              <el-select
                v-model="parms.dataSmallNumber"
                class="inform-input1"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in GInformationWS"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">甲供主材卸车费费率%</div>
              <el-input
                v-model="parms.dataUploadRate"
                oninput="value=value.replace(/[^0-9.]/g,'')"
                placeholder="请输入内容"
                class="inform-input1"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">设备厂家运输距离</div>
              <el-select
                v-model="parms.dataVenderDistance"
                filterable
                allow-create
                default-first-option
                clearable
                class="inform-input1"
                placeholder="请输入内容"
              >
                <el-option
                  v-for="item in GInformationJL"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">甲供主材保管费费率%</div>
              <el-input
                v-model="parms.dataCostodyRate"
                oninput="value=value.replace(/[^0-9.]/g,'')"
                placeholder="请输入内容"
                class="inform-input1"
              />
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!--分割线-->
    <div class="vala-cutLine">
      <div class="vala-cutLineLeft">
        <div class="vala-column" />
        <div class="vala-title">表标参数</div>
      </div>
    </div>
    <!--主要区域-->
    <div class="inform-cardArea">
      <div class="inform-basics">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">工程编号</div>
              <el-input
                v-model="parms.dataNumber"
                placeholder="请输入内容"
                class="inform-input1"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">所属批次</div>
              <el-input
                v-model="parms.dataBatch"
                placeholder="请输入内容"
                class="inform-input1"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">招标人</div>
              <el-input
                v-model="parms.dataTenderee"
                placeholder="请输入内容"
                class="inform-input1"
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">招标法定代表及其授权人</div>
              <el-input
                v-model="parms.dataTendereeAccredit"
                placeholder="请输入内容"
                class="inform-input1"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">投标人</div>
              <el-input
                v-model="parms.dataBidder"
                placeholder="请输入内容"
                class="inform-input1"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">投标法定代表及其授权人</div>
              <el-input
                v-model="parms.dataBidderAccredit"
                placeholder="请输入内容"
                class="inform-input1"
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">工程造价咨询人</div>
              <el-input
                v-model="parms.dataCounselor"
                placeholder="请输入内容"
                class="inform-input1"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">咨询法定代表及其授权人</div>
              <el-input
                v-model="parms.dataCounselorAccredit"
                placeholder="请输入内容"
                class="inform-input1"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">创建人</div>
              <el-input
                v-model="parms.dataLister"
                placeholder="请输入内容"
                class="inform-input1"
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="inform-input-cont">
              <div class="el-input-prepend">创建时间</div>
              <el-input
                v-model="parms.dataStartdDate"
                placeholder="请输入内容"
                class="inform-input1"
              />
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!--分割线-->
    <div class="vala-cutLine">
      <div class="vala-cutLineLeft">
        <div class="vala-column" />
        <div class="vala-title">工程情况</div>
      </div>
    </div>
    <!--主要区域-->
    <div class="inform-cardArea">
      <el-input
        v-model="parms.dataText"
        type="textarea"
        :autosize="{ minRows: 2, maxRows: 4 }"
        placeholder="请输入内容"
      />
    </div>
    <el-dialog
      :title="dialog.title"
      :visible.sync="dialog.Nextvisible"
      width="45%"
      :before-close="handleClose"
      class="valuDialog"
    >
      <div class="vala-addProject">
        <div class="vala-addProject-header">
          <div class="vala-Next-icon">
            <i class="el-icon-info" style="color: #dce0e2; font-size: 40px" />
          </div>
          <div class="vala-Next-name">
            <p class="vala-project-p">
              常用费率来源于各省技经同仁的无私奉献,进行参考。
            </p>
            <p class="vala-project-p">
              在实际工程编制中,请务必以各省电力定额站发布的相关文件为准。
            </p>
          </div>
        </div>
        <div class="MainReportForm">
          <div class="ReportFormList" style="height: 40vh">
            <el-tree
              ref="tree"
              node-key="bomProjectExportId"
              :data="getdataList"
              :props="defaultProps"
              @node-click="handleCheckChange"
            />
          </div>
          <div class="ReportFormExhibition" style="height: 40vh">
            <template>
              <el-table
                :data="CityListTable"
                style="width: 96%; margin-left: 4%; margin-top: 2%"
                row-key="id"
                :header-cell-style="{
                  textAlign: 'center',
                  background: '#dce0e2',
                  color: '#000',
                }"
                border
                lazy
              >
                <el-table-column
                  type="index"
                  align="center"
                  label="序号"
                  width="60"
                />
                <el-table-column
                  prop="cityHouse"
                  align="center"
                  label="住房公积金"
                />
                <el-table-column
                  prop="citySocial"
                  align="center"
                  label="保险"
                />
              </el-table>
            </template>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <button class="close" @click="dialogVis">确定</button>
      </span>
    </el-dialog>
    <el-dialog
      :title="dialog.Filetitle"
      :visible.sync="dialog.FileNextvisible"
      width="60%"
      :before-close="handleClose"
      class="valuDialog"
    >
      <div class="vala-addProject">
        <div class="vala-addProject-content">
          <h2>配送及运杂费率计算规则</h2>
          <p>1、主要材料配送费</p>
          <ul>
            <li>
              计算公式：主要材料配送费＝主要材料价（集中储备库出库价）×配送费费率
            </li>
            <li>
              主要材料配送费费率：从集中仓库运到现场运输距离在30km以内，费率为1.03%；运距超过30km时，每增加30km费率增加0.10%，不足30km部分按30km计算。
            </li>
            <li>
              供货商直接供货到施工现场或集中存储仓库的，另计取卸车费和保管费，按照材料供货合同价的2%计算。
            </li>
          </ul>
          <p>2、设备运杂费</p>
          <ul>
            <li>计算公式：设备运杂费＝设备费×设备运杂费费率</li>
            <li>
              设备运杂费费率计算规定：设备运输方式均按照公路运输考虑，运输距离应按照设备交货地点或项目管理单位集中存储仓库的实际运输距离计算。
            </li>
            <li>
              公路运杂费费率：公路运输的运距在50km以内费率为1.06%；运距超过50km时，每增加50km费率增加0.35%；不足50km按50km计取。
            </li>
            <li>
              供货商直接供货到施工现场或集中存储仓库的，另计取卸车费和保管费，按照材料供货合同价的0.7%计算。
            </li>
          </ul>
          <p>3、设备集中配送费</p>
          <ul>
            <li>
              计算公式：设备集中配送费＝设备费（集中储备库出库价）×设备集中配送费费率
            </li>
            <li>
              当设备从集中仓库起运至施工现场时，运输方式均按照公路运输考虑，运输距离应按照从项目管理单位集中存储仓库到设备安装地点的实际运输距离计算。设备集中配送费费率计算规定：运输距离30km以内，费率为1.03%；运距超过30km时，每增加30km费率增加0.10%；不足30km按30km计取。
            </li>
          </ul>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <button class="close" @click="dialogVis">关闭</button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { apipost } from '@/utils/mapRequest'
import { mapState } from 'vuex'
export default {
  name: 'projectinfo',
  computed: {
    ...mapState('storeModules/dataDic/', [
      'GInformationDY', // 电压等级
      'GInformationZD', // 占地类型
      'GInformationDQ', // 地区类型
      'GInformationTS', // 特殊地区
      'GInformationWS', // 小数位数
      'GInformationFL', // 社会保险费率
      'GInformationJL',
    ]),
  },
  data() {
    return {
      porjectlist: [], // 工程量数组
      dialog: {
        visible: false,
        Nextvisible: false,
        FileNextvisible: false,
        title: '',
        Filetitle: '',
      }, // dialog的所有配置项
      parms: {
        dataName: '', // 工程名称
        dataStage: '', // 工程阶段
        dataVoltageGrade: '', // 电压等级
        dataSpecialArrea: '', // 特殊地区
        dataHoldAreaType: '', // 占地类型
        dataAreaType: '', // 地区类型
        dataTheirVillage: '', // 所属乡镇
        dataTheirCounty: '', // 所属县区
        dataTheirCity: '', // 所属市
        dataTaxrate: '', // 工程税率
        dataPremiumRate: '', // 社会保险费率
        dataMaterialsDistance: '', // 甲供主材配送距离
        dataCpfRate: '', // 住房公积金费率
        dataFacilityDistance: '', // 甲供设备配送距离
        dataVenderDistance: '', // 设备厂家运输距离
        dataUploadRate: '', // 甲供主材卸车费费率%
        dataSmallNumber: '', // 工程费用小位数
        dataCostodyRate: '', // 甲供主材保管费费率%
        dataBatch: '', // 所属批次
        dataTenderee: '', // 招标人
        dataTendereeAccredit: '', // 招标法定代表及其授权人
        dataBidder: '', // 投标人
        dataBidderAccredit: '', // 投标法定代表及其授权人
        dataCounselor: '', // 工程造价咨询人
        dataCounselorAccredit: '', // 咨询法定代表及其授权人
        dataStartdDate: '', // 创建时间
        dataLister: '', // 创建人
        dataNumber: '', // 工程编号
        dataText: '', // 工作情况
      },
      dataCpfRate: '',
      dataPremiumRate: '',
      screenHeight: document.body.clientHeight - 90, // 获取当前body高度减去10
      getdataList: [],
      defaultProps: {
        // 报表列表tree
        children: 'cityInfo',
        label: 'cityName',
      },
      CityListTable: [],
    }
  },

  watch: {
    $route: 'getParams',
    // 监听全屏时页面的高度
    screenHeight(val) {
      // 为了避免频繁触发resize函数导致页面卡顿，使用定时器
      if (!this.timer) {
        // 一旦监听到的screenWidth值改变，就将其重新赋给data里的screenWidth
        this.screenHeight = val
        this.timer = true
        const that = this
        setTimeout(function () {
          // 打印screenWidth变化的值
          console.log(that.screenHeight)
          that.timer = false
        }, 400)
      }
    },
  },
  mounted() {
    this.getParams()
    const that = this
    // 获取当前屏幕的高度
    window.onresize = () => {
      return (() => {
        window.screenHeight = document.body.clientHeight - 90
        that.screenHeight = window.screenHeight
      })()
    }
  },
  methods: {
    getParams() {
      // 从本地获取主页面的项目id
      var routerParams = this.$route.query.projectId
      let data = {
        projectId: routerParams,
      }
      apipost('/bom-project-data/search/', data).then((res) => {
        console.log(res)
        this.parms = res.data
      })
    },
    // 点击打开预览文件
    fileOpen() {
      this.dialog.FileNextvisible = true
      this.dialog.Filetitle = '预览文件'
    },
    btn() {
      apipost('/bom-project-data/edit/', this.parms).then((res) => {
        console.log(res)
        this.getParams()
      })
    },
    // 点击tree获取id
    handleCheckChange(data) {
      let data1 = {
        areaid: data.cityId,
      }
      apipost('/bom-project-data/getSAndH/', data1).then((res) => {
        console.log(res)
        this.parms.dataCpfRate = res.data[0].cityHouse
        this.parms.dataPremiumRate = res.data[0].citySocial
        this.CityListTable = res.data
      })
    },
    // 点击社保
    SelectS() {
      this.dialog.Nextvisible = true
      this.dialog.title = '常用费率'
      // 查询城市
      apipost('/bom-project-data/getListCity/').then((res) => {
        console.log(res)
        this.parms.dataCpfRate = res.data[0].cityHouse
        this.parms.dataPremiumRate = res.data[0].citySocial
        this.CityListTable = res.data
      })
    },
    dialogVis() {
      this.dialog.Nextvisible = false
      this.dialog.FileNextvisible = false
    },
    handleClose() {
      this.dialog.Nextvisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.vala-main {
  height: 100%;
  width: 96%;
  margin-left: 2%;
  overflow-x: hidden;
  overflow-y: auto;
}

.inform-btn {
  height: 30px;
  line-height: 0px;
}

.inform-cardArea {
  width: 96%;
  margin-left: 2%;
}

.inform-input-cont {
  display: flex;
  justify-content: flex-start;

  .el-input-prepend {
    width: 80px;
    height: 30px;
    background-color: #a5d3f3;
    text-align: center;
    color: white;
    line-height: 30px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.7rem;
  }

  .el-select {
    font-size: 1.3rem;
  }
}

.el-input {
  position: relative;
  font-size: 0.7rem;
  display: inline-block;
  width: 87%;
}

.el-textarea {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: bottom;
  font-size: 1.3rem;
  margin-top: 10px;
}

.vala-cutLine {
  margin: 0 20px;
  border-bottom: 1px solid #e2dfdf;
  display: flex;

  .vala-cutLineLeft {
    display: flex;
    flex: 1;
    margin-top: 20px;
    margin-bottom: 10px;

    .vala-column {
      width: 5px;
      height: 24px;
      line-height: 20px;
      background-color: #51b4f7;
      text-align: center;
    }

    .vala-title {
      margin-left: 10px;
      padding-top: 5px;
    }
  }

  .vala-cutLineRight {
    flex: 1;
    text-align: right;
    margin-right: 20px;
    margin-top: 15px;

    ::v-deep {
      .el-switch {
        margin-right: 10px;
      }
    }
  }
}

.el-row {
  margin-bottom: 15px;
  margin-top: 15px;

  &:last-child {
    margin-bottom: 0;
  }
}

.el-col {
  border-radius: 4px;
}

.inform-purple-dark {
  background: #99a9bf;
}

.inform-purple {
  /*background: #d3dce6;*/
}

.inform-purple-light {
  background: #e5e9f2;
}

.inform-content {
  display: flex;
  justify-content: center;
  border-radius: 4px;
  min-height: 36px;
}

.inform-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}

.vala-addProject {
  .vala-addProject-header {
    width: 100%;
    display: flex;
    justify-content: flex-start;
  }

  .vala-Next-name p {
    font-size: 1.3rem;
    letter-spacing: 1px;
    margin-left: 10px;
  }

  .MainReportForm {
    display: flex;
    width: 100%;
    margin-top: 1%;
    border: solid 2px #cccccc;
  }

  .ReportFormList {
    width: 400px;
    text-align: center;
    transition: width 1s;
    -webkit-transition: width 1s;
    overflow: scroll;
    margin: 16px;
    margin-top: 20px;
  }

  .span-ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    min-width: auto;
  }

  .ReportFormExhibition {
    width: 100%;
    border-left: 2px solid #ccc;
    background-color: white;
    transition: width 1s;
    -webkit-transition: width 1s;
    margin-right: 2%;
    overflow: scroll;
  }
}

.vala-addProject-content {
  width: 96%;
  margin: 10px 2% 20px 2%;

  p {
    font-size: 20px;
    margin-bottom: 10px;
  }

  ul {
    width: 96%;
    margin-left: 2%;
    margin-bottom: 20px;
  }

  ul li {
    list-style: none;
    line-height: 25px;
    font-size: 1.3rem;
  }
}

.vala-addProject-content h2 {
  margin-bottom: 20px;
}

.inform-input1 {
  width: 60%;
  height: 36px;
}
::v-deep .el-dialog__body {
  padding: 24px !important;
}
::v-deep .el-dialog__footer {
  padding: 16px 24px !important;
}
</style>
