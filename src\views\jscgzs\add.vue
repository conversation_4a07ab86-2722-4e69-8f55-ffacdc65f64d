<template>
  <el-dialog
    :title="'操作'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    lock-scroll
    width="60%"
    center
    @close="cancleForm()"
  >
    <!-- <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-if="false"
    >
      <div v-for="(item, i) in form.info" :key="i">
        <el-form-item label="文件名称" prop="spbt">
          <el-input v-model="form.spbt" placeholder="文件名称"></el-input>
        </el-form-item>
        <el-form-item label="文件" prop="file">
          <el-upload
            class="upload-demo"
            ref="upload"
            action
            :limit="1"
            accept="video/mp4,video/ogg,video/flv,video/avi,video/wmv,video/rmvb,video/mov"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :on-change="beforeUpload"
            :file-list="fileList"
            :auto-upload="false"
          >
            <el-button slot="trigger" size="small" type="primary"
              >选取文件</el-button
            >
          </el-upload>
        </el-form-item>
      </div>
    </el-form> -->
    <div class="table-content">
      <el-table
        :data="tableData"
        v-loading="loading"
        style="width: 100%"
        :header-cell-style="{ background: '#f7f8fa', color: '#606266' }"
        :cell-style="{ 'text-align': 'center' }"
        :row-class-name="xxxTableRowClassName"
      >
        <el-table-column
          prop="name"
          show-overflow-tooltip
          align="center"
          label="名称"
          width="250"
        >
        </el-table-column>
        <el-table-column prop="name" label="文件" align="center" width="">
          <template slot-scope="scope">
            <!-- <div v-for="(item, i) in scope.row.fileList" :key="i">
              <el-link @click="previewHandle(item)">
                {{ item.name }}
              </el-link>
              <el-button
                type="text"
                style="color:#fa3131"
                @click="delChange(item)"
                >删除</el-button
              >
              <i class="el-icon-close" @click="delChange(item)"></i>
            </div> -->
            <ul class="el-upload-list el-upload-list--text">
              <li
                class="el-upload-list__item"
                v-for="(item, i) in scope.row.fileList"
                :key="i"
              >
                <span>{{ item.name }}</span>
                <i class="el-icon-close" @click="delChange(item)"></i>
              </li>
            </ul>
            <!-- <el-upload
            class="upload-demo"
            ref="upload"
            :action="url"
            multiple
            accept=""
            :data="getformData(scope.row)"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :on-change="beforeUpload"
            :on-success="handleSuccess"
            :on-error="handleError"
            :file-list="scope.row.fileList"
          >
          </el-upload> -->
          </template>
        </el-table-column>
        <el-table-column prop="name" align="center" label="操作" width="100">
          <template slot-scope="scope">
            <!-- <el-button class="blue-btn" @click="download(scope.row)"
            >上传</el-button
          > -->

            <div
              style="display: flex; justify-content: center; column-gap: 16px"
            >
              <!-- <el-button
              type="text"
              style="color:#0aaf51"
              @click="download(0, scope.row)"
              >模板下载</el-button
            > -->
              <el-upload
                class="upload-demo"
                ref="upload"
                :action="url + '/tDtfFileModule/fileUpload'"
                multiple
                accept=""
                :data="getformData(scope.row, scope.$index)"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :on-change="beforeUpload"
                :on-success="handleSuccess"
                :on-error="handleError"
                :file-list="scope.row.fileList"
                :before-upload="beforeAvatarUpload"
                :on-progress="onProgress"
                :show-file-list="false"
              >
                <el-button
                  slot="trigger"
                  type="text"
                  size="small"
                  style="color: #526ade"
                  >上传</el-button
                >
                <!--   <el-button
            style="margin-left: 10px"
            size="small"
            type="success"
            @click="submitUpload"
            >上传到服务器</el-button
          > -->
                <!-- <div slot="tip" class="el-upload__tip">只能上传视频文件</div> -->
              </el-upload>
              <el-button
                type="text"
                style="color: #0aaf51"
                :disabled="scope.row.fileList.length == 0"
                @click="download(1, scope.row)"
                >下载</el-button
              >
              <!-- <el-button
                type="text"
                style="color:#fa3131"
                :disabled="scope.row.fileList.length == 0"
                @click="delChange(scope.row)"
                >删除</el-button
              > -->
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- <div slot="footer" class="dialog-footer">
      <el-button type="info" @click="cancleForm()">取 消</el-button>
      <el-button
        type="primary"
        @click="dataFormSubmit()"
        :loading="loading"
        v-if="!isDetail"
        >确 定</el-button
      >
    </div> -->
    <preview v-if="dialogFormVisible" ref="preview"></preview>
  </el-dialog>
</template>
<script>
import { getJscgDetail, jscgDownloadId, jscgFileDel } from '@/api/api'
import preview from './preview.vue'
import qs from 'qs'
// import axios from "axios";
import { resquest } from '@/api/api'
// import Cookies from "js-cookie";

export default {
  components: {
    preview,
  },
  props: {
    ProCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      visible: false,
      form: {},
      url: resquest,
      formData: {},
      dataObj: {},
      rules: {
        spbt: [
          {
            required: true,
            message: '请选择文件名称',
            trigger: 'blur',
          },
        ],
      },
      tableData: [
        // {
        //   name: '名称',
        //   fileList: [
        //     { name: 'food.jpeg', url: '' },
        //     { name: 'food2.jpeg', url: '' },
        //   ],
        //   flag: 1,
        // },
        // {
        //   name: '名称',
        //   fileList: [
        //     { name: 'food.jpeg', url: '' },
        //     { name: 'food2.jpeg', url: '' },
        //   ],
        //   flag: 2,
        // },
      ],
      isDetail: '',
      loading: false,
      fileList: [],
      // ProCode: '',
      // url: resquest + "/Spgl/addSp",
      // token
      dialogFormVisible: false,
    }
  },
  mounted() {},
  methods: {
    // 斑马纹效果
    xxxTableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 == 0) {
        return ''
      } else {
        return 'statistics-warning-row'
      }
    },
    getformData(row, i) {
      return {
        // flag: this.dataObj.info[i].flag,
        flag: row.index,
        proCode: this.ProCode,
      }
    },
    cancleForm() {
      // this.$refs['form'].resetFields()
      this.visible = false
      this.$emit('getList')
    },

    dataFormSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return
        console.log('haha')
      })
    },
    init(row) {
      // this.ProCode = ProCode
      this.visible = true // 打开弹窗
      // this.isDetail = isDetail || false; // isDetail为true就是详情,没有isDetail就是false为编辑
      this.$nextTick(() => {
        // this.$refs['form'].resetFields() // 清空表单
        // 如果有id就是编辑查看，这里的请求用于做数据回填
      })
      this.dataObj = JSON.parse(JSON.stringify(row))
      console.log(row)
      this.getData()
    },
    getData() {
      this.loading = true
      let data = {
        ProCode: this.ProCode,
        xh: this.dataObj.index,
      }
      getJscgDetail(data)
        .then((res) => {
          this.tableData = res.data.data.tableData
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    download(type, row) {
      // this.btnloading = true
      // let data = {
      //   proCode: '1806CB210007-001',
      //   flag: row.index,
      // }
      let url =
        type == 1
          ? `${this.url}/tDtfFileModule/download?proCode=${this.ProCode}&flag=${row.index}`
          : ''
      console.log(url)
      if (!url) return this.$message.info('暂无文件')
      window.open(url)
      // config.data = qs.stringify(config.data)
      // jscgDownloadId(data)
      //   .then((res) => {
      //     this.$message.success('下载成功')
      //     this.btnloading = false
      //   })
      //   .finally(() => {
      //     this.btnloading = false
      //   })
      // this.axios({
      //   method: 'post',
      //   url: resquest + '/tDtfFileModule/download',
      //   data: qs.stringify(data),
      // }).then((res) => {
      //   // 假设 data 是返回来的二进制数据
      //   const data = res
      //   const url = window.URL.createObjectURL(
      //     new Blob([data], {
      //       type:
      //         'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      //     })
      //   )
      //   const link = document.createElement('a')
      //   link.style.display = 'none'
      //   link.href = url
      //   link.setAttribute('download', 'excel.xlsx')
      //   document.body.appendChild(link)
      //   link.click()
      //   document.body.removeChild(link)
      // })
    },
    delChange(row) {
      // this.loading = true
      let data = {
        // ProCode: '1806CB210007-001',
        id: row.id,
      }
      jscgFileDel(data)
        .then((res) => {
          if (res) {
            this.getData()
            this.$message.success('删除成功')
          }
          // this.loading = false
        })
        .catch(() => {
          // this.loading = false
        })
    },
    // 预览
    previewHandle(e) {
      return
      if (e.name.indexOf('.pdf') == -1) {
        this.$message.info('暂只支持在线预览pdf格式文件')
        return
      }
      this.dialogFormVisible = true // 控制弹出框显示
      this.$nextTick(() => {
        this.$refs.preview.init(e) // init（）是子组件函数
      })
    },
    submitUpload() {
      this.$refs.upload.submit()
    },
    handleRemove(file, fileList) {},
    handlePreview(file) {},
    beforeUpload(file, fileList) {
      // this.form.file = file.raw
      console.log('beforeUpload', file, fileList)
      if (file.status === 'ready') {
        this.loading = true
      }
    },
    beforeAvatarUpload(file) {
      return new Promise((resolve, reject) => {
        const isType =
          /\.(wmv|rm|rmvb|mov|mp4|flv|3gp|mkv|avi|f4v|webv|mepg)$/.test(
            file.type
          )
        const isLt2M = file.size / 1024 / 1024 < 200
        //文件上传之前的校验
        // if (!isType) {
        //   // 限制文件类型校验
        //   this.$message.error('视频上传格式不对！')
        //   return reject(false)
        // } else
        if (!isLt2M) {
          this.$message.error('文件大小不能超过 200MB!')
          return reject(false)
        } else {
          resolve(true)
        }
      })
    },
    // 上传中
    onProgress(event, file, fileList) {
      console.log('onProgress', event, file, fileList)
    },
    //上传成功
    handleSuccess(response, file, fileList) {
      this.loading = false
      console.info(fileList, response, 'fileList')
      this.getData()
    },
    // 上传失败
    handleError(err, file, fileList) {
      console.log(file, fileList)
      console.log('error', err)
      this.$message.error('上传失败')
      this.loading = false
    },
  },
}
</script>
<style lang="scss" scoped>
.file-item {
}

.el-upload-list__item:hover {
  background-color: rgba(230, 234, 239, 0.8);
}

.el-upload-list__item:first-child {
  margin-top: 0;
}

.el-icon-close {
  color: red;
}
</style>
