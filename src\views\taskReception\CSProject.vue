<template>
  <div class="index flex-c h100">
    <!-- 标题 -->
    <div style="height:100vh;width:100vw;background:#fff;  position: absolute;left:0;top:0;z-index:99" v-show="!showPage"></div>
    <div class="main-header">初设任务接收</div>
    <div class="" style="width: 100%; height: 1px; border-bottom: 1px solid #eee"></div>
    <!-- 表单 -->
    <div class="query-form-box">
      <el-form ref="form" :model="form" :inline="true" label-width="80px">
        <el-row>
          <el-col :span="5">
            <el-form-item label="批次年度:" prop="pcnd">
              <el-date-picker v-model="form.pcnd" type="year" format="yyyy" value-format="yyyy" placeholder="选择年" @change="NDPoint">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="批次名称:" prop="batch">
              <el-select v-model="form.batch" clearable placeholder="请选择">
                <el-option v-for="item in batchOptions" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="地市公司:" prop="dsgs">
              <el-select v-model="form.dsgs" clearable placeholder="请选择" @change="CityPoint">
                <el-option v-for="(item,index) in dsgsOptions" :key="index" :label="item.cityname" :value="item.cityid">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" class="lastSearchArea">
            <el-form-item label="县公司:" prop="xgs">
              <el-select v-model="form.xgs" clearable placeholder="请选择">
                <el-option v-for="item in xgsOptions" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="状态:" prop="projState">
              <el-select v-model="form.projState" clearable placeholder="请选择">
                <el-option label="待处理" value="01">待处理</el-option>
                <el-option label="处理中" value="02">处理中</el-option>
                <el-option label="已提交" value="05" v-show="userType=='3'">已提交</el-option>
                <el-option label="已提交" value="06" v-show="userType=='2'">已提交</el-option>
                <el-option label="评审通过" value="04">评审通过</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="工程名称:" prop="projectName">
              <el-input v-model="form.projectName" clearable placeholder="请输入工程名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7" class="midSearchArea">
            <el-form-item label="项目名称:" prop="xmmc">
              <el-input v-model="form.xmName" clearable placeholder="请输入项目名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" v-show="!isShowExpand" class="lastSearchArea">
            <el-form-item label="项目编码:" prop="projectName">
              <el-input v-model="form.code" clearable placeholder="请输入项目编码"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" v-show="isShowExpand" class="lastSearchArea">
            <el-button class="blue-btn" @click="query()">查询</el-button>
            <el-button @click="clearForm()">重置</el-button>
            <span @click="changeExpand">
              <span class="expandArea">
                展开
                <img style="width: 16px; height: 16px" :src="require('@/assets/main/botArrow.png')" alt="" /></span>
            </span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-show="!isShowExpand" class="lastSearchArea" style="margin-bottom: 16px">
            <el-button class="blue-btn" @click="query()">查询</el-button>
            <el-button @click="clearForm()">重置</el-button>
            <span @click="changeExpand">
              <span class="expandArea">
                收起
                <img style="width: 16px; height: 16px" :src="require('@/assets/main/topArrow.png')" alt="" /></span>
            </span>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="driver"></div>
    <div class="actionAreaPage">
      <el-upload class="uploadAction" action="test" accept=".xlsx,.xls" :before-upload="beforeDesignUpload" :show-file-list="false" :http-request="uploadFilesProject">
        <div><el-button>
            <div class="buttonArea"><img src="@/assets/img/批量导入.svg" alt="" style="margin-right:3px">
              <div> 批量导入</div>
            </div>
          </el-button>
        </div>
      </el-upload>

      <!-- <el-button @click="downLoadTemplates" style="margin-left:12px">
        <div class="buttonArea"><img src="@/assets/img/下载.svg" alt="" style="margin-right:3px">
          <div> 模板下载</div>
        </div>
      </el-button> -->

      <el-button @click="removeListPorj" style="margin-left:12px">
        <div class="buttonArea"><img src="@/assets/img/回退.svg" alt="" style="margin-right:3px">
          <div> 批量删除</div>
        </div>
      </el-button>
    </div>
    <!-- 项目列表 -->
    <div class="tablesArea">
      <el-table :data="tableData" ref="table" :header-cell-style="{ background: '#f7f8fa', color: '#606266' }" :height="tableHeight" highlight-current-row @selection-change="handleSelectionChange" :row-key="getRowKeys" style="width: 98%; margin: 0px 16px 0 16px" >
        <el-table-column type="selection" :reserve-selection="true" width="55" fixed="left">
        </el-table-column>
        <el-table-column type="index" label="序号" align="center" :index="indexMethod" :resizable="false" width="60">
        </el-table-column>
        <el-table-column prop="pcnd" label="年度" align="center">
        </el-table-column>
        <el-table-column prop="projectName" label="工程名称" align="left" width="400px">
        </el-table-column>
        <el-table-column prop="batchname" label="项目批次" align="left" width="200px">
        </el-table-column>
        <el-table-column prop="xmmc" label="项目名称" align="left" width="400px">
        </el-table-column>
        <el-table-column prop="code" label="项目管理编码" align="left" width="150px">
        </el-table-column>
        <el-table-column prop="cityName" label="地市公司" align="left" width="130px">
        </el-table-column>
        <el-table-column prop="countyName" label="县公司" align="left" width="130px">
        </el-table-column>
        <el-table-column prop="voltageLevel" label="电压等级" align="left" width="100px">
        </el-table-column>
        <el-table-column label="适用深度规范" align="left" width="140px">
          <template slot-scope="scope">
            <span v-if="scope.row.projectType == '01'">架空</span>
            <span v-if="scope.row.projectType == '02'">电缆</span>
            <span v-if="scope.row.projectType == '03'">配变</span>
          </template>
        </el-table-column>

        <el-table-column prop="projState" label="任务状态" align="left" width="100px">
          <template slot-scope="scope">
            <span v-if="(scope.row.projState == '01' || scope.row.projState == '')" style="color: skyblue">待处理</span>
            <span v-else-if="scope.row.projState == '02'" style="color: orangered">处理中</span>
            <span v-else-if="scope.row.projState == '06' && userType == '3'" style="color: royalblue">已提交（地市公司评审）</span>
            <span v-else-if="scope.row.projState == '06' && userType == '2'" style="color: royalblue">已提交（地市公司评审）</span>
            <span v-else-if="scope.row.projState == '05' && userType == '3'" style="color: royalblue">已提交(县公司评审)</span>
            <span v-else-if="scope.row.projState == '04'" style="color: green">评审通过</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="left" width="200px">
          <template slot-scope="scope">
            <span class="directiveHandle">
              <div :class="!['', '01', '02'].includes(scope.row.projState) || userType == '1'
                ? 'el-buttonDisabled'
                : 'el-buttonStyle'
                ">
                <span @click="handleEdit(scope.row)"> 编辑 </span>
              </div>
              <span class="el-buttonDriver">|</span>
              <div :class="'el-buttonStyle'
                ">
                <span @click="removeProj(scope.row)"> 删除 </span>
              </div>
              <!-- <span class="el-buttonDriver">|</span>
              <div :class="scope.row.projState !== '02' || userType == '1'
                ? 'el-buttonDisabled'
                : 'el-buttonStyle'
                ">
                <span @click="handleReturn(scope.row)"> 退回 </span>
              </div> -->
              <!-- <span class="el-buttonDriver">|</span>
              <el-dropdown>
                <el-button type="text">
                  <i class="el-icon-more directiveicon" style="font-size: 14px"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="获取现状" :class="scope.row.projState !== '01'
                    ? 'el-buttonDisabled'
                    : 'el-buttonStyle'
                    ">
                    <span @click="handleCofirm(scope.row)"> 获取现状 </span>
                  </el-dropdown-item>
                  <el-dropdown-item command="查看馈线" :class="scope.row.projState !== '02'
                    ? 'el-buttonDisabled'
                    : 'el-buttonStyle'
                    ">
                    <span @click="handleFeeder(scope.row)"> 查看馈线 </span>
                  </el-dropdown-item>
                  <el-dropdown-item command="设计软件" :class="!['', '01', '02'].includes(scope.row.projState)
                    ? 'el-buttonDisabled'
                    : 'el-buttonStyle'
                    ">
                    <span @click="handleEamines(scope.row)"> 设计软件 </span>
                  </el-dropdown-item>
                  <el-dropdown-item command="在线设计" :class="!['', '01', '02'].includes(scope.row.projState)
                    ? 'el-buttonDisabled'
                    : 'el-buttonStyle'
                    ">
                    <span @click="handleEamine(scope.row)"> 在线设计 </span>
                  </el-dropdown-item>
                  <el-dropdown-item command="成果分析" :class="scope.row.projState !== '02'
                    ? 'el-buttonDisabled'
                    : 'el-buttonStyle'
                    ">
                    <span @click="handleResults(scope.row)"> 成果分析 </span>
                  </el-dropdown-item>
                  <el-dropdown-item command="设计成果" class="el-buttonStyle">
                    <span @click="handleDesign(scope.row)"> 设计成果 </span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown> -->
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination style="margin: 10px" background :current-page="form.page" :page-sizes="pageSize" :page-size="form.perPage" layout="total, sizes, prev, pager, next" @current-change="handleCurrentChange" @size-change="handleSizeChange"
      :total="total">
    </el-pagination>
    <!-- 设计成果导入 -->
    <el-dialog title="设计成果导入" :visible.sync="requirementsDialog" width="30%" center>
      <el-upload class="upload-demo" :data="getformData()" :action="url + '/tDtfProject/importXqSms'" accept=".zip" :before-upload="beforeAvatarUpload" :on-error="handleError" :file-list="addProjectInfo.file">
        <button class="uploadFiles">
          <i class="el-icon-upload2">点击上传</i>
        </button>
        <div slot="tip" class="el-upload__tip">只能上传.zip文件</div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="requirementsDialog = false">取 消</el-button>
        <el-button class="blue-btn" @click="handleSuccess">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 编辑数据 -->
    <el-dialog title="编辑数据" :visible.sync="editDialog" width="24%" center>
      <el-form ref="formEdit" :model="formEdit" :inline="true">
        <el-form-item label="批次年度:" prop="pcnd" label-width="120px">
          <el-date-picker v-model="formEdit.pcnd" format="yyyy" value-format="yyyy" type="year" placeholder="选择年" @change="NDPointEdit">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="项目名称:" prop="xmmc" label-width="120px">
          <el-input v-model="formEdit.projectName" clearable></el-input>
        </el-form-item>
        <el-form-item label="批次名称:" prop="batch" label-width="120px">
          <el-select v-model="formEdit.batch" clearable placeholder="请选择">
            <el-option v-for="item in batchOptionsEdit" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialog = false">取 消</el-button>
        <el-button class="blue-btn" @click="editDialogQuery">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 现状数据 -->
    <el-dialog title="获取现状数据" :visible.sync="obtainDialog" width="30%" left :close-on-click-modal="false">
      <el-table :data="gridData" :header-cell-style="{ background: '#F7F8FA' }">
        <el-table-column type="index" width="60" align="left" label="序号">
        </el-table-column>
        <el-table-column property="name" label="步骤" align="left"></el-table-column>
        <el-table-column width="100" align="left" property="isSuccess" label="是否成功">
          <template slot-scope="scope">
            {{ scope.row.isSuccess ? "成功" : "加载中" }}
          </template>
        </el-table-column>
        <el-table-column property="" label="进度" align="left" width="150px">
          <template slot-scope="scope">
            <div class="progresswrapper">
              <!-- <div class="pro" :style="{width:scope.row.pro+'%'}">{{scope.row.pro}}%</div> -->
              <el-progress :text-inside="true" :stroke-width="15" :percentage="scope.row.pro" text-color="#fff" :color="scope.row.isSuccess ? '#16A065' : '#7286E8'"></el-progress>
              <!-- #526ADE -->
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 馈线ID -->
    <el-dialog title="馈线ID查看" width="70%" style="height: 100vh" v-if="feederDialog" :visible.sync="feederDialog">
      <div style="height: 500px; overflow-y: auto" v-loading="fullscreenLoading" element-loading-text="拼命加载中">
        <!-- <FeederDialog :ProCode="cuttentProCode" v-if="flag"></FeederDialog> -->
        <json-viewer class="jsonview" :value="feederData" :expand-depth="jsonformat.expandDepth" :copyable="jsonformat.copyable">
        </json-viewer>
      </div>
      <!-- <span slot="footer" class="dialog-footer">
				<el-button @click="feederDialog = false">取 消</el-button>
				<el-button class="blue-btn" @click="feederDialogQuery">保 存</el-button>
			</span> -->
    </el-dialog>
    <!-- 成果分析 -->
    <el-dialog title="选择解析内容" width="25%" style="height: 100vh" :visible.sync="resultDialog">
      <div style="height: 100%">
        <ResultsDialog :ProCode="cuttentresult" v-if="flag"></ResultsDialog>
      </div>
    </el-dialog>
    <el-dialog title="设计成果查看" width="95%" top="5vh" :style="{ height: '100%', 'margin-top': '0' }" :visible.sync="dialogDesign" v-if="dialogDesign">
      <div style="height: 100%; display: flex">
        <Declaration :ProCode.sync="SJProCode" v-if="flag"></Declaration>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogDesign = false">取 消</el-button>
        <el-button class="blue-btn" @click="dialogQuery">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSettlement,
  getCity,
  getCounty,
  getXQEditPull,
  EditSave,
  uadateProstate,
  resquest,
  handleReturnCS,
  handleSubtCS,
  getPCSel,
  getURLSJ,
  getuserInfo,
  gethandleObtain,
  feeederObtainCurrent,
  importFileProjectInfo,
  downLoadTemplate
} from "@/api/api"
import Declaration from "@/views/projecthz/components/index"
import FeederDialog from "@/views/projectAgent/CScomonents/tree"
import ResultsDialog from "@/views/projectAgent/CScomonents/table"
import { progressMixins } from "@/utils/progressMixins.js"
import { EDITMixins } from "@/utils/EDITMixins.js"
export default {
  components: {
    FeederDialog,
    ResultsDialog,
    Declaration
  },
  mixins: [progressMixins,EDITMixins],
  data() {
    return {
      flag: true,
      isShowExpand: true,
      SJProCode: "",//设计成果id
      dialogDesign: false,
      form: {
        //表单参数
        id: "",
        projState: null,
        batch: "",
        stageType: "2",
        projectName: "",
        xmmc: "",
        pcnd: null,
        BATCH: "",
        dsgs: "",
        xgs: "",
        xmName: "",
        tgzt: "09",
        csmc: "",
        dwmc: "",
        page: 1,
        perPage: 10,
        code: '',
        source: '',
      },
      formFeeder: {
        name: "",
        ID: "",
      },
      jsonformat: {
        expandDepth: 2,
        copyable: true,
      },
      feederTableData: [
        {
          name: "10kV碧20线",
          ID: "f74f975-d221-49e3-8370-3b13107dea3d",
        },
        {
          name: "10kV钢75",
          ID: "effd7843-9e83-42e9-bce9-35bf55f2ccc1",
        },
        {
          name: "10kV文413",
          ID: "ee502cec-54d5-47af-bc7e-9965d6dafd90",
        },
        {
          name: "10KV魏线10",
          ID: "e8839bde-69b0-4c75-ad87-4fb9b8b8a178",
        },
        {
          name: "10kV碧20线",
          ID: "f74f975-d221-49e3-8370-3b13107dea3d",
        },
        {
          name: "10kV钢75",
          ID: "effd7843-9e83-42e9-bce9-35bf55f2ccc1",
        },
        {
          name: "10kV文413",
          ID: "ee502cec-54d5-47af-bc7e-9965d6dafd90",
        },
        {
          name: "10KV魏线10",
          ID: "e8839bde-69b0-4c75-ad87-4fb9b8b8a178",
        },
        {
          name: "10kV碧20线",
          ID: "f74f975-d221-49e3-8370-3b13107dea3d",
        },
        {
          name: "10kV钢75",
          ID: "effd7843-9e83-42e9-bce9-35bf55f2ccc1",
        },
        {
          name: "10kV文413",
          ID: "ee502cec-54d5-47af-bc7e-9965d6dafd90",
        },
        {
          name: "10KV魏线10",
          ID: "e8839bde-69b0-4c75-ad87-4fb9b8b8a178",
        },
      ],
      currentPagefeeder: 1,
      widthPro: 100,
      xgsOptions: [], //县公司下拉数据
      userId: "",
      fullscreenLoading: false,
      batchOptionsEdit: [],
      dsgsOptions: [], //城市下拉
      batchOptions: [], //批次名次下拉数据
      pageSize: [10, 20, 50, 100], //分页页数
      total: 0, //总共页数
      tableData: [],
      formEdit: {
        pcnd: new Date().getFullYear() + "",
        batch: "",
        xmmc: "",
        taskID: "",
        projectName: ''
      },
      url: resquest,
      addProjectInfo: {
        file: [], // 文件上传
        id: "",
      },
      tableHeight: 0,
      cuttentProCode: "", //馈线ID
      cuttentresult: "", //成果分析

      editDialog: false, //编辑
      obtainDialog: false, //现状数据
      feederDialog: false, //馈线ID
      resultDialog: false, //成果分析
      requirementsDialog: false, //设计成果导入
      timer: null,
      isProgressExecuting: false,
      isComplete: true, //判断接口请求是否完成
      isErr: false, //判断是否正常返回
      taskID: "",
      interval: null,
      feederData: [],
      detailDate: new Date().getFullYear(),
      userType: '',// 1省 2市 3县
      showPage:false

    }
  },
  // },
  mounted() {
    this.NDPoint()
    this.setTablesHeight()
    const token = sessionStorage.getItem("bhneToken")
    const pageType = sessionStorage.getItem('bhnePageType')

    getuserInfo(token).then((res) => {
      if (Object.keys(res.data.result).length == 0) {
        this.$message.warning("获取用户信息失败，请重新进入页面")
        // this.showPage=false
      }
      let menuTypes = res.data.result.zmenu.split(',')
      if (menuTypes.includes(pageType)) {
        console.log("允许进入");
        this.showPage=true
      }else{
          console.log("无权限")
         this.$message.warning("无权限")
         this.showPage=false
      }

      this.userType = res.data.result.rank
      this.form.csmc = res.data.result.USERID
      this.form.dwmc = res.data.result.DWMC
      this.userId = res.data.result.USERID
      this.getList()
    }).catch((err)=>{
      this.$message.warning("获取权限失败，请重试")
    })
    this.showPage=true
    const that = this

    window.onresize = function () {
      that.setTablesHeight()
    }
  },
  methods: {
    // 模板导入
    changeExpand() {
      this.isShowExpand = !this.isShowExpand
      this.setTablesHeight()
    },
    setTablesHeight() {
      this.$nextTick(() => {
        const tablesAreaHeight = document.getElementsByClassName('driver')[0]
          .getBoundingClientRect().top
        const windowsAreaHeight = document.documentElement.offsetHeight
        // 60是查询结果统计栏div高度 75是整个页面距离底部16px后的高度
        this.tableHeight = windowsAreaHeight - tablesAreaHeight - 120
      })
    },
    // increaseProgress () {
    //   if (!this.isProgressExecuting) {
    //     this.isProgressExecuting = true
    //     this.executeProgress()
    //   }
    // },
    // async executeProgress () {
    //   for (let i = 0; i < this.gridData.length; i++) {
    //     const task = this.gridData[i]
    //     await this.increaseTaskProgress(task)
    //   }

    //   // 在所有任务完成后触发事件

    //   setTimeout(() => {
    //     this.triggerEvent()
    //   }, 500)
    //   // 清除定时器
    //   clearInterval(this.timer)
    //   this.timer = null
    //   this.isProgressExecuting = false
    // },
    // increaseTaskProgress (task) {
    //   return new Promise((resolve) => {
    //     console.log(this.isComplete, "是否加载完成")
    //     if (this.isComplete) {
    //       this.interval = setInterval(() => {
    //         const increment = Math.floor(Math.random() * 30) + 1
    //         task.pro = Math.min(task.pro + increment, 100)

    //         if (task.pro === 100) {
    //           task.isSuccess = true
    //           clearInterval(this.interval)
    //           resolve() // 当任务完成时解析Promise
    //         }
    //       }, 50) // 每秒增加一次进度
    //     } else {
    //       this.interval = setInterval(() => {
    //         const increment = Math.floor(Math.random() * 30) + 1
    //         task.pro = Math.min(task.pro + increment, 100)
    //         if (task.pro === 100) {
    //           task.isSuccess = true
    //           clearInterval(this.interval)
    //           resolve() // 当任务完成时解析Promise
    //         }
    //       }, 500) // 每秒增加一次进度
    //     }
    //   })
    // },
    // //进度条加载完毕后触发
    // triggerEvent () {
    //   if (this.isComplete) {
    //     this.obtainDialog = false
    //     this.gridData.forEach((item) => {
    //       item.pro = 0
    //       item.isSuccess = false
    //     })
    //     if (this.isErr) {
    //       this.$message({
    //         message: "获取成功",
    //         type: "success",
    //       })
    //     } else {
    //       this.$message({
    //         message: "获取失败",
    //         type: "warning",
    //       })
    //     }

    //     console.log(this.obtainDialog, "dldldl")
    //   }
    // },
    handleCofirm(row) {
      this.taskID = row.taskID
      if (row.lineID === '') return this.$message.warning("未获取到有效数据")
      this.$confirm("现状数据只能获取一次,是否确定?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: "warning",
      })
        .then(() => {
          this.handleObtain()
        })
        .catch(() => {

        })
    },
    // table获取现状数据
    // handleObtain () {
    //   let that = this
    //   this.isComplete = false
    //   this.timer = setInterval(this.increaseProgress, 100)
    //   this.obtainDialog = true
    //   gethandleObtain(this.taskID)
    //     .then((res) => {
    //       console.log(res)
    //       if (res.data.result.data.length !== 0) {
    //         this.isComplete = true
    //         this.isErr = true
    //         uadateProstate(this.taskID).then((res) => {
    //           this.getList()
    //           console.log(res, "更新状态")
    //           this.obtainDialog = false
    //         })
    //         // coordinateTransformation(this.taskID).then((res) => {
    //         //   console.log("坐标转换", res)
    //         // })
    //       } else {
    //         this.isComplete = true
    //         this.obtainDialog = false
    //         this.isErr = false
    //       }
    //     })
    //     .catch(() => {
    //       this.isComplete = true
    //       this.obtainDialog = false
    //       this.isErr = false
    //     })
    //   // this.getList();
    //   //     uadateProstate(this.taskID).then((res) => {
    //   //       console.log(res, "更新状态");
    //   //     });
    // },
    // 馈线查看
    inquire() {
      console.log(formFeeder, "馈线查看")
    },
    // 重置表单
    resetinquire() {
      console.log("231231")
      this.formFeeder = {
        name: "",
        ID: "",
      }
    },
    handleSizeChangefeeder(val) {
      console.log(`每页 ${val} 条`)
    },
    handleCurrentChangefeeder(val) {
      console.log(`当前页: ${val}`)
    },
    // 获取列表
    getList() {
      getSettlement(this.form)
        .then((res) => {
          console.log(res.data)
          this.tableData = res.data.data
          this.total = res.data.total
        })
        .catch(() => { })
      if (this.dsgsOptions.length === 0) {
        // 获取城市下拉
        const params = {
          optId: this.form.dwmc,
        }
        getCity(params)
          .then((res) => {
            console.log(res, "optId")
            this.dsgsOptions = res.data.result
          })
          .catch(() => { })
      }
    },
    // 城市点击获取县下拉
    CityPoint(val) {
      const params = {
        optId: val,
        dwmc: this.form.dwmc,
      }
      getCounty(params)
        .then((res) => {
          this.xgsOptions = res.data.result || []

        })
        .catch(() => { })
    },
    NDPoint() {
      // 获取批次名词
      const param = {
        optId: this.form.pcnd,
      }
      getPCSel(param)
        .then((res) => {
          console.log(res)
          this.batchOptions = res.data.result
        })
        .catch(() => { })
    },
    NDPointEdit() {
      // 获取批次名词
      const param = {
        optId: this.formEdit.pcnd,
      }
      getPCSel(param)
        .then((res) => {
          console.log(res)
          this.batchOptionsEdit = res.data.result
        })
        .catch(() => { })
    },
    // table列表序号索引
    indexMethod(index) {
      return (this.form.page - 1) * this.form.perPage + index + 1
    },
    // 查询
    query() {
      this.getList()
    },
    // 重置
    clearForm() {
      this.form = {
        //表单参数
        id: "",
        batch: "",
        stageType: "2",
        projectName: "",
        xmmc: "",
        pcnd: null,
        BATCH: "",
        dsgs: "",
        xgs: "",
        xmName: "",
        tgzt: "09",
        page: 1,
        perPage: 10,
        code: '',
        source: '',
        csmc: this.form.csmc,
        dwmc: this.form.dwmc
      }
      this.getList()
    },


    handleCurrentChange(row) {
      this.form.page = row
      this.getList()
    },
    handleSizeChange(row) {
      this.form.perPage = row
      this.getList()
    },
    // 提交
    handleSubt(row) {
      this.$confirm("是否确定提交?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: "warning",
      })
        .then(() => {
          let params = { taskid: row.taskID, rank: this.userType }
          handleSubtCS(params)
            .then((res) => {
              if (res.data.message == "success") {
                this.$message({
                  message: "提交成功",
                  type: "success",
                })
              } else {
                this.$message.error("提交失败!")
              }
              this.getList()
            })
            .catch(() => { })
        })
    },
    dialogQuery() {
      this.dialogDesign = false
    },
    // 启动设计软件
    handleEamine(row) {
      this.$router.push({
        path: "/onlineDesign",
        query: { taskID: row.taskID, routerType: '/projectAgent/CSProject', kxid: row.relationId },
      })
    },
    handleEamines(row) {
      getURLSJ(row.taskID)
        .then((res) => {
          if (res.data.message == "success") {
            window.location = encodeURI("BhneSJRJ://taskID=" + row.taskID + "&userID=" + this.userId + "&token=" + "11" + "&stageType=1")
          } else {
            this.$message.error("启动失败!")
          }
          console.log(res)
        })
        .catch(() => { })
    },
    // 退回
    handleReturn(row) {
      this.$confirm("是否确定退回?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmBtn",
        cancelButtonClass: "cancleBtn",
        type: "warning",
      })
        .then(() => {
          let params = {
            rank: this.userType,
            taskid: row.taskID
          }
          handleReturnCS(params)
            .then((res) => {
              if (res.data.message == "success") {
                this.$message({
                  message: "退回成功",
                  type: "success",
                })
              } else {
                this.$message.error("退回失败!")
              }
              this.getList()
            })
            .catch(() => { })
        })
    },
    // table 编辑按钮
    handleEdit(row) {
      this.formEdit.taskID = row.taskID
      this.formEdit = row
      getXQEditPull()
        .then((res) => {
          const param = {
            optId: row.pcnd,
          }
          getPCSel(param)
            .then((res) => {
              this.batchOptionsEdit = res.data.result
              this.editDialog = true
            })
            .catch(() => { })
        })
        .catch(() => { })
    },
    // 编辑弹框确认按钮
    editDialogQuery() {
      EditSave(this.formEdit)
        .then((res) => {
          if (res.data.message === "success") {
            this.$message({
              message: "修改成功",
              type: "success",
            })
            this.getList()
          } else {
            this.$message.error("修改失败!")
          }
        })
        .catch(() => { })
      this.editDialog = false
    },
    // 馈线ID
    handleFeeder(row) {
      this.fullscreenLoading = true
      this.cuttentProCode = row.taskID
      this.feederDialog = true
      feeederObtainCurrent(row.taskID).then((res) => {
        console.log(res, " res")
        this.fullscreenLoading = false
        this.feederData = res.data.result.data
      })
      // this.flag = false;
      // this.$nextTick(() => {
      //   this.flag = true;
      // });
    },
    // 馈线保存按钮
    feederDialogQuery() {
      this.feederDialog = false
    },
    // 成果分析
    handleResults(row) {
      this.cuttentresult = row.taskID
      this.resultDialog = true
      this.flag = false
      this.$nextTick(() => {
        this.flag = true
      })
    },
    handleDesign(row) {
      this.dialogDesign = true
      this.SJProCode = row.taskID
      this.taskID = row.taskID
      console.log(row, "rororo")

      this.flag = false
      this.$nextTick(() => {
        this.flag = true
      })
    },
    // table 设计成果导入
    handleRequirements(row) {
      this.addProjectInfo.id = row.taskID
      this.requirementsDialog = true
    },
    // 设计成果导入弹框确认按钮
    requirementsDialogQuery() {
      this.requirementsDialog = false
    },
    // 上传传参
    getformData() {
      return {
        id: this.addProjectInfo.id,
      }
    },
    //上传成功
    handleSuccess() {
      this.$message.success("上传成功")
      this.requirementsDialog = false
    },
    // 上传失败
    handleError() {
      this.$message.error("上传失败")
    },
    // 上传前判断
    beforeAvatarUpload(file) {
      let fileName = file.name
      let pos = fileName.lastIndexOf(".")
      let lastName = fileName.substring(pos, fileName.length)
      if (lastName.toLowerCase() !== ".zip") {
        this.$message.error("文件必须为.zip")
        return
      }
    },
  },
}
</script>

<style lang="scss" >
.actionAreaPage {
  padding: 0 16px 12px 16px;
  display: flex;

}

.buttonArea {
  height: 18px;
  display: flex;
  line-height: 18px;
}
.progresswrapper {
  width: 90%;
  height: 15px;
  border-radius: 10px;
  color: white;
  line-height: 15px;
  position: relative;
}

.pro {
  height: 100%;
  border-radius: 10px;
  background: #00b83f;
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 40px 40px;
  animation: progressbar 2s linear infinite;
}

@keyframes progressbar {
  0% {
    background-position: 40px 0;
  }

  100% {
    background-position: 0 0;
  }
}

.expandArea {
  margin-left: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #606266;
  cursor: pointer;

  img {
    margin-left: 2px;
  }
}

.el-dialog__body {
  padding: 24px;
}
::v-deep .el-dialog__footer {
  padding: 16px 24px !important;
}
.zhezhao{
  position: absolute;
  top: 0;
  left: 0;
  z-index: 99;
}
</style>
