<template>
	<el-container class="layout">
		<el-container v-if="true">
			<el-aside class="aside">
				<el-menu :default-active="this.$route.id" :unique-opened="true" router class="el-menu-vertical-demo"
					text-color="#000" active-text-color="#526ade " style="text-align: center;height: 100%;border-right: solid 0px #e6e6e6;width:300px;
					text-color:#000;" @select="handleSelect">
					<!--单级菜单-->
					<el-menu-item>
						<el-submenu :index="item.name" v-for="item in menuData" :key="item.name">
							<template slot="title">
								<i class="el-icon-menu"></i>
								<span>{{item.text}}</span>
							</template>
							<el-menu-item-group>
								<el-menu-item :index="subItem.id" v-for="subItem in item.children" :key="subItem.id">
									{{subItem.text}}
								</el-menu-item>
							</el-menu-item-group>
						</el-submenu>
					</el-menu-item>
					<!--多级菜单-->

				</el-menu>
			</el-aside>
			<el-main class="main">
				<!-- <tag class="tag"></tag> -->
				<router-view></router-view>
			</el-main>
		</el-container>
		<el-main class="main" v-if="false">
			<!-- <tag class="tag"></tag> -->
			<router-view></router-view>
		</el-main>
	</el-container>
</template>

<script>
	import {
		getMenuChildren,
		resquest,
	} from '@/api/api'
	import Tag from '@/components/tag.vue'
	export default {
		name: 'layout',
		components: {
			Tag
		},
		watch: {
			//监听路由
			// '$route.path'(newVal, oldVal) {
			// 	if (newVal !== oldVal) {
			// 		this.activeIndex = '/' + location.hash.split('/')[1]
			// 	}
			// },
		},
		data() {
			return {
				url: resquest,
				menuData: [

				]
			}
		},
		mounted() {
			alert(1)
			this.getMenuList()
		},
		methods: {
			getMenuList() {

			},
			handleSelect(row) {
				const paramUser = {
					user: row
				}
				getMenuChildren(paramUser)
					.then((res) => {
						// this.menuData = res.data.result
						// console.log(res.data.result)
					})
					.catch(() => {})
			},
			signout() {
				this.$confirm('退出登录, 是否继续?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning',
				}).then(() => {
					this.$router.push({
						path: '/'
					})
					this.$Cookies.remove('userToken', {
						path: ''
					})
				})
			},
		},
	}
</script>
<style lang="scss" scoped>
	.tag {
		margin-bottom: 8px;
	}
</style>
