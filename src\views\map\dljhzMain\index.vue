<template>
  <div>
    <!--电缆井-->
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div v-if="showBackAdds" class="maps-zhedNav" @click="backCurrentDom">
            <img
              class="mapites-backImg"
              :src="require('@/assets/' + 'map/settingImg/backAdds.png')"
              alt=""
            />
          </div>
          <span />
          电缆井
          <div
            v-if="!showBackAdds"
            class="maps-zhedNav"
            @click="showEveryItemSet"
          >
            <img
              v-show="!isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zhedie.png')"
              alt=""
            />
            <img
              v-show="isFoldArea"
              class="mapites-zhed"
              :src="require('@/assets/' + 'map/settingImg/zkzhedie.png')"
              alt=""
            />
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img
            class="settingImg"
            :src="require('@/assets/' + 'map/settingImg/useSetting.png')"
            alt=""
          />
          <p>启用</p>
        </div>
      </div>
      <div v-show="isFoldArea" class="pro-addForm">
        <div class="map-showNav">基本信息</div>
        <van-row>
          <van-field
            v-model="cablePit.mark"
            label="编号"
            placeholder="请输入电缆井编号"
          />
        </van-row>
        <div class="map-showNav">选型信息</div>
        <van-row
          :style="{ pointerEvents: settingObj.isDisabledDraw ? 'none' : '' }"
        >
          <!--操作方式-->
          <van-field
            readonly
            :disabled="settingObj.isDisabledDraw"
            clickable
            :value="cablePit.operation"
            label="操作方式"
            placeholder="请选择操作方式"
            @click="settingObj.cablePit.operation = true"
          />
          <van-popup
            v-model="settingObj.cablePit.operation"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="操作方式"
              value-key="key"
              :columns="operation"
              @confirm="onConfirmDljSel(0, $event)"
              @cancel="settingObj.cablePit.operation = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--电缆井类型-->
          <van-field
            readonly
            clickable
            :value="cablePit.type"
            label="电缆井类型"
            placeholder="请选择电缆井类型"
            @click="settingObj.cablePit.type = true"
          />
          <van-popup v-model="settingObj.cablePit.type" round position="bottom">
            <van-picker
              show-toolbar
              title="电缆井类型"
              value-key="moduletypename"
              :columns="cablePitType"
              @confirm="onConfirmDljSel(1, $event)"
              @cancel="settingObj.cablePit.type = false"
            />
          </van-popup>
        </van-row>
        <!--电缆井型号-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="cablePit.model"
            label="电缆井型号"
            placeholder="请选择电缆井型号"
            @click="settingObj.cablePit.model = true"
          />
          <van-popup
            v-model="settingObj.cablePit.model"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="电缆井型号"
              value-key="moduleName"
              :columns="cablePitModel"
              @confirm="onConfirmDljSel(2, $event)"
              @cancel="settingObj.cablePit.model = false"
            />
          </van-popup>
        </van-row>
        <!--状态-->
        <van-row>
          <van-field
            readonly
            clickable
            :value="cablePit.state"
            label="状态"
            placeholder="请选择状态"
            @click="settingObj.cablePit.state = true"
          />
          <van-popup
            v-model="settingObj.cablePit.state"
            round
            position="bottom"
          >
            <van-picker
              show-toolbar
              title="状态"
              value-key="key"
              :columns="cableChannelsState"
              @confirm="onConfirmDljSel(3, $event)"
              @cancel="settingObj.cablePit.state = false"
            />
          </van-popup>
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { apiget,apipost } from "@/utils/mapRequest";
export default {
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false,
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false,
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false,
    },
    remodeState: {
      type: Boolean,
      defaults: false,
    },
  },
  data() {
    return {
      cablePitType: [], // 电缆井类型
      cablePitModel: [], // 电缆井型号
      isFoldArea: false,
      settingObj: {
        // 电缆井
        cablePit: {
          operation: false, // 操作方式
          type: false, // 电缆井类型
          model: false, // 型号
          state: false, // 状态
        },
      },
      // 电缆井
      cablePit: {
        operation: "插入电缆井", // 操作方式
        type: "直通", // 电缆井类型
        model: "", // 型号
        modelId: "", // 型号
        state: "新建", // 状态
        imgList: [], // 文件列表
        message: "", // 备注信息
        audioList: [], // 语音列表
      },
      cableChannelsState: [], // 土建路径状态
      lineState: [
        {
          key: "新建",
          value: "新建",
        },
        {
          key: "原有",
          value: "原有",
        },
      ],
      operation: [
        {
          key: "绘制电缆井",
          value: "绘制电缆井",
        },
        {
          key: "插入电缆井",
          value: "插入电缆井",
        },
      ], // 电缆井操作方式
    };
  },
  watch: {
    // 监听改前改后状态
    remodeState: {
      handler(newVal) {
        if (newVal) {
          // 改后不显示拆除的数据
          this.cableChannelsState = this.lineState.slice(0, 2);
          this.cablePit.state = "新建";
        } else {
          // 改前不显示新建的数据
          this.cableChannelsState = this.lineState.slice(1);
          this.cablePit.state = "原有";
        }
      },
      deep: true,
      immediate: true,
    },
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal;
      },
      deep: true,
      immediate: true,
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal;
        if (newVal.moduleType === "DLJHZ") {
          let dljType = null;
          for (const j in this.cablePitType) {
            if (data.dljType === this.cablePitType[j].moduletypename) {
              dljType = this.cablePitType[j].moduletypekey;
            }
          }
          this.selectCheck.objVis.showCablePit = true;
          this.settingObj.visible = true;
          // 这里没调用getAwaitTowerOrLineType是因为电缆井型号当中只调用一次
          // 不会出现动态更改得情况 所以就调了getFirstTowerOrLineType
          this.getAwaitTowerOrLineType(
            0,
            "",
            3,
            "",
            "",
            "DLJ",
            "",
            "",
            "",
            data.dljType
          );
          this.selectCheck.objVis.showCablePit = true;
          this.addParam.cablePit.imgList = [];
          this.addParam.cablePit.audioList = [];
          this.addParam.cablePit.operation = data.dljWay;
          this.addParam.cablePit.type = data.dljType;
          this.addParam.cablePit.state = data.state;
          this.addParam.cablePit.modelId = data.moduleId;
          this.addParam.cablePit.model = data.pointModule;
          this.addParam.cablePit.mark = data.mark; // 编号
          this.addParam.cablePit.message = data.note;
          for (const k in data.imgList) {
            const objs = {
              url: data.imgList[k].path,
              isImage: true,
              isSaveReport: data.imgList[k].isSaveReport,
            };
            this.addParam.cablePit.imgList.push(objs);
          }
          for (const s in data.voiceList) {
            const objs = {
              content: data.voiceList[s].path,
            };
            this.addParam.cablePit.audioList.push(objs);
          }
        }
      },
      deep: true,
    },
  },
  mounted() {
    /* 电缆井类型*/
    this.getFirstTowerOrLineType(0, "", 3, "", "", "DLJ", "", "");
  },
  methods: {
    /**
     * 电缆井
     */
    onConfirmDljSel(type, item) {
      const val = item.value;
      switch (type) {
        case 0:
          this.cablePit.operation = val;
          this.settingObj.cablePit.operation = false;
          break;
        case 1:
          this.cablePit.type = item.moduletypename;
          this.getTowerOrLineType(4, "", item.moduletypekey, "", "", "");
          this.settingObj.cablePit.type = false;
          break;
        case 2:
          this.cablePit.model = item.moduleName;
          this.cablePit.modelId = item.moduleID;
          this.settingObj.cablePit.model = false;
          break;
        case 3:
          this.cablePit.state = val;
          this.settingObj.cablePit.state = false;
          break;
      }
    },
    showEveryItemSet() {
      this.isFoldArea = !this.isFoldArea;
    },
    /**
     * 提交数据
     */
    submitData() {
      const parma = {
        type: 17,
        param: this.cablePit,
        visParam: this.settingObj.cablePit,
      };
      this.$emit("submitChildData", parma);
    },
    backCurrentDom() {
      this.$emit("backCurrentDom");
      this.getFirstTowerOrLineType(0, "", 3, "", "", "DLJ", "", "");
      this.cablePit.operation = "插入电缆井";
      this.cablePit.type = "直通";
      this.cablePit.model = "";
      const stateText = this.remodeState ? "新建" : "原有";
      this.cablePit.state = stateText;
      this.cablePit.imgList = [];
      this.cablePit.message = "";
      this.cablePit.audioList = [];
      this.settingObj.cablePit.state = false;
      this.settingObj.cablePit.type = false;
      this.settingObj.cablePit.model = false;
      this.settingObj.cablePit.state = false;
    },
    /**
     * 获取语音数据
     */
    getAudioData(data) {
      this.branchLine.audioList = data.aduioList;
    },
    /**
     * 获取上传图片数据
     */
    getImgData(data) {
      this.branchLine.imgList = data.imgList;
    },
    /**
     * 获取备注信息
     * @param data
     */
    getMsgData(data) {
      this.branchLine.message = data.message;
    },
    getTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          // 电缆井型号
          that.cablePitModel = res.data;
          if (res.data.length === 0) {
            that.cablePit.model = "";
            that.cablePit.modelId = "";
          } else {
            that.cablePit.model = res.data[0].moduleName;
            that.cablePit.modelId = res.data[0].moduleID;
          }
        }
      });
    },
    async getAwaitTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName,
      selectVal,
      selectLevelTwoVal
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      await apipost("/moduleSelection/selectModuleData", param).then(function (
        res
      ) {
        if (res.code === 1001) {
          switch (settype) {
            case 0:
              // 查询电缆井类型
              that.cablePitType = res.data;
              let dljType;
              for (const j in res.data) {
                if (selectVal === res.data[j].moduletypename) {
                  dljType = res.data[j].moduletypekey;
                }
              }
              that.getAwaitTowerOrLineType(1, "", 4, "", dljType, "", "", "");
              break;
            case 1:
              // 查询电缆井型号
              that.settingObj.cablePitModel = res.data;
              break;
          }
        }
      });
    },
    getFirstTowerOrLineType(
      settype,
      type,
      moduleType,
      materialsTypeKey,
      moduleTypeKey,
      parentKey,
      moduleCode,
      voltage,
      moduleName
    ) {
      const that = this;
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: "aaf86d47-14b7-4da6-9938-398e476a2a75", // 版本号暂时写死 版本号是后台给的
      };
      apipost("/moduleSelection/selectModuleData", param).then(function (res) {
        if (res.code === 1001) {
          switch (settype) {
            case 0:
              // 电缆井类型
              that.cablePit.type = res.data[0].moduletypename;
              that.cablePitType = res.data;
              /* 电缆井型号*/
              that.getFirstTowerOrLineType(
                1,
                "",
                4,
                "",
                res.data[0].moduletypekey,
                "",
                "",
                ""
              );
              break;
            case 1:
              // 电缆井型号
              that.cablePit.model = res.data[0].moduleName;
              that.cablePit.modelId = res.data[0].moduleID;
              that.cablePitModel = res.data;
              break;
          }
        }
      });
    },
  },
};
</script>

<style lang="sass" scoped>
</style>

