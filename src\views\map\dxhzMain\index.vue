<template>
  <div>
    <div v-show="showMainArea" class="pro-addEveryArea">
      <div class="pro-addTitle">
        <div class="pro-leftTitle">
          <div class="pro-leftTitle">
            <div v-if="showBackAdds" class="maps-zhedNav" @click="backCurrentDom">
              <img class="mapites-backImg" :src="require('@/assets/'+'map/settingImg/backAdds.png')" alt="">
            </div>
            <span />
            导线绘制
            <div v-if="!showBackAdds" class="maps-zhedNav" @click="showEveryItemSet">
              <img v-show="!isFoldArea" class="mapites-zhed" :src="require('@/assets/'+'map/settingImg/zhedie.png')" alt="">
              <img v-show="isFoldArea" class="mapites-zhed" :src="require('@/assets/'+'map/settingImg/zkzhedie.png')" alt="">
            </div>
          </div>
        </div>
        <div class="pro-rigTitle" @click="submitData">
          <img class="settingImg" :src="require('@/assets/'+'map/settingImg/useSetting.png')" alt="">
          <p>启用</p>
        </div>
      </div>
      <div v-show="isFoldArea" class="pro-addForm">
        <div v-show="isShowNav" class="map-showNav">基本信息</div>
        <van-row v-show="isShowNav">
          <van-field
            v-model="startPointMark"
            label="开始点编号"
            disabled
            placeholder="请输入杆塔编号"
          />
        </van-row>
        <van-row v-show="isShowNav">
          <van-field
            v-model="endPointMark"
            label="结束点编号"
            disabled
            placeholder="请输入杆塔编号"
          />
        </van-row>
        <div class="map-showNav">选型信息</div>
        <van-row>
          <!--导线电压等级-->
          <van-field
            readonly
            clickable
            :value="onductor.voltage"
            label="电压等级"
            placeholder="选择电压等级"
            @click="settingObj.onductor.voltageVis = true"
          />
          <van-popup v-model="settingObj.onductor.voltageVis" round position="bottom">
            <van-picker
              show-toolbar
              title="电压等级"
              value-key="key"
              :columns="allVoltage"
              @confirm="onConfirmDxSel(0, '', $event)"
              @cancel="settingObj.onductor.voltageVis = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--导线回路数-->
          <van-field
            readonly
            clickable
            :value="onductor.backLine"
            label="回路数"
            placeholder="选择回路数"
            @click="settingObj.onductor.backLineVis = true"
          />
          <van-popup v-model="settingObj.onductor.backLineVis" round position="bottom">
            <van-picker
              show-toolbar
              title="回路数"
              value-key="key"
              :columns="backLine"
              @confirm="onConfirmDxSel(1, '', $event)"
              @cancel="settingObj.onductor.backLineVis = false"
            />
          </van-popup>
        </van-row>
        <van-row>
          <!--导线-->
          <div :style="{width: '100%', overflowX: 'scroll'}">
            <table border="1" class="map-tables" cellspacing="0" cellpadding="0" align="center">
              <tr>
                <th :style="{width: '7rem'}">线路名称</th>
                <th :style="{width: '7rem'}">状态</th>
                <th :style="{width: '14rem'}">导线型号</th>
                <th>操作</th>
              </tr>
              <tr v-for="(item,index) in onductor.listArr">
                <td>
                  <van-field
                    v-model="item.lineName"
                    placeholder="请输入线路名称"
                  />
                </td>
                <td :style="{textAlign: 'center'}">
                  <van-field
                    readonly
                    clickable
                    :value="item.lineState"
                    placeholder="状态"
                    @click="settingObj.onductor.listArr[index].lineStateVis = true"
                  />
                  <van-popup v-model="settingObj.onductor.listArr[index].lineStateVis" round position="bottom">
                    <van-picker
                      title="状态"
                      show-toolbar
                      value-key="key"
                      :columns="cableLineState"
                      @confirm="onConfirmDxSel(2, index, $event)"
                      @cancel="settingObj.onductor.listArr[index].lineStateVis = false"
                    />
                  </van-popup>
                </td>
                <td :style="{textAlign: 'center'}">
                  <van-field
                    readonly
                    clickable
                    :value="item.lineModel"
                    placeholder="选导线型号"
                    @click="settingObj.onductor.listArr[index].lineModelVis = true"
                  />
                  <van-popup v-model="settingObj.onductor.listArr[index].lineModelVis" round position="bottom">
                    <van-picker
                      title="导线型号"
                      show-toolbar
                      value-key="name"
                      :columns="dxlineType"
                      @confirm="onConfirmDxSel(3, index, $event)"
                      @cancel="settingObj.onductor.listArr[index].lineModelVis = false"
                    />
                  </van-popup>
                </td>
                <td :style="{width:'4rem',textAlign:'center'}">
                  <span :style="{color:'red'}" @click="removeMainLine(0, index)">删除</span>
                </td>
              </tr>
            </table>
          </div>
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { apiget,apipost } from '@/utils/mapRequest'

export default {
  components: {},
  props: {
    showMainArea: {
      type: Boolean,
      defaults: false
    },
    // 是否展示主线路编辑区域
    showBackAdds: {
      type: Boolean,
      defaults: false
    },
    // 编辑的时候展示出对应的经纬度
    isShowNav: {
      type: Boolean,
      defaults: false
    },
    // 编辑的id
    editData: {
      type: Object,
      defaults: () => {
      }
    },
    // 导线型号默认值
    dxList: {
      type: Array,
      defaults: () => []
    },
    remodeState: {
      type: Boolean,
      defaults: false
    }
  },
  watch: {
    // 监听改前改后状态
    remodeState: {
      handler(newVal) {
        if (newVal) {
          // 改后不显示拆除的数据
          this.cableLineState = this.lineState.slice(0, 2)
          for (const j in this.onductor.listArr) {
            this.onductor.listArr[j].lineState = '新建'
          }
        } else {
          // 改前不显示新建的数据
          this.cableLineState = this.lineState.slice(1)
          for (const j in this.onductor.listArr) {
            this.onductor.listArr[j].lineState = '原有'
          }
        }
      },
      deep: true,
      immediate: true
    },
    showBackAdds: {
      handler(newVal) {
        this.isFoldArea = newVal
      },
      deep: true,
      immediate: true
    },
    // 这里监听id 如果有值 那就是编辑，重新去赋值
    editData: {
      handler(newVal) {
        const data = newVal
        if (data.moudleType === 'DXHZ') {
          this.settingObj.onductor.listArr = []
          this.onductor.listArr = []
          const valltegedxhz = data.voltage === '10kV' ? '10kV' : '0.4kV'
          this.getAwaitTowerOrLineType(2, 1, '', '', '', '', valltegedxhz)
          this.onductor.backLine = data.loopNum
          this.onductor.voltage = data.voltage
          for (const j in data.listArr) {
            const obj = {
              voltage: data.listArr[j].voltage,
              lineName: data.listArr[j].lineName, // 线路名称
              lineState: data.listArr[j].lineState, // 线路状态
              lineModel: data.listArr[j].lineModel, // 导线型号
              lineModelId: data.listArr[j].moduleId // 导线id
            }
            const isShow_one = {
              lineStateVis: false, // 主线路状态
              lineModelVis: false // 主线路导线型号
            }
            this.settingObj.onductor.listArr.push(isShow_one)
            this.onductor.listArr.push(obj)
          }
        }
      },
      deep: true
    },
    dxList: {
      handler(newVal) {
        this.onductor.listArr[0].lineModel = newVal[0].name
        this.onductor.listArr[0].lineModelId = newVal[0].id
        this.dxlineType = newVal
      },
      deep: true
    }
  },
  data() {
    return {
      isFoldArea: false,
      startPointMark: '', // 开始点编号
      endPointMark: '', // 结束点编号
      startLngtitude: 0, // 有起始点存在情况的开始点经度
      startLattitude: 0, // 有起始点存在情况的开始点纬度
      endLngtitude: 0, // 有起始点存在情况的结束点经度
      endLattitude: 0, // 有起始点存在情况的结束点纬度
      settingObj: {
        // 导线
        onductor: {
          voltageVis: false, // 电压等级
          backLineVis: false, // 状态
          listArr: [
            {
              lineStateVis: false, // 主线路状态
              lineModelVis: false // 主线路导线型号
            }
          ]
        }
      },
      backLine: [
        {
          key: '单回',
          value: '单回'
        },
        {
          key: '双回',
          value: '双回'
        },
        {
          key: '三回',
          value: '三回'
        },
        {
          key: '四回',
          value: '四回'
        }
      ], // 回路
      allVoltage: [
        {
          key: '10kV',
          value: '10kV'
        },
        {
          key: '380V',
          value: '380V'
        },
        {
          key: '220V',
          value: '220V'
        }
      ], // 电压等级

      // 导线
      onductor: {
        voltage: '10kV', // 电压等级
        backLine: '单回', // 回路数
        imgList: [], // 文件列表
        message: '', // 备注信息
        audioList: [], // 语音列表
        listArr: [
          {
            lineName: '线路一', // 线路名称
            lineState: '新建', // 线路状态
            lineModel: '', // 导线型号
            lineModelId: '', // 导线型号id
            voltage: '', // 电压等级
            projectId: this.$route.query.childProjectId // 工程id
          }
        ]
      },
      cableLineState: [], // 电缆线路状态
      lineState: [
        {
          key: '新建',
          value: '新建'
        },
        {
          key: '原有',
          value: '原有'
        },
        {
          key: '拆除',
          value: '拆除'
        }
      ],
      dxlineType: [] // 导线型号
    }
  },
  mounted() {
  },
  methods: {
    showEveryItemSet() {
      this.isFoldArea = !this.isFoldArea
    },
    backCurrentDom() {
      this.$emit('backCurrentDom')
      this.onductor.listArr[0].lineModel = this.dxList[0].name
      this.onductor.listArr[0].lineModelId = this.dxList[0].id
      this.dxlineType = this.dxList.tenDx
      // 导线
      this.onductor.voltage = '10kV'
      this.onductor.backLine = '单回'
      this.onductor.imgList = []
      this.onductor.message = ''
      this.onductor.audioList = []
      const stateText = this.remodeState ? '新建' : '原有'
      this.onductor.listArr = [
        {
          lineName: '线路一', // 线路名称
          lineState: stateText, // 线路状态
          lineModel: '' // 导线型号
        }
      ]
      this.settingObj.onductor.voltageVis = false
      this.settingObj.onductor.backLineVis = false
      this.settingObj.onductor.listArr = [
        {
          lineStateVis: false, // 主线路状态
          lineModelVis: false // 主线路导线型号
        }
      ]
    },
    submitData() {
      const parma = {
        type: 3,
        param: this.onductor,
        visParam: this.settingObj.onductor
      }
      this.$emit('submitChildData', parma)
    },
    onConfirmDxSel(type, index, item) {
      const val = item.value
      switch (type) {
        case 0:
          this.onductor.voltage = val
          const valltege = val === '10kV' ? '10kV' : '0.4kV'
          // 查询导线绘制的导线型号
          this.getFirstTowerOrLineType(2, 1, '', '', '', '', valltege)
          this.settingObj.onductor.voltageVis = false
          break
        case 1:
          this.onductor.backLine = val
          const stateText = this.remodeState ? '新建' : '原有'
          let length = 0
          if (val === '单回') {
            length = 1
          } else if (val === '双回') {
            length = 2
          } else if (val === '三回') {
            length = 3
          } else {
            length = 4
          }
          this.onductor.listArr = []
          this.settingObj.onductor.listArr = []
          console.log('this.dxlineType', this.dxlineType)
          for (var i = 0; i < length; i++) {
            const item_one = {
              lineName: '线路' + (Number(i) + 1), // 线路名称
              lineState: stateText, // 线路状态
              lineModel: this.dxlineType[0].name, // 导线型号
              lineModelId: this.dxlineType[0].id, // 导线id
              voltage: this.onductor.voltage,
              projectId: this.$route.query.childProjectId // 工程id
            }
            const isShow_one = {
              lineStateVis: false, // 主线路状态
              lineModelVis: false // 主线路导线型号
            }
            this.onductor.listArr.push(item_one)
            this.settingObj.onductor.listArr.push(isShow_one)
          }
          this.settingObj.onductor.backLineVis = false
          break
        case 2:
          this.onductor.listArr[index].lineState = val
          this.settingObj.onductor.listArr[index].lineStateVis = false
          break
        case 3:
          this.onductor.listArr[index].lineModel = item.name
          this.onductor.listArr[index].lineModelId = item.id
          this.settingObj.onductor.listArr[index].lineModelVis = false
          break
      }
    },
    getFirstTowerOrLineType(type, moduleType, materialsTypeKey, moduleTypeKey, parentKey, moduleCode, voltage, moduleName) {
      const that = this
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: 'aaf86d47-14b7-4da6-9938-398e476a2a75' // 版本号暂时写死 版本号是后台给的
      }
      apipost('/moduleSelection/selectModuleData', param).then(function(res) {
        console.log('res.data.', res.data)
        if (res.code === 1001) {
          if (voltage === '10kV') {
            // 同杆并架导线型号
            that.onductor.listArr[0].lineModel = res.data.tenDx[0].name
            that.onductor.listArr[0].lineModelId = res.data.tenDx[0].id
            that.dxlineType = res.data.tenDx
          } else {
            // 同杆并架导线型号
            that.dxlineType = res.data.lowDx
            that.onductor.listArr[0].lineModel = res.data.lowDx[0].name
            that.onductor.listArr[0].lineModelId = res.data.lowDx[0].id
          }
        }
      })
    },
    getAwaitTowerOrLineType(settype, type, moduleType, materialsTypeKey, moduleTypeKey, parentKey, moduleCode, voltage, moduleName, selectVal, selectLevelTwoVal) {
      const that = this
      const param = {
        voltage: voltage,
        type: type,
        materialsTypeKey: materialsTypeKey,
        moduleType: moduleType,
        moduleTypeKey: moduleTypeKey,
        parentKey: parentKey,
        moduleCode: moduleCode,
        userId: this.userId,
        moduleName: moduleName,
        version: 'aaf86d47-14b7-4da6-9938-398e476a2a75' // 版本号暂时写死 版本号是后台给的
      }
      apipost('/moduleSelection/selectModuleData', param).then(function(res) {
        if (res.code === 1001) {
          if (voltage === '10kV') {
            that.dxlineType = res.data.tenDx
          } else {
            that.dxlineType = res.data.lowDx
          }
        }
      })
    }
  }
}

</script>

<style lang="sass" scoped>
</style>

