@charset "utf-8";

/* 禁用iPhone中Safari的字号自动调整 */
html,body {
  touch-action: manipulation;
  height: 100vh;
  width: 100vw;
  margin: 0 !important;
  padding: 0 !important;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  /* 解决IOS默认滑动很卡的情况 */
  -webkit-overflow-scrolling : touch;
  overflow-scrolling: touch;
  font-size: 14px;
}

/* 禁止缩放表单 */
input[type="submit"], input[type="reset"], input[type="button"], input {
  resize: none;
  border: none;
}
/*隐藏地图得定位按钮*/
.sgmap-ctrl-group{
  display: none;
}

/* 取消链接高亮  */
body, div, ul, li, ol, h1, h2, h3, h4, h5, h6, input, textarea, select, p, dl, dt, dd, a, img, button, form, table, th, tr, td, tbody, article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/* 设置HTML5元素为块 */
article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
  display: block;
}

/* 图片自适应 */
img {
  width: 100%;
  height: auto;
  width: auto\9; /* ie8 */
  display: block;
  -ms-interpolation-mode: bicubic;/*为了照顾ie图片缩放失真*/
}

/* 初始化 */
body, div, ul, li, ol, h1, h2, h3, h4, h5, h6, input, textarea, select, p, dl, dt, dd, a, img, button, form, table, th, tr, td, tbody, article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
  margin: 0;
  padding: 0;
}
body {
  color: #555;
  background-color: #F7F7F7;
}
/*@media screen and (min-width: 375px){*/
/*  html {*/
/*    font-size: 16px;*/
/*  }*/
/*  .markerPointLine{*/
/*    margin-top: -2rem;*/
/*  }*/
/*}*/
/*@media screen and (max-width: 375px) {*/
/*  html {*/
/*    font-size: 13.5px;*/
/*  }*/
/*  .markerPointLine{*/
/*    margin-top: -1rem;*/
/*  }*/
/*}*/
/*@media screen and (max-width: 360px){*/
/*  html {*/
/*    font-size: 12px;*/
/*  }*/
/*}*/
em, i {
  font-style: normal;
}
ul,li{
  list-style-type: none;
}
strong {
  font-weight: normal;
}
.clearfix:after {
  content: "";
  display: block;
  visibility: hidden;
  height: 0;
  clear: both;
}
.clearfix {
  zoom: 1;
}
a {
  text-decoration: none;
  color: #969696;
  font-family: 'Microsoft YaHei', Tahoma, Arial, sans-serif;
}
a:hover {
  text-decoration: none;
}
ul, ol {
  list-style: none;
}
h1, h2, h3, h4, h5, h6 {
  font-size: 100%;
  font-family: 'Microsoft YaHei';
}
img {
  border: none;
}
input{
  font-family: 'Microsoft YaHei';
}
/*单行溢出*/
.one-txt-cut{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
/*多行溢出 手机端使用*/
.txt-cut{
  overflow : hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  /* -webkit-line-clamp: 2; */
  -webkit-box-orient: vertical;
}
/* 移动端点击a链接出现蓝色背景问题解决 */
a:link,a:active,a:visited,a:hover {
  background: none;
  -webkit-tap-highlight-color: rgba(0,0,0,0);
  -webkit-tap-highlight-color: transparent;
}
.w50{
  width: 50%;
}
.w25{
  width: 25%;
}
.w20{
  width: 20%;
}
.w33{
  width: 33.333333%;
}
.fl{
  float: left;
}
.fr{
  float: right;
}
.db{
  display: block !important;
}
.dn{
  display: none;
}
#Vue{
  height: 100vh;
  width: 100vw;
}
/*设置button得样式*/
.setBtn{
  width: 3rem;
  height: 1.4rem;
  line-height: 1.4rem;
  border-radius: 4px;
  color: #526ADE;
  text-align: center;
  background: none;
  margin-right: 0.3rem;
  border: 1px solid rgba(187, 187, 187, 100);
}
/*vant navbar的样式*/
.van-nav-bar__content{
  background: #526ADE;
}
.van-nav-bar__content .van-nav-bar__title {
  color: #ffffff;
}
.van-nav-bar__content .van-nav-bar__right .van-icon{
  color: #ffffff;
}
/*vant van-collapse-item__wrapper*/
.van-collapse-item__wrapper .van-collapse-item__content{
  color: #717070;
}
/*van-tabbar-item__icon*/
.van-tabbar{
  height: 4rem !important;
}
.van-tabbar-item__icon {
  width: 2.8rem;
  height: 2.1rem;
  margin-bottom: 0 !important;
}
.van-tabbar-item__icon img{
  width: 100% !important;
  height: 100% !important;
}
.van-tabbar-item__text{
  margin-top: 0.2rem;
}
.prolist-setting{
  overflow-y: scroll;
  margin: 8px 9px 8px 7px;
  border-radius: 8px;
  border: 1px solid #fff;
  background: #ffffff;
}
.pro-oveflowScroll{
  position: absolute;
  width: 100%;
  overflow-y: scroll;
  height: calc(100vh - 3rem);
}
.prolist-addpro{
  position: fixed;
  z-index: 100;
  height: 100vh;
  top: 0;
  width: 100vw;
  text-align: center;
  background: #F5F6FA;
  overflow-y: scroll;
}
.pro-addNav{
  height: 46px;
  line-height: 46px;
  font-size: 16px;
  color: #ffffff;
  background: #526ADE;
}
.pro-addCard{
  margin: 8px 9px 8px 7px;
  border-radius: 8px;
  border: 1px solid #fff;
  background: #ffffff;
}
.pro-addTitle {
  text-align: left;
  font-size: 1rem;
  height: 2.5rem;
  line-height: 2.5rem;
  display: flex;
  background: #ffffff;
  font-weight: bold;
  color: #000000;
  margin: 0 6px;
  border-bottom: 1px solid #f5f2f2;
}
.pro-addTitle .leftScrow{
  width: 0.3rem;
  background: #526ADE;
  height: 1.2rem;
  display: inline-block;
  margin-top: 0.65rem;
  margin-right: 0.5rem;
}
.pro-addTitle .pro-leftTitle{
  display: flex;
  flex: 1;
}
.pro-addTitle .pro-rigTitle{
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-right: 1rem;
  width: 4rem;
}
.pro-addTitle .pro-rigTitle .settingImg{
  width: 1.2rem;
  height: 1.2rem;
  margin-right: 0.3rem;
}
.pro-addTitle .pro-rigTitle span{
  display: inline-block;
  margin-left: 0.3rem;
  width: 2.5rem;
}

.pro-addTitle .pro-leftTitle .maps-zhedNav .mapites-zhed{
  font-size: 1rem;
  width: 1.5rem;
  height: 1.2rem;
  margin-left: 1rem;
  margin-top: 0.65rem;
}
.pro-addForm{
  /*height: 2.8rem;*/
  line-height:2.8rem;
  border-bottom: 1px solid #f5f2f2;
  margin: 0 6px;
}
.pro-addForm .van-field__control{
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}
.pro-addForm .map-dlxh .van-field__control{
  white-space: nowrap;
  overflow: auto;
  text-overflow: unset;
}
.prolist-warning{
  color: #F71212;
  height: 1.5rem;
  line-height: 1.5rem;
}
.pro-setBtn{
  position: fixed;
  bottom: 0;
  width: 100vw;
  height: 3rem;
  line-height: 3rem;
  display: flex;
  background: #ffffff;
}
.pro-setBtn button{
  flex: 1;
  border: none;
  color: #ffffff;
}
.pro-setBtn .pro-cancle{
  background: #ffffff;
  border: 1px solid #526ADE;
  border-radius: 20px;
  color: #526ADE;
}
.pro-setBtn .pro-confirm{
  background: #526ADE;
  border-radius: 20px;
}
/*.van-nav-bar .van-icon*/
.van-nav-bar .van-icon,.van-nav-bar__text{
  color: #ffffff !important;
}
/*van-tab*/
.van-tab {
  background: #ffffff;
  color: black;
  border-bottom: 1px solid #d4cece;
}
.van-tabs__wrap{
  position: fixed;
  height: 3rem;
  width: 100vw;
  z-index: 99;
  top: 46px;
}
.van-tabs .van-tabs__content{
  position: absolute;
  width: 100%;
  top: 2.8rem;
}
.van-tab__pane{
  background: #F5F6FA;
}
/*.van-tabs__wrap .van-tabs__nav .van-tab--active{*/
/*    background: #526ADE;*/
/*    color: #ffffff;*/
/*}*/
/*设置全局的表格样式*/
.proinfo-scaleTable{
  width: 97vw;
  border-color: white;
  text-align: center;
  font-size: 0.9rem;
  margin: 0 6px;
}
.proinfo-scaleTable .proinfo-scaleThead{
  height: 2rem;
  line-height: 2rem;
  background: #adabab;
  color: white;
}
/*van-popup气泡弹出框样式*/
.van-popover--light{
  right: 5px;
}
.van-collapse-item .van-cell__title{
  font-size: 1rem;
}
/*全局loading*/
.overDelay {
  z-index: 100;
  background: rgba(0, 0, 0, 0.7);
}
/*解决vue得html页面未加载完毕闪烁问题*/
[v-cloak] {
  display: none ;
}
.van-row{
  border-bottom: 1px solid #f5f2f2;
}
.van-uploader .van-uploader__wrapper .van-uploader__upload{
  margin: 0;
}
.van-tabbar-item--active{
  color: #526ADE !important;
}
