/*
 * @Description: 描述
 * @version:
 * @Author: fengcan<PERSON><PERSON>@gmail.com
 * @Date: 2023-08-25 15:42:40
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-08-28 17:00:21
 * @FilePath: \safeHttpDemoV2\src\http.js
 */
import axios from "axios"
import { sm2, sm3 } from "sm-crypto"
/**
 * axios 实例
 * */
const axiosInstance = axios.create({
  timeout: 1000 * 60,
  baseURL: process.env.VUE_APP_API_publicUrlLocal
})
/**
 * @description: 请求拦截器
 * @param {*}
 * @return {*}
 */
axiosInstance.interceptors.request.use(
  (config) => {
    console.log(config.url,'lllllll');
    // 浏览器指纹ID
    const browserFingerprint = sessionStorage.getItem("browserFingerprint")
    // getTime接口返回的安全数据
    let safetyData = sessionStorage.getItem("bhxnsafetyData")
    // let safetyData = sessionStorage.getItem("wbSafetyData")
    console.log(safetyData,"测试")
    if (safetyData) {
      safetyData = JSON.parse(safetyData)
      // 请求头中添加浏览器指纹ID属性
      config.headers.zwId = "04" + sm2.doEncrypt(browserFingerprint, safetyData.flagfour)
      // 请求头中添加防重放属性
      config.headers.antiReplayId =
      "04" +
      sm2.doEncrypt(
        `${new Date().getTime() + safetyData.diffeValue}${sm3(
         config.url + (config.data ? JSON.stringify(config.data) : "")
        )}-FGF-${safetyData.flagthr}`,
        safetyData.flagfour
      );
        if(process.env.VUE_APP_API_isHasprofessionalType=='1'){
          config.headers.professionalType = "unifyworkbench" //网关标识
        }
        if(!config.url.includes('portalController/getInfo')){
          config.data &&
          (config.data = {
            param:
              "04" +
              sm2.doEncrypt(JSON.stringify(config.data), safetyData.flagfour),
          })
        }
      config.headers.accessTicket = sessionStorage.getItem('PWYJYBJ-BHXN')
    }
    
    config.headers['Content-Type']='application/json'
    config.data && (config.data = JSON.stringify(config.data))
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

/**
 * @description: 响应拦截器
 * @param {*}
 * @return {*}
 */
axiosInstance.interceptors.response.use(
  (response) => {
    console.log(response);
    // getTime接口返回的安全数据 
    const safetyData = JSON.parse(sessionStorage.getItem("bhxnsafetyData"))
    // const safetyData = JSON.parse(sessionStorage.getItem("wbSafetyData"))
  
   if(!response.config.url.includes("portalController/getInfo")){
    response.data &&
    (response.data =
      safetyData && !response.config.noDoDecrypt
        ? sm2.doDecrypt(
          response.data.slice(2, response.data.length),
          safetyData.flagfive
        ) // 返参解密
        : response.data)
   }
    
    let parseData = typeof response.data === "string" && JSON.parse(response.data)
    console.warn(parseData,'返回参数222');
    return { ...response, data: parseData || response.data }

  },
  (error) => {
    return Promise.reject(error)
  }
)

export default axiosInstance
