define(["./when-b60132fc","./buildModuleUrl-4e1b81e7","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./GeometryAttribute-3a88ba31","./GeometryAttributes-252e9929","./PrimitiveType-a54dc62f","./Cartesian2-47311507","./Math-119be1a3","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./FeatureDetection-c3b71206","./Cartesian4-3ca25aab"],(function(e,t,n,r,a,i,o,u,c,y,p,b,d,s,m){"use strict";function f(){this._workerName="createPlaneOutlineGeometry"}f.packedLength=0,f.pack=function(e,t){return t},f.unpack=function(t,n,r){return e.defined(r)?r:new f};var w=new n.Cartesian3(-.5,-.5,0),C=new n.Cartesian3(.5,.5,0);return f.createGeometry=function(){var e=new o.GeometryAttributes,r=new Uint16Array(8),c=new Float64Array(12);return c[0]=w.x,c[1]=w.y,c[2]=w.z,c[3]=C.x,c[4]=w.y,c[5]=w.z,c[6]=C.x,c[7]=C.y,c[8]=w.z,c[9]=w.x,c[10]=C.y,c[11]=w.z,e.position=new i.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:c}),r[0]=0,r[1]=1,r[2]=1,r[3]=2,r[4]=2,r[5]=3,r[6]=3,r[7]=0,new i.Geometry({attributes:e,indices:r,primitiveType:u.PrimitiveType.LINES,boundingSphere:new t.BoundingSphere(n.Cartesian3.ZERO,Math.sqrt(2))})},function(t,n){return e.defined(n)&&(t=f.unpack(t,n)),f.createGeometry(t)}}));
