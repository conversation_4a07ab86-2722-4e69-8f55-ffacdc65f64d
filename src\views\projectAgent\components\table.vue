<template>
  <div id="">
    <el-table :data="gridData">
      <el-table-column type="index" width="60" align="center" label="序号">
      </el-table-column>
      <el-table-column property="name" label="解析内容" width="120" align="center"></el-table-column>
      <el-table-column property="" label="解析" align="center">
        <template slot-scope="scope">
          <span @click="handleView(scope.row)" style="color: #526ade; cursor: pointer">查看</span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination style="margin: 10px 0px" background :current-page="q.page" :page-sizes="pageSize"
      :page-size="q.limit" layout="total, sizes, prev, pager, next" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" :total="totals">
    </el-pagination>
    <el-dialog title="完整性分析" :visible.sync="DialogVisible" width="50%" center :append-to-body="true">
      <el-table :data="gridDatas">
        <el-table-column type="index" label="序号" align="center" :resizable="false" width="60">
        </el-table-column>
        <el-table-column property="name" label="必备材料" width="120" align="center"></el-table-column>
        <el-table-column property="ist" label="是否具有" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.ist == '1'" style="color: green">是</span>
            <span v-else style="color: red">否</span>
          </template>
        </el-table-column>
        <el-table-column property="filenum" label="文件数" align="center">
        </el-table-column>
      </el-table>
      <el-pagination style="margin: 10px 0px" background :current-page="params.pageIndex" :page-size="params.pageSize"
        layout="total, sizes, prev, pager, next" @current-change="handleCurrentChanges" @size-change="handleSizeChanges"
        :total="total">
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import {
  completeView
} from '@/api/api'
export default {
  components: {},
  props: {
    ProCode: {
      type: String,
      default: '',
    }
  },
  data () {
    return {
      gridData: [{
        name: '完整性解析',
      }],
      gridDatas: [
      ],
      pageSize: [5, 10, 20, 50, 100], //分页页数
      q: {
        page: 1,
        limit: 5
      },
      totals: 1,
      total: 0, //总共页数
      DialogVisible: false, //分析
      params: {
        taskid: this.ProCode,
        pageIndex: 1,
        pageSize: 5,
      }
    }
  },
  mounted () {
    // this.getHeight()
    window.addEventListener('resize', this.getHeight)
  },
  methods: {
    // ↓在methods里面(窗体大小改变计算表格高度)
    // getHeight() {
    // 	this.tableHeight = (window.innerHeight - 320)
    // },
    // table列表序号索引
    indexMethod (index) {
      return (this.form.page - 1) * 10 + index + 1
    },
    // 查看
    handleView () {
      this.params.taskid = this.ProCode
      this.DialogVisible = true
      completeView(this.params)
        .then((res) => {
          console.log(res)
          this.gridDatas = res.data.result
          this.total = res.data.count
        })
        .catch(() => { })
    },
    handleCurrentChange () {

    },
    handleSizeChange () {

    },
    handleCurrentChanges (row) {
      this.params.pageIndex = row
      this.handleView()
    },
    handleSizeChanges (row) {
      this.params.pageSize = row
      this.handleView()
    }
  }
}
</script>

<style scoped="scoped">
.TreeBorder {
  border: 1px solid #cccccc;
  height: 100vh;
  border-radius: 5px;
  overflow: hidden;
}
</style>
