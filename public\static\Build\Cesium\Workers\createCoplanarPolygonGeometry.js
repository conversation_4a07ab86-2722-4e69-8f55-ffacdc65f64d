define(["./arrayRemoveDuplicates-d2f048c5","./BoundingRectangle-3ed8ca6d","./buildModuleUrl-4e1b81e7","./Cartesian2-47311507","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./CoplanarPolygonGeometryLibrary-e9907932","./when-b60132fc","./GeometryAttribute-3a88ba31","./GeometryAttributes-252e9929","./GeometryInstance-68aae013","./GeometryPipeline-9d1ef0b6","./IndexDatatype-8a5eead4","./Math-119be1a3","./PrimitiveType-a54dc62f","./PolygonGeometryLibrary-92af6f1e","./PolygonPipeline-660e1625","./VertexFormat-6446fca0","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./OrientedBoundingBox-87d59c2a","./Cartesian4-3ca25aab","./EllipsoidTangentPlane-edb321d3","./IntersectionTests-7386ffbf","./Plane-7ae8294c","./FeatureDetection-c3b71206","./AttributeCompression-90851096","./EncodedCartesian3-f1396b05","./ArcType-29cf2197","./EllipsoidRhumbLine-ed1a6bf4","./earcut-2.2.1-20c8012f"],(function(e,t,a,n,r,o,i,l,s,p,c,y,u,m,d,g,b,v,f,h,C,x,P,w,A,F,G,L,E,T,D,_,k){"use strict";var V=new r.Cartesian3,R=new t.BoundingRectangle,M=new n.Cartesian2,I=new n.Cartesian2,H=new r.Cartesian3,B=new r.Cartesian3,O=new r.Cartesian3,z=new r.Cartesian3,S=new r.Cartesian3,N=new r.Cartesian3,Q=new p.Quaternion,U=new g.Matrix3,j=new g.Matrix3,Y=new r.Cartesian3;function q(e,t,a,o,l,s,y,u){var b=e.positions,f=v.PolygonPipeline.triangulate(e.positions2D,e.holes);f.length<3&&(f=[0,1,2]);var h=m.IndexDatatype.createTypedArray(b.length,f.length);h.set(f);var C=U;if(0!==o){var x=p.Quaternion.fromAxisAngle(s,o,Q);if(C=g.Matrix3.fromQuaternion(x,C),t.tangent||t.bitangent){x=p.Quaternion.fromAxisAngle(s,-o,Q);var P=g.Matrix3.fromQuaternion(x,j);y=r.Cartesian3.normalize(g.Matrix3.multiplyByVector(P,y,y),y),t.bitangent&&(u=r.Cartesian3.normalize(r.Cartesian3.cross(s,y,u),u))}}else C=g.Matrix3.clone(g.Matrix3.IDENTITY,C);var w=I;t.st&&(w.x=a.x,w.y=a.y);for(var A=b.length,F=3*A,G=new Float64Array(F),L=t.normal?new Float32Array(F):void 0,E=t.tangent?new Float32Array(F):void 0,T=t.bitangent?new Float32Array(F):void 0,D=t.st?new Float32Array(2*A):void 0,_=0,k=0,R=0,H=0,B=0,O=0;O<A;O++){var z=b[O];if(G[_++]=z.x,G[_++]=z.y,G[_++]=z.z,t.st){var S=l(g.Matrix3.multiplyByVector(C,z,V),M);n.Cartesian2.subtract(S,w,S);var N=d.CesiumMath.clamp(S.x/a.width,0,1),Y=d.CesiumMath.clamp(S.y/a.height,0,1);D[B++]=N,D[B++]=Y}t.normal&&(L[k++]=s.x,L[k++]=s.y,L[k++]=s.z),t.tangent&&(E[H++]=y.x,E[H++]=y.y,E[H++]=y.z),t.bitangent&&(T[R++]=u.x,T[R++]=u.y,T[R++]=u.z)}var q=new c.GeometryAttributes;return t.position&&(q.position=new p.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:G})),t.normal&&(q.normal=new p.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:L})),t.tangent&&(q.tangent=new p.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:E})),t.bitangent&&(q.bitangent=new p.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:T})),t.st&&(q.st=new p.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:D})),new p.Geometry({attributes:q,indices:h,primitiveType:g.PrimitiveType.TRIANGLES})}function J(e){var t=(e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT)).polygonHierarchy,a=s.defaultValue(e.vertexFormat,f.VertexFormat.DEFAULT);this._vertexFormat=f.VertexFormat.clone(a),this._polygonHierarchy=t,this._stRotation=s.defaultValue(e.stRotation,0),this._ellipsoid=n.Ellipsoid.clone(s.defaultValue(e.ellipsoid,n.Ellipsoid.WGS84)),this._workerName="createCoplanarPolygonGeometry",this.packedLength=b.PolygonGeometryLibrary.computeHierarchyPackedLength(t)+f.VertexFormat.packedLength+n.Ellipsoid.packedLength+2}J.fromPositions=function(e){return new J({polygonHierarchy:{positions:(e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT)).positions},vertexFormat:e.vertexFormat,stRotation:e.stRotation,ellipsoid:e.ellipsoid})},J.pack=function(e,t,a){return a=s.defaultValue(a,0),a=b.PolygonGeometryLibrary.packPolygonHierarchy(e._polygonHierarchy,t,a),n.Ellipsoid.pack(e._ellipsoid,t,a),a+=n.Ellipsoid.packedLength,f.VertexFormat.pack(e._vertexFormat,t,a),a+=f.VertexFormat.packedLength,t[a++]=e._stRotation,t[a]=e.packedLength,t};var W=n.Ellipsoid.clone(n.Ellipsoid.UNIT_SPHERE),Z=new f.VertexFormat,K={polygonHierarchy:{}};return J.unpack=function(e,t,a){t=s.defaultValue(t,0);var r=b.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t);t=r.startingIndex,delete r.startingIndex;var o=n.Ellipsoid.unpack(e,t,W);t+=n.Ellipsoid.packedLength;var i=f.VertexFormat.unpack(e,t,Z);t+=f.VertexFormat.packedLength;var l=e[t++],p=e[t];return s.defined(a)||(a=new J(K)),a._polygonHierarchy=r,a._ellipsoid=n.Ellipsoid.clone(o,a._ellipsoid),a._vertexFormat=f.VertexFormat.clone(i,a._vertexFormat),a._stRotation=l,a.packedLength=p,a},J.createGeometry=function(t){var n=t._vertexFormat,o=t._polygonHierarchy,i=t._stRotation,s=o.positions;if(!((s=e.arrayRemoveDuplicates(s,r.Cartesian3.equalsEpsilon,!0)).length<3)){var c=H,g=B,v=O,f=S,h=N;if(l.CoplanarPolygonGeometryLibrary.computeProjectTo2DArguments(s,z,f,h)){if(c=r.Cartesian3.cross(f,h,c),c=r.Cartesian3.normalize(c,c),!r.Cartesian3.equalsEpsilon(z,r.Cartesian3.ZERO,d.CesiumMath.EPSILON6)){var C=t._ellipsoid.geodeticSurfaceNormal(z,Y);r.Cartesian3.dot(c,C)<0&&(c=r.Cartesian3.negate(c,c),f=r.Cartesian3.negate(f,f))}var x=l.CoplanarPolygonGeometryLibrary.createProjectPointsTo2DFunction(z,f,h),P=l.CoplanarPolygonGeometryLibrary.createProjectPointTo2DFunction(z,f,h);n.tangent&&(g=r.Cartesian3.clone(f,g)),n.bitangent&&(v=r.Cartesian3.clone(h,v));var w=b.PolygonGeometryLibrary.polygonsFromHierarchy(o,x,!1),A=w.hierarchy,F=w.polygons;if(0!==A.length){s=A[0].outerRing;for(var G=a.BoundingSphere.fromPoints(s),L=b.PolygonGeometryLibrary.computeBoundingRectangle(c,P,s,i,R),E=[],T=0;T<F.length;T++){var D=new y.GeometryInstance({geometry:q(F[T],n,L,i,P,c,g,v)});E.push(D)}var _=u.GeometryPipeline.combineInstances(E)[0];_.attributes.position.values=new Float64Array(_.attributes.position.values),_.indices=m.IndexDatatype.createTypedArray(_.attributes.position.values.length/3,_.indices);var k=_.attributes;return n.position||delete k.position,new p.Geometry({attributes:k,indices:_.indices,primitiveType:_.primitiveType,boundingSphere:G})}}}},function(e,t){return s.defined(t)&&(e=J.unpack(e,t)),J.createGeometry(e)}}));
