import http from '../utils/http'
import qs from 'qs'
import axios from 'axios'
// import ip from '/public/ip.json'
/**
 *  @parms resquest 请求地址 例如：http://************:8088/request/...
 *  @param '/testIp'代表vue-cil中config，index.js中配置的代理
 */
// http://*************:20214
//  const resquest = "http://************:29302"
const resquest = window.wgParameter.publicUrl
// const resquest = "http://************:29302"

export function getSb (params) {
	return http.get('/tDtfProject', params)
}

export {
	resquest
}
