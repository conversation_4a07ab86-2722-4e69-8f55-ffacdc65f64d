<template>
  <div>
    <el-button
      v-show="showType === '主材'"
      size="mini"
      type="warning"
      style="
        float: right;
        position: relative;
        right: 25px;
        top: 5px;
        z-index: 999;
      "
      @click="SaveMaterial"
      >保存</el-button
    >
    <el-button
      v-show="showType === '设备'"
      size="mini"
      type="warning"
      style="
        float: right;
        position: relative;
        right: 25px;
        top: 5px;
        z-index: 999;
      "
      @click="SaveEquip"
      >保1存</el-button
    >
    <el-button
      v-show="showType === '清单' && activeName !== 'first'"
      size="mini"
      type="warning"
      style="
        float: right;
        position: relative;
        right: 25px;
        top: 5px;
        z-index: 999;
      "
      @click="SaveQd"
      >保存</el-button
    >
    <el-button
      v-show="showType === '定额' && activeName !== 'second'"
      size="mini"
      type="warning"
      style="
        float: right;
        position: relative;
        right: 25px;
        top: 5px;
        z-index: 999;
      "
      @click="SaveRation"
      >保存</el-button
    >
    <el-tabs
      v-show="showType === '清单' || showType === '自定义清单'"
      v-model="activeName"
    >
      <el-tab-pane label="项目特征" name="first">
        <el-table
          ref="detailTable"
          :data="qdProps"
          highlight-current-row
          border
          style="width: 100%"
          class="detailTableStyle"
        >
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="listName" label="特征名称" width="180" align="center"/>
          <el-table-column prop="listValue" label="特征值" align="center"/>
          <el-table-column label="操作" width="140" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="scope.$index === qdMaxIndex - 1"
                type="text"
                size="small"
                style="color: darkorange"
                @click="addQdInfo(scope.row)"
                >添加</el-button
              >
              <el-button
                type="text"
                size="small"
                style="color: black"
                @click="editQdInfo(scope.row)"
                >编辑</el-button
              >
              <el-button
                type="text"
                size="small"
                style="color: red"
                @click="delQdInfo(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="工作内容" name="second" >
        <el-input
          v-model="workerQdDetail.workerContent"
          type="textarea"
          :rows="6"
          style=""
        />
      </el-tab-pane>
      <el-tab-pane label="计算规则" name="third">
        <el-input
          v-model="workerQdDetail.calculateRule"
          type="textarea"
          :rows="6"
          style=""
        />
      </el-tab-pane>
      <el-tab-pane label="批注" name="fourth">批注</el-tab-pane>
    </el-tabs>

    <el-tabs v-show="showType === '定额'" v-model="activeName">
      <el-tab-pane label="基本信息" name="first">
        <el-row style="width: 100%" class="el-input-new el-detail-form">
          <el-form :inline="true" label-width="120px">
            <el-form-item label="定额费用" class="el-form-item">
              <el-input
                v-model="rationInfo.deQuotoMoney"
                class="form-item-width"
              />
            </el-form-item>
            <el-form-item label="人工费用" class="el-form-item">
              <el-input
                v-model="rationInfo.dePersonMoney"
                class="form-item-width"
              />
            </el-form-item>
            <el-form-item label="材料费用" class="el-form-item">
              <el-input
                v-model="rationInfo.deMaterialsMoney"
                class="form-item-width"
              />
            </el-form-item>
            <el-form-item label="机械费用" class="el-form-item">
              <el-input
                v-model="rationInfo.deRigidMoney"
                class="form-item-width"
              />
            </el-form-item>
            <el-form-item label="定额范围" class="el-form-item">
              <el-select
                v-model="rationInfo.deQuotoScope"
                class="form-item-width"
              >
                <el-option label="预算" value="预算" />
                <el-option label="概算" value="概算" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-row>
        <el-row style="width: 100%" class="el-input-new el-detail-form">
          <el-form :inline="true" label-width="120px">
            <el-form-item label="定额系数">
              <el-input
                v-model="rationInfo.deQuotoCoef"
                class="form-item-width"
              />
            </el-form-item>
            <el-form-item label="人工系数">
              <el-input
                v-model="rationInfo.dePersonCoef"
                class="form-item-width"
              />
            </el-form-item>
            <el-form-item label="材料系数">
              <el-input
                v-model="rationInfo.deMaterialsCoef"
                class="form-item-width"
              />
            </el-form-item>
            <el-form-item label="机械系数">
              <el-input
                v-model="rationInfo.deRigidCoef"
                class="form-item-width"
              />
            </el-form-item>
          </el-form>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="材机列表" name="second">
        <el-table
          ref="detailTFTable"
          :data="rationRCJInfo"
          highlight-current-row
          border
          style="width: 100%"
          height="59vh"
        >
          <el-table-column
            show-overflow-tooltip
            prop="rcjcoding"
            align="center"
            label="编号"
            width="180"
          />
          <el-table-column
            show-overflow-tooltip
            prop="rcjname"
            align="center"
            label="名称"
            ><template slot-scope="scope">
              <span
                v-show="scope.row.rcjtype === '人工'"
                style="
                  color: white;
                  background-color: rgb(79, 187, 165);
                  padding: 3px;
                  border-radius: 5px;
                  font-weight: 600;
                "
                >人</span
              >
              <span
                v-show="scope.row.rcjtype === '计价材料'"
                style="
                  color: white;
                  background-color: #409eff;
                  padding: 3px;
                  border-radius: 5px;
                  font-weight: 600;
                "
                >材</span
              >
              <span
                v-show="scope.row.rcjtype === '机械'"
                style="
                  color: white;
                  background-color: rgb(204, 0, 255);
                  padding: 3px;
                  border-radius: 5px;
                  font-weight: 600;
                "
                >机</span
              >
              <span
                v-show="scope.row.rcjtype === '未计价材料'"
                style="
                  color: white;
                  background-color: #e6a23c;
                  padding: 3px;
                  border-radius: 5px;
                  font-weight: 600;
                "
                >未</span
              >
              {{ scope.row.rcjname }}
            </template>
          </el-table-column>
          <el-table-column
            show-overflow-tooltip
            prop="rcjunit"
            align="center"
            label="单位"
          />
          <el-table-column prop="rcjquantity" align="center" label="含量" />
          <el-table-column
            show-overflow-tooltip
            prop="rcjprice"
            align="center"
            label="预算价"
          />
          <el-table-column label="操作" width="140">
            <template slot-scope="scope">
              <el-button
                v-if="scope.$index === rcjMaxIndex - 1"
                type="text"
                size="small"
                style="color: darkorange"
                @click="addRCJInfo(scope.row)"
                >添加</el-button
              >
              <el-button
                type="text"
                size="small"
                style="color: black"
                @click="editRCJInfo(scope.row)"
                >编辑</el-button
              >
              <el-button
                type="text"
                size="small"
                style="color: red"
                @click="delRCJ(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="定额调整系数" name="third">
        <el-table
          ref="detailRationTable"
          :data="rationResizeInfo"
          highlight-current-row
          border
          style="width: 100%"
          height="70vh"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column
            show-overflow-tooltip
            prop="deResizeCondition"
            align="center"
            label="调整条件"
          />
          <el-table-column
            show-overflow-tooltip
            prop="deResizeType"
            align="center"
            label="调整类型"
            width="180"
          />
          <el-table-column
            show-overflow-tooltip
            prop="deResizePerson"
            align="center"
            label="人工"
            width="100"
          />
          <el-table-column
            prop="deResizeMaterials"
            align="center"
            label="材料"
            width="100"
          />
          <el-table-column
            show-overflow-tooltip
            prop="deResizeRigid"
            align="center"
            label="机械"
            width="100"
          />
          <el-table-column
            show-overflow-tooltip
            prop="deResizeConsumption"
            align="center"
            label="消耗量"
            width="100"
          />
          <el-table-column
            show-overflow-tooltip
            prop="deResizeNumber"
            align="center"
            label="人材机编号"
            width="120"
          />
          <el-table-column
            show-overflow-tooltip
            prop="deResizeN"
            align="center"
            label="取值"
            width="100"
          />
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <el-tabs v-show="showType === '设备'" v-model="activeName">
      <el-tab-pane label="基本信息" name="first">
        <el-row style="width: 100%" class="el-input-new el-detail-form">
          <el-form :inline="true" label-width="120px">
            <el-form-item label="供货方">
              <el-select v-model="equipInfo.sbSupplier" style="width: 60%">
                <el-option label="甲供" value="甲供" />
                <el-option label="乙供" value="乙供" />
              </el-select>
            </el-form-item>
            <el-form-item label="含税价">
              <el-input v-model="equipInfo.sbUnicPrice" style="width: 60%" />
            </el-form-item>
            <el-form-item label="配送">
              <el-select v-model="equipInfo.sbDelivery" style="width: 60%">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
            <el-form-item label="类别/运输类型">
              <el-input
                v-model="equipInfo.sbCarriageTypep"
                style="width: 60%"
              />
            </el-form-item>
            <el-form-item label="运杂费率(%)">
              <el-input v-model="equipInfo.sbCarriageRate" style="width: 60%" />
            </el-form-item>
            <el-form-item label="不含税价">
              <el-input v-model="equipInfo.sbNounicPrice" style="width: 60%" />
            </el-form-item>
            <el-form-item label="暂估">
              <el-select v-model="equipInfo.sbProEst" style="width: 60%">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-row>
      </el-tab-pane>
      <!--      <el-tab-pane label="批注" name="second" />-->
    </el-tabs>

    <el-tabs v-show="showType === '主材'" v-model="activeName">
      <el-tab-pane label="基本信息" name="first">
        <el-row style="width: 100%" class="el-input-new el-detail-form">
          <el-form :inline="true" label-width="120px">
            <el-form-item label="供货方">
              <el-select
                v-model="materialInfo.zcSupplier"
                class="form-item-width"
              >
                <el-option label="甲供" value="甲供" />
                <el-option label="乙供" value="乙供" />
              </el-select>
            </el-form-item>
            <el-form-item label="含税价">
              <el-input
                v-model="materialInfo.zcUnicPrice"
                class="form-item-width"
              />
            </el-form-item>
            <el-form-item label="类别/运输类型">
              <el-input
                v-model="materialInfo.zcCarriageTypep"
                class="form-item-width"
              />
            </el-form-item>
            <el-form-item label="损耗率(%)">
              <el-input v-model="materialInfo.zcLoss" class="form-item-width" />
            </el-form-item>
            <el-form-item label="包装系数(%)">
              <el-input
                v-model="materialInfo.zcPackCoe"
                class="form-item-width"
              />
            </el-form-item>
            <el-form-item label="重量(kg)">
              <el-input
                v-model="materialInfo.zcWeight"
                class="form-item-width"
              />
            </el-form-item>
            <el-form-item label="不含税价">
              <el-input
                v-model="materialInfo.zcNounicPrice"
                class="form-item-width"
              />
            </el-form-item>
            <el-form-item label="配送">
              <el-select
                v-model="materialInfo.zcDelivery"
                class="form-item-width"
              >
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
            <el-form-item label="暂估">
              <el-select
                v-model="materialInfo.zcProEst"
                class="form-item-width"
              >
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
            <el-form-item label="设备行材料">
              <el-select
                v-model="materialInfo.zcManager"
                class="form-item-width"
              >
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
            <el-form-item label="交货地点及方式">
              <el-input
                v-model="materialInfo.zcSiteWay"
                class="form-item-width"
              />
            </el-form-item>
          </el-form>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="卸车保管" name="second">
        <el-form
          label-width="60px"
          style="display: flex; justify-content: flex-start"
        >
          <el-form-item label="卸车">
            <el-select v-model="materialInfo.zcSiteWay" class="form-item-width">
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
          </el-form-item>
          <el-form-item label="保管">
            <el-select v-model="materialInfo.zcCustody" class="form-item-width">
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <!--      <el-tab-pane label="批注" name="third" />-->
    </el-tabs>

    <el-tabs v-show="showType === '土方'" v-model="activeName">
      <el-tab-pane label="土方列表" name="first">
        <el-table
          ref="detailTFTable"
          :data="tFInfo"
          highlight-current-row
          border
          style="width: 100%"
          height="70vh"
        >
          <el-table-column
            type="index"
            align="center"
            label="序号"
            width="60"
          />
          <el-table-column
            show-overflow-tooltip
            prop="tfGraph"
            align="center"
            label="图形"
            width="180"
          />
          <el-table-column
            show-overflow-tooltip
            prop="tfPitType"
            align="center"
            label="坑类型"
          />
          <el-table-column
            show-overflow-tooltip
            prop="tfSoilRatio"
            align="center"
            label="土质比例"
          />
          <el-table-column prop="tfSize" align="center" label="数量" />
          <el-table-column
            show-overflow-tooltip
            prop="tfParam"
            align="center"
            label="参数信息"
          />
          <el-table-column
            show-overflow-tooltip
            prop="tfEarthdata"
            align="center"
            label="单坑土方量(m)"
          />
          <el-table-column label="操作" align="center" width="140">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                style="color: black"
                @click="editTF(scope.row)"
                >编辑</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <!--    <el-tabs v-show="showType === '一笔性费用'" v-model="activeName">-->
    <!--      <el-tab-pane label="批注" name="first">批注</el-tab-pane>-->
    <!--    </el-tabs>-->

    <el-dialog
      class="el-dialog-new"
      title="特征属性"
      :visible.sync="qdVisible"
      width="45vh"
    >
      <el-form :inline="true">
        <el-form-item label="特征名称 : " class="el-form-items">
          <el-input
            v-model="featureKey"
            style="width: 110px; margin-left: 10px"
          />
        </el-form-item>
        <el-form-item label="特 征 值 :" class="el-form-items">
          <el-select
            v-model="featureValue"
            allow-create
            clearable
            filterable
            placeholder="请输入内容"
            style="width: 110px; margin-left: 10px"
          >
            <el-option
              v-for="item in EigenvalueArr"
              :key="item.inventoryWorkContent"
              :label="item.inventoryWorkContent"
              :value="item.inventoryWorkContent"
              >{{ item.inventoryWorkContent }}</el-option
            >
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="qdVisible = false">取 消</el-button>
        <el-button size="mini" type="primary" @click="addFeature"
          >确 定</el-button
        >
      </span>
    </el-dialog>

    <!--土方-->
    <el-dialog
      class="el-dialog-new"
      title="设置土方量"
      :visible.sync="detailTFVisible"
      width="100vh"
    >
      <el-row>
        <el-col :span="12" style="border-right: #bbbbbb solid 1px">
          <el-form v-model="detailTFForm" label-width="80px">
            <el-form-item label="图形">
              <el-select
                v-model="detailTFForm.tfGraph"
                placeholder="请选择"
                style="width: 35vh"
                @change="tChartChange"
              >
                <el-option
                  v-for="item in TChart"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="计算公式">
              <el-input v-model="detailTFForm.tfFormula" style="width: 35vh" />
            </el-form-item>
            <el-form-item label="坑类型">
              <el-select
                v-model="detailTFForm.tfPitType"
                placeholder="请选择"
                style="width: 35vh"
              >
                <el-option
                  v-for="item in holeList"
                  :key="item.holeKey"
                  :label="item.holeValue"
                  :value="item.holeKey"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="挖土方式">
              <el-select
                v-model="detailTFForm.tfCutMethods"
                placeholder="请选择"
                style="width: 35vh"
              >
                <el-option
                  v-for="item in cutMethodsList"
                  :key="item.cutKey"
                  :label="item.cutValue"
                  :value="item.cutKey"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-checkbox v-model="detailTFForm.tfTock"
                >岩石(有模板)</el-checkbox
              >
            </el-form-item>
            <el-form-item label="马道(个)">
              <el-input
                v-model="detailTFForm.tfBridlePath"
                style="width: 35vh"
              />
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12">
          <template>
            <el-table
              :data="detailTFForm.params"
              style="width: 45vh; margin-left: 20px"
              border
              height="calc(100vh - 85vh)"
              :header-cell-style="{
                textAlign: 'center',
                background: '#526ade',
                color: '#000',
              }"
            >
              <el-table-column prop="tfParamName" align="center" label="参数名称	" />
              <el-table-column prop="tfParamValue" align="center" label="参数值">
                <template scope="scope">
                  <el-input
                    v-model="scope.row.tfParamValue"
                    size="small"
                    placeholder="0"
                    style="width: 100%"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="tfParamExplain" align="center" label="说明" />
            </el-table>
          </template>
          <!-- <img :src="require('@/assets/tChart/'+srcUrl+'.jpg')" style="width: 320px;height: 180px;background-color: black;margin: 10px 20px"> -->
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="detailTFVisible = false">取 消</el-button>
        <el-button type="primary" @click="eidtTFSave">确 定</el-button>
      </span>
    </el-dialog>

    <!--人材机-->
    <el-dialog
      class="el-dialog-new"
      title="设置人材机"
      :visible.sync="detailRCJVisible"
      width="80vh"
    >
      <el-form :inline="true" label-width="80px">
        <el-form-item label="编号 : " class="el-form-items">
          <el-input
            v-model="detailRCJFrom.rcjcoding"
            style="width: 240px"
            class="el-input-marng"
          />
        </el-form-item>
        <el-form-item label="名称 : " class="el-form-items">
          <el-input
            v-model="detailRCJFrom.rcjname"
            style="width: 240px"
            class="el-input-marng"
          />
        </el-form-item>
        <el-form-item label="单位 :" class="el-form-items">
          <el-input
            v-model="detailRCJFrom.rcjunit"
            style="width: 240px"
            class="el-input-marng"
          />
        </el-form-item>
        <el-form-item label="含量 :" class="el-form-items">
          <el-input
            v-model="detailRCJFrom.rcjquantity"
            style="width: 240px"
            class="el-input-marng"
          />
        </el-form-item>
        <el-form-item label="预算价 :" class="el-form-items">
          <el-input
            v-model="detailRCJFrom.rcjprice"
            style="width: 240px"
            class="el-input-marng"
          />
        </el-form-item>
        <el-form-item label="机械类型 :" class="el-form-items">
          <el-select
            v-model="detailRCJFrom.rcjprice"
            style="width: 240px"
            placeholder="请选择"
            class="el-input-marng"
          >
            <el-option
              v-for="item in TruckCrane"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="detailRCJVisible = false">取 消</el-button>
        <el-button type="primary" @click="eidtRCJSave">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'DetailTemplate',
  props: [
    'qdProps',
    'qdMaxIndex',
    'workerQdDetail',
    'showType',
    'materialInfo',
    'equipInfo',
    'tFInfo',
    'rationInfo',
    'rationRCJInfo',
    'rationResizeInfo',
    'rcjMaxIndex',
  ],
  computed: {
    ...mapState('storeModules/dataDic/', [
      'TChart', // 土方土质数据
      'TruckCrane', // 机械类型
    ]),
  },
  data() {
    return {
      activeName: 'first',
      qdVisible: false,
      featureKey: '',
      featureValue: '',
      selectFeature: '',
      detailTFVisible: false, // 土方弹框
      detailRCJVisible: false, // 人材机弹框
      detailTFForm: {
        tfGraph: '',
        tfFormula: '',
        tfPitType: '',
        tfCutMethods: '',
        tfTock: '',
        tfBridlePath: '',
        params: [],
      },
      srcUrl: '1', // 图形地址
      holeList: [], // 坑类型
      cutMethodsList: [], // 挖土方式
      detailRCJFrom: {
        // 人材机详情
        rcjcoding: '',
        rcjname: '',
        rcjunit: '',
        rcjquantity: '',
        rcjprice: '',
      },
      EigenvalueId: '',
      EigenvalueArr: [], // 渲染特征值下拉
    }
  },
  methods: {
    /**
     * 定额调整系数勾选事件
     * @constructor
     */
    handleSelectionChange(row) {
      let newPeopleRate = 0
      let newMaterialRate = 0
      let newRigidRate = 0
      const list = this.$refs.detailRationTable.selection
      list.forEach((item) => {
        newPeopleRate =
          newPeopleRate +
          (parseFloat(item.deResizePerson) - parseFloat(this.rationInfo.oldRg))
        newMaterialRate =
          newMaterialRate +
          (parseFloat(item.deResizeMaterials) -
            parseFloat(this.rationInfo.oldCl))
        newRigidRate =
          newRigidRate +
          (parseFloat(item.deResizeRigid) - parseFloat(this.rationInfo.oldJx))
      })

      newPeopleRate = newPeopleRate + parseFloat(this.rationInfo.oldRg)
      newMaterialRate = newMaterialRate + parseFloat(this.rationInfo.oldCl)
      newRigidRate = newRigidRate + parseFloat(this.rationInfo.oldJx)

      if (!newPeopleRate) {
        newPeopleRate = this.rationInfo.oldRg
      }
      if (!newMaterialRate) {
        newMaterialRate = this.rationInfo.oldCl
      }
      if (!newRigidRate) {
        newRigidRate = this.rationInfo.oldJx
      }

      this.$emit(
        'handleSelectionChange',
        newPeopleRate,
        newMaterialRate,
        newRigidRate
      )
    },

    /**
     * 土方编辑保存
     * @constructor
     */
    eidtTFSave() {
      const _that = this
      // GcAddCost.editWorkloadTf(this.detailTFForm).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.$message({
      //         showClose: true,
      //         message: '保存成功',
      //         type: 'success',
      //         duration: 2000,
      //         onClose: function() {
      //           _that.$emit('getDetailTFParams', _that.detailTFForm.projectWorkloadId)
      //         }
      //       })
      //     }
      //   }
      // })
    },

    /**
     * 土方编辑
     * @constructor
     */
    editTF(row) {
      this.detailTFForm.projectWorkloadId = row.projectWorkloadId
      this.detailTFForm.projectWorkloadTfId = row.projectWorkloadTfId
      this.detailTFForm.tfUserId = row.tfUserId
      this.detailTFForm.tfUserUnitId = row.tfUserUnitId
      this.detailTFForm.tfGraph = row.tfGraph
      this.tChartChange(row.tfGraph)
      this.detailTFForm.tfPitType = row.tfPitType
      this.detailTFForm.tfFormula = row.tfFormula
      this.detailTFForm.tfCutMethods = row.tfCutMethods
      this.detailTFForm.tfTock = row.tfTock
      this.detailTFForm.tfBridlePath = row.tfBridlePath
      const tfParams = row.tfParam.split(',')

      for (let i = 0; i < this.detailTFForm.params.length; i++) {
        tfParams.forEach((item) => {
          const childItem = item.split(':')
          if (this.detailTFForm.params[i].tfParamExplain === childItem[0]) {
            this.detailTFForm.params[i].tfParamValue = childItem[1]
          }
        })
      }
      this.detailTFVisible = true
    },

    /**
     * 定额保存
     * @constructor
     */
    SaveRation() {
      const newRationInfo = this.deepCopy(this.rationInfo)
      newRationInfo.resizeList = new Array()
      const list = this.$refs.detailRationTable.selection
      this.rationResizeInfo.forEach((item) => {
        let tempItem = ''
        for (let i = 0; i < list.length; i++) {
          if (
            item.projectWorkloadDeResizeId === list[i].projectWorkloadDeResizeId
          ) {
            tempItem = this.deepCopy(item)
            tempItem.isChoose = '1'
            newRationInfo.resizeList.push(tempItem)
          }
        }
        if (tempItem === '') {
          tempItem = this.deepCopy(item)
          tempItem.isChoose = '0'
          newRationInfo.resizeList.push(tempItem)
        }
      })
      // GcAddCost.addWorkloadDetailRCJ(newRationInfo).then(res => {
      //   if (res.data) {
      //     if (res.data.code === 1001) {
      //       this.$message({
      //         showClose: true,
      //         message: '保存成功',
      //         type: 'success',
      //         duration: 2000
      //       })
      //     }
      //   }
      // })
    },

    /**
     * 深度拷贝对象
     */
    deepCopy(obj) {
      let newObj = null
      if (typeof obj === 'object' && obj !== null) {
        newObj = obj instanceof Array ? [] : {}
        for (const i in obj) {
          newObj[i] =
            typeof obj[i] === 'object' ? this.deepCopy(obj[i]) : obj[i]
        }
      } else {
        newObj = obj
      }
      return newObj
    },

    /**
     * 新增人材机
     */
    addRCJInfo(row) {
      this.detailRCJFrom = {
        rcjcoding: '',
        rcjname: '',
        rcjunit: '',
        rcjquantity: '',
        rcjprice: '',
        deid: row.deid,
        projectid: row.projectid,
      }
      this.detailRCJVisible = true
    },

    /**
     * 编辑人材机
     */
    editRCJInfo(row) {
      this.detailRCJFrom = row
      this.detailRCJVisible = true
    },

    /**
     * 保存编辑的人材机
     */
    eidtRCJSave() {
      if (this.detailRCJFrom.id) {
        // 修改
        // GcAddCost.editWorkloadDetailRCJ(this.detailRCJFrom).then(res => {
        //   if (res.data) {
        //     if (res.data.code === 1001) {
        //       this.$message({
        //         showClose: true,
        //         message: '保存成功',
        //         type: 'success',
        //         duration: 2000
        //       })
        //     }
        //   }
        // })
      } else {
        // 新增
        // GcAddCost.addWorkloadDetailRCJ(this.detailRCJFrom).then(res => {
        //   if (res.data) {
        //     if (res.data.code === 1001) {
        //       this.$message({
        //         showClose: true,
        //         message: '保存成功',
        //         type: 'success',
        //         duration: 2000
        //       })
        //     }
        //   }
        // })
      }
    },

    /**
     * 删除人材机
     */
    delRCJ(row) {
      const _that = this
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'confirmBtn',
        cancelButtonClass: 'cancleBtn',
        type: 'warning',
      }).then(() => {
        // GcAddCost.delWorkloadDetailRCJ(row.id).then(res => {
        //   _that.$message({
        //     showClose: true,
        //     message: '删除成功',
        //     type: 'success',
        //     duration: 2000
        //   })
        // })
      })
    },

    /**
     * 主材保存
     * @constructor
     */
    SaveMaterial() {
      if (this.materialInfo.projectWorkloadZcId !== '') {
        // 修改
        // GcAddCost.editWorkZcInfo(this.materialInfo).then(res => {
        //   if (res.data) {
        //     if (res.data.code === 1001) {
        //       this.$message({
        //         showClose: true,
        //         message: '保存成功',
        //         type: 'success',
        //         duration: 2000
        //       })
        //     } else {
        //       this.$message({
        //         showClose: true,
        //         message: res.data.message,
        //         type: 'error',
        //         duration: 2000
        //       })
        //     }
        //   }
        // })
      } else {
        // 新增
        // GcAddCost.addWorkZcInfo(this.materialInfo).then(res => {
        //   if (res.data) {
        //     if (res.data.code === 1001) {
        //       this.$message({
        //         showClose: true,
        //         message: '保存成功',
        //         type: 'success',
        //         duration: 2000
        //       })
        //     } else {
        //       this.$message({
        //         showClose: true,
        //         message: res.data.message,
        //         type: 'error',
        //         duration: 2000
        //       })
        //     }
        //   }
        // })
      }
    },

    /**
     * 设备保存
     * @constructor
     */
    SaveEquip() {
      if (this.equipInfo.projectWorkloadSbId !== '') {
        // 修改
        // GcAddCost.editWorkSbInfo(this.equipInfo).then(res => {
        //   console.log(this.equipInfo)
        //   if (res.data) {
        //     if (res.data.code === 1001) {
        //       this.$message({
        //         showClose: true,
        //         message: '保存成功',
        //         type: 'success',
        //         duration: 2000
        //       })
        //     } else {
        //       this.$message({
        //         showClose: true,
        //         message: res.data.message,
        //         type: 'error',
        //         duration: 2000
        //       })
        //     }
        //   }
        // })
      } else {
        // 新增
        // GcAddCost.addWorkSbInfo(this.equipInfo).then(res => {
        //   if (res.data) {
        //     if (res.data.code === 1001) {
        //       this.$message({
        //         showClose: true,
        //         message: '保存成功',
        //         type: 'success',
        //         duration: 2000
        //       })
        //     } else {
        //       this.$message({
        //         showClose: true,
        //         message: res.data.message,
        //         type: 'error',
        //         duration: 2000
        //       })
        //     }
        //   }
        // })
      }
    },

    /**
     * 新增清单弹出页面
     */
    addQdInfo(row) {
      console.log(row.listTzz)
      this.selectFeature = ''
      this.qdVisible = true
      // GcAddCost.Eigenvalue(row.listTzz).then(res => {
      //   console.log(res)
      // })
    },

    /**
     * 编辑清单弹出页面
     */
    editQdInfo(row) {
      console.log(row)
      // 点击编辑拿到一行的id
      this.EigenvalueId = row.listTzz
      this.selectFeature = row
      this.featureKey = row.listName
      this.featureValue = row.listValue
      this.qdVisible = true
      // GcAddCost.Eigenvalue(row.listTzz).then(res => {
      //   console.log(res.data.data)
      //   this.EigenvalueArr = res.data.data
      // })
    },
    /**
     * 删除清单
     */
    delQdInfo(row) {
      const _that = this
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'confirmBtn',
        cancelButtonClass: 'cancleBtn',
        type: 'warning',
      })
        .then(() => {
          // GcAddCost.delFeature(row.projectWorkloadListId).then(res => {
          //   if (res.data) {
          //     if (res.data.code === 1001) {
          //       this.$message({
          //         showClose: true,
          //         message: '删除成功',
          //         type: 'success',
          //         duration: 2000,
          //         onClose: function() {
          //           _that.$emit('getFeature', row.projectWorkloadId)
          //         }
          //       })
          //     }
          //   }
          // })
        })
        .catch(() => {})
    },

    /**
     * 新增保存项目特征
     */
    addFeature(row) {
      const _that = this
      const featureModel = {
        projectWorkloadId: this.workerQdDetail.projectWorkloadId,
        listValue: this.featureValue,
        listName: this.featureKey,
      }
      if (this.selectFeature !== '') {
        // 编辑
        this.selectFeature.listValue = this.featureValue
        this.selectFeature.listName = this.featureKey
        // GcAddCost.editFeature(this.selectFeature).then(res => {
        //   if (res.data) {
        //     if (res.data.code === 1001) {
        //       this.$message({
        //         showClose: true,
        //         message: '保存成功',
        //         type: 'success',
        //         duration: 2000
        //       })
        //       this.qdVisible = false
        //     }
        //   }
        // })
      } else {
        // 新增
        // GcAddCost.addFeature(featureModel).then(res => {
        //   if (res.data) {
        //     if (res.data.code === 1001) {
        //       this.$message({
        //         showClose: true,
        //         message: '保存成功',
        //         type: 'success',
        //         duration: 2000,
        //         onClose: function() {
        //           _that.$emit('getFeature', featureModel.projectWorkloadId)
        //         }
        //       })
        //       this.qdVisible = false
        //     }
        //   }
        // })
      }
    },

    /**
     * 保存清单工作内容、计算规则
     * @constructor
     */
    SaveQd() {
      this.$emit('SaveDetailTemplateQd')
    },

    /**
     * 图形下拉框改变事件*
     */
    tChartChange(id) {
      const child = this.TChart.find((item) => {
        return item.key === id
      })
      const childInfo = child.children
      this.detailTFForm.tfCutMethods = '' // 赋值前清空
      this.detailTFForm.tfPitType = '' // 赋值前清空
      this.srcUrl = child.id
      this.detailTFForm.tfFormula = childInfo.formula
      this.detailTFForm.params = childInfo.parameter
      this.holeList = childInfo.holeType
      this.cutMethodsList = childInfo.cutMethods
      if (childInfo.holeType.length > 0) {
        this.detailTFForm.tfPitType = childInfo.holeType[0].holeKey
      }
      if (childInfo.cutMethods.length > 0) {
        this.detailTFForm.tfCutMethods = childInfo.cutMethods[0].cutKey
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.form-item-width {
  width: 100px;
}
.detailTableStyle {
  height: 70vh;
  overflow: auto;
}
.el-form-items {
  width: 100%;
  display: flex;
  justify-content: center;
}
.el-input-marng {
  margin-left: 10px;
}
</style>
