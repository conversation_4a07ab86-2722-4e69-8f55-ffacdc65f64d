.map {
  height: 96vh;
  width: 100vw;
  overflow: hidden;
}
.markerPointLine {
  background-size: 100% 100%;
  font-weight: bolder;
  color: #0af3f3;
  margin-top: -1rem;
  display: none;
}
.markerEveryLines {
  background-size: 100% 100%;
  font-weight: bolder;
  color: #0af3f3;
  margin-top: -1rem;
  perspective: 500;
  -webkit-perspective: 500;
  display: none;
}
.map-area {
  height: calc(100vh - 7.9rem);
}
.prolist {
  height: calc(100vh - 6rem);
  overflow-y: scroll;
}
.map-rankgt {
  background: white;
  height: 100%;
}
.map-changeRadio {
  height: 2rem;
  display: flex;
  align-items: center;
  padding: 10px 16px;
}
.map-changText {
  -webkit-box-flex: 0;
  -webkit-flex: none;
  flex: none;
  box-sizing: border-box;
  width: 6.2em;
  margin-right: 12px;
  color: #646566;
  text-align: left;
  word-wrap: break-word;
}
.map-showNav {
  height: 3rem;
  border-bottom: 1px solid #f5f2f2;
  font-weight: 600;
  padding-left: 0.5rem;
  line-height: 3rem;
  font-size: 1rem;
}
.map-uploadImg {
  padding: 0.5rem;
}
.map-setting {
  display: flex;
  background: #eeece4;
  height: 5rem;
  justify-content: center;
  align-items: center;
  background-image: url('~@/assets/map/settingImg/systemSet.png');
}
.map-setting .button {
  width: 4rem;
  height: 4rem;
  border-radius: 4rem;
  border: none;
  text-align: center;
}
.map-setting .map-draw .map-icon {
  background-image: url('~@/assets/map/draw.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.map-setting .map-choose .map-icon {
  background-image: url('~@/assets/map/choose.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.map-setting .map-draw {
  background: #6190ee;
  color: white;
}
.map-setting .map-choose {
  background: #25b053;
  color: white;
}

.map-setting .map-icon {
  height: 2.5rem;
  width: 2.5rem;
  margin-left: 0.8rem;
  margin-top: 0.2rem;
}
.movePointShowAutoLine {
  position: absolute;
  color: red;
  top: -1rem;
  display: none;
}
.movePointShowFSSS {
  position: absolute;
  color: red;
  font-size: 0.6rem;
  left: -3.5rem;
  top: 0rem;
  display: none;
}
.movePointShowZSSBHZ {
  position: absolute;
  color: red;
  font-size: 0.6rem;
  left: 4rem;
  top: 0rem;
  display: none;
}
.movePointShowGt {
  position: absolute;
  color: red;
  font-size: 1.2rem;
  top: -1.7rem;
}
.movePointShowMark {
  position: absolute;
  color: red;
  top: -2rem;
  display: none;
}
.shi {
  width: 2rem;
  height: 2rem;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-image: url('~@/assets/map/define_green.png');
}
.mapdLJMarker {
  background-size: 100% 100% !important;
  background-repeat: no-repeat !important;
  display: none;
}
.map-area .map-show {
  /*top: 2rem;*/
  display: none;
  left: 1rem;
}
.map-area .map-distance {
  /*top: 6rem;*/
  display: none;
  left: 1rem;
}
.map-area .map-all {
  /*top: 10rem;*/
  display: none;
  left: 1rem;
}
.map-area .map-remodelBtn {
  /*top: 10rem;*/
  display: none;
  left: 1rem;
}
.map-area .map-auxiliary {
  /*top: 2rem;*/
  display: none;
  right: 1rem;
}
.map-area .map-reload {
  top: 40rem;
  right: 1rem;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background-color: rgba(255, 255, 255, 0.65);
}
.map-area .map-packup {
  background-image: url('~@/assets/map/settingImg/packup.png');
  /*top: 2rem;*/
  right: 1rem;
  top: 28rem;
  width: 2.5rem !important;
  height: 2.5rem !important;
}
.map-area .map-satellite {
  top: 6rem;
  display: none;
  right: 1rem;
}
.map-area .map-satellite img {
  height: 100%;
  width: 100%;
  background-size: 100% 100%;
}
.map-area .map-location {
  top: 10rem;
  display: none;
  right: 1rem;
}
.map-area .map-setting {
  top: 10rem;
  right: 4rem;
  /*  border-radius: 30%;*/
  background-color: rgba(219, 223, 214, 0);
  /*  text-align: center;*/
}
.map-area .map-endkc {
  top: 18.5rem;
  right: 4rem;
  display: none;
  background-image: url('~@/assets/map/settingImg/stopHz.png');
}
.map-area .map-removeDrawPoy {
  top: 9.5rem;
  right: 4rem;
  display: none;
  background-image: url('~@/assets/map/settingImg/removeRange.png');
}
.map-area .map-expand {
  background-image: url('~@/assets/map/settingImg/expand.png');
  top: 20rem;
  right: 1rem;
  display: block;
}
.mapimg {
  width: 2.5rem;
  height: 2.5rem;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  z-index: 999;
}
/*采集类型*/
.map-selectType {
  position: relative;
}
.map-selectType,
.map-setValue {
  background: #ffffff;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 999;
}
/*2022628*/
/*.map-selectType .map-selectArea{*/
/*    height: calc(100vh - 5rem);*/
/*    overflow-y: scroll;*/
/*    margin: 0;*/
/*    padding: 0;*/
/*    z-index: 11;*/
/*}*/
.map-selectType .map-selectArea {
  height: 3rem;
  position: absolute;
  width: 100%;
}
.map-selectType .map-packdown {
  width: 1.5rem;
  height: 1.5rem;
  position: absolute;
  right: 1rem;
  background: url('~@/assets/map/settingImg/packdown.png');
  background-size: 100% 100%;
}
.map-selectType .map-selectArea .map-selectAreaChos {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  text-align: center;
  font-size: 1rem;
  background: rgb(82, 106, 222);
  color: #ffffff;
  height: 100%;
}
.pro-addAllArea {
  position: absolute;
  top: 3rem;
  overflow-y: scroll;
  /*height: calc(100vh - 6.3rem);*/
  background: #ffffff;
  width: 100%;
  height: calc(100% - 3rem);
}
.pro-addEveryArea {
  border-bottom: 1px solid white;
}
/*2022628*/
.map-selectType .map-selectArea .van-collapse-item .van-cell--clickable {
  border-bottom: 1px solid #f8f6f6;
}
.map-selectType .map-selectArea .map-collapseImg {
  width: 1.3rem;
  height: 1.3rem;
  margin-right: 0.5rem;
}
.map-selectType .map-selectCheck .van-radio {
  margin-bottom: 1rem;
}
.sgmap-popup {
  top: 14px !important;
  left: -9px !important;
  z-index: 10;
  font-size: 1rem;
  letter-spacing: 0.1rem;
  padding-left: 1rem;
}
.map-table {
  width: 400%;
  border: 1px solid #f3f0f0;
  background: #ffffff;
}
.map-tables {
  border: 1px solid #f3f0f0;
  width: 100%;
  background: #ffffff;
}
/*主线路绘制下的覆盖物*/
.mapMarker {
  background-size: 100% 100% !important;
  display: none;
}
.mapdlfzxMarker {
  width: 2rem;
  height: 2rem;
  left: 1.5rem;
  top: 2rem;
  background-size: 100% 100% !important;
}
.mapZssbMarker {
  width: 1.5rem;
  height: 2.5rem;
  border-radius: 1rem;
  background-size: 100% 100% !important;
  left: 2rem;
}
.mapDlhzMarker {
  background-size: 100% 100% !important;
  display: none;
}
.mapPdzfMarker {
  background-size: 100% 100% !important;
  display: none;
}
.mapDlhzgdMarker {
  background-size: 100% 100% !important;
  display: none;
}
.movePointShowother {
  position: absolute;
  color: red;
  top: 0.5rem;
  display: none;
}
.movePointShowoLXHZ {
  position: absolute;
  color: red;
  font-size: 1.2rem;
  left: -2rem;
  top: 0.5rem;
}
.mapsZssbMarker {
  background-size: 100% 100% !important;
  display: none;
}

.mapjcMarker {
  width: 2rem;
  height: 2rem;
  border-radius: 1.5rem;
  background-size: 100% 100% !important;
}

.mapdyxhMarker {
  width: 2rem;
  height: 2rem;
  border-radius: 2rem;
  background-size: 100% 100% !important;
}
.mark-distance,
.chooseStartPoint {
  width: 2rem;
  height: 2rem;
  background-size: 100% 100%;
  position: absolute;
  left: 2rem;
  z-index: 9;
  font-size: 1rem;
  font-weight: bold;
  color: red;
}
.mark-lnglat {
  background-size: 100% 100%;
  position: absolute;
  left: 7rem;
  z-index: 9;
  font-size: 1rem;
  font-weight: bold;
  color: red;
}
.dlxxjSpec {
  color: red;
  background: linear-gradient(
    90deg,
    rgb(239, 232, 241) 0%,
    rgb(239, 238, 245) 52%,
    rgb(227, 231, 236) 100%
  );
  position: absolute;
  width: max-content;
}
.zssbxj {
  display: none;
}
.dlxxj {
  width: 0.8rem;
  display: none;
}
.angleImg {
  width: 100%;
  height: 100%;
}
.zxlhzzxxj {
  background: url('~@/assets/map/mapmarker/new/ganta.png');
}
.zxlhzzxyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/ganta.png');
}
.zxlhzzxgz {
  background: url('~@/assets/map/mapmarker/reform/ganta.png');
}
.zxlhzzxcc {
  background: url('~@/assets/map/mapmarker/remove/ganta.png');
}
/*分支线路绘制*/
.fzxlhzzxxj {
  background: url('~@/assets/map/mapmarker/new/fzxlg_new.png');
}
.fzxlhzzxyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/fzxlg_yy.png');
}
.fzxlhzzxgz {
  background: url('~@/assets/map/mapmarker/reform/fzxlg_gz.png');
}
.fzxlhzzxcc {
  background: url('~@/assets/map/mapmarker/remove/fzxlg_cc.png');
}
/*低压下户线路图元*/
.dyxhdxxj {
  background: url('~@/assets/map/mapmarker/new/dxbx_new.png');
}
.dyxhdxyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/dxbx_yy.png');
}
.dyxhdxcc {
  background: url('~@/assets/map/mapmarker/remove/dxbx_cc.png');
}
.dyxhsxxj {
  background: url('~@/assets/map/mapmarker/new/sxbx_new.png');
}
.dyxhsxyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/sxbx_yy.png');
}
.dyxhsxcc {
  background: url('~@/assets/map/mapmarker/remove/sxbx_cc.png');
}
/*附属设施*/
.fssshz {
  left: 5.2rem;
  font-weight: bold;
  color: red;
  font-size: 1.2rem;
  top: 2rem;
}
/*柱上设备*/
.zsdlqxj {
  background: url('~@/assets/map/mapmarker/new/zsdlq_new.png');
}
.zsdlqyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/zsdlq_yy.png');
}
.zsdlqcc {
  background: url('~@/assets/map/mapmarker/remove/zsdlq_cc.png');
}
.zsfhkgxj {
  background: url('~@/assets/map/mapmarker/new/zsfhkg_new.png');
}
.zsfhkgyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/zsfhkg_yy.png');
}
.zsfhkgcc {
  background: url('~@/assets/map/mapmarker/remove/zsfhkg_cc.png');
}
.zsglkgxj {
  background: url('~@/assets/map/mapmarker/new/zsglkg_new.png');
}
.zsglkgyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/zsglkg_yy.png');
}
.zsglkgcc {
  background: url('~@/assets/map/mapmarker/remove/zsglkg_cc.png');
}
.zsblqxj {
  background: url('~@/assets/map/mapmarker/new/zsblq_new.png');
}
.zsblqyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/zsblq_yy.png');
}
.zsblqcc {
  background: url('~@/assets/map/mapmarker/remove/zsblq_cc.png');
}
.zsrdqxj {
  background: url('~@/assets/map/mapmarker/new/zsrdq_new.png');
}
.zsrdqyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/zsrdq_yy.png');
}
.zsrdqcc {
  background: url('~@/assets/map/mapmarker/remove/zsrdq_cc.png');
}
/*电缆拐点*/
.dlgddhz {
  background: url('~@/assets/map/mapmarker/new/dlgd_new.png');
}
/*终端头绘制*/
.dlzdtxj {
  background: url('~@/assets/map/mapmarker/new/dlzdt_new.png');
  left: 2rem;
}
.dlzdtyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/dlzdt_yy.png');
  left: 2rem;
}
.dlzdtcc {
  background: url('~@/assets/map/mapmarker/remove/dlzdt_cc.png');
  left: 2rem;
}

/*电缆中间头*/
.dlzjtxj {
  background: url('~@/assets/map/mapmarker/new/dlzjt_new.png');
}
.dlzjtyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/dlzjt_yy.png');
}
.dlzjtcc {
  background: url('~@/assets/map/mapmarker/remove/dlzjt_cc.png');
}
/*电缆井*/
.dljxjks {
  background: url('~@/assets/map/mapmarker/new/dlj_new.png');
}
.dljxjjs {
  background: url('~@/assets/map/mapmarker/new/dlj_new.png');
}
.dljyyks {
  background: url('~@/assets/map/mapmarker/whiteOriginal/dlj_yy.png');
}
.dljyyjs {
  background: url('~@/assets/map/mapmarker/whiteOriginal/dlj_yy.png');
}
/*电缆分支箱绘制*/
.dlfzxxj {
  background: url('~@/assets/map/mapmarker/new/dlfzx_new.png');
}
.dlfzxyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/dlfzx_yy.png');
}
.dlzjtcc {
  background: url('~@/assets/map/mapmarker/remove/dlfzx_cc.png');
}
/*配电站房*/
/*箱式变*/
.pdzfxsbxj {
  background: url('~@/assets/map/mapmarker/new/pdzfxsb_new.png');
}
.pdzfxsbyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/pdzfxsb_yy.png');
}
.pdzfxsbcc {
  background: url('~@/assets/map/mapmarker/remove/pdzfxsb_cc.png');
}
/*配电站*/
.dlbdzxj {
  background: url('~@/assets/map/mapmarker/new/bdzx_.png');
}
.dlbdzyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/bdzy_.png');
}
.dlbdzcc {
  background: url('~@/assets/map/mapmarker/remove/bdzc_.png');
}
/*环网箱*/
.pdzfhwxxj {
  background: url('~@/assets/map/mapmarker/new/pdzfhwx_new.png');
}
.pdzfhwxyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/pdzfhwx_yy.png');
}
.pdzfhwxcc {
  background: url('~@/assets/map/mapmarker/remove/pdzfhwx_cc.png');
}
/*环网室*/
.pdzfhwsxj {
  background: url('~@/assets/map/mapmarker/new/pdzfhws_new.png');
}
.pdzfhwsyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/pdzfhws_yy.png');
}
.pdzfhwscc {
  background: url('~@/assets/map/mapmarker/remove/pdzfhws_cc.png');
}
/*配电室*/
.pdzfpdsdxj {
  background: url('~@/assets/map/mapmarker/new/pdzfpds_new.png');
}
.pdzfpdsdyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/pdzfpds_yy.png');
}
.pdzfpdsdcc {
  background: url('~@/assets/map/mapmarker/remove/pdzfpds_cc.png');
}
/*开关站*/
.pdzfkgzxj {
  background: url('~@/assets/map/mapmarker/new/pdzfkgz_new.png');
}
.pdzfkgzyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/pdzfkgz_yy.png');
}
.pdzfkgzcc {
  background: url('~@/assets/map/mapmarker/remove/pdzfkgz_cc.png');
}
/*变电站*/
.pdzfbdzxj {
  background: url('~@/assets/map/mapmarker/new/pdzfbdz_new.png');
}
.pdzfbdzyy {
  background: url('~@/assets/map/mapmarker/whiteOriginal/pdzfbdz_yy.png');
}
.pdzfbdzcc {
  background: url('~@/assets/map/mapmarker/remove/pdzfbdz_cc.png');
}
.pickerSelect .van-cell__value .van-field__body input {
  text-align: center;
}
.map-midArea {
  display: flex;
  height: 100%;
}
.map-midArea .map-rows {
  /*flex: 1;*/
  width: 100%;
  height: 100%;
}
.map-midArea .map-rows .map-cols {
  height: 100%;
}
.map-midArea .map-leftArea {
  flex: 1;
  width: 100%;
  height: 100%;
  display: flex;
}
@media screen and (min-width: 400px) {
  .map-rightArea {
    flex: 1;
    width: 40%;
    height: 50%;
    position: absolute;
    right: 1rem;
    bottom: 6rem;
  }
  .pro-addEveryArea .van-popup {
    position: fixed;
    width: 40%;
    right: 13px;
    bottom: 6rem;
    left: unset;
  }
  .map-rankgt .van-popup {
    position: fixed;
    width: 40%;
    right: 13px;
    bottom: 6rem;
    left: unset;
  }
  .mapimg {
    width: 3.5rem;
    height: 3.5rem;
  }
}
@media screen and (max-width: 399px) and (min-width: 350px) {
  .map-rightArea {
    flex: 1;
    width: 85%;
    height: 50%;
    position: absolute;
    right: 1rem;
    bottom: 6rem;
  }
  .mapimg {
    width: 2.5rem;
    height: 2.5rem;
  }
  .pro-addEveryArea .van-popup {
    position: fixed;
    width: 85%;
    right: 13px;
    bottom: 6rem;
    left: unset;
  }
  .map-rankgt .van-popup {
    position: fixed;
    width: 85%;
    right: 13px;
    bottom: 6rem;
    left: unset;
  }
}
#dw2map {
  width: 100%;
  height: 100%;
}
.mapites-backImg {
  width: 2rem;
  margin-right: 1rem;
  font-size: 1rem;
  margin-top: 0.5rem;
  height: 1.5rem;
}
.mapLineCroow {
  color: red;
  font-size: 2rem;
}
.mapLineFourLine,
.mapLineTwoLine {
  color: red;
  font-size: 1.5rem;
}
/*选择下面区域的地方*/
.map-choseIcon {
  position: absolute;
  bottom: 40px;
  width: 21rem;
  height: 5rem;
  left: 50%;
  display: flex;
  z-index: 999;
  transform: translate(-50%, 0);
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.52) 0%,
    #ffffff 100%
  );
  border-radius: 0.5rem;
  /*background: url("~@/assets/map/settingImg/bgicon.png");*/
  /*background-size: 100% 100%;*/
}
.map-choseIcon .map-iconleftArea,
.map-iconmidArea,
.map-iconrightArea {
  flex: 1;
  height: 100%;
  width: 100%;
}
.map-choseIcon .map-levelHead,
.map-cable,
.map-courts,
.map-znsj,
.map-znsj-active {
  width: 55%;
  height: 85%;
  margin: 0 auto;
  margin-top: 0.3rem;
}
.map-choseIcon .map-highlevelHead,
.map-highcable,
.map-highcourts {
  /*width: 100%;*/
  /*height: 125%;*/
  /*margin: 0 auto;*/
  /*position: relative;*/
  /*top: -10px;*/
  width: 55%;
  height: 85%;
  margin: 0 auto;
  margin-top: 0.3rem;
}
.map-choseIcon .map-znsj {
  background: url('~@/assets/map/settingImg/zn.png');
  background-size: 100% 100%;
}
.map-choseIcon .map-znsj-active {
  background: url('~@/assets/map/settingImg/zn-active.png');
  background-size: 100% 100%;
}
.map-choseIcon .map-levelHead {
  /*background: url("~@/assets/map/settingImg/levelhead.png");*/
  background: url('~@/assets/map/settingImg/jk.png');
  background-size: 100% 100%;
}
.map-choseIcon .map-cable {
  /*background: url("~@/assets/map/settingImg/cable.png");*/
  background: url('~@/assets/map/settingImg/dl.png');
  background-size: 100% 100%;
}
.map-choseIcon .map-courts {
  /*background: url("~@/assets/map/settingImg/caurts.png");*/
  background: url('~@/assets/map/settingImg/tq.png');
  background-size: 100% 100%;
}
.map-choseIcon .map-highlevelHead {
  /*background: url("~@/assets/map/settingImg/levelheadHigjh.png");*/
  background: url('~@/assets/map/settingImg/jk-active.png');
  background-size: 100% 100%;
}
.map-choseIcon .map-highcable {
  /*background: url("~@/assets/map/settingImg/cableHigh.png");*/
  background: url('~@/assets/map/settingImg/dl-active.png');
  background-size: 100% 100%;
}
.map-choseIcon .map-highcourts {
  /*background: url("~@/assets/map/settingImg/courtsHigh.png");*/
  background: url('~@/assets/map/settingImg/tq-active.png');
  background-size: 100% 100%;
}

.pro-addEveryArea .van-picker__columns {
  height: 250px !important;
}
.pro-setRageBtn {
  display: flex;
  height: 37px;
  position: absolute;
  bottom: 5px;
  width: 100%;
}
.pro-setRageBtn button {
  flex: 1;
  border: none;
  color: #ffffff;
}
.pro-setRageBtn .pro-cancle {
  background: #ffffff;
  border: 1px solid #526ade;
  border-radius: 20px;
  color: #526ade;
}
.pro-setRageBtn .pro-confirm {
  background: #526ade;
  border-radius: 20px;
}
.van-picker-column__wrapper {
  /*transform: translate3d(0px, 30px, 0px) !important;*/
}
/* 适量化平台的设置样式开始 */
.directiveDisTance{
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  font-size: 16px !important;
  color: red !important;
  box-shadow: none !important;
  border-radius: none !important;
  background: none !important;
}
.directiveMarks, .directiveLines{
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  font-size: 12px !important;
  color: red !important;
  box-shadow: none !important;
  border-radius: none !important;
  text-align: center !important;
  background: none !important;
}
.directiveDisTance::before{
  content: none !important;
}
.directiveMarks::before{
  content: none !important;
}
.directiveLines::before{
  content: none !important;
}
.leaflet-popup-content{
  cursor: pointer !important;
}
/* 适量化平台的设置样式结束 */