define(["./when-b60132fc","./Cartesian2-47311507","./ArcType-29cf2197","./arrayFill-4513d7ad","./BoundingRectangle-3ed8ca6d","./buildModuleUrl-4e1b81e7","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./EllipsoidGeodesic-0f19ac62","./EllipsoidTangentPlane-edb321d3","./GeometryAttribute-3a88ba31","./GeometryInstance-68aae013","./GeometryOffsetAttribute-fbeb6f1a","./GeometryPipeline-9d1ef0b6","./IndexDatatype-8a5eead4","./Math-119be1a3","./PrimitiveType-a54dc62f","./PolygonGeometryLibrary-92af6f1e","./PolygonPipeline-660e1625","./VertexFormat-6446fca0","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian4-3ca25aab","./IntersectionTests-7386ffbf","./Plane-7ae8294c","./FeatureDetection-c3b71206","./AttributeCompression-90851096","./EncodedCartesian3-f1396b05","./arrayRemoveDuplicates-d2f048c5","./EllipsoidRhumbLine-ed1a6bf4","./GeometryAttributes-252e9929","./earcut-2.2.1-20c8012f"],(function(e,t,o,r,a,i,n,s,l,u,p,d,c,g,m,y,h,f,b,_,v,C,P,x,T,w,A,E,I,G,V,F,H,N){"use strict";var O=new n.Cartographic,R=new n.Cartographic;function D(e,t,o,r){var a=r.cartesianToCartographic(e,O).height,i=r.cartesianToCartographic(t,R);i.height=a,r.cartographicToCartesian(i,t);var n=r.cartesianToCartographic(o,R);n.height=a-100,r.cartographicToCartesian(n,o)}var L=new a.BoundingRectangle,M=new n.Cartesian3,B=new n.Cartesian3,S=new n.Cartesian3,k=new n.Cartesian3,z=new n.Cartesian3,Y=new n.Cartesian3,U=new n.Cartesian3,W=new n.Cartesian3,j=new n.Cartesian3,Q=new t.Cartesian2,q=new t.Cartesian2,K=new n.Cartesian3,Z=new d.Quaternion,J=new f.Matrix3,X=new f.Matrix3;function $(o){var a=o.vertexFormat,i=o.geometry,s=o.shadowVolume,u=i.attributes.position.values,p=u.length,c=o.wall,m=o.top,y=o.bottom;if(a.st||a.normal||a.tangent||a.bitangent||s){var b=o.boundingRectangle,_=o.tangentPlane,v=o.ellipsoid,C=o.stRotation,P=o.perPositionHeight,x=Q;x.x=b.x,x.y=b.y;var T,w=a.st?new Float32Array(p):void 0;a.normal&&(T=P&&m&&!c?i.attributes.normal.values:new Float32Array(p));var A=a.tangent?new Float32Array(p):void 0,E=a.bitangent?new Float32Array(p):void 0,I=s?new Float32Array(p):void 0,G=0,V=0,F=B,H=S,N=k,O=!0,R=J,L=X;if(0!==C){var $=d.Quaternion.fromAxisAngle(_._plane.normal,C,Z);R=f.Matrix3.fromQuaternion($,R),$=d.Quaternion.fromAxisAngle(_._plane.normal,-C,Z),L=f.Matrix3.fromQuaternion($,L)}else R=f.Matrix3.clone(f.Matrix3.IDENTITY,R),L=f.Matrix3.clone(f.Matrix3.IDENTITY,L);var ee=0;(m&&y||c)&&(ee=p/2,p/=2);var te=1,oe=[];if(a.st&&c&&o.isComputeTexCoord){let e=[...o.outerPositions,o.outerPositions[0]];for(let t=1;t<e.length;t++){te+=n.Cartesian3.distance(e[t-1],e[t]),oe.push(te)}}for(var re=0;re<p;re+=3){var ae=n.Cartesian3.fromArray(u,re,K);if(a.st){var ie=f.Matrix3.multiplyByVector(R,ae,M);ie=v.scaleToGeodeticSurface(ie,ie);var ne=_.projectPointOntoPlane(ie,q);t.Cartesian2.subtract(ne,x,ne);var se=h.CesiumMath.clamp(ne.x/b.width,0,1),le=h.CesiumMath.clamp(ne.y/b.height,0,1);if(c&&te>1){let e=Math.ceil(re/6)-1;se=oe[e]?oe[e]/te:0,w[G]=1-se,w[G+1]=1,w[G+2]=0,w[G+ee]=1-se,w[G+1+ee]=0,w[G+2+ee]=0}y&&(w[G+ee]=se,w[G+1+ee]=le,w[G+2+ee]=-1),m&&(w[G]=se,w[G+1]=le,w[G+2]=1),G+=3}if(a.normal||a.tangent||a.bitangent||s){var ue=V+1,pe=V+2;if(c){if(re+3<p){var de=n.Cartesian3.fromArray(u,re+3,z);if(O){var ce=n.Cartesian3.fromArray(u,re+p,Y);P&&D(ae,de,ce,v),n.Cartesian3.subtract(de,ae,de),n.Cartesian3.subtract(ce,ae,ce),F=n.Cartesian3.normalize(n.Cartesian3.cross(ce,de,F),F),O=!1}n.Cartesian3.equalsEpsilon(de,ae,h.CesiumMath.EPSILON10)&&(O=!0)}(a.tangent||a.bitangent)&&(N=v.geodeticSurfaceNormal(ae,N),a.tangent&&(H=n.Cartesian3.normalize(n.Cartesian3.cross(N,F,H),H)))}else F=v.geodeticSurfaceNormal(ae,F),(a.tangent||a.bitangent)&&(P&&(U=n.Cartesian3.fromArray(T,V,U),W=n.Cartesian3.cross(n.Cartesian3.UNIT_Z,U,W),W=n.Cartesian3.normalize(f.Matrix3.multiplyByVector(L,W,W),W),a.bitangent&&(j=n.Cartesian3.normalize(n.Cartesian3.cross(U,W,j),j))),H=n.Cartesian3.cross(n.Cartesian3.UNIT_Z,F,H),H=n.Cartesian3.normalize(f.Matrix3.multiplyByVector(L,H,H),H),a.bitangent&&(N=n.Cartesian3.normalize(n.Cartesian3.cross(F,H,N),N)));a.normal&&(o.wall?(T[V+ee]=F.x,T[ue+ee]=F.y,T[pe+ee]=F.z):y&&(T[V+ee]=-F.x,T[ue+ee]=-F.y,T[pe+ee]=-F.z),(m&&!P||c)&&(T[V]=F.x,T[ue]=F.y,T[pe]=F.z)),s&&(c&&(F=v.geodeticSurfaceNormal(ae,F)),I[V+ee]=-F.x,I[ue+ee]=-F.y,I[pe+ee]=-F.z),a.tangent&&(o.wall?(A[V+ee]=H.x,A[ue+ee]=H.y,A[pe+ee]=H.z):y&&(A[V+ee]=-H.x,A[ue+ee]=-H.y,A[pe+ee]=-H.z),m&&(P?(A[V]=W.x,A[ue]=W.y,A[pe]=W.z):(A[V]=H.x,A[ue]=H.y,A[pe]=H.z))),a.bitangent&&(y&&(E[V+ee]=N.x,E[ue+ee]=N.y,E[pe+ee]=N.z),m&&(P?(E[V]=j.x,E[ue]=j.y,E[pe]=j.z):(E[V]=N.x,E[ue]=N.y,E[pe]=N.z))),V+=3}}a.st&&(i.attributes.st=new d.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:w})),a.normal&&(i.attributes.normal=new d.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:T})),a.tangent&&(i.attributes.tangent=new d.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:A})),a.bitangent&&(i.attributes.bitangent=new d.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:E})),s&&(i.attributes.extrudeDirection=new d.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:I}))}if(o.extrude&&e.defined(o.offsetAttribute)){var ge=u.length/3,me=new Uint8Array(ge);if(o.offsetAttribute===g.GeometryOffsetAttribute.TOP)m&&y||c?me=r.arrayFill(me,1,0,ge/2):m&&(me=r.arrayFill(me,1));else{var ye=o.offsetAttribute===g.GeometryOffsetAttribute.NONE?0:1;me=r.arrayFill(me,ye)}i.attributes.applyOffset=new d.GeometryAttribute({componentDatatype:l.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:me})}return i}var ee=new n.Cartographic,te=new n.Cartographic,oe={west:0,east:0},re=new u.EllipsoidGeodesic;function ae(r,a,i,n,s){if(s=e.defaultValue(s,new t.Rectangle),!e.defined(r)||r.length<3)return s.west=0,s.north=0,s.south=0,s.east=0,s;if(i===o.ArcType.RHUMB)return t.Rectangle.fromCartesianArray(r,a,s);re.ellipsoid.equals(a)||(re=new u.EllipsoidGeodesic(void 0,void 0,a)),s.west=Number.POSITIVE_INFINITY,s.east=Number.NEGATIVE_INFINITY,s.south=Number.POSITIVE_INFINITY,s.north=Number.NEGATIVE_INFINITY,oe.west=Number.POSITIVE_INFINITY,oe.east=Number.NEGATIVE_INFINITY;for(var l,p=1/h.CesiumMath.chordLength(n,a.maximumRadius),d=r.length,c=a.cartesianToCartographic(r[0],te),g=ee,m=1;m<d;m++)l=g,g=c,c=a.cartesianToCartographic(r[m],l),re.setEndPoints(g,c),ne(re,p,s,oe);return l=g,g=c,c=a.cartesianToCartographic(r[0],l),re.setEndPoints(g,c),ne(re,p,s,oe),s.east-s.west>oe.west-oe.east&&(s.east=oe.east,s.west=oe.west),s}var ie=new n.Cartographic;function ne(e,t,o,r){for(var a=e.surfaceDistance,i=Math.ceil(a*t),n=i>0?a/(i-1):Number.POSITIVE_INFINITY,s=0,l=0;l<i;l++){var u=e.interpolateUsingSurfaceDistance(s,ie);s+=n;var p=u.longitude,d=u.latitude;o.west=Math.min(o.west,p),o.east=Math.max(o.east,p),o.south=Math.min(o.south,d),o.north=Math.max(o.north,d),r.west=p>0?Math.min(p,r.west):r.west,r.east=p<0?Math.max(p,r.east):r.east}}var se=[];function le(e,t,o,r,a,i,n,s,l,u){var d,g={walls:[]};if(i||n){var m,h,f=b.PolygonGeometryLibrary.createGeometryFromPositions(e,t,o,a,s,l),v=f.attributes.position.values,C=f.indices;if(i&&n){var P=v.concat(v);m=P.length/3,(h=y.IndexDatatype.createTypedArray(m,2*C.length)).set(C);var x=C.length,T=m/2;for(d=0;d<x;d+=3){var w=h[d]+T,A=h[d+1]+T,E=h[d+2]+T;h[d+x]=E,h[d+1+x]=A,h[d+2+x]=w}if(f.attributes.position.values=P,a&&s.normal){var I=f.attributes.normal.values;f.attributes.normal.values=new Float32Array(P.length),f.attributes.normal.values.set(I)}f.indices=h}else if(n){for(m=v.length/3,h=y.IndexDatatype.createTypedArray(m,C.length),d=0;d<C.length;d+=3)h[d]=C[d+2],h[d+1]=C[d+1],h[d+2]=C[d];f.indices=h}g.topAndBottom=new c.GeometryInstance({geometry:f})}var G,V=r.outerRing,F=p.EllipsoidTangentPlane.fromPoints(V,e),H=F.projectPointsOntoPlane(V,se),N=_.PolygonPipeline.computeWindingOrder2D(H);N===_.WindingOrder.CLOCKWISE&&(V=V.slice().reverse()),u&&(G=b.PolygonGeometryLibrary.computeWallGeometry(V,e,o,a,l),g.walls.push(new c.GeometryInstance({geometry:G})));var O=r.holes;for(d=0;d<O.length;d++){var R=O[d];H=(F=p.EllipsoidTangentPlane.fromPoints(R,e)).projectPointsOntoPlane(R,se),(N=_.PolygonPipeline.computeWindingOrder2D(H))===_.WindingOrder.COUNTER_CLOCKWISE&&(R=R.slice().reverse()),G=b.PolygonGeometryLibrary.computeWallGeometry(R,e,o,a,l),g.walls.push(new c.GeometryInstance({geometry:G}))}return g}function ue(r){var a=r.polygonHierarchy,i=e.defaultValue(r.vertexFormat,v.VertexFormat.DEFAULT),n=e.defaultValue(r.ellipsoid,t.Ellipsoid.WGS84),s=e.defaultValue(r.granularity,h.CesiumMath.RADIANS_PER_DEGREE),l=e.defaultValue(r.stRotation,0),u=e.defaultValue(r.perPositionHeight,!1),p=u&&e.defined(r.extrudedHeight),d=e.defaultValue(r.height,0),c=e.defaultValue(r.extrudedHeight,d);if(!p){var g=Math.max(d,c);c=Math.min(d,c),d=g}this._vertexFormat=v.VertexFormat.clone(i),this._ellipsoid=t.Ellipsoid.clone(n),this._granularity=s,this._stRotation=l,this._height=d,this._extrudedHeight=c,this._closeTop=e.defaultValue(r.closeTop,!0),this._closeBottom=e.defaultValue(r.closeBottom,!0),this._extrudeOutering=e.defaultValue(r.extrudeOutering,!0),this._polygonHierarchy=a,this._perPositionHeight=u,this._perPositionHeightExtrude=p,this._shadowVolume=e.defaultValue(r.shadowVolume,!1),this._workerName="createPolygonGeometry",this._offsetAttribute=r.offsetAttribute,this._arcType=e.defaultValue(r.arcType,o.ArcType.GEODESIC),this._groundBottomAltitude=e.defaultValue(r.groundBottomAltitude,void 0),this._groundExtrudedHeight=e.defaultValue(r.groundExtrudedHeight,0),this._rectangle=void 0,this._textureCoordinateRotationPoints=void 0,this._isComputeTexCoord=r.isComputeTexCoord,this.packedLength=b.PolygonGeometryLibrary.computeHierarchyPackedLength(a)+t.Ellipsoid.packedLength+v.VertexFormat.packedLength+12}ue.fromPositions=function(t){return new ue({polygonHierarchy:{positions:(t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT)).positions},height:t.height,extrudedHeight:t.extrudedHeight,vertexFormat:t.vertexFormat,stRotation:t.stRotation,ellipsoid:t.ellipsoid,granularity:t.granularity,perPositionHeight:t.perPositionHeight,closeTop:t.closeTop,closeBottom:t.closeBottom,offsetAttribute:t.offsetAttribute,arcType:t.arcType,isComputeTexCoord:t.isComputeTexCoord})},ue.pack=function(o,r,a){return a=e.defaultValue(a,0),a=b.PolygonGeometryLibrary.packPolygonHierarchy(o._polygonHierarchy,r,a),t.Ellipsoid.pack(o._ellipsoid,r,a),a+=t.Ellipsoid.packedLength,v.VertexFormat.pack(o._vertexFormat,r,a),a+=v.VertexFormat.packedLength,r[a++]=o._height,r[a++]=o._extrudedHeight,r[a++]=o._granularity,r[a++]=o._stRotation,r[a++]=o._perPositionHeightExtrude?1:0,r[a++]=o._perPositionHeight?1:0,r[a++]=o._closeTop?1:0,r[a++]=o._closeBottom?1:0,r[a++]=o._shadowVolume?1:0,r[a++]=e.defaultValue(o._offsetAttribute,-1),r[a++]=o._arcType,r[a]=o.packedLength,r};var pe=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),de=new v.VertexFormat,ce={polygonHierarchy:{}};return ue.unpack=function(o,r,a){r=e.defaultValue(r,0);var i=b.PolygonGeometryLibrary.unpackPolygonHierarchy(o,r);r=i.startingIndex,delete i.startingIndex;var n=t.Ellipsoid.unpack(o,r,pe);r+=t.Ellipsoid.packedLength;var s=v.VertexFormat.unpack(o,r,de);r+=v.VertexFormat.packedLength;var l=o[r++],u=o[r++],p=o[r++],d=o[r++],c=1===o[r++],g=1===o[r++],m=1===o[r++],y=1===o[r++],h=1===o[r++],f=o[r++],_=o[r++],C=o[r];return e.defined(a)||(a=new ue(ce)),a._polygonHierarchy=i,a._ellipsoid=t.Ellipsoid.clone(n,a._ellipsoid),a._vertexFormat=v.VertexFormat.clone(s,a._vertexFormat),a._height=l,a._extrudedHeight=u,a._granularity=p,a._stRotation=d,a._perPositionHeightExtrude=c,a._perPositionHeight=g,a._closeTop=m,a._closeBottom=y,a._shadowVolume=h,a._offsetAttribute=-1===f?void 0:f,a._arcType=_,a.packedLength=C,a},ue.computeRectangle=function(r,a){var i=e.defaultValue(r.granularity,h.CesiumMath.RADIANS_PER_DEGREE),n=e.defaultValue(r.arcType,o.ArcType.GEODESIC),s=r.polygonHierarchy,l=e.defaultValue(r.ellipsoid,t.Ellipsoid.WGS84);return ae(s.positions,l,n,i,a)},ue.createGeometry=function(t){var o=t._vertexFormat,a=t._ellipsoid,n=t._granularity,s=t._stRotation,u=t._polygonHierarchy,f=t._perPositionHeight,v=t._closeTop,C=t._closeBottom,P=t._arcType,x=u.positions;if(!(x.length<3)){var T=p.EllipsoidTangentPlane.fromPoints(x,a),w=b.PolygonGeometryLibrary.polygonsFromHierarchy(u,T.projectPointsOntoPlane.bind(T),!f,a),A=w.hierarchy,E=w.polygons;if(0!==A.length){x=A[0].outerRing;var I,G=b.PolygonGeometryLibrary.computeBoundingRectangle(T.plane.normal,T.projectPointOntoPlane.bind(T),x,s,L),V=[],F=t._height,H=t._extrudedHeight,N=t._perPositionHeightExtrude||!h.CesiumMath.equalsEpsilon(F,H,0,h.CesiumMath.EPSILON2),O={perPositionHeight:f,vertexFormat:o,geometry:void 0,tangentPlane:T,boundingRectangle:G,ellipsoid:a,stRotation:s,bottom:!1,top:!0,wall:!1,extrude:!1,arcType:P,outerPositions:x,isComputeTexCoord:t._isComputeTexCoord};if(N)for(O.extrude=!0,O.top=v,O.bottom=C,O.shadowVolume=t._shadowVolume,O.offsetAttribute=t._offsetAttribute,I=0;I<E.length;I++){var R,D=le(a,E[I],n,A[I],f,v,C,o,P,t._extrudeOutering);v&&C?(R=D.topAndBottom,O.geometry=b.PolygonGeometryLibrary.scaleToGeodeticHeightExtruded(R.geometry,F,H,a,f)):v?((R=D.topAndBottom).geometry.attributes.position.values=_.PolygonPipeline.scaleToGeodeticHeight(R.geometry.attributes.position.values,F,a,!f),O.geometry=R.geometry):C&&((R=D.topAndBottom).geometry.attributes.position.values=_.PolygonPipeline.scaleToGeodeticHeight(R.geometry.attributes.position.values,H,a,!0),O.geometry=R.geometry),(v||C)&&(O.wall=!1,R.geometry=$(O),V.push(R));var M=D.walls;O.wall=!0;for(var B=0;B<M.length;B++){var S=M[B];O.top=!1,O.bottom=!1,O.geometry=b.PolygonGeometryLibrary.scaleToGeodeticHeightExtruded(S.geometry,F,H,a,f),S.geometry=$(O),V.push(S)}}else for(I=0;I<E.length;I++){var k=new c.GeometryInstance({geometry:b.PolygonGeometryLibrary.createGeometryFromPositions(a,E[I],n,f,o,P)});if(k.geometry.attributes.position.values=_.PolygonPipeline.scaleToGeodeticHeight(k.geometry.attributes.position.values,F,a,!f),O.geometry=k.geometry,k.geometry=$(O),e.defined(t._offsetAttribute)){var z=k.geometry.attributes.position.values.length,Y=new Uint8Array(z/3),U=t._offsetAttribute===g.GeometryOffsetAttribute.NONE?0:1;r.arrayFill(Y,U),k.geometry.attributes.applyOffset=new d.GeometryAttribute({componentDatatype:l.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:Y})}V.push(k)}var W=m.GeometryPipeline.combineInstances(V)[0];W.attributes.position.values=new Float64Array(W.attributes.position.values),W.indices=y.IndexDatatype.createTypedArray(W.attributes.position.values.length/3,W.indices);var j=W.attributes,Q=i.BoundingSphere.fromVertices(j.position.values);return o.position||delete j.position,new d.Geometry({attributes:j,indices:W.indices,primitiveType:W.primitiveType,boundingSphere:Q,offsetAttribute:t._offsetAttribute})}}},ue.createShadowVolume=function(e,t,o){var r=e._granularity,a=e._ellipsoid,i=e._groundBottomAltitude+e._groundExtrudedHeight,n=e._groundBottomAltitude?e._groundBottomAltitude:t(r,a),s=i||o(r,a);return new ue({polygonHierarchy:e._polygonHierarchy,ellipsoid:a,stRotation:e._stRotation,granularity:r,perPositionHeight:!1,extrudedHeight:n,height:s,vertexFormat:v.VertexFormat.POSITION_ONLY,shadowVolume:!0,arcType:e._arcType})},Object.defineProperties(ue.prototype,{rectangle:{get:function(){if(!e.defined(this._rectangle)){var t=this._polygonHierarchy.positions;this._rectangle=ae(t,this._ellipsoid,this._arcType,this._granularity)}return this._rectangle}},textureCoordinateRotationPoints:{get:function(){return e.defined(this._textureCoordinateRotationPoints)||(this._textureCoordinateRotationPoints=function(e){var t=-e._stRotation;if(0===t)return[0,0,0,1,1,0];var o=e._ellipsoid,r=e._polygonHierarchy.positions,a=e.rectangle;return d.Geometry._textureCoordinateRotationPoints(r,t,o,a)}(this)),this._textureCoordinateRotationPoints}}}),function(o,r){return e.defined(r)&&(o=ue.unpack(o,r)),o._ellipsoid=t.Ellipsoid.clone(o._ellipsoid),ue.createGeometry(o)}}));
